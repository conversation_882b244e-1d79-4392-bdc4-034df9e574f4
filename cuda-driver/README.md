# cuda-driver

**The CUDA Driver is a C API that allows developers to create CUDA Driver applications for GPGPU architecture.**

## Build

### Bash build
```
./build.sh rel|dbg|clean
```

### Ninja build
```
mkdir build && cd build

# Build with kfe thunk layer
cmake -GNinja -DCMAKE_BUILD_TYPE=Debug -DENABLE_THUNK_LAYER=kfe -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..

# Build with dummy thunk layer
cmake -GNinja -DCMAKE_BUILD_TYPE=Debug -DENABLE_THUNK_LAYER=dummy -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..

# Build with kmd thunk layer
cmake -GNinja -DCMAKE_BUILD_TYPE=Debug -DENABLE_THUNK_LAYER=kmd -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..

ninja

# Optional, install it.
ninja install
```

- `-DENABLE_PRINTS_ASSERTS=ON`, build driver with enabling debug print and assertion, it is enabled by default.
- `-DENABLE_DEBUG_BREAK=ON`, build driver with enabling debug break, it is enabled by default.
- `-DCMAKE_INSTALL_PREFIX=<YOUR_INSTALL_PATH>`, install driver library into your installed path.
  By default, it will be installed into the current directory '<YOUR_WORKSPACE>/cuda-driver/install/'
- `-DENABLE_THUNK_LAYER=kfe|kmd|dummy`, Compile the THUNK layer as kfe, kmd or dummy.
  By default, it is `kfe`.

# Run test case

First, you need to add the corresponding library path for cuda.
```
export LD_LIBRARY_PATH=<YOUR_WORKSPACE_PATH>/cuda-driver/build/lib:${LD_LIBRARY_PATH}

cd build/bin/tests/unittests/0_simpleDrvInit

./simpleDrvInit
```
