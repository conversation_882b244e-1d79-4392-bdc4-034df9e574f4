cuda {
    global:
        cuArray3DCreate_v2;
        cuArray3DGetDescriptor_v2;
        cuArrayCreate_v2;
        cuArrayDestroy;
        cuArrayGetDescriptor_v2;
        cuArrayGetMemoryRequirements;
        cuArrayGetPlane;
        cuArrayGetSparseProperties;
        cuCoredumpGetAttribute;
        cuCoredumpGetAttributeGlobal;
        cuCoredumpSetAttribute;
        cuCoredumpSetAttributeGlobal;
        cuCtxAttach;
        cuCtxCreate_v2;
        cuCtxCreate_v3;
        cuCtxDetach;
        cuCtxDestroy_v2;
        cuCtxDisablePeerAccess;
        cuCtxEnablePeerAccess;
        cuCtxFromGreenCtx;
        cuCtxGetApiVersion;
        cuCtxGetCacheConfig;
        cuCtxGetCurrent;
        cuCtxGetDevice;
        cuCtxGetDevResource;
        cuCtxGetExecAffinity;
        cuCtxGetFlags;
        cuCtxGetId;
        cuCtxGetLimit;
        cuCtxGetSharedMemConfig;
        cuCtxGetStreamPriorityRange;
        cuCtxPopCurrent_v2;
        cuCtxPushCurrent_v2;
        cuCtxResetPersistingL2Cache;
        cuCtxSetCacheConfig;
        cuCtxSetCurrent;
        cuCtxSetFlags;
        cuCtxSetLimit;
        cuCtxSetSharedMemConfig;
        cuCtxSynchronize;
        cuDestroyExternalMemory;
        cuDestroyExternalSemaphore;
        cuDeviceCanAccessPeer;
        cuDeviceComputeCapability;
        cuDeviceGet;
        cuDeviceGetAttribute;
        cuDeviceGetByPCIBusId;
        cuDeviceGetCount;
        cuDeviceGetDefaultMemPool;
        cuDeviceGetDevResource;
        cuDeviceGetExecAffinitySupport;
        cuDeviceGetLuid;
        cuDeviceGetGraphMemAttribute;
        cuDeviceGetMemPool;
        cuDeviceGetName;
        cuDeviceGetNvSciSyncAttributes;
        cuDeviceGetP2PAttribute;
        cuDeviceGetPCIBusId;
        cuDeviceGetProperties;
        cuDeviceGetTexture1DLinearMaxWidth;
        cuDeviceGetUuid;
        cuDeviceGetUuid_v2;
        cuDevicePrimaryCtxGetState;
        cuDevicePrimaryCtxRelease_v2;
        cuDevicePrimaryCtxReset_v2;
        cuDevicePrimaryCtxRetain;
        cuDevicePrimaryCtxSetFlags_v2;
        cuDeviceRegisterAsyncNotification;
        cuDeviceSetGraphMemAttribute;
        cuDeviceSetMemPool;
        cuDeviceTotalMem;
        cuDeviceUnregisterAsyncNotification;
        cuDevResourceGenerateDesc;
        cuDevSmResourceSplitByCount;
        cuDriverGetVersion;
        cuEventCreate;
        cuEventDestroy_v2;
        cuEventElapsedTime;
        cuEventQuery;
        cuEventRecord;
        cuEventRecordWithFlags;
        cuEventSynchronize;
        cuExternalMemoryGetMappedBuffer;
        cuFlushGPUDirectRDMAWrites;
        cuFuncGetAttribute;
        cuFuncGetModule;
        cuFuncGetName;
        cuFuncGetParamInfo;
        cuFuncIsLoaded;
        cuFuncLoad;
        cuFuncSetAttribute;
        cuFuncSetBlockShape;
        cuFuncSetCacheConfig;
        cuFuncSetSharedMemConfig;
        cuFuncSetSharedSize;
        cuGetErrorName;
        cuGetErrorString;
        cuGetExportTable;
        cuGetProcAddress_v2;
        cuGraphAddBatchMemOpNode;
        cuGraphAddChildGraphNode;
        cuGraphAddDependencies;
        cuGraphAddDependencies_v2;
        cuGraphAddEmptyNode;
        cuGraphAddEventRecordNode;
        cuGraphAddEventWaitNode;
        cuGraphAddExternalSemaphoresSignalNode;
        cuGraphAddExternalSemaphoresWaitNode;
        cuGraphAddHostNode;
        cuGraphAddKernelNode_v2;
        cuGraphAddMemAllocNode;
        cuGraphAddMemcpyNode;
        cuGraphAddMemFreeNode;
        cuGraphAddMemsetNode;
        cuGraphAddNode;
        cuGraphAddNode_v2;
        cuGraphBatchMemOpNodeGetParams;
        cuGraphBatchMemOpNodeSetParams;
        cuGraphChildGraphNodeGetGraph;
        cuGraphClone;
        cuGraphConditionalHandleCreate;
        cuGraphCreate;
        cuGraphDebugDotPrint;
        cuGraphDestroy;
        cuGraphDestroyNode;
        cuGraphEventRecordNodeGetEvent;
        cuGraphEventRecordNodeSetEvent;
        cuGraphEventWaitNodeGetEvent;
        cuGraphEventWaitNodeSetEvent;
        cuGraphExecBatchMemOpNodeSetParams;
        cuGraphExecChildGraphNodeSetParams;
        cuGraphExecDestroy;
        cuGraphExecEventRecordNodeSetEvent;
        cuGraphExecEventWaitNodeSetEvent;
        cuGraphExecExternalSemaphoresSignalNodeSetParams;
        cuGraphExecExternalSemaphoresWaitNodeSetParams;
        cuGraphExecGetFlags;
        cuGraphExecHostNodeSetParams;
        cuGraphExecKernelNodeSetParams_v2;
        cuGraphExecMemcpyNodeSetParams;
        cuGraphExecMemsetNodeSetParams;
        cuGraphExecNodeSetParams;
        cuGraphExecUpdate_v2;
        cuGraphExternalSemaphoresSignalNodeGetParams;
        cuGraphExternalSemaphoresSignalNodeSetParams;
        cuGraphExternalSemaphoresWaitNodeGetParams;
        cuGraphExternalSemaphoresWaitNodeSetParams;
        cuGraphGetEdges;
        cuGraphGetEdges_v2;
        cuGraphGetNodes;
        cuGraphGetRootNodes;
        cuGraphHostNodeGetParams;
        cuGraphHostNodeSetParams;
        cuGraphInstantiateWithFlags;
        cuGraphInstantiateWithParams;
        cuGraphKernelNodeCopyAttributes;
        cuGraphKernelNodeGetAttribute;
        cuGraphKernelNodeGetParams_v2;
        cuGraphKernelNodeSetAttribute;
        cuGraphKernelNodeSetParams_v2;
        cuGraphLaunch;
        cuGraphMemcpyNodeGetParams;
        cuGraphMemcpyNodeSetParams;
        cuGraphMemFreeNodeGetParams;
        cuGraphMemsetNodeGetParams;
        cuGraphMemsetNodeSetParams;
        cuGraphNodeFindInClone;
        cuGraphNodeGetDependencies;
        cuGraphNodeGetDependencies_v2;
        cuGraphNodeGetDependentNodes;
        cuGraphNodeGetDependentNodes_v2;
        cuGraphNodeGetEnabled;
        cuGraphNodeGetType;
        cuGraphNodeSetEnabled;
        cuGraphNodeSetParams;
        cuGraphReleaseUserObject;
        cuGraphRemoveDependencies;
        cuGraphRemoveDependencies_v2;
        cuGraphRetainUserObject;
        cuGraphUpload;
        cuGreenCtxCreate;
        cuGreenCtxDestroy;
        cuGreenCtxGetDevResource;
        cuGreenCtxRecordEvent;
        cuGreenCtxWaitEvent;
        cuImportExternalMemory;
        cuImportExternalSemaphore;
        cuInit;
        cuIpcCloseMemHandle;
        cuIpcGetEventHandle;
        cuIpcGetMemHandle;
        cuIpcOpenEventHandle;
        cuIpcOpenMemHandle_v2;
        cuKernelGetAttribute;
        cuKernelGetFunction;
        cuKernelGetName;
        cuKernelGetParamInfo;
        cuKernelSetAttribute;
        cuKernelSetCacheConfig;
        cuLaunch;
        cuLaunchCooperativeKernel;
        cuLaunchCooperativeKernelMultiDevice;
        cuLaunchGrid;
        cuLaunchGridAsync;
        cuLaunchHostFunc;
        cuLaunchKernel;
        cuLaunchKernelEx;
        cuLibraryEnumerateKernels;
        cuLibraryGetGlobal;
        cuLibraryGetKernel;
        cuLibraryGetKernelCount;
        cuLibraryGetManaged;
        cuLibraryGetModule;
        cuLibraryGetUnifiedFunction;
        cuLibraryLoadData;
        cuLibraryLoadFromFile;
        cuLibraryUnload;
        cuLinkAddData_v2;
        cuLinkAddFile_v2;
        cuLinkComplete;
        cuLinkCreate_v2;
        cuLinkDestroy;
        cuMemAddressFree;
        cuMemAddressReserve;
        cuMemAdvise;
        cuMemAdvise_v2;
        cuMemAlloc_v2;
        cuMemAllocAsync;
        cuMemAllocFromPoolAsync;
        cuMemAllocHost_v2;
        cuMemAllocManaged;
        cuMemAllocPitch_v2;
        cuMemcpy;
        cuMemcpy2D_v2;
        cuMemcpy2DAsync_v2;
        cuMemcpy2DUnaligned_v2;
        cuMemcpy3D_v2;
        cuMemcpy3DAsync_v2;
        cuMemcpy3DPeer;
        cuMemcpy3DPeerAsync;
        cuMemcpyAsync;
        cuMemcpyAtoA_v2;
        cuMemcpyAtoD_v2;
        cuMemcpyAtoH_v2;
        cuMemcpyAtoHAsync_v2;
        cuMemcpyDtoA_v2;
        cuMemcpyDtoD_v2;
        cuMemcpyDtoDAsync_v2;
        cuMemcpyDtoH_v2;
        cuMemcpyDtoHAsync_v2;
        cuMemcpyHtoA_v2;
        cuMemcpyHtoAAsync_v2;
        cuMemcpyHtoD_v2;
        cuMemcpyHtoDAsync_v2;
        cuMemcpyPeer;
        cuMemcpyPeerAsync;
        cuMemCreate;
        cuMemExportToShareableHandle;
        cuMemFree_v2;
        cuMemFreeAsync;
        cuMemFreeHost;
        cuMemGetAccess;
        cuMemGetAddressRange_v2;
        cuMemGetAllocationGranularity;
        cuMemGetAllocationPropertiesFromHandle;
        cuMemGetHandleForAddressRange;
        cuMemGetInfo_v2;
        cuMemHostAlloc;
        cuMemHostGetDevicePointer_v2;
        cuMemHostGetFlags;
        cuMemHostRegister_v2;
        cuMemHostUnregister;
        cuMemImportFromShareableHandle;
        cuMemMap;
        cuMemMapArrayAsync;
        cuMemRelease;
        cuMemRetainAllocationHandle;
        cuMemSetAccess;
        cuMemUnmap;
        cuMemsetD16_v2;
        cuMemsetD16Async;
        cuMemsetD2D16_v2;
        cuMemsetD2D16Async;
        cuMemsetD2D32_v2;
        cuMemsetD2D32Async;
        cuMemsetD2D8_v2;
        cuMemsetD2D8Async;
        cuMemsetD32_v2;
        cuMemsetD32Async;
        cuMemsetD8_v2;
        cuMemsetD8Async;
        cuMemPoolCreate;
        cuMemPoolDestroy;
        cuMemPoolExportPointer;
        cuMemPoolExportToShareableHandle;
        cuMemPoolGetAccess;
        cuMemPoolGetAttribute;
        cuMemPoolImportFromShareableHandle;
        cuMemPoolImportPointer;
        cuMemPoolSetAccess;
        cuMemPoolSetAttribute;
        cuMemPoolTrimTo;
        cuMemPrefetchAsync;
        cuMemPrefetchAsync_v2;
        cuMemRangeGetAttribute;
        cuMemRangeGetAttributes;
        cuModuleEnumerateFunctions;
        cuModuleGetFunction;
        cuModuleGetFunctionCount;
        cuModuleGetGlobal_v2;
        cuModuleGetLoadingMode;
        cuModuleLoad;
        cuModuleLoadData;
        cuModuleLoadDataEx;
        cuModuleLoadFatBinary;
        cuModuleUnload;
        cuMulticastAddDevice;
        cuMulticastBindAddr;
        cuMulticastBindMem;
        cuMulticastCreate;
        cuMulticastGetGranularity;
        cuMulticastUnbind;
        cuOccupancyAvailableDynamicSMemPerBlock;
        cuOccupancyMaxActiveBlocksPerMultiprocessor;
        cuOccupancyMaxActiveBlocksPerMultiprocessorWithFlags;
        cuOccupancyMaxPotentialBlockSize;
        cuOccupancyMaxPotentialBlockSizeWithFlags;
        cuOccupancyMaxPotentialClusterSize;
        cuParamSetf;
        cuParamSeti;
        cuParamSetSize;
        cuParamSetv;
        cuProfilerInitialize;
        cuProfilerStart;
        cuProfilerStop;
        cuPointerGetAttribute;
        cuPointerGetAttributes;
        cuPointerSetAttribute;
        cuSignalExternalSemaphoresAsync;
        cuStreamAddCallback;
        cuStreamAttachMemAsync;
        cuStreamBeginCapture_v2;
        cuStreamBeginCaptureToGraph;
        cuStreamCopyAttributes;
        cuStreamCreate;
        cuStreamCreateWithPriority;
        cuStreamDestroy_v2;
        cuStreamEndCapture;
        cuStreamGetAttribute;
        cuStreamGetCaptureInfo_v2;
        cuStreamGetCaptureInfo_v3;
        cuStreamGetCtx;
        cuStreamGetFlags;
        cuStreamGetGreenCtx;
        cuStreamGetId;
        cuStreamGetPriority;
        cuStreamIsCapturing;
        cuStreamQuery;
        cuStreamSetAttribute;
        cuStreamSynchronize;
        cuStreamUpdateCaptureDependencies;
        cuStreamUpdateCaptureDependencies_v2;
        cuStreamWaitEvent;
        cuStreamWaitValue32_v2;
        cuStreamWaitValue64_v2;
        cuStreamWriteValue32_v2;
        cuStreamWriteValue64_v2;
        cuTensorMapEncodeIm2col;
        cuTensorMapEncodeTiled;
        cuTensorMapReplaceAddress;
        cuThreadExchangeStreamCaptureMode;
        cuUserObjectCreate;
        cuUserObjectRelease;
        cuUserObjectRetain;
        cuWaitExternalSemaphoresAsync;
    local:
        *;
};