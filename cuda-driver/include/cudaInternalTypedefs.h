#ifndef CUDAINTERNALTYPEDEFS_H
#define CUDAINTERNALTYPEDEFS_H

#include <cuda_driver_internal.h>

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

/*
 * Macros for the latest version for each driver function in cuda.h
 */
#define PFN_cuDevicePrimaryCtxGet PFN_cuDevicePrimaryCtxGet_v3010
#define PFN_cuDevicePrimaryCtxInit PFN_cuDevicePrimaryCtxInit_v3010

/*
 * Function pointer typedefs for the internal driver function in cuda_driver_internal.h
 */
typedef CUresult (CUDAAPI *PFN_cuDevicePrimaryCtxGet_v3010)(CUcontext* pctx, CUdevice dev);
typedef CUresult (CUDAAPI *PFN_cuDevicePrimaryCtxInit_v3010)(CUdevice dev);

#ifdef __cplusplus
}
#endif // __cplusplus

#endif // CUDAINTERNALTYPEDEFS_H