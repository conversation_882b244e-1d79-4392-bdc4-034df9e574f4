#ifndef __cuda_driver_internal_h__
#define __cuda_driver_internal_h__

#include <cuda.h>

/**
 * \brief Get the handle of the primary context
 *
 * Returns in \p *pctx the handle of the primary context of \p dev.
 *
 * \param pctx   - Pointer to context handle.
 * \param dev    - Device to get primary context flags for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa
 * ::cuDevicePrimaryCtxRelease,
 * ::cuDevicePrimaryCtxSetFlags,
 * ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult CUDAAPI cuDevicePrimaryCtxGet(CUcontext* pctx, CUdevice dev);

/**
 * \brief Initialize the primary context of the corresponding device
 *
 * Initialize the handle of the primary context of \p dev.
 *
 * \param dev    - Device to get primary context flags for
 * \param pctx   - Pointer to context handle.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa
 * ::cuDevicePrimaryCtxRelease,
 * ::cuDevicePrimaryCtxRetain,
 * * ::cuDevicePrimaryCtxReset,
 * ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult CUDAAPI cuDevicePrimaryCtxInit(CUdevice dev);

#endif // __cuda_driver_internal_h__