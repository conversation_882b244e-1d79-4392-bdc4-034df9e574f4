#include "cudaTypedefs.h"
#include "cudaInternalTypedefs.h"
#include "cudaProfilerTypedefs.h"

typedef struct CUdriverExportTable_st
{
    PFN_cuGetErrorName pfnGetErrorName;
    PFN_cuGetErrorString pfnGetErrorString;
    PFN_cuInit pfnInit;
    PFN_cuDriverGetVersion pfnDriverGetVersion;
    PFN_cuDeviceGet pfnDeviceGet;
    PFN_cuDeviceGetAttribute pfnDeviceGetAttribute;
    PFN_cuDeviceGetCount pfnDeviceGetCount;
    PFN_cuDeviceGetDefaultMemPool pfnDeviceGetDefaultMemPool;
    PFN_cuDeviceGetExecAffinitySupport pfnDeviceGetExecAffinitySupport;
    PFN_cuDeviceGetLuid pfnDeviceGetLuid;
    PFN_cuDeviceGetMemPool pfnDeviceGetMemPool;
    PFN_cuDeviceGetName pfnDeviceGetName;
    PFN_cuDeviceGetUuid pfnDeviceGetUuid;
    PFN_cuDeviceGetUuid_v2 pfnDeviceGetUuid_v2;
    PFN_cuDeviceSetMemPool pfnDeviceSetMemPool;
    PFN_cuDeviceTotalMem pfnDeviceTotalMem;
    PFN_cuFlushGPUDirectRDMAWrites pfnFlushGPUDirectRDMAWrites;
    PFN_cuDeviceComputeCapability pfnDeviceComputeCapability;
    PFN_cuDeviceGetProperties pfnDeviceGetProperties;
    PFN_cuDevicePrimaryCtxGet pfnDevicePrimaryCtxGet;
    PFN_cuDevicePrimaryCtxGetState pfnDevicePrimaryCtxGetState;
    PFN_cuDevicePrimaryCtxInit pfnDevicePrimaryCtxInit;
    PFN_cuDevicePrimaryCtxRelease pfnDevicePrimaryCtxRelease;
    PFN_cuDevicePrimaryCtxReset pfnDevicePrimaryCtxReset;
    PFN_cuDevicePrimaryCtxRetain pfnDevicePrimaryCtxRetain;
    PFN_cuDevicePrimaryCtxSetFlags pfnDevicePrimaryCtxSetFlags;
    PFN_cuCtxCreate pfnCtxCreate;
    PFN_cuCtxCreate_v3 pfnCtxCreate_v3;
    PFN_cuCtxDestroy pfnCtxDestroy;
    PFN_cuCtxGetApiVersion pfnCtxGetApiVersion;
    PFN_cuCtxGetCacheConfig pfnCtxGetCacheConfig;
    PFN_cuCtxGetCurrent pfnCtxGetCurrent;
    PFN_cuCtxGetDevice pfnCtxGetDevice;
    PFN_cuCtxGetExecAffinity pfnCtxGetExecAffinity;
    PFN_cuCtxGetFlags pfnCtxGetFlags;
    PFN_cuCtxGetId pfnCtxGetId;
    PFN_cuCtxGetLimit pfnCtxGetLimit;
    PFN_cuCtxGetStreamPriorityRange pfnCtxGetStreamPriorityRange;
    PFN_cuCtxPopCurrent pfnCtxPopCurrent;
    PFN_cuCtxResetPersistingL2Cache pfnCtxResetPersistingL2Cache;
    PFN_cuCtxSetCacheConfig pfnCtxSetCacheConfig;
    PFN_cuCtxSetCurrent pfnCtxSetCurrent;
    PFN_cuCtxSetFlags pfnCtxSetFlags;
    PFN_cuCtxSetLimit pfnCtxSetLimit;
    PFN_cuCtxSynchronize pfnCtxSynchronize;
    PFN_cuCtxAttach pfnCtxAttach;
    PFN_cuCtxDetach pfnCtxDetach;
    PFN_cuCtxGetSharedMemConfig pfnCtxGetSharedMemConfig;
    PFN_cuCtxSetSharedMemConfig pfnCtxSetSharedMemConfig;
    PFN_cuLinkAddData pfnLinkAddData;
    PFN_cuLinkAddFile pfnLinkAddFile;
    PFN_cuLinkComplete pfnLinkComplete;
    PFN_cuLinkCreate pfnLinkCreate;
    PFN_cuLinkDestroy pfnLinkDestroy;
    PFN_cuModuleEnumerateFunctions pfnModuleEnumerateFunctions;
    PFN_cuModuleGetFunction pfnModuleGetFunction;
    PFN_cuModuleGetFunctionCount pfnModuleGetFunctionCount;
    PFN_cuModuleGetGlobal pfnModuleGetGlobal;
    PFN_cuModuleGetLoadingMode pfnModuleGetLoadingMode;
    PFN_cuModuleLoad pfnModuleLoad;
    PFN_cuModuleLoadData pfnModuleLoadData;
    PFN_cuModuleLoadDataEx pfnModuleLoadDataEx;
    PFN_cuModuleLoadFatBinary pfnModuleLoadFatBinary;
    PFN_cuModuleUnload pfnModuleUnload;
    PFN_cuKernelGetAttribute pfnKernelGetAttribute;
    PFN_cuKernelGetFunction pfnKernelGetFunction;
    PFN_cuKernelGetName pfnKernelGetName;
    PFN_cuKernelGetParamInfo pfnKernelGetParamInfo;
    PFN_cuKernelSetAttribute pfnKernelSetAttribute;
    PFN_cuKernelSetCacheConfig pfnKernelSetCacheConfig;
    PFN_cuLibraryEnumerateKernels pfnLibraryEnumerateKernels;
    PFN_cuLibraryGetGlobal pfnLibraryGetGlobal;
    PFN_cuLibraryGetKernel pfnLibraryGetKernel;
    PFN_cuLibraryGetKernelCount pfnLibraryGetKernelCount;
    PFN_cuLibraryGetManaged pfnLibraryGetManaged;
    PFN_cuLibraryGetModule pfnLibraryGetModule;
    PFN_cuLibraryGetUnifiedFunction pfnLibraryGetUnifiedFunction;
    PFN_cuLibraryLoadData pfnLibraryLoadData;
    PFN_cuLibraryLoadFromFile pfnLibraryLoadFromFile;
    PFN_cuLibraryUnload pfnLibraryUnload;
    PFN_cuArray3DCreate pfnArray3DCreate;
    PFN_cuArray3DGetDescriptor pfnArray3DGetDescriptor;
    PFN_cuArrayCreate pfnArrayCreate;
    PFN_cuArrayDestroy pfnArrayDestroy;
    PFN_cuArrayGetDescriptor pfnArrayGetDescriptor;
    PFN_cuArrayGetMemoryRequirements pfnArrayGetMemoryRequirements;
    PFN_cuArrayGetPlane pfnArrayGetPlane;
    PFN_cuArrayGetSparseProperties pfnArrayGetSparseProperties;
    PFN_cuDeviceGetByPCIBusId pfnDeviceGetByPCIBusId;
    PFN_cuDeviceGetPCIBusId pfnDeviceGetPCIBusId;
    PFN_cuDeviceRegisterAsyncNotification pfnDeviceRegisterAsyncNotification;
    PFN_cuDeviceUnregisterAsyncNotification pfnDeviceUnregisterAsyncNotification;
    PFN_cuIpcCloseMemHandle pfnIpcCloseMemHandle;
    PFN_cuIpcGetEventHandle pfnIpcGetEventHandle;
    PFN_cuIpcGetMemHandle pfnIpcGetMemHandle;
    PFN_cuIpcOpenEventHandle pfnIpcOpenEventHandle;
    PFN_cuIpcOpenMemHandle pfnIpcOpenMemHandle;
    PFN_cuMemAlloc pfnMemAlloc;
    PFN_cuMemAllocHost pfnMemAllocHost;
    PFN_cuMemAllocManaged pfnMemAllocManaged;
    PFN_cuMemAllocPitch pfnMemAllocPitch;
    PFN_cuMemcpy pfnMemcpy;
    PFN_cuMemcpy2D pfnMemcpy2D;
    PFN_cuMemcpy2DAsync pfnMemcpy2DAsync;
    PFN_cuMemcpy2DUnaligned pfnMemcpy2DUnaligned;
    PFN_cuMemcpy3D pfnMemcpy3D;
    PFN_cuMemcpy3DAsync pfnMemcpy3DAsync;
    PFN_cuMemcpy3DPeer pfnMemcpy3DPeer;
    PFN_cuMemcpy3DPeerAsync pfnMemcpy3DPeerAsync;
    PFN_cuMemcpyAsync pfnMemcpyAsync;
    PFN_cuMemcpyAtoA pfnMemcpyAtoA;
    PFN_cuMemcpyAtoD pfnMemcpyAtoD;
    PFN_cuMemcpyAtoH pfnMemcpyAtoH;
    PFN_cuMemcpyAtoHAsync pfnMemcpyAtoHAsync;
    PFN_cuMemcpyDtoA pfnMemcpyDtoA;
    PFN_cuMemcpyDtoD pfnMemcpyDtoD;
    PFN_cuMemcpyDtoDAsync pfnMemcpyDtoDAsync;
    PFN_cuMemcpyDtoH pfnMemcpyDtoH;
    PFN_cuMemcpyDtoHAsync pfnMemcpyDtoHAsync;
    PFN_cuMemcpyHtoA pfnMemcpyHtoA;
    PFN_cuMemcpyHtoAAsync pfnMemcpyHtoAAsync;
    PFN_cuMemcpyHtoD pfnMemcpyHtoD;
    PFN_cuMemcpyHtoDAsync pfnMemcpyHtoDAsync;
    PFN_cuMemcpyPeer pfnMemcpyPeer;
    PFN_cuMemcpyPeerAsync pfnMemcpyPeerAsync;
    PFN_cuMemFree pfnMemFree;
    PFN_cuMemFreeHost pfnMemFreeHost;
    PFN_cuMemGetAddressRange pfnMemGetAddressRange;
    PFN_cuMemGetHandleForAddressRange pfnMemGetHandleForAddressRange;
    PFN_cuMemGetInfo pfnMemGetInfo;
    PFN_cuMemHostAlloc pfnMemHostAlloc;
    PFN_cuMemHostGetDevicePointer pfnMemHostGetDevicePointer;
    PFN_cuMemHostGetFlags pfnMemHostGetFlags;
    PFN_cuMemHostRegister pfnMemHostRegister;
    PFN_cuMemHostUnregister pfnMemHostUnregister;
    PFN_cuMemsetD16 pfnMemsetD16;
    PFN_cuMemsetD16Async pfnMemsetD16Async;
    PFN_cuMemsetD2D16 pfnMemsetD2D16;
    PFN_cuMemsetD2D16Async pfnMemsetD2D16Async;
    PFN_cuMemsetD2D32 pfnMemsetD2D32;
    PFN_cuMemsetD2D32Async pfnMemsetD2D32Async;
    PFN_cuMemsetD2D8 pfnMemsetD2D8;
    PFN_cuMemsetD2D8Async pfnMemsetD2D8Async;
    PFN_cuMemsetD32 pfnMemsetD32;
    PFN_cuMemsetD32Async pfnMemsetD32Async;
    PFN_cuMemsetD8 pfnMemsetD8;
    PFN_cuMemsetD8Async pfnMemsetD8Async;
    PFN_cuMemAddressFree pfnMemAddressFree;
    PFN_cuMemAddressReserve pfnMemAddressReserve;
    PFN_cuMemCreate pfnMemCreate;
    PFN_cuMemExportToShareableHandle pfnMemExportToShareableHandle;
    PFN_cuMemGetAccess pfnMemGetAccess;
    PFN_cuMemGetAllocationGranularity pfnMemGetAllocationGranularity;
    PFN_cuMemGetAllocationPropertiesFromHandle pfnMemGetAllocationPropertiesFromHandle;
    PFN_cuMemImportFromShareableHandle pfnMemImportFromShareableHandle;
    PFN_cuMemMap pfnMemMap;
    PFN_cuMemMapArrayAsync pfnMemMapArrayAsync;
    PFN_cuMemRelease pfnMemRelease;
    PFN_cuMemRetainAllocationHandle pfnMemRetainAllocationHandle;
    PFN_cuMemSetAccess pfnMemSetAccess;
    PFN_cuMemUnmap pfnMemUnmap;
    PFN_cuMemAllocAsync pfnMemAllocAsync;
    PFN_cuMemAllocFromPoolAsync pfnMemAllocFromPoolAsync;
    PFN_cuMemFreeAsync pfnMemFreeAsync;
    PFN_cuMemPoolCreate pfnMemPoolCreate;
    PFN_cuMemPoolDestroy pfnMemPoolDestroy;
    PFN_cuMemPoolExportPointer pfnMemPoolExportPointer;
    PFN_cuMemPoolExportToShareableHandle pfnMemPoolExportToShareableHandle;
    PFN_cuMemPoolGetAccess pfnMemPoolGetAccess;
    PFN_cuMemPoolGetAttribute pfnMemPoolGetAttribute;
    PFN_cuMemPoolImportFromShareableHandle pfnMemPoolImportFromShareableHandle;
    PFN_cuMemPoolImportPointer pfnMemPoolImportPointer;
    PFN_cuMemPoolSetAccess pfnMemPoolSetAccess;
    PFN_cuMemPoolSetAttribute pfnMemPoolSetAttribute;
    PFN_cuMemPoolTrimTo pfnMemPoolTrimTo;
    PFN_cuMulticastAddDevice pfnMulticastAddDevice;
    PFN_cuMulticastBindAddr pfnMulticastBindAddr;
    PFN_cuMulticastBindMem pfnMulticastBindMem;
    PFN_cuMulticastCreate pfnMulticastCreate;
    PFN_cuMulticastGetGranularity pfnMulticastGetGranularity;
    PFN_cuMulticastUnbind pfnMulticastUnbind;
    PFN_cuMemAdvise pfnMemAdvise;
    PFN_cuMemAdvise_v2 pfnMemAdvise_v2;
    PFN_cuMemPrefetchAsync pfnMemPrefetchAsync;
    PFN_cuMemPrefetchAsync_v2 pfnMemPrefetchAsync_v2;
    PFN_cuMemRangeGetAttribute pfnMemRangeGetAttribute;
    PFN_cuMemRangeGetAttributes pfnMemRangeGetAttributes;
    PFN_cuPointerGetAttribute pfnPointerGetAttribute;
    PFN_cuPointerGetAttributes pfnPointerGetAttributes;
    PFN_cuPointerSetAttribute pfnPointerSetAttribute;
    PFN_cuStreamAddCallback pfnStreamAddCallback;
    PFN_cuStreamAttachMemAsync pfnStreamAttachMemAsync;
    PFN_cuStreamBeginCapture pfnStreamBeginCapture;
    PFN_cuStreamBeginCaptureToGraph pfnStreamBeginCaptureToGraph;
    PFN_cuStreamCopyAttributes pfnStreamCopyAttributes;
    PFN_cuStreamCreate pfnStreamCreate;
    PFN_cuStreamCreateWithPriority pfnStreamCreateWithPriority;
    PFN_cuStreamDestroy pfnStreamDestroy;
    PFN_cuStreamEndCapture pfnStreamEndCapture;
    PFN_cuStreamGetAttribute pfnStreamGetAttribute;
    PFN_cuStreamGetCaptureInfo_v2 pfnStreamGetCaptureInfo_v2;
    PFN_cuStreamGetCaptureInfo_v3 pfnStreamGetCaptureInfo_v3;
    PFN_cuStreamGetCtx pfnStreamGetCtx;
    PFN_cuStreamGetFlags pfnStreamGetFlags;
    PFN_cuStreamGetId pfnStreamGetId;
    PFN_cuStreamGetPriority pfnStreamGetPriority;
    PFN_cuStreamIsCapturing pfnStreamIsCapturing;
    PFN_cuStreamQuery pfnStreamQuery;
    PFN_cuStreamSetAttribute pfnStreamSetAttribute;
    PFN_cuStreamSynchronize pfnStreamSynchronize;
    PFN_cuStreamUpdateCaptureDependencies pfnStreamUpdateCaptureDependencies;
    PFN_cuStreamUpdateCaptureDependencies_v2 pfnStreamUpdateCaptureDependencies_v2;
    PFN_cuStreamWaitEvent pfnStreamWaitEvent;
    PFN_cuThreadExchangeStreamCaptureMode pfnThreadExchangeStreamCaptureMode;
    PFN_cuEventCreate pfnEventCreate;
    PFN_cuEventDestroy pfnEventDestroy;
    PFN_cuEventElapsedTime pfnEventElapsedTime;
    PFN_cuEventQuery pfnEventQuery;
    PFN_cuEventRecord pfnEventRecord;
    PFN_cuEventRecordWithFlags pfnEventRecordWithFlags;
    PFN_cuEventSynchronize pfnEventSynchronize;
    PFN_cuDestroyExternalMemory pfnDestroyExternalMemory;
    PFN_cuDestroyExternalSemaphore pfnDestroyExternalSemaphore;
    PFN_cuExternalMemoryGetMappedBuffer pfnExternalMemoryGetMappedBuffer;
    PFN_cuImportExternalMemory pfnImportExternalMemory;
    PFN_cuImportExternalSemaphore pfnImportExternalSemaphore;
    PFN_cuSignalExternalSemaphoresAsync pfnSignalExternalSemaphoresAsync;
    PFN_cuWaitExternalSemaphoresAsync pfnWaitExternalSemaphoresAsync;
    PFN_cuStreamBatchMemOp pfnStreamBatchMemOp;
    PFN_cuStreamWaitValue32 pfnStreamWaitValue32;
    PFN_cuStreamWaitValue64 pfnStreamWaitValue64;
    PFN_cuStreamWriteValue32 pfnStreamWriteValue32;
    PFN_cuStreamWriteValue64 pfnStreamWriteValue64;
    PFN_cuFuncGetAttribute pfnFuncGetAttribute;
    PFN_cuFuncGetModule pfnFuncGetModule;
    PFN_cuFuncGetName pfnFuncGetName;
    PFN_cuFuncGetParamInfo pfnFuncGetParamInfo;
    PFN_cuFuncIsLoaded pfnFuncIsLoaded;
    PFN_cuFuncLoad pfnFuncLoad;
    PFN_cuFuncSetAttribute pfnFuncSetAttribute;
    PFN_cuFuncSetCacheConfig pfnFuncSetCacheConfig;
    PFN_cuLaunchCooperativeKernel pfnLaunchCooperativeKernel;
    PFN_cuLaunchCooperativeKernelMultiDevice pfnLaunchCooperativeKernelMultiDevice;
    PFN_cuLaunchHostFunc pfnLaunchHostFunc;
    PFN_cuLaunchKernel pfnLaunchKernel;
    PFN_cuLaunchKernelEx pfnLaunchKernelEx;
    PFN_cuFuncSetBlockShape pfnFuncSetBlockShape;
    PFN_cuFuncSetSharedMemConfig pfnFuncSetSharedMemConfig;
    PFN_cuFuncSetSharedSize pfnFuncSetSharedSize;
    PFN_cuLaunch pfnLaunch;
    PFN_cuLaunchGrid pfnLaunchGrid;
    PFN_cuLaunchGridAsync pfnLaunchGridAsync;
    PFN_cuParamSetf pfnParamSetf;
    PFN_cuParamSeti pfnParamSeti;
    PFN_cuParamSetSize pfnParamSetSize;
    PFN_cuParamSetv pfnParamSetv;
    PFN_cuDeviceGetGraphMemAttribute pfnDeviceGetGraphMemAttribute;
    PFN_cuDeviceGraphMemTrim pfnDeviceGraphMemTrim;
    PFN_cuDeviceSetGraphMemAttribute pfnDeviceSetGraphMemAttribute;
    PFN_cuGraphAddBatchMemOpNode pfnGraphAddBatchMemOpNode;
    PFN_cuGraphAddChildGraphNode pfnGraphAddChildGraphNode;
    PFN_cuGraphAddDependencies pfnGraphAddDependencies;
    PFN_cuGraphAddDependencies_v2 pfnGraphAddDependencies_v2;
    PFN_cuGraphAddEmptyNode pfnGraphAddEmptyNode;
    PFN_cuGraphAddEventRecordNode pfnGraphAddEventRecordNode;
    PFN_cuGraphAddEventWaitNode pfnGraphAddEventWaitNode;
    PFN_cuGraphAddExternalSemaphoresSignalNode pfnGraphAddExternalSemaphoresSignalNode;
    PFN_cuGraphAddExternalSemaphoresWaitNode pfnGraphAddExternalSemaphoresWaitNode;
    PFN_cuGraphAddHostNode pfnGraphAddHostNode;
    PFN_cuGraphAddKernelNode pfnGraphAddKernelNode;
    PFN_cuGraphAddMemAllocNode pfnGraphAddMemAllocNode;
    PFN_cuGraphAddMemcpyNode pfnGraphAddMemcpyNode;
    PFN_cuGraphAddMemFreeNode pfnGraphAddMemFreeNode;
    PFN_cuGraphAddMemsetNode pfnGraphAddMemsetNode;
    PFN_cuGraphAddNode pfnGraphAddNode;
    PFN_cuGraphAddNode_v2 pfnGraphAddNode_v2;
    PFN_cuGraphBatchMemOpNodeGetParams pfnGraphBatchMemOpNodeGetParams;
    PFN_cuGraphBatchMemOpNodeSetParams pfnGraphBatchMemOpNodeSetParams;
    PFN_cuGraphChildGraphNodeGetGraph pfnGraphChildGraphNodeGetGraph;
    PFN_cuGraphClone pfnGraphClone;
    PFN_cuGraphConditionalHandleCreate pfnGraphConditionalHandleCreate;
    PFN_cuGraphCreate pfnGraphCreate;
    PFN_cuGraphDebugDotPrint pfnGraphDebugDotPrint;
    PFN_cuGraphDestroy pfnGraphDestroy;
    PFN_cuGraphDestroyNode pfnGraphDestroyNode;
    PFN_cuGraphEventRecordNodeGetEvent pfnGraphEventRecordNodeGetEvent;
    PFN_cuGraphEventRecordNodeSetEvent pfnGraphEventRecordNodeSetEvent;
    PFN_cuGraphEventWaitNodeGetEvent pfnGraphEventWaitNodeGetEvent;
    PFN_cuGraphEventWaitNodeSetEvent pfnGraphEventWaitNodeSetEvent;
    PFN_cuGraphExecBatchMemOpNodeSetParams pfnGraphExecBatchMemOpNodeSetParams;
    PFN_cuGraphExecChildGraphNodeSetParams pfnGraphExecChildGraphNodeSetParams;
    PFN_cuGraphExecDestroy pfnGraphExecDestroy;
    PFN_cuGraphExecEventRecordNodeSetEvent pfnGraphExecEventRecordNodeSetEvent;
    PFN_cuGraphExecEventWaitNodeSetEvent pfnGraphExecEventWaitNodeSetEvent;
    PFN_cuGraphExecExternalSemaphoresSignalNodeSetParams pfnGraphExecExternalSemaphoresSignalNodeSetParams;
    PFN_cuGraphExecExternalSemaphoresWaitNodeSetParams pfnGraphExecExternalSemaphoresWaitNodeSetParams;
    PFN_cuGraphExecGetFlags pfnGraphExecGetFlags;
    PFN_cuGraphExecHostNodeSetParams pfnGraphExecHostNodeSetParams;
    PFN_cuGraphExecKernelNodeSetParams pfnGraphExecKernelNodeSetParams;
    PFN_cuGraphExecMemcpyNodeSetParams pfnGraphExecMemcpyNodeSetParams;
    PFN_cuGraphExecMemsetNodeSetParams pfnGraphExecMemsetNodeSetParams;
    PFN_cuGraphExecNodeSetParams pfnGraphExecNodeSetParams;
    PFN_cuGraphExecUpdate pfnGraphExecUpdate;
    PFN_cuGraphExternalSemaphoresSignalNodeGetParams pfnGraphExternalSemaphoresSignalNodeGetParams;
    PFN_cuGraphExternalSemaphoresSignalNodeSetParams pfnGraphExternalSemaphoresSignalNodeSetParams;
    PFN_cuGraphExternalSemaphoresWaitNodeGetParams pfnGraphExternalSemaphoresWaitNodeGetParams;
    PFN_cuGraphExternalSemaphoresWaitNodeSetParams pfnGraphExternalSemaphoresWaitNodeSetParams;
    PFN_cuGraphGetEdges pfnGraphGetEdges;
    PFN_cuGraphGetEdges_v2 pfnGraphGetEdges_v2;
    PFN_cuGraphGetNodes pfnGraphGetNodes;
    PFN_cuGraphGetRootNodes pfnGraphGetRootNodes;
    PFN_cuGraphHostNodeGetParams pfnGraphHostNodeGetParams;
    PFN_cuGraphHostNodeSetParams pfnGraphHostNodeSetParams;
    PFN_cuGraphInstantiate pfnGraphInstantiate;
    PFN_cuGraphInstantiateWithParams pfnGraphInstantiateWithParams;
    PFN_cuGraphKernelNodeCopyAttributes pfnGraphKernelNodeCopyAttributes;
    PFN_cuGraphKernelNodeGetAttribute pfnGraphKernelNodeGetAttribute;
    PFN_cuGraphKernelNodeGetParams pfnGraphKernelNodeGetParams;
    PFN_cuGraphKernelNodeSetAttribute pfnGraphKernelNodeSetAttribute;
    PFN_cuGraphKernelNodeSetParams pfnGraphKernelNodeSetParams;
    PFN_cuGraphLaunch pfnGraphLaunch;
    PFN_cuGraphMemAllocNodeGetParams pfnGraphMemAllocNodeGetParams;
    PFN_cuGraphMemcpyNodeGetParams pfnGraphMemcpyNodeGetParams;
    PFN_cuGraphMemcpyNodeSetParams pfnGraphMemcpyNodeSetParams;
    PFN_cuGraphMemFreeNodeGetParams pfnGraphMemFreeNodeGetParams;
    PFN_cuGraphMemsetNodeGetParams pfnGraphMemsetNodeGetParams;
    PFN_cuGraphMemsetNodeSetParams pfnGraphMemsetNodeSetParams;
    PFN_cuGraphNodeFindInClone pfnGraphNodeFindInClone;
    PFN_cuGraphNodeGetDependencies pfnGraphNodeGetDependencies;
    PFN_cuGraphNodeGetDependencies_v2 pfnGraphNodeGetDependencies_v2;
    PFN_cuGraphNodeGetDependentNodes pfnGraphNodeGetDependentNodes;
    PFN_cuGraphNodeGetDependentNodes_v2 pfnGraphNodeGetDependentNodes_v2;
    PFN_cuGraphNodeGetEnabled pfnGraphNodeGetEnabled;
    PFN_cuGraphNodeGetType pfnGraphNodeGetType;
    PFN_cuGraphNodeSetEnabled pfnGraphNodeSetEnabled;
    PFN_cuGraphNodeSetParams pfnGraphNodeSetParams;
    PFN_cuGraphReleaseUserObject pfnGraphReleaseUserObject;
    PFN_cuGraphRemoveDependencies pfnGraphRemoveDependencies;
    PFN_cuGraphRemoveDependencies_v2 pfnGraphRemoveDependencies_v2;
    PFN_cuGraphRetainUserObject pfnGraphRetainUserObject;
    PFN_cuGraphUpload pfnGraphUpload;
    PFN_cuUserObjectCreate pfnUserObjectCreate;
    PFN_cuUserObjectRelease pfnUserObjectRelease;
    PFN_cuUserObjectRetain pfnUserObjectRetain;
    PFN_cuOccupancyAvailableDynamicSMemPerBlock pfnOccupancyAvailableDynamicSMemPerBlock;
    PFN_cuOccupancyMaxActiveBlocksPerMultiprocessor pfnOccupancyMaxActiveBlocksPerMultiprocessor;
    PFN_cuOccupancyMaxActiveBlocksPerMultiprocessorWithFlags pfnOccupancyMaxActiveBlocksPerMultiprocessorWithFlags;
    PFN_cuOccupancyMaxActiveClusters pfnOccupancyMaxActiveClusters;
    PFN_cuOccupancyMaxPotentialBlockSize pfnOccupancyMaxPotentialBlockSize;
    PFN_cuOccupancyMaxPotentialBlockSizeWithFlags pfnOccupancyMaxPotentialBlockSizeWithFlags;
    PFN_cuOccupancyMaxPotentialClusterSize pfnOccupancyMaxPotentialClusterSize;
    PFN_cuTensorMapEncodeIm2col pfnTensorMapEncodeIm2col;
    PFN_cuTensorMapEncodeTiled pfnTensorMapEncodeTiled;
    PFN_cuTensorMapReplaceAddress pfnTensorMapReplaceAddress;
    PFN_cuCtxDisablePeerAccess pfnCtxDisablePeerAccess;
    PFN_cuCtxEnablePeerAccess pfnCtxEnablePeerAccess;
    PFN_cuDeviceCanAccessPeer pfnDeviceCanAccessPeer;
    PFN_cuDeviceGetP2PAttribute pfnDeviceGetP2PAttribute;
    PFN_cuGetProcAddress pfnGetProcAddress;
    PFN_cuGetExportTable pfnGetExportTable;
    PFN_cuCoredumpGetAttribute pfnCoredumpGetAttribute;
    PFN_cuCoredumpGetAttributeGlobal pfnCoredumpGetAttributeGlobal;
    PFN_cuCoredumpSetAttribute pfnCoredumpSetAttribute;
    PFN_cuCoredumpSetAttributeGlobal pfnCoredumpSetAttributeGlobal;
    PFN_cuCtxFromGreenCtx pfnCtxFromGreenCtx;
    PFN_cuCtxGetDevResource pfnCtxGetDevResource;
    PFN_cuDeviceGetDevResource pfnDeviceGetDevResource;
    PFN_cuDevResourceGenerateDesc pfnDevResourceGenerateDesc;
    PFN_cuDevSmResourceSplitByCount pfnDevSmResourceSplitByCount;
    PFN_cuGreenCtxCreate pfnGreenCtxCreate;
    PFN_cuGreenCtxDestroy pfnGreenCtxDestroy;
    PFN_cuGreenCtxGetDevResource pfnGreenCtxGetDevResource;
    PFN_cuGreenCtxRecordEvent pfnGreenCtxRecordEvent;
    PFN_cuGreenCtxWaitEvent pfnGreenCtxWaitEvent;
    PFN_cuStreamGetGreenCtx pfnStreamGetGreenCtx;
    PFN_cuProfilerInitialize pfnProfilerInitialize;
    PFN_cuProfilerStart pfnProfilerStart;
    PFN_cuProfilerStop pfnProfilerStop;
} CUdriverExportTable;