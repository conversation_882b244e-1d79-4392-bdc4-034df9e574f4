#!/bin/bash
set -e

# Auto-detect build directory
if [[ -f "CMakeCache.txt" ]]; then
    # We're already in a build directory
    BUILD_DIR="$(pwd)"
elif [[ -d "build" ]]; then
    # Local environment - use default build directory
    BUILD_DIR="build"
else
    # Use current directory as fallback
    BUILD_DIR="$(pwd)"
fi

echo "Using build directory: $BUILD_DIR"

# Add the passing test binaries to the list
passinglist=(
    "$BUILD_DIR/bin/tests/unittests/0_simpleDrvInit/simpleDrvInit"
    "$BUILD_DIR/bin/tests/unittests/1_simplePrimaryCtx/simplePrimaryCtx"
    "$BUILD_DIR/bin/tests/unittests/2_simpleCudaVisibleDevices/simpleCudaVisibleDevices"
    "$BUILD_DIR/bin/tests/unittests/3_simpleDrvExportTable/simpleDrvExportTable"
    "$BUILD_DIR/bin/tests/unittests/4_simpleMemAlloc/simpleMemAlloc"
)

for testexe in "${passinglist[@]}"
do
    echo "----------------------------------------------------------------------------------------------------"

    if [[ -x "$testexe" ]]; then
        echo "Running $testexe"
        "$testexe"
    else
        echo "Skipping $testexe (not found or not executable)"
        exit 1
    fi
done