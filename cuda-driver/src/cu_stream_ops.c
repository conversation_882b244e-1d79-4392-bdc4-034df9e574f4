#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Batch operations to synchronize the stream via memory operations
 *
 * This is a batch version of ::cuStreamWaitValue32() and ::cuStreamWriteValue32().
 * Batching operations may avoid some performance overhead in both the API call
 * and the device execution versus adding them to the stream in separate API
 * calls. The operations are enqueued in the order they appear in the array.
 *
 * See ::CUstreamBatchMemOpType for the full set of supported operations, and
 * ::cuStreamWaitValue32(), ::cuStreamWaitValue64(), ::cuStreamWriteValue32(),
 * and ::cuStreamWriteValue64() for details of specific operations.
 *
 * See related APIs for details on querying support for specific operations.
 *
 * \note
 * Warning:
 * Improper use of this API may deadlock the application. Synchronization
 * ordering established through this API is not visible to CUDA. CUDA tasks
 * that are (even indirectly) ordered by this API should also have that order
 * expressed with CUDA-visible dependencies such as events. This ensures that
 * the scheduler does not serialize them in an improper order. For more
 * information, see the Stream Memory Operations section in the programming
 * guide(https://docs.nvidia.com/cuda/cuda-c-programming-guide/index.html).
 *
 * \param stream The stream to enqueue the operations in.
 * \param count The number of operations in the array. Must be less than 256.
 * \param paramArray The types and parameters of the individual operations.
 * \param flags Reserved for future expansion; must be 0.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa ::cuStreamWaitValue32,
 * ::cuStreamWaitValue64,
 * ::cuStreamWriteValue32,
 * ::cuStreamWriteValue64,
 * ::cuMemHostRegister
 */
CUresult cuStreamBatchMemOp_v2(CUstream stream, unsigned int count, CUstreamBatchMemOpParams *paramArray, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Wait on a memory location
 *
 * Enqueues a synchronization of the stream on the given memory location. Work
 * ordered after the operation will block until the given condition on the
 * memory is satisfied. By default, the condition is to wait for
 * (int32_t)(*addr - value) >= 0, a cyclic greater-or-equal.
 * Other condition types can be specified via \p flags.
 *
 * If the memory was registered via ::cuMemHostRegister(), the device pointer
 * should be obtained with ::cuMemHostGetDevicePointer(). This function cannot
 * be used with managed memory (::cuMemAllocManaged).
 *
 * Support for CU_STREAM_WAIT_VALUE_NOR can be queried with ::cuDeviceGetAttribute() and
 * ::CU_DEVICE_ATTRIBUTE_CAN_USE_STREAM_WAIT_VALUE_NOR_V2.
 *
 * \note
 * Warning:
 * Improper use of this API may deadlock the application. Synchronization
 * ordering established through this API is not visible to CUDA. CUDA tasks
 * that are (even indirectly) ordered by this API should also have that order
 * expressed with CUDA-visible dependencies such as events. This ensures that
 * the scheduler does not serialize them in an improper order.
 *
 * \param stream The stream to synchronize on the memory location.
 * \param addr The memory location to wait on.
 * \param value The value to compare with the memory location.
 * \param flags See ::CUstreamWaitValue_flags.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa ::cuStreamWaitValue64,
 * ::cuStreamWriteValue32,
 * ::cuStreamWriteValue64,
 * ::cuStreamBatchMemOp,
 * ::cuMemHostRegister,
 * ::cuStreamWaitEvent
 */
CUresult cuStreamWaitValue32_v2(CUstream stream, CUdeviceptr addr, cuuint32_t value, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Wait on a memory location
 *
 * Enqueues a synchronization of the stream on the given memory location. Work
 * ordered after the operation will block until the given condition on the
 * memory is satisfied. By default, the condition is to wait for
 * (int64_t)(*addr - value) >= 0, a cyclic greater-or-equal.
 * Other condition types can be specified via \p flags.
 *
 * If the memory was registered via ::cuMemHostRegister(), the device pointer
 * should be obtained with ::cuMemHostGetDevicePointer().
 *
 * Support for this can be queried with ::cuDeviceGetAttribute() and
 * ::CU_DEVICE_ATTRIBUTE_CAN_USE_64_BIT_STREAM_MEM_OPS.
 *
 * \note
 * Warning:
 * Improper use of this API may deadlock the application. Synchronization
 * ordering established through this API is not visible to CUDA. CUDA tasks
 * that are (even indirectly) ordered by this API should also have that order
 * expressed with CUDA-visible dependencies such as events. This ensures that
 * the scheduler does not serialize them in an improper order.
 *
 * \param stream The stream to synchronize on the memory location.
 * \param addr The memory location to wait on.
 * \param value The value to compare with the memory location.
 * \param flags See ::CUstreamWaitValue_flags.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa ::cuStreamWaitValue32,
 * ::cuStreamWriteValue32,
 * ::cuStreamWriteValue64,
 * ::cuStreamBatchMemOp,
 * ::cuMemHostRegister,
 * ::cuStreamWaitEvent
 */
CUresult cuStreamWaitValue64_v2(CUstream stream, CUdeviceptr addr, cuuint64_t value, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Write a value to memory
 *
 * Write a value to memory.
 *
 * If the memory was registered via ::cuMemHostRegister(), the device pointer
 * should be obtained with ::cuMemHostGetDevicePointer(). This function cannot
 * be used with managed memory (::cuMemAllocManaged).
 *
 * \param stream The stream to do the write in.
 * \param addr The device address to write to.
 * \param value The value to write.
 * \param flags See ::CUstreamWriteValue_flags.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa ::cuStreamWriteValue64,
 * ::cuStreamWaitValue32,
 * ::cuStreamWaitValue64,
 * ::cuStreamBatchMemOp,
 * ::cuMemHostRegister,
 * ::cuEventRecord
 */
CUresult cuStreamWriteValue32_v2(CUstream stream, CUdeviceptr addr, cuuint32_t value, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Write a value to memory
 *
 * Write a value to memory.
 *
 * If the memory was registered via ::cuMemHostRegister(), the device pointer
 * should be obtained with ::cuMemHostGetDevicePointer().
 *
 * Support for this can be queried with ::cuDeviceGetAttribute() and
 * ::CU_DEVICE_ATTRIBUTE_CAN_USE_64_BIT_STREAM_MEM_OPS.
 *
 * \param stream The stream to do the write in.
 * \param addr The device address to write to.
 * \param value The value to write.
 * \param flags See ::CUstreamWriteValue_flags.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa ::cuStreamWriteValue32,
 * ::cuStreamWaitValue32,
 * ::cuStreamWaitValue64,
 * ::cuStreamBatchMemOp,
 * ::cuMemHostRegister,
 * ::cuEventRecord
 */
CUresult cuStreamWriteValue64_v2(CUstream stream, CUdeviceptr addr, cuuint64_t value, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}