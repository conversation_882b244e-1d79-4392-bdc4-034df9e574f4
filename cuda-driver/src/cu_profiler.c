#include "cu_init.h"

/**
 * \brief Initialize the profiling.
 *
 * \deprecated
 *
 * Note that this function is deprecated and should not be used.
 * Starting with CUDA 12.0, it always returns error code ::CUDA_ERROR_NOT_SUPPORTED.
 *
 * Using this API user can initialize the CUDA profiler by specifying
 * the configuration file, output file and output file format. This
 * API is generally used to profile different set of counters by
 * looping the kernel launch. The \p configFile parameter can be used
 * to select profiling options including profiler counters. Refer to
 * the "Compute Command Line Profiler User Guide" for supported
 * profiler options and counters.
 *
 * Limitation: The CUDA profiler cannot be initialized with this API
 * if another profiling tool is already active, as indicated by the
 * ::CUDA_ERROR_PROFILER_DISABLED return code.
 *
 * Typical usage of the profiling APIs is as follows:
 *
 * for each set of counters/options\n
 * {\n
 *     cuProfilerInitialize(); //Initialize profiling, set the counters or options in the config file \n
 *     ...\n
 *     cuProfilerStart(); \n
 *     // code to be profiled \n
 *     cuProfilerStop(); \n
 *     ...\n
 *     cuProfilerStart(); \n
 *     // code to be profiled \n
 *     cuProfilerStop(); \n
 *     ...\n
 * }\n
 *
 * \param configFile - Name of the config file that lists the counters/options
 * for profiling.
 * \param outputFile - Name of the outputFile where the profiling results will
 * be stored.
 * \param outputMode - outputMode, can be ::CU_OUT_KEY_VALUE_PAIR or ::CU_OUT_CSV.
 *
 * \return
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa
 * ::cuProfilerStart,
 * ::cuProfilerStop,
 */
CUresult cuProfilerInitialize(const char *configFile, const char *outputFile, CUoutput_mode outputMode)
{
    // This function is deprecated and always returns CUDA_ERROR_NOT_SUPPORTED
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Enable profiling.
 *
 * Enables profile collection by the active profiling tool for the
 * current context. If profiling is already enabled, then
 * cuProfilerStart() has no effect.
 *
 * cuProfilerStart and cuProfilerStop APIs are used to
 * programmatically control the profiling granularity by allowing
 * profiling to be done only on selective pieces of code.
 *
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa
 * ::cuProfilerInitialize,
 * ::cuProfilerStop,
 * ::cudaProfilerStart
 */
CUresult cuProfilerStart(void)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Disable profiling.
 *
 * Disables profile collection by the active profiling tool for the
 * current context. If profiling is already disabled, then
 * cuProfilerStop() has no effect.
 *
 * cuProfilerStart and cuProfilerStop APIs are used to
 * programmatically control the profiling granularity by allowing
 * profiling to be done only on selective pieces of code.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa
 * ::cuProfilerInitialize,
 * ::cuProfilerStart,
 * ::cudaProfilerStop
 */
CUresult cuProfilerStop(void)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}