#include "cu_init.h"

#include "pal_array.h"
#include "pal_context.h"
#include "pal_devicemanager.h"
#include "pal_globalmanager.h"
#include "pal_memorymanager.h"
#include "pal_memorypool.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Returns a handle to a compute device
 *
 * Returns in \p *device a device handle given an ordinal in the range <b>[0,
 * ::cuDeviceGetCount()-1]</b>.
 *
 * \param device  - Returned device handle
 * \param ordinal - Device number to get handle for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGetLuid,
 * ::cuDeviceTotalMem,
 * ::cuDeviceGetExecAffinitySupport
 */
CUresult cuDeviceGet(CUdevice *device, int ordinal)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (device == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, ordinal, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", ordinal);
        return (CUresult)result;
    }

    *device = palDeviceGetId(pDevice);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns information about the device
 *
 * Returns in \p *pi the integer value of the attribute \p attrib on device
 * \p dev. The supported attributes are:
 * - ::CU_DEVICE_ATTRIBUTE_MAX_THREADS_PER_BLOCK: Maximum number of threads per
 *   block;
 * - ::CU_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_X: Maximum x-dimension of a block
 * - ::CU_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_Y: Maximum y-dimension of a block
 * - ::CU_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_Z: Maximum z-dimension of a block
 * - ::CU_DEVICE_ATTRIBUTE_MAX_GRID_DIM_X: Maximum x-dimension of a grid
 * - ::CU_DEVICE_ATTRIBUTE_MAX_GRID_DIM_Y: Maximum y-dimension of a grid
 * - ::CU_DEVICE_ATTRIBUTE_MAX_GRID_DIM_Z: Maximum z-dimension of a grid
 * - ::CU_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_BLOCK: Maximum amount of
 *   shared memory available to a thread block in bytes
 * - ::CU_DEVICE_ATTRIBUTE_TOTAL_CONSTANT_MEMORY: Memory available on device for
 *   __constant__ variables in a CUDA C kernel in bytes
 * - ::CU_DEVICE_ATTRIBUTE_WARP_SIZE: Warp size in threads
 * - ::CU_DEVICE_ATTRIBUTE_MAX_PITCH: Maximum pitch in bytes allowed by the
 *   memory copy functions that involve memory regions allocated through
 *   ::cuMemAllocPitch()
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_WIDTH: Maximum 1D
 *  texture width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LINEAR_WIDTH: Maximum width
 *  for a 1D texture bound to linear memory
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_MIPMAPPED_WIDTH: Maximum
 *  mipmapped 1D texture width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_WIDTH: Maximum 2D
 *  texture width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_HEIGHT: Maximum 2D
 *  texture height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_WIDTH: Maximum width
 *  for a 2D texture bound to linear memory
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_HEIGHT: Maximum height
 *  for a 2D texture bound to linear memory
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_PITCH: Maximum pitch
 *  in bytes for a 2D texture bound to linear memory
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_MIPMAPPED_WIDTH: Maximum
 *  mipmapped 2D texture width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_MIPMAPPED_HEIGHT: Maximum
 *  mipmapped 2D texture height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_WIDTH: Maximum 3D
 *  texture width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_HEIGHT: Maximum 3D
 *  texture height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_DEPTH: Maximum 3D
 *  texture depth
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_WIDTH_ALTERNATE:
 *  Alternate maximum 3D texture width, 0 if no alternate
 *  maximum 3D texture size is supported
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_HEIGHT_ALTERNATE:
 *  Alternate maximum 3D texture height, 0 if no alternate
 *  maximum 3D texture size is supported
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_DEPTH_ALTERNATE:
 *  Alternate maximum 3D texture depth, 0 if no alternate
 *  maximum 3D texture size is supported
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_WIDTH:
 *  Maximum cubemap texture width or height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LAYERED_WIDTH:
 *  Maximum 1D layered texture width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LAYERED_LAYERS:
 *   Maximum layers in a 1D layered texture
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_WIDTH:
 *  Maximum 2D layered texture width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_HEIGHT:
 *   Maximum 2D layered texture height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_LAYERS:
 *   Maximum layers in a 2D layered texture
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_LAYERED_WIDTH:
 *   Maximum cubemap layered texture width or height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_LAYERED_LAYERS:
 *   Maximum layers in a cubemap layered texture
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_WIDTH:
 *   Maximum 1D surface width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_WIDTH:
 *   Maximum 2D surface width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_HEIGHT:
 *   Maximum 2D surface height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_WIDTH:
 *   Maximum 3D surface width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_HEIGHT:
 *   Maximum 3D surface height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_DEPTH:
 *   Maximum 3D surface depth
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_LAYERED_WIDTH:
 *   Maximum 1D layered surface width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_LAYERED_LAYERS:
 *   Maximum layers in a 1D layered surface
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_WIDTH:
 *   Maximum 2D layered surface width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_HEIGHT:
 *   Maximum 2D layered surface height
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_LAYERS:
 *   Maximum layers in a 2D layered surface
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_WIDTH:
 *   Maximum cubemap surface width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_LAYERED_WIDTH:
 *   Maximum cubemap layered surface width
 * - ::CU_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_LAYERED_LAYERS:
 *   Maximum layers in a cubemap layered surface
 * - ::CU_DEVICE_ATTRIBUTE_MAX_REGISTERS_PER_BLOCK: Maximum number of 32-bit
 *   registers available to a thread block
 * - ::CU_DEVICE_ATTRIBUTE_CLOCK_RATE: The typical clock frequency in kilohertz
 * - ::CU_DEVICE_ATTRIBUTE_TEXTURE_ALIGNMENT: Alignment requirement; texture
 *   base addresses aligned to ::textureAlign bytes do not need an offset
 *   applied to texture fetches
 * - ::CU_DEVICE_ATTRIBUTE_TEXTURE_PITCH_ALIGNMENT: Pitch alignment requirement
 *   for 2D texture references bound to pitched memory
 * - ::CU_DEVICE_ATTRIBUTE_GPU_OVERLAP: 1 if the device can concurrently copy
 *   memory between host and device while executing a kernel, or 0 if not
 * - ::CU_DEVICE_ATTRIBUTE_MULTIPROCESSOR_COUNT: Number of multiprocessors on
 *   the device
 * - ::CU_DEVICE_ATTRIBUTE_KERNEL_EXEC_TIMEOUT: 1 if there is a run time limit
 *   for kernels executed on the device, or 0 if not
 * - ::CU_DEVICE_ATTRIBUTE_INTEGRATED: 1 if the device is integrated with the
 *   memory subsystem, or 0 if not
 * - ::CU_DEVICE_ATTRIBUTE_CAN_MAP_HOST_MEMORY: 1 if the device can map host
 *   memory into the CUDA address space, or 0 if not
 * - ::CU_DEVICE_ATTRIBUTE_COMPUTE_MODE: Compute mode that device is currently
 *   in. Available modes are as follows:
 *   - ::CU_COMPUTEMODE_DEFAULT: Default mode - Device is not restricted and
 *     can have multiple CUDA contexts present at a single time.
 *   - ::CU_COMPUTEMODE_PROHIBITED: Compute-prohibited mode - Device is
 *     prohibited from creating new CUDA contexts.
 *   - ::CU_COMPUTEMODE_EXCLUSIVE_PROCESS:  Compute-exclusive-process mode - Device
 *     can have only one context used by a single process at a time.
 * - ::CU_DEVICE_ATTRIBUTE_CONCURRENT_KERNELS: 1 if the device supports
 *   executing multiple kernels within the same context simultaneously, or 0 if
 *   not. It is not guaranteed that multiple kernels will be resident
 *   on the device concurrently so this feature should not be relied upon for
 *   correctness.
 * - ::CU_DEVICE_ATTRIBUTE_ECC_ENABLED: 1 if error correction is enabled on the
 *    device, 0 if error correction is disabled or not supported by the device
 * - ::CU_DEVICE_ATTRIBUTE_PCI_BUS_ID: PCI bus identifier of the device
 * - ::CU_DEVICE_ATTRIBUTE_PCI_DEVICE_ID: PCI device (also known as slot) identifier
 *   of the device
 * - ::CU_DEVICE_ATTRIBUTE_PCI_DOMAIN_ID: PCI domain identifier of the device
 * - ::CU_DEVICE_ATTRIBUTE_TCC_DRIVER: 1 if the device is using a TCC driver. TCC
 *    is only available on Tesla hardware running Windows Vista or later
 * - ::CU_DEVICE_ATTRIBUTE_MEMORY_CLOCK_RATE: Peak memory clock frequency in kilohertz
 * - ::CU_DEVICE_ATTRIBUTE_GLOBAL_MEMORY_BUS_WIDTH: Global memory bus width in bits
 * - ::CU_DEVICE_ATTRIBUTE_L2_CACHE_SIZE: Size of L2 cache in bytes. 0 if the device doesn't have L2 cache
 * - ::CU_DEVICE_ATTRIBUTE_MAX_THREADS_PER_MULTIPROCESSOR: Maximum resident threads per multiprocessor
 * - ::CU_DEVICE_ATTRIBUTE_UNIFIED_ADDRESSING: 1 if the device shares a unified address space with
 *   the host, or 0 if not
 * - ::CU_DEVICE_ATTRIBUTE_COMPUTE_CAPABILITY_MAJOR: Major compute capability version number
 * - ::CU_DEVICE_ATTRIBUTE_COMPUTE_CAPABILITY_MINOR: Minor compute capability version number
 * - ::CU_DEVICE_ATTRIBUTE_GLOBAL_L1_CACHE_SUPPORTED: 1 if device supports caching globals
 *    in L1 cache, 0 if caching globals in L1 cache is not supported by the device
 * - ::CU_DEVICE_ATTRIBUTE_LOCAL_L1_CACHE_SUPPORTED: 1 if device supports caching locals
 *    in L1 cache, 0 if caching locals in L1 cache is not supported by the device
 * - ::CU_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_MULTIPROCESSOR: Maximum amount of
 *   shared memory available to a multiprocessor in bytes; this amount is shared
 *   by all thread blocks simultaneously resident on a multiprocessor
 * - ::CU_DEVICE_ATTRIBUTE_MAX_REGISTERS_PER_MULTIPROCESSOR: Maximum number of 32-bit
 *   registers available to a multiprocessor; this number is shared by all thread
 *   blocks simultaneously resident on a multiprocessor
 * - ::CU_DEVICE_ATTRIBUTE_MANAGED_MEMORY: 1 if device supports allocating managed memory
 *   on this system, 0 if allocating managed memory is not supported by the device on this system.
 * - ::CU_DEVICE_ATTRIBUTE_MULTI_GPU_BOARD: 1 if device is on a multi-GPU board, 0 if not.
 * - ::CU_DEVICE_ATTRIBUTE_MULTI_GPU_BOARD_GROUP_ID: Unique identifier for a group of devices
 *   associated with the same board. Devices on the same multi-GPU board will share the same identifier.
 * - ::CU_DEVICE_ATTRIBUTE_HOST_NATIVE_ATOMIC_SUPPORTED: 1 if Link between the device and the host
 *   supports native atomic operations.
 * - ::CU_DEVICE_ATTRIBUTE_SINGLE_TO_DOUBLE_PRECISION_PERF_RATIO: Ratio of single precision performance
 *   (in floating-point operations per second) to double precision performance.
 * - ::CU_DEVICE_ATTRIBUTE_PAGEABLE_MEMORY_ACCESS: Device supports coherently accessing
 *   pageable memory without calling cudaHostRegister on it.
 * - ::CU_DEVICE_ATTRIBUTE_CONCURRENT_MANAGED_ACCESS: Device can coherently access managed memory
 *   concurrently with the CPU.
 * - ::CU_DEVICE_ATTRIBUTE_COMPUTE_PREEMPTION_SUPPORTED: Device supports Compute Preemption.
 * - ::CU_DEVICE_ATTRIBUTE_CAN_USE_HOST_POINTER_FOR_REGISTERED_MEM: Device can access host registered
 *   memory at the same virtual address as the CPU.
 * -  ::CU_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_BLOCK_OPTIN: The maximum per block shared memory size
 *    supported on this device. This is the maximum value that can be opted into when using the cuFuncSetAttribute() or cuKernelSetAttribute() call.
 *    For more details see ::CU_FUNC_ATTRIBUTE_MAX_DYNAMIC_SHARED_SIZE_BYTES
 * - ::CU_DEVICE_ATTRIBUTE_PAGEABLE_MEMORY_ACCESS_USES_HOST_PAGE_TABLES: Device accesses pageable memory via the host's
 *   page tables.
 * - ::CU_DEVICE_ATTRIBUTE_DIRECT_MANAGED_MEM_ACCESS_FROM_HOST: The host can directly access managed memory on the device without migration.
 * - ::CU_DEVICE_ATTRIBUTE_VIRTUAL_MEMORY_MANAGEMENT_SUPPORTED:  Device supports virtual memory management APIs like ::cuMemAddressReserve, ::cuMemCreate, ::cuMemMap and related APIs
 * - ::CU_DEVICE_ATTRIBUTE_HANDLE_TYPE_POSIX_FILE_DESCRIPTOR_SUPPORTED: Device supports exporting memory to a posix file descriptor with ::cuMemExportToShareableHandle, if requested via ::cuMemCreate
 * - ::CU_DEVICE_ATTRIBUTE_HANDLE_TYPE_WIN32_HANDLE_SUPPORTED:  Device supports exporting memory to a Win32 NT handle with ::cuMemExportToShareableHandle, if requested via ::cuMemCreate
 * - ::CU_DEVICE_ATTRIBUTE_HANDLE_TYPE_WIN32_KMT_HANDLE_SUPPORTED: Device supports exporting memory to a Win32 KMT handle with ::cuMemExportToShareableHandle, if requested via ::cuMemCreate
 * - ::CU_DEVICE_ATTRIBUTE_MAX_BLOCKS_PER_MULTIPROCESSOR: Maximum number of thread blocks that can reside on a multiprocessor
 * - ::CU_DEVICE_ATTRIBUTE_GENERIC_COMPRESSION_SUPPORTED: Device supports compressible memory allocation via ::cuMemCreate
 * - ::CU_DEVICE_ATTRIBUTE_MAX_PERSISTING_L2_CACHE_SIZE: Maximum L2 persisting lines capacity setting in bytes
 * - ::CU_DEVICE_ATTRIBUTE_MAX_ACCESS_POLICY_WINDOW_SIZE: Maximum value of CUaccessPolicyWindow::num_bytes
 * - ::CU_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_WITH_CUDA_VMM_SUPPORTED: Device supports specifying the GPUDirect RDMA flag with ::cuMemCreate.
 * - ::CU_DEVICE_ATTRIBUTE_RESERVED_SHARED_MEMORY_PER_BLOCK: Amount of shared memory per block reserved by CUDA driver in bytes
 * - ::CU_DEVICE_ATTRIBUTE_SPARSE_CUDA_ARRAY_SUPPORTED: Device supports sparse CUDA arrays and sparse CUDA mipmapped arrays.
 * - ::CU_DEVICE_ATTRIBUTE_READ_ONLY_HOST_REGISTER_SUPPORTED: Device supports using the ::cuMemHostRegister flag ::CU_MEMHOSTERGISTER_READ_ONLY to register memory that must be mapped as read-only to the GPU
 * - ::CU_DEVICE_ATTRIBUTE_MEMORY_POOLS_SUPPORTED: Device supports using the ::cuMemAllocAsync and ::cuMemPool family of APIs
 * - ::CU_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_SUPPORTED: Device supports GPUDirect RDMA APIs, like nvidia_p2p_get_pages (see https://docs.nvidia.com/cuda/gpudirect-rdma for more information)
 * - ::CU_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_FLUSH_WRITES_OPTIONS: The returned attribute shall be interpreted as a bitmask, where the individual bits are described by the ::CUflushGPUDirectRDMAWritesOptions enum
 * - ::CU_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_WRITES_ORDERING: GPUDirect RDMA writes to the device do not need to be flushed for consumers within the scope indicated by the returned attribute. See ::CUGPUDirectRDMAWritesOrdering for the numerical values returned here.
 * - ::CU_DEVICE_ATTRIBUTE_MEMPOOL_SUPPORTED_HANDLE_TYPES: Bitmask of handle types supported with mempool based IPC
 * - ::CU_DEVICE_ATTRIBUTE_DEFERRED_MAPPING_CUDA_ARRAY_SUPPORTED: Device supports deferred mapping CUDA arrays and CUDA mipmapped arrays.
 *
 * \param pi     - Returned device attribute value
 * \param attrib - Device attribute to query
 * \param dev    - Device handle
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem,
 * ::cuDeviceGetExecAffinitySupport,
 * ::cudaDeviceGetAttribute,
 * ::cudaGetDeviceProperties
 */
CUresult cuDeviceGetAttribute(int *pi, CUdevice_attribute attrib, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pi == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if ((attrib < CU_DEVICE_ATTRIBUTE_MAX_THREADS_PER_BLOCK) || (attrib >= CU_DEVICE_ATTRIBUTE_MAX))
    {
        CU_DBG_PRINTF_ERROR("Invalid device attribute: %d", attrib);
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetAttribute(pDevice, attrib, pi);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device attribute %d for device %d.", attrib, dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the number of compute-capable devices
 *
 * Returns in \p *count the number of devices with compute capability greater
 * than or equal to 2.0 that are available for execution. If there is no such
 * device, ::cuDeviceGetCount() returns 0.
 *
 * \param count - Returned number of compute-capable devices
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGetLuid,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem,
 * ::cuDeviceGetExecAffinitySupport,
 * ::cudaGetDeviceCount
 */
CUresult cuDeviceGetCount(int *count)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (count == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    *count = palDeviceManagerGetDeviceCount(g_globalManager.pDeviceManager);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the default mempool of a device
 *
 * The default mempool of a device contains device memory from that device.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa ::cuMemAllocAsync, ::cuMemPoolTrimTo, ::cuMemPoolGetAttribute, ::cuMemPoolSetAttribute, cuMemPoolSetAccess, ::cuDeviceGetMemPool, ::cuMemPoolCreate
 */
CUresult cuDeviceGetDefaultMemPool(CUmemoryPool *pool_out, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;
    palMemoryManager* pMemoryManager = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pool_out == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    pMemoryManager = pDevice->pMemoryManager;
    result = palMemoryManagerGetDefaultMemoryPool(pMemoryManager, (palMemoryPool**)pool_out);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get default memory pool for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns information about the execution affinity support of the device.
 *
 * Returns in \p *pi whether execution affinity type \p type is supported by device \p dev.
 * The supported types are:
 * - ::CU_EXEC_AFFINITY_TYPE_SM_COUNT: 1 if context with limited SMs is supported by the device,
 *   or 0 if not;
 *
 * \param pi   - 1 if the execution affinity type \p type is supported by the device, or 0 if not
 * \param type - Execution affinity type to query
 * \param dev  - Device handle
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem
 */
CUresult cuDeviceGetExecAffinitySupport(int *pi, CUexecAffinityType type, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pi == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if ((type < CU_EXEC_AFFINITY_TYPE_SM_COUNT) || (type >= CU_EXEC_AFFINITY_TYPE_MAX))
    {
        CU_DBG_PRINTF_ERROR("Invalid execution affinity type: %d", type);
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    *pi = palDeviceIsSupportExecAffinity(pDevice, (palExecAffinityType)type);

    return CUDA_SUCCESS;
}

/**
 * \brief Return an LUID and device node mask for the device
 *
 * Return identifying information (\p luid and \p deviceNodeMask) to allow
 * matching device with graphics APIs.
 *
 * \param luid - Returned LUID
 * \param deviceNodeMask - Returned device node mask
 * \param dev  - Device to get identifier string for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem,
 * ::cuDeviceGetExecAffinitySupport,
 * ::cudaGetDeviceProperties
 */
CUresult cuDeviceGetLuid(char *luid, unsigned int *deviceNodeMask, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if ((luid == NULL) || (deviceNodeMask == NULL))
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetLuid(pDevice, deviceNodeMask, luid);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get LUID for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Gets the current mempool for a device
 *
 * Returns the last pool provided to ::cuDeviceSetMemPool for this device
 * or the device's default memory pool if ::cuDeviceSetMemPool has never been called.
 * By default the current mempool is the default mempool for a device.
 * Otherwise the returned pool must have been set with ::cuDeviceSetMemPool.
 *
 * \returns
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa ::cuDeviceGetDefaultMemPool, ::cuMemPoolCreate, ::cuDeviceSetMemPool
 */
CUresult cuDeviceGetMemPool(CUmemoryPool *pool, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;
    palMemoryManager* pMemoryManager = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pool == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    pMemoryManager = pDevice->pMemoryManager;
    result = palMemoryManagerGetCurrentMemoryPool(pMemoryManager, (palMemoryPool**)pool);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current memory pool for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns an identifier string for the device
 *
 * Returns an ASCII string identifying the device \p dev in the NULL-terminated
 * string pointed to by \p name. \p len specifies the maximum length of the
 * string that may be returned.
 *
 * \param name - Returned identifier string for the device
 * \param len  - Maximum length of string to store in \p name
 * \param dev  - Device to get identifier string for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGetLuid,
 * ::cuDeviceGetCount,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem,
 * ::cuDeviceGetExecAffinitySupport,
 * ::cudaGetDeviceProperties
 */
CUresult cuDeviceGetName(char *name, int len, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if ((name == NULL) || (len <= 0))
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer or invalid length");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetName(pDevice, name, len);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get name for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Return NvSciSync attributes that this device can support.
 *
 * Returns in \p nvSciSyncAttrList, the properties of NvSciSync that
 * this CUDA device, \p dev can support. The returned \p nvSciSyncAttrList
 * can be used to create an NvSciSync object that matches this device's capabilities.
 *
 * If NvSciSyncAttrKey_RequiredPerm field in \p nvSciSyncAttrList is
 * already set this API will return ::CUDA_ERROR_INVALID_VALUE.
 *
 * The applications should set \p nvSciSyncAttrList to a valid
 * NvSciSyncAttrList failing which this API will return
 * ::CUDA_ERROR_INVALID_HANDLE.
 *
 * The \p flags controls how applications intends to use
 * the NvSciSync created from the \p nvSciSyncAttrList. The valid flags are:
 * - ::CUDA_NVSCISYNC_ATTR_SIGNAL, specifies that the applications intends to
 * signal an NvSciSync on this CUDA device.
 * - ::CUDA_NVSCISYNC_ATTR_WAIT, specifies that the applications intends to
 * wait on an NvSciSync on this CUDA device.
 *
 * At least one of these flags must be set, failing which the API
 * returns ::CUDA_ERROR_INVALID_VALUE. Both the flags are orthogonal
 * to one another: a developer may set both these flags that allows to
 * set both wait and signal specific attributes in the same \p nvSciSyncAttrList.
 *
 * Note that this API updates the input \p nvSciSyncAttrList with values equivalent
 * to the following public attribute key-values:
 * NvSciSyncAttrKey_RequiredPerm is set to
 * - NvSciSyncAccessPerm_SignalOnly if ::CUDA_NVSCISYNC_ATTR_SIGNAL is set in \p flags.
 * - NvSciSyncAccessPerm_WaitOnly if ::CUDA_NVSCISYNC_ATTR_WAIT is set in \p flags.
 * - NvSciSyncAccessPerm_WaitSignal if both ::CUDA_NVSCISYNC_ATTR_WAIT and
 * ::CUDA_NVSCISYNC_ATTR_SIGNAL are set in \p flags.
 * NvSciSyncAttrKey_PrimitiveInfo is set to
 * - NvSciSyncAttrValPrimitiveType_SysmemSemaphore on any valid \p device.
 * - NvSciSyncAttrValPrimitiveType_Syncpoint if \p device is a Tegra device.
 * - NvSciSyncAttrValPrimitiveType_SysmemSemaphorePayload64b if \p device is GA10X+.
 * NvSciSyncAttrKey_GpuId is set to the same UUID that is returned for this
 * \p device from ::cuDeviceGetUuid.
 *
 * \param nvSciSyncAttrList     - Return NvSciSync attributes supported.
 * \param dev                   - Valid Cuda Device to get NvSciSync attributes for.
 * \param flags                 - flags describing NvSciSync usage.
 *
 * \return
 *
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_NOT_SUPPORTED,
 * ::CUDA_ERROR_OUT_OF_MEMORY
 *
 * \sa
 * ::cuImportExternalSemaphore,
 * ::cuDestroyExternalSemaphore,
 * ::cuSignalExternalSemaphoresAsync,
 * ::cuWaitExternalSemaphoresAsync
 */
CUresult cuDeviceGetNvSciSyncAttributes(void *nvSciSyncAttrList, CUdevice dev, int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (nvSciSyncAttrList == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if ((flags == 0) || (flags & ~(CUDA_NVSCISYNC_ATTR_SIGNAL | CUDA_NVSCISYNC_ATTR_WAIT)))
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: flags must be either CUDA_NVSCISYNC_ATTR_SIGNAL or CUDA_NVSCISYNC_ATTR_WAIT");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetNvSciSyncAttributes(pDevice, flags, nvSciSyncAttrList);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get NvSciSync attributes for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the maximum number of elements allocatable in a 1D linear texture for a given texture element size.
 *
 * Returns in \p maxWidthInElements the maximum number of texture elements allocatable in a 1D linear texture
 * for given \p format and \p numChannels.
 *
 * \param maxWidthInElements    - Returned maximum number of texture elements allocatable for given \p format and \p numChannels.
 * \param format                - Texture format.
 * \param numChannels           - Number of channels per texture element.
 * \param dev                   - Device handle.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGet,
 * ::cudaMemGetInfo,
 * ::cuDeviceTotalMem
 */
CUresult cuDeviceGetTexture1DLinearMaxWidth(size_t *maxWidthInElements, CUarray_format format, unsigned numChannels, CUdevice dev)
{
    palResult         result           = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData   = NULL;
    palDevice*        pDevice          = NULL;
    palUint64         arrayElementSize = 0;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (maxWidthInElements == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palArrayCheckFormatAndNumChannels(format, numChannels);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Invalid array format or number of channels: format=%d, numChannels=%d", format, numChannels);
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    arrayElementSize = palArrayGetElementSize(format, numChannels);
    if (arrayElementSize == 0)
    {
        CU_DBG_PRINTF_ERROR("Invalid array format or number of channels: format=%d, numChannels=%d", format, numChannels);
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceGetTexture1DLinearMaxWidth(pDevice, arrayElementSize, maxWidthInElements);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get maximum texture 1D linear width for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Return an UUID for the device
 *
 * Note there is a later version of this API, ::cuDeviceGetUuid_v2. It will
 * supplant this version in 12.0, which is retained for minor version compatibility.
 *
 * Returns 16-octets identifying the device \p dev in the structure
 * pointed by the \p uuid.
 *
 * \param uuid - Returned UUID
 * \param dev  - Device to get identifier string for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetUuid_v2
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetLuid,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem,
 * ::cuDeviceGetExecAffinitySupport,
 * ::cudaGetDeviceProperties
 */
CUresult cuDeviceGetUuid(CUuuid *uuid, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (uuid == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetUuid(pDevice, uuid->bytes, PAL_FALSE);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get UUID for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Return an UUID for the device (11.4+)
 *
 * Returns 16-octets identifying the device \p dev in the structure
 * pointed by the \p uuid. If the device is in MIG mode, returns its
 * MIG UUID which uniquely identifies the subscribed MIG compute instance.
 *
 * \param uuid - Returned UUID
 * \param dev  - Device to get identifier string for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetLuid,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem,
 * ::cudaGetDeviceProperties
 */
CUresult cuDeviceGetUuid_v2(CUuuid *uuid, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (uuid == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    // TODO: Implement support for MIG UUIDs in PAL.
    result = palDeviceGetUuid(pDevice, uuid->bytes, PAL_TRUE);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get UUID for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Sets the current memory pool of a device
 *
 * The memory pool must be local to the specified device.
 * ::cuMemAllocAsync allocates from the current mempool of the provided stream's device.
 * By default, a device's current memory pool is its default memory pool.
 *
 * \note Use ::cuMemAllocFromPoolAsync to specify asynchronous allocations from a device different
 * than the one the stream runs on.
 *
 * \returns
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa ::cuDeviceGetDefaultMemPool, ::cuDeviceGetMemPool, ::cuMemPoolCreate, ::cuMemPoolDestroy, ::cuMemAllocFromPoolAsync
 */
CUresult cuDeviceSetMemPool(CUdevice dev, CUmemoryPool pool)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;
    palMemoryManager* pMemoryManager = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pool == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL input pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    pMemoryManager = pDevice->pMemoryManager;
    result = palMemoryManagerSetCurrentMemoryPool(pMemoryManager, (palMemoryPool*)pool);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to set current memory pool for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the total amount of memory on the device
 *
 * Returns in \p *bytes the total amount of memory available on the device
 * \p dev in bytes.
 *
 * \param bytes - Returned memory available on device in bytes
 * \param dev   - Device handle
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGet,
 * ::cuDeviceGetExecAffinitySupport,
 * ::cudaMemGetInfo
 */
CUresult cuDeviceTotalMem(size_t *bytes, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (bytes == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL input pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetTotalMemory(pDevice, bytes);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get total memory for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Blocks until remote writes are visible to the specified scope
 *
 * Blocks until GPUDirect RDMA writes to the target context via mappings
 * created through APIs like nvidia_p2p_get_pages (see
 * https://docs.nvidia.com/cuda/gpudirect-rdma for more information), are
 * visible to the specified scope.
 *
 * If the scope equals or lies within the scope indicated by
 * ::CU_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_WRITES_ORDERING, the call
 * will be a no-op and can be safely omitted for performance. This can be
 * determined by comparing the numerical values between the two enums, with
 * smaller scopes having smaller values.
 *
 * Users may query support for this API via
 * ::CU_DEVICE_ATTRIBUTE_FLUSH_FLUSH_GPU_DIRECT_RDMA_OPTIONS.
 *
 * \param target - The target of the operation, see ::CUflushGPUDirectRDMAWritesTarget
 * \param scope  - The scope of the operation, see ::CUflushGPUDirectRDMAWritesScope
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 */
CUresult cuFlushGPUDirectRDMAWrites(CUflushGPUDirectRDMAWritesTarget target, CUflushGPUDirectRDMAWritesScope scope)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (target != PAL_FLUSH_GPU_DIRECT_RDMA_WRITES_TARGET_CURRENT_CTX)
    {
        CU_DBG_PRINTF_ERROR("Invalid target: %d", target);
        return CUDA_ERROR_INVALID_VALUE;
    }

    if ((scope != PAL_FLUSH_GPU_DIRECT_RDMA_WRITES_TO_OWNER) &&
        (scope != PAL_FLUSH_GPU_DIRECT_RDMA_WRITES_TO_ALL_DEVICES))
    {
        CU_DBG_PRINTF_ERROR("Invalid scope: %d", scope);
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palContextFlushGPUDirectRDMAWrites(pCurrentCtx, target, scope);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to flush GPUDirect RDMA writes for target %d and scope %d.", target, scope);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the compute capability of the device
 *
 * \deprecated
 *
 * This function was deprecated as of CUDA 5.0 and its functionality superseded
 * by ::cuDeviceGetAttribute().
 *
 * Returns in \p *major and \p *minor the major and minor revision numbers that
 * define the compute capability of the device \p dev.
 *
 * \param major - Major revision number
 * \param minor - Minor revision number
 * \param dev   - Device handle
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem
 */
CUresult cuDeviceComputeCapability(int *major, int *minor, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if ((major == NULL) || (minor == NULL))
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetComputeCapability(pDevice, major, minor);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get compute capability for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns properties for a selected device
 *
 * \deprecated
 *
 * This function was deprecated as of CUDA 5.0 and replaced by ::cuDeviceGetAttribute().
 *
 * Returns in \p *prop the properties of device \p dev. The ::CUdevprop
 * structure is defined as:
 *
 * \code
     typedef struct CUdevprop_st {
     int maxThreadsPerBlock;
     int maxThreadsDim[3];
     int maxGridSize[3];
     int sharedMemPerBlock;
     int totalConstantMemory;
     int SIMDWidth;
     int memPitch;
     int regsPerBlock;
     int clockRate;
     int textureAlign
  } CUdevprop;
 * \endcode
 * where:
 *
 * - ::maxThreadsPerBlock is the maximum number of threads per block;
 * - ::maxThreadsDim[3] is the maximum sizes of each dimension of a block;
 * - ::maxGridSize[3] is the maximum sizes of each dimension of a grid;
 * - ::sharedMemPerBlock is the total amount of shared memory available per
 *   block in bytes;
 * - ::totalConstantMemory is the total amount of constant memory available on
 *   the device in bytes;
 * - ::SIMDWidth is the warp size;
 * - ::memPitch is the maximum pitch allowed by the memory copy functions that
 *   involve memory regions allocated through ::cuMemAllocPitch();
 * - ::regsPerBlock is the total number of registers available per block;
 * - ::clockRate is the clock frequency in kilohertz;
 * - ::textureAlign is the alignment requirement; texture base addresses that
 *   are aligned to ::textureAlign bytes do not need an offset applied to
 *   texture fetches.
 *
 * \param prop - Returned properties of device
 * \param dev  - Device to get properties for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetCount,
 * ::cuDeviceGetName,
 * ::cuDeviceGetUuid,
 * ::cuDeviceGet,
 * ::cuDeviceTotalMem
 */
CUresult cuDeviceGetProperties(CUdevprop *prop, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (prop == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetLegacyProperties(pDevice, (palLegacyDeviceProperties*)prop);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get properties for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns a handle to a compute device
 *
 * Returns in \p *device a device handle given a PCI bus ID string.
 *
 * \param dev      - Returned device handle
 *
 * \param pciBusId - String in one of the following forms:
 * [domain]:[bus]:[device].[function]
 * [domain]:[bus]:[device]
 * [bus]:[device].[function]
 * where \p domain, \p bus, \p device, and \p function are all hexadecimal values
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGet,
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetPCIBusId,
 * ::cudaDeviceGetByPCIBusId
 */
CUresult cuDeviceGetByPCIBusId(CUdevice *dev, const char *pciBusId)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if ((dev == NULL) || (pciBusId == NULL))
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDeviceByPCIBusId(g_globalManager.pDeviceManager, pciBusId, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with PCI bus ID: %s.", pciBusId);
        return (CUresult)result;
    }

    *dev = palDeviceGetId(pDevice);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns a PCI Bus Id string for the device
 *
 * Returns an ASCII string identifying the device \p dev in the NULL-terminated
 * string pointed to by \p pciBusId. \p len specifies the maximum length of the
 * string that may be returned.
 *
 * \param pciBusId - Returned identifier string for the device in the following format
 * [domain]:[bus]:[device].[function]
 * where \p domain, \p bus, \p device, and \p function are all hexadecimal values.
 * pciBusId should be large enough to store 13 characters including the NULL-terminator.
 *
 * \param len      - Maximum length of string to store in \p name
 *
 * \param dev      - Device to get identifier string for
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 * \notefnerr
 *
 * \sa
 * ::cuDeviceGet,
 * ::cuDeviceGetAttribute,
 * ::cuDeviceGetByPCIBusId,
 * ::cudaDeviceGetPCIBusId
 */
CUresult cuDeviceGetPCIBusId(char *pciBusId, int len, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pciBusId == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if (len <= 0)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified negative or zero length");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", dev);
        return (CUresult)result;
    }

    result = palDeviceGetPCIBusId(pDevice, len, pciBusId);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get PCI bus ID for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
* \brief Registers a callback function to receive async notifications
*
* Registers \p callbackFunc to receive async notifications.
*
* The \p userData parameter is passed to the callback function at async notification time.
* Likewise, \p callback is also passed to the callback function to distinguish between
* multiple registered callbacks.
*
* The callback function being registered should be designed to return quickly (~10ms).
* Any long running tasks should be queued for execution on an application thread.
*
* Callbacks may not call cuDeviceRegisterAsyncNotification or cuDeviceUnregisterAsyncNotification.
* Doing so will result in ::CUDA_ERROR_NOT_PERMITTED. Async notification callbacks execute
* in an undefined order and may be serialized.
*
* Returns in \p *callback a handle representing the registered callback instance.
*
* \param device - The device on which to register the callback
* \param callbackFunc - The function to register as a callback
* \param userData - A generic pointer to user data. This is passed into the callback function.
* \param callback - A handle representing the registered callback instance
*
* \return
* ::CUDA_SUCCESS
* ::CUDA_ERROR_NOT_SUPPORTED
* ::CUDA_ERROR_INVALID_DEVICE
* ::CUDA_ERROR_INVALID_VALUE
* ::CUDA_ERROR_NOT_PERMITTED
* ::CUDA_ERROR_UNKNOWN
* \notefnerr
*
* \sa
* ::cuDeviceUnregisterAsyncNotification
*/
CUresult cuDeviceRegisterAsyncNotification(CUdevice device, CUasyncCallback callbackFunc, void *userData, CUasyncCallbackHandle *callback)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, device, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", device);
        return (CUresult)result;
    }

    if (callbackFunc == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL callback function");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if (userData == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL user data pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if (callback == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer for callback handle");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceRegisterAsyncNotification(pDevice, (palAsyncCallback)callbackFunc, userData,
                                                (palAsyncCallbackEntry*)callback);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to register async notification callback for device %d.", device);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
* \brief Unregisters an async notification callback
*
* Unregisters \p callback so that the corresponding callback function will stop receiving
* async notifications.
*
* \param device - The device from which to remove \p callback.
* \param callback - The callback instance to unregister from receiving async notifications.
*
* \return
* ::CUDA_SUCCESS
* ::CUDA_ERROR_NOT_SUPPORTED
* ::CUDA_ERROR_INVALID_DEVICE
* ::CUDA_ERROR_INVALID_VALUE
* ::CUDA_ERROR_NOT_PERMITTED
* ::CUDA_ERROR_UNKNOWN
* \notefnerr
*
* \sa
* ::cuDeviceRegisterAsyncNotification
*/
CUresult cuDeviceUnregisterAsyncNotification(CUdevice device, CUasyncCallbackHandle callback)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, device, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with device id: %d.", device);
        return (CUresult)result;
    }

    if (callback == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL callback handle");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceUnregisterAsyncNotification(pDevice, (palAsyncCallbackEntry*)callback);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to unregister async notification callback for device %d.", device);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}