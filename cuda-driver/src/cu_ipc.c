#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Attempts to close memory mapped with ::cuIpcOpenMemHandle
 *
 * Decrements the reference count of the memory returned by ::cuIpcOpenMemHandle by 1.
 * When the reference count reaches 0, this API unmaps the memory. The original allocation
 * in the exporting process as well as imported mappings in other processes
 * will be unaffected.
 *
 * Any resources used to enable peer access will be freed if this is the
 * last mapping using them.
 *
 * IPC functionality is restricted to devices with support for unified
 * addressing on Linux and Windows operating systems.
 * IPC functionality on Windows is restricted to GPUs in TCC mode
 * Users can test their device for IPC functionality by calling
 * ::cuapiDeviceGetAttribute with ::CU_DEVICE_ATTRIBUTE_IPC_EVENT_SUPPORTED
 *
 * \param dptr - Device pointer returned by ::cuIpcOpenMemHandle
 *
 * \returns
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_MAP_FAILED,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_VALUE
 * \sa
 * ::cuMemAlloc,
 * ::cuMemFree,
 * ::cuIpcGetEventHandle,
 * ::cuIpcOpenEventHandle,
 * ::cuIpcGetMemHandle,
 * ::cuIpcOpenMemHandle,
 * ::cudaIpcCloseMemHandle
 */
CUresult cuIpcCloseMemHandle(CUdeviceptr dptr)
{
   palResult         result         = PAL_SUCCESS;
   palTlsThreadData* pTlsThreadData = NULL;

   result = palGlobalManagerGetStatus();
   if (result != PAL_SUCCESS)
   {
       return (CUresult)result;
   }

   result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
   if (result != PAL_SUCCESS)
   {
       CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
       return (CUresult)result;
   }

   if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
   {
       CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
       return CUDA_ERROR_NOT_PERMITTED;
   }

   return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Gets an interprocess handle for a previously allocated event
 *
 * Takes as input a previously allocated event. This event must have been
 * created with the ::CU_EVENT_INTERPROCESS and ::CU_EVENT_DISABLE_TIMING
 * flags set. This opaque handle may be copied into other processes and
 * opened with ::cuIpcOpenEventHandle to allow efficient hardware
 * synchronization between GPU work in different processes.
 *
 * After the event has been opened in the importing process,
 * ::cuEventRecord, ::cuEventSynchronize, ::cuStreamWaitEvent and
 * ::cuEventQuery may be used in either process. Performing operations
 * on the imported event after the exported event has been freed
 * with ::cuEventDestroy will result in undefined behavior.
 *
 * IPC functionality is restricted to devices with support for unified
 * addressing on Linux and Windows operating systems.
 * IPC functionality on Windows is restricted to GPUs in TCC mode
 * Users can test their device for IPC functionality by calling
 * ::cuapiDeviceGetAttribute with ::CU_DEVICE_ATTRIBUTE_IPC_EVENT_SUPPORTED
 *
 * \param pHandle - Pointer to a user allocated CUipcEventHandle
 *                    in which to return the opaque event handle
 * \param event   - Event allocated with ::CU_EVENT_INTERPROCESS and
 *                    ::CU_EVENT_DISABLE_TIMING flags.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_MAP_FAILED,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::cuEventCreate,
 * ::cuEventDestroy,
 * ::cuEventSynchronize,
 * ::cuEventQuery,
 * ::cuStreamWaitEvent,
 * ::cuIpcOpenEventHandle,
 * ::cuIpcGetMemHandle,
 * ::cuIpcOpenMemHandle,
 * ::cuIpcCloseMemHandle,
 * ::cudaIpcGetEventHandle
 */
CUresult cuIpcGetEventHandle(CUipcEventHandle *pHandle, CUevent event)
{
   palResult         result         = PAL_SUCCESS;
   palTlsThreadData* pTlsThreadData = NULL;

   result = palGlobalManagerGetStatus();
   if (result != PAL_SUCCESS)
   {
       return (CUresult)result;
   }

   result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
   if (result != PAL_SUCCESS)
   {
       CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
       return (CUresult)result;
   }

   if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
   {
       CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
       return CUDA_ERROR_NOT_PERMITTED;
   }

   return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Gets an interprocess memory handle for an existing device memory
 * allocation
 *
 * Takes a pointer to the base of an existing device memory allocation created
 * with ::cuMemAlloc and exports it for use in another process. This is a
 * lightweight operation and may be called multiple times on an allocation
 * without adverse effects.
 *
 * If a region of memory is freed with ::cuMemFree and a subsequent call
 * to ::cuMemAlloc returns memory with the same device address,
 * ::cuIpcGetMemHandle will return a unique handle for the
 * new memory.
 *
 * IPC functionality is restricted to devices with support for unified
 * addressing on Linux and Windows operating systems.
 * IPC functionality on Windows is restricted to GPUs in TCC mode
 * Users can test their device for IPC functionality by calling
 * ::cuapiDeviceGetAttribute with ::CU_DEVICE_ATTRIBUTE_IPC_EVENT_SUPPORTED
 *
 * \param pHandle - Pointer to user allocated ::CUipcMemHandle to return
 *                    the handle in.
 * \param dptr    - Base pointer to previously allocated device memory
 *
 * \returns
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_MAP_FAILED,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::cuMemAlloc,
 * ::cuMemFree,
 * ::cuIpcGetEventHandle,
 * ::cuIpcOpenEventHandle,
 * ::cuIpcOpenMemHandle,
 * ::cuIpcCloseMemHandle,
 * ::cudaIpcGetMemHandle
 */
CUresult cuIpcGetMemHandle(CUipcMemHandle *pHandle, CUdeviceptr dptr)
{
   palResult         result         = PAL_SUCCESS;
   palTlsThreadData* pTlsThreadData = NULL;

   result = palGlobalManagerGetStatus();
   if (result != PAL_SUCCESS)
   {
       return (CUresult)result;
   }

   result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
   if (result != PAL_SUCCESS)
   {
       CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
       return (CUresult)result;
   }

   if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
   {
       CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
       return CUDA_ERROR_NOT_PERMITTED;
   }

   return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Opens an interprocess event handle for use in the current process
 *
 * Opens an interprocess event handle exported from another process with
 * ::cuIpcGetEventHandle. This function returns a ::CUevent that behaves like
 * a locally created event with the ::CU_EVENT_DISABLE_TIMING flag specified.
 * This event must be freed with ::cuEventDestroy.
 *
 * Performing operations on the imported event after the exported event has
 * been freed with ::cuEventDestroy will result in undefined behavior.
 *
 * IPC functionality is restricted to devices with support for unified
 * addressing on Linux and Windows operating systems.
 * IPC functionality on Windows is restricted to GPUs in TCC mode
 * Users can test their device for IPC functionality by calling
 * ::cuapiDeviceGetAttribute with ::CU_DEVICE_ATTRIBUTE_IPC_EVENT_SUPPORTED
 *
 * \param phEvent - Returns the imported event
 * \param handle  - Interprocess handle to open
 *
 * \returns
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_MAP_FAILED,
 * ::CUDA_ERROR_PEER_ACCESS_UNSUPPORTED,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::cuEventCreate,
 * ::cuEventDestroy,
 * ::cuEventSynchronize,
 * ::cuEventQuery,
 * ::cuStreamWaitEvent,
 * ::cuIpcGetEventHandle,
 * ::cuIpcGetMemHandle,
 * ::cuIpcOpenMemHandle,
 * ::cuIpcCloseMemHandle,
 * ::cudaIpcOpenEventHandle
 */
CUresult cuIpcOpenEventHandle(CUevent *phEvent, CUipcEventHandle handle)
{
   palResult         result         = PAL_SUCCESS;
   palTlsThreadData* pTlsThreadData = NULL;

   result = palGlobalManagerGetStatus();
   if (result != PAL_SUCCESS)
   {
       return (CUresult)result;
   }

   result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
   if (result != PAL_SUCCESS)
   {
       CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
       return (CUresult)result;
   }

   if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
   {
       CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
       return CUDA_ERROR_NOT_PERMITTED;
   }

   return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Opens an interprocess memory handle exported from another process
 * and returns a device pointer usable in the local process.
 *
 * Maps memory exported from another process with ::cuIpcGetMemHandle into
 * the current device address space. For contexts on different devices
 * ::cuIpcOpenMemHandle can attempt to enable peer access between the
 * devices as if the user called ::cuCtxEnablePeerAccess. This behavior is
 * controlled by the ::CU_IPC_MEM_LAZY_ENABLE_PEER_ACCESS flag.
 * ::cuDeviceCanAccessPeer can determine if a mapping is possible.
 *
 * Contexts that may open ::CUipcMemHandles are restricted in the following way.
 * ::CUipcMemHandles from each ::CUdevice in a given process may only be opened
 * by one ::CUcontext per ::CUdevice per other process.
 *
 * If the memory handle has already been opened by the current context, the
 * reference count on the handle is incremented by 1 and the existing device pointer
 * is returned.
 *
 * Memory returned from ::cuIpcOpenMemHandle must be freed with
 * ::cuIpcCloseMemHandle.
 *
 * Calling ::cuMemFree on an exported memory region before calling
 * ::cuIpcCloseMemHandle in the importing context will result in undefined
 * behavior.
 *
 * IPC functionality is restricted to devices with support for unified
 * addressing on Linux and Windows operating systems.
 * IPC functionality on Windows is restricted to GPUs in TCC mode
 * Users can test their device for IPC functionality by calling
 * ::cuapiDeviceGetAttribute with ::CU_DEVICE_ATTRIBUTE_IPC_EVENT_SUPPORTED
 *
 * \param pdptr  - Returned device pointer
 * \param handle - ::CUipcMemHandle to open
 * \param Flags  - Flags for this operation. Must be specified as ::CU_IPC_MEM_LAZY_ENABLE_PEER_ACCESS
 *
 * \returns
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_MAP_FAILED,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_TOO_MANY_PEERS,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \note No guarantees are made about the address returned in \p *pdptr.
 * In particular, multiple processes may not receive the same address for the same \p handle.
 *
 * \sa
 * ::cuMemAlloc,
 * ::cuMemFree,
 * ::cuIpcGetEventHandle,
 * ::cuIpcOpenEventHandle,
 * ::cuIpcGetMemHandle,
 * ::cuIpcCloseMemHandle,
 * ::cuCtxEnablePeerAccess,
 * ::cuDeviceCanAccessPeer,
 * ::cudaIpcOpenMemHandle
 */
CUresult cuIpcOpenMemHandle_v2(CUdeviceptr *pdptr, CUipcMemHandle handle, unsigned int Flags)
{
   palResult         result         = PAL_SUCCESS;
   palTlsThreadData* pTlsThreadData = NULL;

   result = palGlobalManagerGetStatus();
   if (result != PAL_SUCCESS)
   {
       return (CUresult)result;
   }

   result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
   if (result != PAL_SUCCESS)
   {
       CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
       return (CUresult)result;
   }

   if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
   {
       CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
       return CUDA_ERROR_NOT_PERMITTED;
   }

   return CUDA_ERROR_NOT_SUPPORTED;
}