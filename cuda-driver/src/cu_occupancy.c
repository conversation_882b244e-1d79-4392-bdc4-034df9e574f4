#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Returns dynamic shared memory available per block when launching \p numBlocks blocks on SM
 *
 * Returns in \p *dynamicSmemSize the maximum size of dynamic shared memory to allow \p numBlocks blocks per SM.
 *
 * \param dynamicSmemSize - Returned maximum dynamic shared memory
 * \param func            - Kernel function for which occupancy is calculated
 * \param numBlocks       - Number of blocks to fit on SM
 * \param blockSize       - Size of the blocks
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 */
CUresult cuOccupancyAvailableDynamicSMemPerBlock(size_t *dynamicSmemSize, CUfunction func, int numBlocks, int blockSize)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns occupancy of a function
 *
 * Returns in \p *numBlocks the number of the maximum active blocks per
 * streaming multiprocessor.
 *
 * \param numBlocks       - Returned occupancy
 * \param func            - Kernel for which occupancy is calculated
 * \param blockSize       - Block size the kernel is intended to be launched with
 * \param dynamicSMemSize - Per-block dynamic shared memory usage intended, in bytes
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa
 * ::cudaOccupancyMaxActiveBlocksPerMultiprocessor
 */
CUresult cuOccupancyMaxActiveBlocksPerMultiprocessor(int *numBlocks, CUfunction func, int blockSize, size_t dynamicSMemSize)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns occupancy of a function
 *
 * Returns in \p *numBlocks the number of the maximum active blocks per
 * streaming multiprocessor.
 *
 * The \p Flags parameter controls how special cases are handled. The
 * valid flags are:
 *
 * - ::CU_OCCUPANCY_DEFAULT, which maintains the default behavior as
 *   ::cuOccupancyMaxActiveBlocksPerMultiprocessor;
 *
 * - ::CU_OCCUPANCY_DISABLE_CACHING_OVERRIDE, which suppresses the
 *   default behavior on platform where global caching affects
 *   occupancy. On such platforms, if caching is enabled, but
 *   per-block SM resource usage would result in zero occupancy, the
 *   occupancy calculator will calculate the occupancy as if caching
 *   is disabled. Setting ::CU_OCCUPANCY_DISABLE_CACHING_OVERRIDE makes
 *   the occupancy calculator to return 0 in such cases. More information
 *   can be found about this feature in the "Unified L1/Texture Cache"
 *   section of the Maxwell tuning guide.
 *
 * \param numBlocks       - Returned occupancy
 * \param func            - Kernel for which occupancy is calculated
 * \param blockSize       - Block size the kernel is intended to be launched with
 * \param dynamicSMemSize - Per-block dynamic shared memory usage intended, in bytes
 * \param flags           - Requested behavior for the occupancy calculator
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa
 * ::cudaOccupancyMaxActiveBlocksPerMultiprocessorWithFlags
 */
CUresult cuOccupancyMaxActiveBlocksPerMultiprocessorWithFlags(int *numBlocks, CUfunction func, int blockSize, size_t dynamicSMemSize, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Given the kernel function (\p func) and launch configuration
 * (\p config), return the maximum number of clusters that could co-exist
 * on the target device in \p *numClusters.
 *
 * If the function has required cluster size already set (see
 * ::cudaFuncGetAttributes / ::cuFuncGetAttribute), the cluster size
 * from config must either be unspecified or match the required size.
 * Without required sizes, the cluster size must be specified in config,
 * else the function will return an error.
 *
 * Note that various attributes of the kernel function may affect occupancy
 * calculation. Runtime environment may affect how the hardware schedules
 * the clusters, so the calculated occupancy is not guaranteed to be achievable.
 *
 * \param numClusters - Returned maximum number of clusters that
 *                      could co-exist on the target device
 * \param func        - Kernel function for which maximum number
 *                      of clusters are calculated
 * \param config      - Launch configuration for the given kernel function
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_CLUSTER_SIZE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa
 * ::cudaFuncGetAttributes,
 * ::cuFuncGetAttribute
 */
CUresult cuOccupancyMaxActiveClusters(int *numClusters, CUfunction func, const CUlaunchConfig *config)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Suggest a launch configuration with reasonable occupancy
 *
 * Returns in \p *blockSize a reasonable block size that can achieve
 * the maximum occupancy (or, the maximum number of active warps with
 * the fewest blocks per multiprocessor), and in \p *minGridSize the
 * minimum grid size to achieve the maximum occupancy.
 *
 * If \p blockSizeLimit is 0, the configurator will use the maximum
 * block size permitted by the device / function instead.
 *
 * If per-block dynamic shared memory allocation is not needed, the
 * user should leave both \p blockSizeToDynamicSMemSize and \p
 * dynamicSMemSize as 0.
 *
 * If per-block dynamic shared memory allocation is needed, then if
 * the dynamic shared memory size is constant regardless of block
 * size, the size should be passed through \p dynamicSMemSize, and \p
 * blockSizeToDynamicSMemSize should be NULL.
 *
 * Otherwise, if the per-block dynamic shared memory size varies with
 * different block sizes, the user needs to provide a unary function
 * through \p blockSizeToDynamicSMemSize that computes the dynamic
 * shared memory needed by \p func for any given block size. \p
 * dynamicSMemSize is ignored. An example signature is:
 *
 * \code
 *    // Take block size, returns dynamic shared memory needed
 *    size_t blockToSmem(int blockSize);
 * \endcode
 *
 * \param minGridSize - Returned minimum grid size needed to achieve the maximum occupancy
 * \param blockSize   - Returned maximum block size that can achieve the maximum occupancy
 * \param func        - Kernel for which launch configuration is calculated
 * \param blockSizeToDynamicSMemSize - A function that calculates how much per-block dynamic shared memory \p func uses based on the block size
 * \param dynamicSMemSize - Dynamic shared memory usage intended, in bytes
 * \param blockSizeLimit  - The maximum block size \p func is designed to handle
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa
 * ::cudaOccupancyMaxPotentialBlockSize
 */
CUresult cuOccupancyMaxPotentialBlockSize(int *minGridSize, int *blockSize, CUfunction func, CUoccupancyB2DSize blockSizeToDynamicSMemSize, size_t dynamicSMemSize, int blockSizeLimit)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Suggest a launch configuration with reasonable occupancy
 *
 * An extended version of ::cuOccupancyMaxPotentialBlockSize. In
 * addition to arguments passed to ::cuOccupancyMaxPotentialBlockSize,
 * ::cuOccupancyMaxPotentialBlockSizeWithFlags also takes a \p Flags
 * parameter.
 *
 * The \p Flags parameter controls how special cases are handled. The
 * valid flags are:
 *
 * - ::CU_OCCUPANCY_DEFAULT, which maintains the default behavior as
 *   ::cuOccupancyMaxPotentialBlockSize;
 *
 * - ::CU_OCCUPANCY_DISABLE_CACHING_OVERRIDE, which suppresses the
 *   default behavior on platform where global caching affects
 *   occupancy. On such platforms, the launch configurations that
 *   produces maximal occupancy might not support global
 *   caching. Setting ::CU_OCCUPANCY_DISABLE_CACHING_OVERRIDE
 *   guarantees that the the produced launch configuration is global
 *   caching compatible at a potential cost of occupancy. More information
 *   can be found about this feature in the "Unified L1/Texture Cache"
 *   section of the Maxwell tuning guide.
 *
 * \param minGridSize - Returned minimum grid size needed to achieve the maximum occupancy
 * \param blockSize   - Returned maximum block size that can achieve the maximum occupancy
 * \param func        - Kernel for which launch configuration is calculated
 * \param blockSizeToDynamicSMemSize - A function that calculates how much per-block dynamic shared memory \p func uses based on the block size
 * \param dynamicSMemSize - Dynamic shared memory usage intended, in bytes
 * \param blockSizeLimit  - The maximum block size \p func is designed to handle
 * \param flags       - Options
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa
 * ::cudaOccupancyMaxPotentialBlockSizeWithFlags
 */
CUresult cuOccupancyMaxPotentialBlockSizeWithFlags(int *minGridSize, int *blockSize, CUfunction func, CUoccupancyB2DSize blockSizeToDynamicSMemSize, size_t dynamicSMemSize, int blockSizeLimit, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Given the kernel function (\p func) and launch configuration
 * (\p config), return the maximum cluster size in \p *clusterSize.
 *
 * The cluster dimensions in \p config are ignored. If func has a required
 * cluster size set (see ::cudaFuncGetAttributes / ::cuFuncGetAttribute),\p
 * *clusterSize will reflect the required cluster size.
 *
 * By default this function will always return a value that's portable on
 * future hardware. A higher value may be returned if the kernel function
 * allows non-portable cluster sizes.
 *
 * This function will respect the compile time launch bounds.
 *
 * \param clusterSize - Returned maximum cluster size that can be launched
 *                      for the given kernel function and launch configuration
 * \param func        - Kernel function for which maximum cluster
 *                      size is calculated
 * \param config      - Launch configuration for the given kernel function
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa
 * ::cudaFuncGetAttributes,
 * ::cuFuncGetAttribute
 */
CUresult cuOccupancyMaxPotentialClusterSize(int *clusterSize, CUfunction func, const CUlaunchConfig *config)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}