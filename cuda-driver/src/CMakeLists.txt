add_library(cuda SHARED
    cu_context.c
    cu_coredump.c
    cu_device.c
    cu_entrypoint.c
    cu_error.c
    cu_event.c
    cu_execution.c
    cu_external.c
    cu_graph.c
    cu_greenctx.c
    cu_init.c
    cu_ipc.c
    cu_kernel.c
    cu_memory.c
    cu_module.c
    cu_multicast.c
    cu_occupancy.c
    cu_peer.c
    cu_primaryctx.c
    cu_profiler.c
    cu_stream_ops.c
    cu_stream_ordered.c
    cu_stream.c
    cu_tensormap.c
    cu_unified.c
    cu_version.c
    cu_vmm.c
)

# Link the dependencies of module.
target_link_libraries(cuda PUBLIC util thunk pal)

# Add the path of including file.
target_include_directories(cuda PUBLIC
    ${CMAKE_SOURCE_DIR}/src/pal         # public API
    ${CMAKE_SOURCE_DIR}/src/pal/core    # Core module
    ${CMAKE_SOURCE_DIR}/src/pal/util    # Util module
    ${CMAKE_SOURCE_DIR}/src/pal/thunk   # Thunk module
)

# Dynamic library symbol control
if (WIN32)
    target_sources(cuda PRIVATE ${CMAKE_SOURCE_DIR}/cuda.def)
elseif (UNIX)
    set_target_properties(cuda PROPERTIES
        LINK_FLAGS "-Wl,--version-script=${CMAKE_SOURCE_DIR}/cuda.def"
    )
endif()

# Set the output name with dynamic library.
set_target_properties(cuda PROPERTIES OUTPUT_NAME "cuda")

# compiler options
target_compile_options(cuda PRIVATE -Wno-deprecated-declarations)

# Install libraries (both static and dynamic)
install(TARGETS cuda
    LIBRARY DESTINATION lib # For shared libraries
    ARCHIVE DESTINATION lib # For static libraries
)

# Install header files
install(FILES
    ${CMAKE_SOURCE_DIR}/include/cuda.h
    ${CMAKE_SOURCE_DIR}/include/cudaProfiler.h
    ${CMAKE_SOURCE_DIR}/include/cudaTypedefs.h
    ${CMAKE_SOURCE_DIR}/include/cudaProfilerTypedefs.h
    DESTINATION include
)