#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Allows caller to fetch a coredump attribute value for the current context
 *
 * Returns in \p *value the requested value specified by \p attrib. It is up to the caller
 * to ensure that the data type and size of \p *value matches the request.
 *
 * If the caller calls this function with \p *value equal to NULL, the size of the memory
 * region (in bytes) expected for \p attrib will be placed in \p size.
 *
 * The supported attributes are:
 * - ::CU_COREDUMP_ENABLE_ON_EXCEPTION: Bool where ::true means that GPU exceptions from
 *      this context will create a coredump at the location specified by ::CU_COREDUMP_FILE.
 *      The default value is ::false unless set to ::true globally or locally, or the
 *      CU_CTX_USER_COREDUMP_ENABLE flag was set during context creation.
 * - ::CU_COREDUMP_TRIGGER_HOST: Bool where ::true means that the host CPU will
 *      also create a coredump. The default value is ::true unless set to ::false globally or
 *      or locally.
 * - ::CU_COREDUMP_LIGHTWEIGHT: Bool where ::true means that any resulting coredumps
 *      will not have a dump of GPU memory or non-reloc ELF images. The default value is
 *      ::false unless set to ::true globally or locally.
 * - ::CU_COREDUMP_ENABLE_USER_TRIGGER: Bool where ::true means that a coredump can be
 *      created by writing to the system pipe specified by ::CU_COREDUMP_PIPE. The default
 *      value is ::false unless set to ::true globally or locally.
 * - ::CU_COREDUMP_FILE: String of up to 1023 characters that defines the location where
 *      any coredumps generated by this context will be written. The default value is
 *      ::core.cuda.HOSTNAME.PID where ::HOSTNAME is the host name of the machine running
 *      the CUDA applications and ::PID is the process ID of the CUDA application.
 * - ::CU_COREDUMP_PIPE: String of up to 1023 characters that defines the name of the pipe
 *      that will be monitored if user-triggered coredumps are enabled. The default value is
 *      ::corepipe.cuda.HOSTNAME.PID where ::HOSTNAME is the host name of the machine running
 *      the CUDA application and ::PID is the process ID of the CUDA application.
 *
 * \param attrib - The enum defining which value to fetch.
 * \param value - void* containing the requested data.
 * \param size - The size of the memory region \p value points to.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_PERMITTED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_CONTEXT_IS_DESTROYED
 *
 * \sa
 * ::cuCoredumpGetAttributeGlobal,
 * ::cuCoredumpSetAttribute,
 * ::cuCoredumpSetAttributeGlobal
 */
CUresult cuCoredumpGetAttribute(CUcoredumpSettings attrib, void* value, size_t *size)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Allows caller to fetch a coredump attribute value for the entire application
 *
 * Returns in \p *value the requested value specified by \p attrib. It is up to the caller
 * to ensure that the data type and size of \p *value matches the request.
 *
 * If the caller calls this function with \p *value equal to NULL, the size of the memory
 * region (in bytes) expected for \p attrib will be placed in \p size.
 *
 * The supported attributes are:
 * - ::CU_COREDUMP_ENABLE_ON_EXCEPTION: Bool where ::true means that GPU exceptions from
 *      this context will create a coredump at the location specified by ::CU_COREDUMP_FILE.
 *      The default value is ::false.
 * - ::CU_COREDUMP_TRIGGER_HOST: Bool where ::true means that the host CPU will
 *      also create a coredump. The default value is ::true.
 * - ::CU_COREDUMP_LIGHTWEIGHT: Bool where ::true means that any resulting coredumps
 *      will not have a dump of GPU memory or non-reloc ELF images. The default value is
 *      ::false.
 * - ::CU_COREDUMP_ENABLE_USER_TRIGGER: Bool where ::true means that a coredump can be
 *      created by writing to the system pipe specified by ::CU_COREDUMP_PIPE. The default
 *      value is ::false.
 * - ::CU_COREDUMP_FILE: String of up to 1023 characters that defines the location where
 *      any coredumps generated by this context will be written. The default value is
 *      ::core.cuda.HOSTNAME.PID where ::HOSTNAME is the host name of the machine running
 *      the CUDA applications and ::PID is the process ID of the CUDA application.
 * - ::CU_COREDUMP_PIPE: String of up to 1023 characters that defines the name of the pipe
 *      that will be monitored if user-triggered coredumps are enabled. The default value is
 *      ::corepipe.cuda.HOSTNAME.PID where ::HOSTNAME is the host name of the machine running
 *      the CUDA application and ::PID is the process ID of the CUDA application.
 *
 * \param attrib - The enum defining which value to fetch.
 * \param value - void* containing the requested data.
 * \param size - The size of the memory region \p value points to.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::cuCoredumpGetAttribute,
 * ::cuCoredumpSetAttribute,
 * ::cuCoredumpSetAttributeGlobal
 */
CUresult cuCoredumpGetAttributeGlobal(CUcoredumpSettings attrib, void *value, size_t *size)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Allows caller to set a coredump attribute value for the current context
 *
 * This function should be considered an alternate interface to the CUDA-GDB environment
 * variables defined in this document: https://docs.nvidia.com/cuda/cuda-gdb/index.html#gpu-coredump
 *
 * An important design decision to note is that any coredump environment variable values
 * set before CUDA initializes will take permanent precedence over any values set with this
 * this function. This decision was made to ensure no change in behavior for any users that
 * may be currently using these variables to get coredumps.
 *
 * \p *value shall contain the requested value specified by \p set. It is up to the caller
 * to ensure that the data type and size of \p *value matches the request.
 *
 * If the caller calls this function with \p *value equal to NULL, the size of the memory
 * region (in bytes) expected for \p set will be placed in \p size.
 *
 * /note This function will return ::CUDA_ERROR_NOT_SUPPORTED if the caller attempts to set
 * ::CU_COREDUMP_ENABLE_ON_EXCEPTION on a GPU of with Compute Capability < 6.0. ::cuCoredumpSetAttributeGlobal
 * works on those platforms as an alternative.
 *
 * /note ::CU_COREDUMP_ENABLE_USER_TRIGGER and ::CU_COREDUMP_PIPE cannot be set on a per-context basis.
 *
 * The supported attributes are:
 * - ::CU_COREDUMP_ENABLE_ON_EXCEPTION: Bool where ::true means that GPU exceptions from
 *      this context will create a coredump at the location specified by ::CU_COREDUMP_FILE.
 *      The default value is ::false.
 * - ::CU_COREDUMP_TRIGGER_HOST: Bool where ::true means that the host CPU will
 *      also create a coredump. The default value is ::true.
 * - ::CU_COREDUMP_LIGHTWEIGHT: Bool where ::true means that any resulting coredumps
 *      will not have a dump of GPU memory or non-reloc ELF images. The default value is
 *      ::false.
 * - ::CU_COREDUMP_FILE: String of up to 1023 characters that defines the location where
 *      any coredumps generated by this context will be written. The default value is
 *      ::core.cuda.HOSTNAME.PID where ::HOSTNAME is the host name of the machine running
 *      the CUDA applications and ::PID is the process ID of the CUDA application.
 *
 * \param attrib - The enum defining which value to set.
 * \param value - void* containing the requested data.
 * \param size - The size of the memory region \p value points to.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_PERMITTED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_CONTEXT_IS_DESTROYED,
 * ::CUDA_ERROR_NOT_SUPPORTED
 *
 * \sa
 * ::cuCoredumpGetAttributeGlobal,
 * ::cuCoredumpGetAttribute,
 * ::cuCoredumpSetAttributeGlobal
 */
CUresult cuCoredumpSetAttribute(CUcoredumpSettings attrib, void* value, size_t *size)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Allows caller to set a coredump attribute value globally
 *
 * This function should be considered an alternate interface to the CUDA-GDB environment
 * variables defined in this document: https://docs.nvidia.com/cuda/cuda-gdb/index.html#gpu-coredump
 *
 * An important design decision to note is that any coredump environment variable values
 * set before CUDA initializes will take permanent precedence over any values set with this
 * this function. This decision was made to ensure no change in behavior for any users that
 * may be currently using these variables to get coredumps.
 *
 * \p *value shall contain the requested value specified by \p set. It is up to the caller
 * to ensure that the data type and size of \p *value matches the request.
 *
 * If the caller calls this function with \p *value equal to NULL, the size of the memory
 * region (in bytes) expected for \p set will be placed in \p size.
 *
 * The supported attributes are:
 * - ::CU_COREDUMP_ENABLE_ON_EXCEPTION: Bool where ::true means that GPU exceptions from
 *      this context will create a coredump at the location specified by ::CU_COREDUMP_FILE.
 *      The default value is ::false.
 * - ::CU_COREDUMP_TRIGGER_HOST: Bool where ::true means that the host CPU will
 *      also create a coredump. The default value is ::true.
 * - ::CU_COREDUMP_LIGHTWEIGHT: Bool where ::true means that any resulting coredumps
 *      will not have a dump of GPU memory or non-reloc ELF images. The default value is
 *      ::false.
 * - ::CU_COREDUMP_ENABLE_USER_TRIGGER: Bool where ::true means that a coredump can be
 *      created by writing to the system pipe specified by ::CU_COREDUMP_PIPE. The default
 *      value is ::false.
 * - ::CU_COREDUMP_FILE: String of up to 1023 characters that defines the location where
 *      any coredumps generated by this context will be written. The default value is
 *      ::core.cuda.HOSTNAME.PID where ::HOSTNAME is the host name of the machine running
 *      the CUDA applications and ::PID is the process ID of the CUDA application.
 * - ::CU_COREDUMP_PIPE: String of up to 1023 characters that defines the name of the pipe
 *      that will be monitored if user-triggered coredumps are enabled. This value may not be
 *      changed after ::CU_COREDUMP_ENABLE_USER_TRIGGER is set to ::true. The default
 *      value is ::corepipe.cuda.HOSTNAME.PID where ::HOSTNAME is the host name of the machine
 *      running the CUDA application and ::PID is the process ID of the CUDA application.
 *
 * \param attrib - The enum defining which value to set.
 * \param value - void* containing the requested data.
 * \param size - The size of the memory region \p value points to.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_PERMITTED
 *
 * \sa
 * ::cuCoredumpGetAttribute,
 * ::cuCoredumpGetAttributeGlobal,
 * ::cuCoredumpSetAttribute
 */
CUresult cuCoredumpSetAttributeGlobal(CUcoredumpSettings attrib, void *value, size_t *size)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}