#ifndef CU_INIT_H_
#define CU_INIT_H_

#include "cuda.h"
#include "cudaProfiler.h"
#include "cuda_driver_export_table.h"

#include "pal_assert.h"
#include "pal_globalmanager.h"

extern CUdriverExportTable g_driverExportTable;

#if ENABLE_PRINTS_ASSERTS
/// Debug printf macro.
#define CU_DBG_PRINTF(_level, _stype, ...) palDbgPrintf(_level, _style, PAL_TRUE, __VA_ARGS__)

/// Debug info printf macro.
#define CU_DBG_PRINTF_INFO(_pFormat, ...)                                                        \
    {                                                                                            \
        palDbgPrintf(PAL_DBG_PRINT_CAT_INFO_MSG, PAL_DBG_PRINT_STYLE_DEFAULT, PAL_TRUE,          \
                     PAL_DBG_PRINT_COLOR_GREEN _pFormat " (%s:%d:%s)" PAL_DBG_PRINT_COLOR_RESET, \
                     ##__VA_ARGS__, __FILE__, __LINE__, __func__);                               \
    }

/// Debug warning printf macro.
#define CU_DBG_PRINTF_WARN(_pFormat, ...)                                                         \
    {                                                                                             \
        palDbgPrintf(PAL_DBG_PRINT_CAT_WARN_MSG, PAL_DBG_PRINT_STYLE_DEFAULT, PAL_TRUE,           \
                     PAL_DBG_PRINT_COLOR_YELLOW _pFormat " (%s:%d:%s)" PAL_DBG_PRINT_COLOR_RESET, \
                     ##__VA_ARGS__, __FILE__, __LINE__, __func__);                                \
    }

/// Debug error printf macro.
#define CU_DBG_PRINTF_ERROR(_pFormat, ...)                                                     \
    {                                                                                          \
        palDbgPrintf(PAL_DBG_PRINT_CAT_ERROR_MSG, PAL_DBG_PRINT_STYLE_DEFAULT, PAL_TRUE,       \
                     PAL_DBG_PRINT_COLOR_RED _pFormat " (%s:%d:%s)" PAL_DBG_PRINT_COLOR_RESET, \
                     ##__VA_ARGS__, __FILE__, __LINE__, __func__);                             \
    }
#else
// Debug printf macro
#define CU_DBG_PRINTF(...) ((void)0)
/// Debug info printf macro.
#define CU_DBG_PRINTF_INFO(...) ((void)0)
/// Debug warning printf macro.
#define CU_DBG_PRINTF_WARN(...) ((void)0)
/// Debug error printf macro.
#define CU_DBG_PRINTF_ERROR(...) ((void)0)
#endif

#endif // CU_INIT_H_