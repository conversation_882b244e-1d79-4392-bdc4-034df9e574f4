#include "cu_init.h"

#include "pal_context.h"
#include "pal_device.h"
#include "pal_devicemanager.h"
#include "pal_globalmanager.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Get the state of the primary context
 *
 * Returns in \p *flags the flags for the primary context of \p dev, and in
 * \p *active whether it is active.  See ::cuDevicePrimaryCtxSetFlags for flag
 * values.
 *
 * \param dev    - Device to get primary context flags for
 * \param flags  - Pointer to store flags
 * \param active - Pointer to store context state; 0 = inactive, 1 = active
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa
 * ::cuDevicePrimaryCtxSetFlags,
 * ::cuCtxGetFlags,
 * ::cuCtxSetFlags,
 * ::cudaGetDeviceFlags
 */
CUresult cuDevicePrimaryCtxGetState(CUdevice dev, unsigned int *flags, int *active)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    result = palDevicePrimaryContextGetState(pDevice, flags, active);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get the state of primary context for device ID %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Release the primary context on the GPU
 *
 * Releases the primary context interop on the device.
 * A retained context should always be released once the user is done using
 * it. The context is automatically reset once the last reference to it is
 * released. This behavior is different when the primary context was retained
 * by the CUDA runtime from CUDA 4.0 and earlier. In this case, the primary
 * context remains always active.
 *
 * Releasing a primary context that has not been previously retained will
 * fail with ::CUDA_ERROR_INVALID_CONTEXT.
 *
 * Please note that unlike ::cuCtxDestroy() this method does not pop the context
 * from stack in any circumstances.
 *
 * \param dev - Device which primary context is released
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa ::cuDevicePrimaryCtxRetain,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuDevicePrimaryCtxRelease_v2(CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    result = palDevicePrimaryContextRelease(pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to release primary context for device ID %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Destroy all allocations and reset all state on the primary context
 *
 * Explicitly destroys and cleans up all resources associated with the current
 * device in the current process.
 *
 * Note that it is responsibility of the calling function to ensure that no
 * other module in the process is using the device any more. For that reason
 * it is recommended to use ::cuDevicePrimaryCtxRelease() in most cases.
 * However it is safe for other modules to call ::cuDevicePrimaryCtxRelease()
 * even after resetting the device.
 * Resetting the primary context does not release it, an application that has
 * retained the primary context should explicitly release its usage.
 *
 * \param dev - Device for which primary context is destroyed
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_PRIMARY_CONTEXT_ACTIVE
 * \notefnerr
 *
 * \sa ::cuDevicePrimaryCtxRetain,
 * ::cuDevicePrimaryCtxRelease,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cudaDeviceReset
 */
CUresult cuDevicePrimaryCtxReset_v2(CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    result = palDevicePrimaryContextReset(pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to reset primary context for device ID %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Retain the primary context on the GPU
 *
 * Retains the primary context on the device.
 * Once the user successfully retains the primary context, the primary context
 * will be active and available to the user until the user releases it
 * with ::cuDevicePrimaryCtxRelease() or resets it with ::cuDevicePrimaryCtxReset().
 * Unlike ::cuCtxCreate() the newly retained context is not pushed onto the stack.
 *
 * Retaining the primary context for the first time will fail with ::CUDA_ERROR_UNKNOWN
 * if the compute mode of the device is ::CU_COMPUTEMODE_PROHIBITED. The function
 * ::cuDeviceGetAttribute() can be used with ::CU_DEVICE_ATTRIBUTE_COMPUTE_MODE to
 * determine the compute mode  of the device.
 * The <i>nvidia-smi</i> tool can be used to set the compute mode for
 * devices. Documentation for <i>nvidia-smi</i> can be obtained by passing a
 * -h option to it.
 *
 * Please note that the primary context always supports pinned allocations. Other
 * flags can be specified by ::cuDevicePrimaryCtxSetFlags().
 *
 * \param pctx  - Returned context handle of the new context
 * \param dev   - Device for which primary context is requested
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa ::cuDevicePrimaryCtxRelease,
 * ::cuDevicePrimaryCtxSetFlags,
 * ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuDevicePrimaryCtxRetain(CUcontext *pctx, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: pctx is NULL.");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    result = palDevicePrimaryContextRetain(pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to retain primary context for device ID %d.", dev);
        *pctx = NULL;
        return (CUresult)result;
    }

    *pctx = (CUcontext)palDeviceGetPrimaryContext(pDevice);

    return CUDA_SUCCESS;
}

/**
 * \brief Set flags for the primary context
 *
 * Sets the flags for the primary context on the device overwriting previously
 * set ones.
 *
 * The three LSBs of the \p flags parameter can be used to control how the OS
 * thread, which owns the CUDA context at the time of an API call, interacts
 * with the OS scheduler when waiting for results from the GPU. Only one of
 * the scheduling flags can be set when creating a context.
 *
 * - ::CU_CTX_SCHED_SPIN: Instruct CUDA to actively spin when waiting for
 * results from the GPU. This can decrease latency when waiting for the GPU,
 * but may lower the performance of CPU threads if they are performing work in
 * parallel with the CUDA thread.
 *
 * - ::CU_CTX_SCHED_YIELD: Instruct CUDA to yield its thread when waiting for
 * results from the GPU. This can increase latency when waiting for the GPU,
 * but can increase the performance of CPU threads performing work in parallel
 * with the GPU.
 *
 * - ::CU_CTX_SCHED_BLOCKING_SYNC: Instruct CUDA to block the CPU thread on a
 * synchronization primitive when waiting for the GPU to finish work.
 *
 * - ::CU_CTX_BLOCKING_SYNC: Instruct CUDA to block the CPU thread on a
 * synchronization primitive when waiting for the GPU to finish work. <br>
 * <b>Deprecated:</b> This flag was deprecated as of CUDA 4.0 and was
 * replaced with ::CU_CTX_SCHED_BLOCKING_SYNC.
 *
 * - ::CU_CTX_SCHED_AUTO: The default value if the \p flags parameter is zero,
 * uses a heuristic based on the number of active CUDA contexts in the
 * process \e C and the number of logical processors in the system \e P. If
 * \e C > \e P, then CUDA will yield to other OS threads when waiting for
 * the GPU (::CU_CTX_SCHED_YIELD), otherwise CUDA will not yield while
 * waiting for results and actively spin on the processor (::CU_CTX_SCHED_SPIN).
 * Additionally, on Tegra devices, ::CU_CTX_SCHED_AUTO uses a heuristic based on
 * the power profile of the platform and may choose ::CU_CTX_SCHED_BLOCKING_SYNC
 * for low-powered devices.
 *
 * - ::CU_CTX_LMEM_RESIZE_TO_MAX: Instruct CUDA to not reduce local memory
 * after resizing local memory for a kernel. This can prevent thrashing by
 * local memory allocations when launching many kernels with high local
 * memory usage at the cost of potentially increased memory usage. <br>
 * <b>Deprecated:</b> This flag is deprecated and the behavior enabled
 * by this flag is now the default and cannot be disabled.
 *
 * - ::CU_CTX_COREDUMP_ENABLE: If GPU coredumps have not been enabled globally
 * with ::cuCoredumpSetAttributeGlobal or environment variables, this flag can
 * be set during context creation to instruct CUDA to create a coredump if
 * this context raises an exception during execution. These environment variables
 * are described in the CUDA-GDB user guide under the "GPU core dump support"
 * section.
 * The initial settings will be taken from the global settings at the time of
 * context creation. The other settings that control coredump output can be 
 * modified by calling ::cuCoredumpSetAttribute from the created context after
 * it becomes current.
 *
 * - ::CU_CTX_USER_COREDUMP_ENABLE: If user-triggered GPU coredumps have not
 * been enabled globally with ::cuCoredumpSetAttributeGlobal or environment 
 * variables, this flag can be set during context creation to instruct CUDA to
 * create a coredump if data is written to a certain pipe that is present in the
 * OS space. These environment variables are described in the CUDA-GDB user
 * guide under the "GPU core dump support" section.
 * It is important to note that the pipe name *must* be set with
 * ::cuCoredumpSetAttributeGlobal before creating the context if this flag is
 * used. Setting this flag implies that ::CU_CTX_COREDUMP_ENABLE is set.
 * The initial settings will be taken from the global settings at the time of
 * context creation. The other settings that control coredump output can be 
 * modified by calling ::cuCoredumpSetAttribute from the created context after 
 * it becomes current.
 *
 * - ::CU_CTX_SYNC_MEMOPS: Ensures that synchronous memory operations initiated
 * on this context will always synchronize. See further documentation in the
 * section titled "API Synchronization behavior" to learn more about cases when
 * synchronous memory operations can exhibit asynchronous behavior.
 *
 * \param dev   - Device for which the primary context flags are set
 * \param flags - New flags for the device
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa ::cuDevicePrimaryCtxRetain,
 * ::cuDevicePrimaryCtxGetState,
 * ::cuCtxCreate,
 * ::cuCtxGetFlags,
 * ::cuCtxSetFlags,
 * ::cudaSetDeviceFlags
 */
CUresult cuDevicePrimaryCtxSetFlags_v2(CUdevice dev, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palDevicePrimaryContextCheckFlags(flags);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Invalid flags specified for primary context: 0x%x.", flags);
        return (CUresult)result;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    result = palDevicePrimaryContextSetFlags(pDevice, flags);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to set primary context flags for device ID %d: 0x%x.", dev, flags);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Get the handle of the primary context
 *
 * Returns in \p *pctx the handle of the primary context of \p dev.
 *
 * \param dev    - Device to get primary context flags for
 * \param pctx   - Pointer to context handle.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa
 * ::cuDevicePrimaryCtxRelease,
 * ::cuDevicePrimaryCtxSetFlags,
 * ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuDevicePrimaryCtxGet(CUcontext* pctx, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer.");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    *pctx = (CUcontext)palDeviceGetPrimaryContext(pDevice);

    return CUDA_SUCCESS;
}

/**
 * \brief Initialize the primary context of the corresponding device
 *
 * Initialize the handle of the primary context of \p dev.
 *
 * \param dev    - Device to get primary context flags for
 * \param pctx   - Pointer to context handle.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa
 * ::cuDevicePrimaryCtxRelease,
 * ::cuDevicePrimaryCtxRetain,
 * * ::cuDevicePrimaryCtxReset,
 * ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuDevicePrimaryCtxInit(CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    result = palDevicePrimaryContextInitialize(pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to initialize primary context for device %d.", dev);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}