#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Associate a device to a multicast object.
 *
 * Associates a device to a multicast object. The added device will be a part of
 * the multicast team of size specified by CUmulticastObjectProp::numDevices
 * during ::cuMulticastCreate.
 * The association of the device to the multicast object is permanent during
 * the life time of the multicast object.
 * All devices must be added to the multicast team before any memory can be
 * bound to any device in the team. Any calls to ::cuMulticastBindMem or
 * ::cuMulticastBindAddr will block until all devices have been added.
 * Similarly all devices must be added to the multicast team before a virtual
 * address range can be mapped to the multicast object. A call to ::cuMemMap
 * will block until all devices have been added.
 *
 * \param[in] mcHandle     Handle representing a multicast object.
 * \param[in] dev          Device that will be associated to the multicast
 *                         object.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_PERMITTED,
 * ::CUDA_ERROR_NOT_SUPPORTED
 *
 * \sa ::cuMulticastCreate, ::cuMulticastBindMem, ::cuMulticastBindAddr
 */
CUresult cuMulticastAddDevice(CUmemGenericAllocationHandle mcHandle, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Bind a memory allocation represented by a virtual address to a multicast object.
 *
 * Binds a memory allocation specified by its mapped address \p memptr to a
 * multicast object represented by \p mcHandle.
 * The memory must have been allocated via ::cuMemCreate or ::cudaMallocAsync.
 * The intended \p size of the bind, the offset in the multicast range
 * \p mcOffset and \p memptr must be a multiple of the value returned by
 * ::cuMulticastGetGranularity with the flag ::CU_MULTICAST_GRANULARITY_MINIMUM.
 * For best performance however, \p size, \p mcOffset and \p memptr should be
 * aligned to the value returned by ::cuMulticastGetGranularity with the flag
 * ::CU_MULTICAST_GRANULARITY_RECOMMENDED.
 *
 * The \p size must be smaller than the size of the allocated memory.
 * Similarly the \p size + \p mcOffset must be smaller than the total size
 * of the multicast object.
 * The memory allocation must have beeen created on one of the devices
 * that was added to the multicast team via ::cuMulticastAddDevice.
 * Externally shareable as well as imported multicast objects can be bound only
 * to externally shareable memory.
 * Note that this call will return CUDA_ERROR_OUT_OF_MEMORY if there are
 * insufficient resources required to perform the bind. This call may also
 * return CUDA_ERROR_SYSTEM_NOT_READY if the necessary system software is not
 * initialized or running.
 *
 * \param[in]  mcHandle     Handle representing a multicast object.
 * \param[in]  mcOffset     Offset into multicast va range for attachment.
 * \param[in]  memptr       Virtual address of the memory allocation.
 * \param[in]  size         Size of memory that will be bound to the
 *                          multicast object.
 * \param[in]  flags        Flags for future use, must be zero now.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_PERMITTED,
 * ::CUDA_ERROR_NOT_SUPPORTED,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_SYSTEM_NOT_READY
 *
 * \sa ::cuMulticastCreate, ::cuMulticastAddDevice, ::cuMemCreate
 */
CUresult cuMulticastBindAddr(CUmemGenericAllocationHandle mcHandle, size_t mcOffset, CUdeviceptr memptr, size_t size, unsigned long long flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Bind a memory allocation represented by a handle to a multicast object.
 *
 * Binds a memory allocation specified by \p memHandle and created via
 * ::cuMemCreate to a multicast object represented by \p mcHandle and created
 * via ::cuMulticastCreate. The intended \p size of the bind, the offset in the
 * multicast range \p mcOffset as well as the offset in the memory \p memOffset
 * must be a multiple of the value returned by ::cuMulticastGetGranularity with
 * the flag ::CU_MULTICAST_GRANULARITY_MINIMUM. For best performance however,
 * \p size, \p mcOffset and \p memOffset should be aligned to the granularity of
 * the memory allocation(see ::cuMemGetAllocationGranularity) or to the value
 * returned by ::cuMulticastGetGranularity with the flag
 * ::CU_MULTICAST_GRANULARITY_RECOMMENDED.
 *
 * The \p size + \p memOffset must be smaller than the size of the allocated
 * memory. Similarly the \p size + \p mcOffset must be smaller than the size
 * of the multicast object.
 * The memory allocation must have beeen created on one of the devices
 * that was added to the multicast team via ::cuMulticastAddDevice.
 * Externally shareable as well as imported multicast objects can be bound only
 * to externally shareable memory.
 * Note that this call will return CUDA_ERROR_OUT_OF_MEMORY if there are
 * insufficient resources required to perform the bind. This call may also
 * return CUDA_ERROR_SYSTEM_NOT_READY if the necessary system software is not
 * initialized or running.
 *
 * \param[in]  mcHandle     Handle representing a multicast object.
 * \param[in]  mcOffset     Offset into the multicast object for attachment.
 * \param[in]  memHandle    Handle representing a memory allocation.
 * \param[in]  memOffset    Offset into the memory for attachment.
 * \param[in]  size         Size of the memory that will be bound to the
 *                          multicast object.
 * \param[in]  flags        Flags for future use, must be zero for now.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_PERMITTED,
 * ::CUDA_ERROR_NOT_SUPPORTED,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_SYSTEM_NOT_READY
 *
 * \sa ::cuMulticastCreate, ::cuMulticastAddDevice, ::cuMemCreate
 */
CUresult cuMulticastBindMem(CUmemGenericAllocationHandle mcHandle, size_t mcOffset, CUmemGenericAllocationHandle memHandle, size_t memOffset, size_t size, unsigned long long flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Create a generic allocation handle representing a multicast object described by the given properties.
 *
 * This creates a multicast object as described by \p prop. The number of
 * participating devices is specified by ::CUmulticastObjectProp::numDevices.
 * Devices can be added to the multicast object via ::cuMulticastAddDevice.
 * All participating devices must be added to the multicast object before memory
 * can be bound to it. Memory is bound to the multicast object via either
 * ::cuMulticastBindMem or ::cuMulticastBindAddr, and can be unbound via
 * ::cuMulticastUnbind. The total amount of memory that can be bound per device
 * is specified by :CUmulticastObjectProp::size. This size must be a multiple of
 * the value returned by ::cuMulticastGetGranularity with the flag
 * ::CU_MULTICAST_GRANULARITY_MINIMUM. For best performance however, the size
 * should be aligned to the value returned by ::cuMulticastGetGranularity with
 * the flag ::CU_MULTICAST_GRANULARITY_RECOMMENDED.
 *
 * After all participating devices have been added, multicast objects can also
 * be mapped to a device's virtual address space using the virtual memory
 * management APIs (see ::cuMemMap and ::cuMemSetAccess). Multicast objects can
 * also be shared with other processes by requesting a shareable handle via
 * ::cuMemExportToShareableHandle. Note that the desired types of shareable
 * handles must be specified in the bitmask ::CUmulticastObjectProp::handleTypes.
 * Multicast objects can be released using the virtual memory management API
 * ::cuMemRelease.
 *
 * \param[out] mcHandle     Value of handle returned.
 * \param[in]  prop         Properties of the multicast object to create.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_PERMITTED,
 * ::CUDA_ERROR_NOT_SUPPORTED
 *
 * \sa ::cuMulticastAddDevice, ::cuMulticastBindMem, ::cuMulticastBindAddr, ::cuMulticastUnbind
 * \sa ::cuMemCreate, ::cuMemRelease, ::cuMemExportToShareableHandle, ::cuMemImportFromShareableHandle
 */
CUresult CUDAAPI cuMulticastCreate(CUmemGenericAllocationHandle *mcHandle, const CUmulticastObjectProp *prop)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Calculates either the minimal or recommended granularity for multicast object
*
* Calculates either the minimal or recommended granularity for a given set of
* multicast object properties and returns it in granularity.  This granularity
* can be used as a multiple for size, bind offsets and address mappings of the
* multicast object.
*
* \param[out] granularity Returned granularity.
* \param[in]  prop        Properties of the multicast object.
* \param[in]  option      Determines which granularity to return.
*
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMulticastCreate, ::cuMulticastBindMem, ::cuMulticastBindAddr, ::cuMulticastUnbind
*/
CUresult cuMulticastGetGranularity(size_t *granularity, const CUmulticastObjectProp *prop, CUmulticastGranularity_flags option)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Unbind any memory allocations bound to a multicast object at a given offset and upto a given size.
 *
 * Unbinds any memory allocations hosted on \p dev and bound to a multicast
 * object at \p mcOffset and upto a given \p size.
 * The intended \p size of the unbind and the offset in the multicast range
 * ( \p mcOffset ) must be a multiple of the value returned by
 * ::cuMulticastGetGranularity flag ::CU_MULTICAST_GRANULARITY_MINIMUM.
 * The \p size + \p mcOffset must be smaller than the total size of the
 * multicast object.
 *
 * \note
 * Warning:
 * The \p mcOffset and the \p size must match the corresponding values specified
 * during the bind call. Any other values may result in undefined behavior.
 *
 * \param[in]  mcHandle     Handle representing a multicast object.
 * \param[in]  dev          Device that hosts the memory allocation.
 * \param[in]  mcOffset     Offset into the multicast object.
 * \param[in]  size         Desired size to unbind.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_PERMITTED,
 * ::CUDA_ERROR_NOT_SUPPORTED
 *
 * \sa ::cuMulticastBindMem, ::cuMulticastBindAddr
 */
CUresult cuMulticastUnbind(CUmemGenericAllocationHandle mcHandle, CUdevice dev, size_t mcOffset, size_t size)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}