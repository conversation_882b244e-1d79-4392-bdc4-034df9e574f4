#include "cu_init.h"

/**
 * \brief Returns the requested driver API function pointer
 *
 * Returns in \p **pfn the address of the CUDA driver function for the requested
 * CUDA version and flags.
 *
 * The CUDA version is specified as (1000 * major + 10 * minor), so CUDA 11.2
 * should be specified as 11020. For a requested driver symbol, if the specified
 * CUDA version is greater than or equal to the CUDA version in which the driver symbol
 * was introduced, this API will return the function pointer to the corresponding
 * versioned function.
 *
 * The pointer returned by the API should be cast to a function pointer matching the
 * requested driver function's definition in the API header file. The function pointer
 * typedef can be picked up from the corresponding typedefs header file. For example,
 * cudaTypedefs.h consists of function pointer typedefs for driver APIs defined in cuda.h.
 *
 * The API will return ::CUDA_SUCCESS and set the returned \p pfn to NULL if the
 * requested driver function is not supported on the platform, no ABI
 * compatible driver function exists for the specified \p cudaVersion or if the
 * driver symbol is invalid.
 *
 * It will also set the optional \p symbolStatus to one of the values in
 * ::CUdriverProcAddressQueryResult with the following meanings:
 * - ::CU_GET_PROC_ADDRESS_SUCCESS - The requested symbol was succesfully found based
 *   on input arguments and \p pfn is valid
 * - ::CU_GET_PROC_ADDRESS_SYMBOL_NOT_FOUND - The requested symbol was not found
 * - ::CU_GET_PROC_ADDRESS_VERSION_NOT_SUFFICIENT - The requested symbol was found but is
 *   not supported by cudaVersion specified
 *
 * The requested flags can be:
 * - ::CU_GET_PROC_ADDRESS_DEFAULT: This is the default mode. This is equivalent to
 *   ::CU_GET_PROC_ADDRESS_PER_THREAD_DEFAULT_STREAM if the code is compiled with
 *   --default-stream per-thread compilation flag or the macro CUDA_API_PER_THREAD_DEFAULT_STREAM
 *   is defined; ::CU_GET_PROC_ADDRESS_LEGACY_STREAM otherwise.
 * - ::CU_GET_PROC_ADDRESS_LEGACY_STREAM: This will enable the search for all driver symbols
 *   that match the requested driver symbol name except the corresponding per-thread versions.
 * - ::CU_GET_PROC_ADDRESS_PER_THREAD_DEFAULT_STREAM: This will enable the search for all
 *   driver symbols that match the requested driver symbol name including the per-thread
 *   versions. If a per-thread version is not found, the API will return the legacy version
 *   of the driver function.
 *
 * \param symbol - The base name of the driver API function to look for. As an example,
 *                 for the driver API ::cuMemAlloc_v2, \p symbol would be cuMemAlloc and
 *                 \p cudaVersion would be the ABI compatible CUDA version for the _v2 variant.
 * \param pfn - Location to return the function pointer to the requested driver function
 * \param cudaVersion - The CUDA version to look for the requested driver symbol
 * \param flags -  Flags to specify search options.
 * \param symbolStatus - Optional location to store the status of the search for
 *                       \p symbol based on \p cudaVersion. See ::CUdriverProcAddressQueryResult
 *                       for possible values.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \note_version_mixing
 *
 * \sa
 * ::cudaGetDriverEntryPoint
 */
CUresult cuGetProcAddress_v2(const char *symbol, void **pfn, int cudaVersion, cuuint64_t flags, CUdriverProcAddressQueryResult *symbolStatus)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns the driver API export table
 *
 * \param ppExportTable - Location to return the function pointer to the requested driver function
 * \param pExportTableId - The UUID of the requested driver function
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \note_version_mixing
 */
CUresult cuGetExportTable(const void **ppExportTable, const CUuuid *pExportTableId)
{
    /* {00508eae-a163-48bc-b0ac-256c4364c56b} */
    const CUuuid cudaDriverFunctionExportTableUuid = {
        .bytes = {'\x00', '\x50', '\x8e', '\xae', '\xa1', '\x63', '\x48', '\xbc',
                  '\xb0', '\xac', '\x25', '\x6c', '\x43', '\x64', '\xc5', '\x6b' } };

    if ((ppExportTable == NULL) || (pExportTableId == NULL))
    {
        return CUDA_ERROR_INVALID_VALUE;
    }

    if (memcmp(pExportTableId, &cudaDriverFunctionExportTableUuid, sizeof(CUuuid)) == 0)
    {
        *ppExportTable = (const void *)&g_driverExportTable;
        return CUDA_SUCCESS;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}