#include "cuda.h"

/**
 * \brief Returns the latest CUDA version supported by driver
 *
 * Returns in \p *driverVersion the version of CUDA supported by
 * the driver.  The version is returned as
 * (1000 &times; major + 10 &times; minor). For example, CUDA 9.2
 * would be represented by 9020.
 *
 * This function automatically returns ::CUDA_ERROR_INVALID_VALUE if
 * \p driverVersion is NULL.
 *
 * \param driverVersion - Returns the CUDA driver version
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa
 * ::cudaDriverGetVersion,
 * ::cudaRuntimeGetVersion
 */
CUresult cuDriverGetVersion(int *driverVersion)
{
    if (driverVersion == NULL)
    {
        return CUDA_ERROR_INVALID_VALUE;
    }

    *driverVersion = CUDA_VERSION;

    return CUDA_SUCCESS;
}