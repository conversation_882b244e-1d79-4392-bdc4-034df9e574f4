#include "cu_init.h"
#include "pal_context.h"
#include "pal_devicemanager.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Create a CUDA context
 *
 * \note In most cases it is recommended to use ::cuDevicePrimaryCtxRetain.
 *
 * Creates a new CUDA context and associates it with the calling thread. The
 * \p flags parameter is described below. The context is created with a usage
 * count of 1 and the caller of ::cuCtxCreate() must call ::cuCtxDestroy()
 * when done using the context. If a context is already current to the thread,
 * it is supplanted by the newly created context and may be restored by a subsequent
 * call to ::cuCtxPopCurrent().
 *
 * The three LSBs of the \p flags parameter can be used to control how the OS
 * thread, which owns the CUDA context at the time of an API call, interacts
 * with the OS scheduler when waiting for results from the GPU. Only one of
 * the scheduling flags can be set when creating a context.
 *
 * - ::CU_CTX_SCHED_SPIN: Instruct CUDA to actively spin when waiting for
 * results from the GPU. This can decrease latency when waiting for the GPU,
 * but may lower the performance of CPU threads if they are performing work in
 * parallel with the CUDA thread.
 *
 * - ::CU_CTX_SCHED_YIELD: Instruct CUDA to yield its thread when waiting for
 * results from the GPU. This can increase latency when waiting for the GPU,
 * but can increase the performance of CPU threads performing work in parallel
 * with the GPU.
 *
 * - ::CU_CTX_SCHED_BLOCKING_SYNC: Instruct CUDA to block the CPU thread on a
 * synchronization primitive when waiting for the GPU to finish work.
 *
 * - ::CU_CTX_BLOCKING_SYNC: Instruct CUDA to block the CPU thread on a
 * synchronization primitive when waiting for the GPU to finish work. <br>
 * <b>Deprecated:</b> This flag was deprecated as of CUDA 4.0 and was
 * replaced with ::CU_CTX_SCHED_BLOCKING_SYNC.
 *
 * - ::CU_CTX_SCHED_AUTO: The default value if the \p flags parameter is zero,
 * uses a heuristic based on the number of active CUDA contexts in the
 * process \e C and the number of logical processors in the system \e P. If
 * \e C > \e P, then CUDA will yield to other OS threads when waiting for
 * the GPU (::CU_CTX_SCHED_YIELD), otherwise CUDA will not yield while
 * waiting for results and actively spin on the processor (::CU_CTX_SCHED_SPIN).
 * Additionally, on Tegra devices, ::CU_CTX_SCHED_AUTO uses a heuristic based on
 * the power profile of the platform and may choose ::CU_CTX_SCHED_BLOCKING_SYNC
 * for low-powered devices.
 *
 * - ::CU_CTX_MAP_HOST: Instruct CUDA to support mapped pinned allocations.
 * This flag must be set in order to allocate pinned host memory that is
 * accessible to the GPU.
 *
 * - ::CU_CTX_LMEM_RESIZE_TO_MAX: Instruct CUDA to not reduce local memory
 * after resizing local memory for a kernel. This can prevent thrashing by
 * local memory allocations when launching many kernels with high local
 * memory usage at the cost of potentially increased memory usage. <br>
 * <b>Deprecated:</b> This flag is deprecated and the behavior enabled
 * by this flag is now the default and cannot be disabled.
 * Instead, the per-thread stack size can be controlled with ::cuCtxSetLimit().
 *
 * - ::CU_CTX_COREDUMP_ENABLE: If GPU coredumps have not been enabled globally
 * with ::cuCoredumpSetAttributeGlobal or environment variables, this flag can
 * be set during context creation to instruct CUDA to create a coredump if
 * this context raises an exception during execution. These environment variables
 * are described in the CUDA-GDB user guide under the "GPU core dump support"
 * section.
 * The initial attributes will be taken from the global attributes at the time of
 * context creation. The other attributes that control coredump output can be
 * modified by calling ::cuCoredumpSetAttribute from the created context after
 * it becomes current.
 *
 * - ::CU_CTX_USER_COREDUMP_ENABLE: If user-triggered GPU coredumps have not
 * been enabled globally with ::cuCoredumpSetAttributeGlobal or environment
 * variables, this flag can be set during context creation to instruct CUDA to
 * create a coredump if data is written to a certain pipe that is present in the
 * OS space. These environment variables are described in the CUDA-GDB user
 * guide under the "GPU core dump support" section.
 * It is important to note that the pipe name *must* be set with
 * ::cuCoredumpSetAttributeGlobal before creating the context if this flag is
 * used. Setting this flag implies that ::CU_CTX_COREDUMP_ENABLE is set.
 * The initial attributes will be taken from the global attributes at the time of
 * context creation. The other attributes that control coredump output can be
 * modified by calling ::cuCoredumpSetAttribute from the created context after
 * it becomes current.
 * Setting this flag on any context creation is equivalent to setting the
 * ::CU_COREDUMP_ENABLE_USER_TRIGGER attribute to \p true globally.
 *
 * - ::CU_CTX_SYNC_MEMOPS: Ensures that synchronous memory operations initiated
 * on this context will always synchronize. See further documentation in the
 * section titled "API Synchronization behavior" to learn more about cases when
 * synchronous memory operations can exhibit asynchronous behavior.
 *
 * Context creation will fail with ::CUDA_ERROR_UNKNOWN if the compute mode of
 * the device is ::CU_COMPUTEMODE_PROHIBITED. The function ::cuDeviceGetAttribute()
 * can be used with ::CU_DEVICE_ATTRIBUTE_COMPUTE_MODE to determine the
 * compute mode of the device. The <i>nvidia-smi</i> tool can be used to set
 * the compute mode for * devices.
 * Documentation for <i>nvidia-smi</i> can be obtained by passing a
 * -h option to it.
 *
 * \param pctx  - Returned context handle of the new context
 * \param flags - Context creation flags
 * \param dev   - Device to create context on
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCoredumpSetAttributeGlobal,
 * ::cuCoredumpSetAttribute,
 * ::cuCtxSynchronize
 */
CUresult cuCtxCreate_v2(CUcontext *pctx, unsigned int flags, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    palContextCreateInfo createInfo = {0};
    createInfo.isPrimary            = PAL_FALSE;
    createInfo.flags                = flags;
    createInfo.pExecAffinityParams  = NULL;
    createInfo.numParams            = 0;
    result = palContextCreate(&pCurrentCtx, pDevice, &createInfo);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to create context: %d", result);
        return (CUresult)result;
    }

    result = palTlsThreadDataPushCurrentContext(pTlsThreadData, pCurrentCtx);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to push context onto thread data stack: %d", result);
        palContextDestroy(pCurrentCtx);
        return (CUresult)result;
    }

    *pctx = (CUcontext)pCurrentCtx;

    return CUDA_SUCCESS;
}

/**
 * \brief Create a CUDA context with execution affinity
 *
 * Creates a new CUDA context with execution affinity and associates it with
 * the calling thread. The \p paramsArray and \p flags parameter are described below.
 * The context is created with a usage count of 1 and the caller of ::cuCtxCreate() must
 * call ::cuCtxDestroy() when done using the context. If a context is already
 * current to the thread, it is supplanted by the newly created context and may
 * be restored by a subsequent call to ::cuCtxPopCurrent().
 *
 * The type and the amount of execution resource the context can use is limited by \p paramsArray
 * and \p numParams. The \p paramsArray is an array of \p CUexecAffinityParam and the \p numParams
 * describes the size of the array. If two \p CUexecAffinityParam in the array have the same type,
 * the latter execution affinity parameter overrides the former execution affinity parameter.
 * The supported execution affinity types are:
 * - ::CU_EXEC_AFFINITY_TYPE_SM_COUNT limits the portion of SMs that the context can use. The portion
 *   of SMs is specified as the number of SMs via \p CUexecAffinitySmCount. This limit will be internally
 *   rounded up to the next hardware-supported amount. Hence, it is imperative to query the actual execution
 *   affinity of the context via \p cuCtxGetExecAffinity after context creation. Currently, this attribute
 *   is only supported under Volta+ MPS.
 *
 * The three LSBs of the \p flags parameter can be used to control how the OS
 * thread, which owns the CUDA context at the time of an API call, interacts
 * with the OS scheduler when waiting for results from the GPU. Only one of
 * the scheduling flags can be set when creating a context.
 *
 * - ::CU_CTX_SCHED_SPIN: Instruct CUDA to actively spin when waiting for
 * results from the GPU. This can decrease latency when waiting for the GPU,
 * but may lower the performance of CPU threads if they are performing work in
 * parallel with the CUDA thread.
 *
 * - ::CU_CTX_SCHED_YIELD: Instruct CUDA to yield its thread when waiting for
 * results from the GPU. This can increase latency when waiting for the GPU,
 * but can increase the performance of CPU threads performing work in parallel
 * with the GPU.
 *
 * - ::CU_CTX_SCHED_BLOCKING_SYNC: Instruct CUDA to block the CPU thread on a
 * synchronization primitive when waiting for the GPU to finish work.
 *
 * - ::CU_CTX_BLOCKING_SYNC: Instruct CUDA to block the CPU thread on a
 * synchronization primitive when waiting for the GPU to finish work. <br>
 * <b>Deprecated:</b> This flag was deprecated as of CUDA 4.0 and was
 * replaced with ::CU_CTX_SCHED_BLOCKING_SYNC.
 *
 * - ::CU_CTX_SCHED_AUTO: The default value if the \p flags parameter is zero,
 * uses a heuristic based on the number of active CUDA contexts in the
 * process \e C and the number of logical processors in the system \e P. If
 * \e C > \e P, then CUDA will yield to other OS threads when waiting for
 * the GPU (::CU_CTX_SCHED_YIELD), otherwise CUDA will not yield while
 * waiting for results and actively spin on the processor (::CU_CTX_SCHED_SPIN).
 * Additionally, on Tegra devices, ::CU_CTX_SCHED_AUTO uses a heuristic based on
 * the power profile of the platform and may choose ::CU_CTX_SCHED_BLOCKING_SYNC
 * for low-powered devices.
 *
 * - ::CU_CTX_MAP_HOST: Instruct CUDA to support mapped pinned allocations.
 * This flag must be set in order to allocate pinned host memory that is
 * accessible to the GPU.
 *
 * - ::CU_CTX_LMEM_RESIZE_TO_MAX: Instruct CUDA to not reduce local memory
 * after resizing local memory for a kernel. This can prevent thrashing by
 * local memory allocations when launching many kernels with high local
 * memory usage at the cost of potentially increased memory usage. <br>
 * <b>Deprecated:</b> This flag is deprecated and the behavior enabled
 * by this flag is now the default and cannot be disabled.
 * Instead, the per-thread stack size can be controlled with ::cuCtxSetLimit().
 *
 * - ::CU_CTX_COREDUMP_ENABLE: If GPU coredumps have not been enabled globally
 * with ::cuCoredumpSetAttributeGlobal or environment variables, this flag can
 * be set during context creation to instruct CUDA to create a coredump if
 * this context raises an exception during execution. These environment variables
 * are described in the CUDA-GDB user guide under the "GPU core dump support"
 * section.
 * The initial attributes will be taken from the global attributes at the time of
 * context creation. The other attributes that control coredump output can be
 * modified by calling ::cuCoredumpSetAttribute from the created context after
 * it becomes current.
 *
 * - ::CU_CTX_USER_COREDUMP_ENABLE: If user-triggered GPU coredumps have not
 * been enabled globally with ::cuCoredumpSetAttributeGlobal or environment
 * variables, this flag can be set during context creation to instruct CUDA to
 * create a coredump if data is written to a certain pipe that is present in the
 * OS space. These environment variables are described in the CUDA-GDB user
 * guide under the "GPU core dump support" section.
 * It is important to note that the pipe name *must* be set with
 * ::cuCoredumpSetAttributeGlobal before creating the context if this flag is
 * used. Setting this flag implies that ::CU_CTX_COREDUMP_ENABLE is set.
 * The initial attributes will be taken from the global attributes at the time of
 * context creation. The other attributes that control coredump output can be
 * modified by calling ::cuCoredumpSetAttribute from the created context after
 * it becomes current.
 * Setting this flag on any context creation is equivalent to setting the
 * ::CU_COREDUMP_ENABLE_USER_TRIGGER attribute to \p true globally.
 *
 * Context creation will fail with ::CUDA_ERROR_UNKNOWN if the compute mode of
 * the device is ::CU_COMPUTEMODE_PROHIBITED. The function ::cuDeviceGetAttribute()
 * can be used with ::CU_DEVICE_ATTRIBUTE_COMPUTE_MODE to determine the
 * compute mode of the device. The <i>nvidia-smi</i> tool can be used to set
 * the compute mode for * devices.
 * Documentation for <i>nvidia-smi</i> can be obtained by passing a
 * -h option to it.
 *
 * \param pctx        - Returned context handle of the new context
 * \param paramsArray - Execution affinity parameters
 * \param numParams   - Number of execution affinity parameters
 * \param flags       - Context creation flags
 * \param dev         - Device to create context on
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_UNSUPPORTED_EXEC_AFFINITY,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cuCoredumpSetAttributeGlobal,
 * ::cuCoredumpSetAttribute,
 * ::CUexecAffinityParam
 */
CUresult cuCtxCreate_v3(CUcontext *pctx, CUexecAffinityParam *paramsArray, int numParams, unsigned int flags, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;
    palDevice*        pDevice        = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palDeviceManagerGetDevice(g_globalManager.pDeviceManager, dev, &pDevice);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get device with ordinal %d.", dev);
        return (CUresult)result;
    }

    palContextCreateInfo createInfo = {0};
    createInfo.isPrimary            = PAL_FALSE;
    createInfo.flags                = flags;
    createInfo.pExecAffinityParams  = (palExecAffinityParam*)paramsArray;
    createInfo.numParams            = numParams;
    result = palContextCreate(&pCurrentCtx, pDevice, &createInfo);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to create context: %d", result);
        return (CUresult)result;
    }

    result = palTlsThreadDataPushCurrentContext(pTlsThreadData, pCurrentCtx);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to push context onto thread data stack: %d", result);
        palContextDestroy(pCurrentCtx);
        return (CUresult)result;
    }

    *pctx = (CUcontext)pCurrentCtx;

    return CUDA_SUCCESS;
}

/**
 * \brief Destroy a CUDA context
 *
 * Destroys the CUDA context specified by \p ctx.  The context \p ctx will be
 * destroyed regardless of how many threads it is current to.
 * It is the responsibility of the calling function to ensure that no API
 * call issues using \p ctx while ::cuCtxDestroy() is executing.
 *
 * Destroys and cleans up all resources associated with the context.
 * It is the caller's responsibility to ensure that the context or its resources
 * are not accessed or passed in subsequent API calls and doing so will result in undefined behavior.
 * These resources include CUDA types such as ::CUmodule, ::CUfunction, ::CUstream, ::CUevent,
 * ::CUarray, ::CUmipmappedArray, ::CUtexObject, ::CUsurfObject, ::CUtexref, ::CUsurfref,
 * ::CUgraphicsResource, ::CUlinkState, ::CUexternalMemory and ::CUexternalSemaphore.
 *
 * If \p ctx is current to the calling thread then \p ctx will also be
 * popped from the current thread's context stack (as though ::cuCtxPopCurrent()
 * were called).  If \p ctx is current to other threads, then \p ctx will
 * remain current to those threads, and attempting to access \p ctx from
 * those threads will result in the error ::CUDA_ERROR_CONTEXT_IS_DESTROYED.
 *
 * \param ctx - Context to destroy
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuCtxDestroy_v2(CUcontext ctx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pDestroyedCtx  = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (ctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    pDestroyedCtx = (palContext*)ctx;
    if (pDestroyedCtx->persistentState.isPrimary == PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Cannot destroy primary context.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pDestroyedCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    // Get the current context for the calling thread.
    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);

    // Destroy the context.
    palContextDestroy(pDestroyedCtx);

    // If the context is current to the calling thread, pop it
    if (pCurrentCtx == pDestroyedCtx)
    {
        // If the destroyed context was current to the calling thread, pop it from the stack.
        result = palTlsThreadDataPopCurrentContext(pTlsThreadData);
        if (result != PAL_SUCCESS)
        {
            CU_DBG_PRINTF_ERROR("Failed to pop current context.");
            return (CUresult)result;
        }
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Gets the context's API version.
 *
 * Returns a version number in \p version corresponding to the capabilities of
 * the context (e.g. 3010 or 3020), which library developers can use to direct
 * callers to a specific API version. If \p ctx is NULL, returns the API version
 * used to create the currently bound context.
 *
 * Note that new API versions are only introduced when context capabilities are
 * changed that break binary compatibility, so the API version and driver version
 * may be different. For example, it is valid for the API version to be 3020 while
 * the driver version is 4020.
 *
 * \param ctx     - Context to check
 * \param version - Pointer to version
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNKNOWN
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuCtxGetApiVersion(CUcontext ctx, unsigned int *version)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (version == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if (ctx == NULL)
    {
        pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
        if (pCurrentCtx == NULL)
        {
            CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
            return CUDA_ERROR_INVALID_CONTEXT;
        }
        else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
        {
            CU_DBG_PRINTF_ERROR("Context is destroyed.");
            return CUDA_ERROR_CONTEXT_IS_DESTROYED;
        }
    }
    else
    {
        pCurrentCtx = (palContext*)ctx;
        if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
        {
            CU_DBG_PRINTF_ERROR("Context is destroyed.");
            return CUDA_ERROR_CONTEXT_IS_DESTROYED;
        }
    }


    *version = (unsigned int)palContextGetApiVersion(pCurrentCtx);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the preferred cache configuration for the current context.
 *
 * On devices where the L1 cache and shared memory use the same hardware
 * resources, this function returns through \p pconfig the preferred cache configuration
 * for the current context. This is only a preference. The driver will use
 * the requested configuration if possible, but it is free to choose a different
 * configuration if required to execute functions.
 *
 * This will return a \p pconfig of ::CU_FUNC_CACHE_PREFER_NONE on devices
 * where the size of the L1 cache and shared memory are fixed.
 *
 * The supported cache configurations are:
 * - ::CU_FUNC_CACHE_PREFER_NONE: no preference for shared memory or L1 (default)
 * - ::CU_FUNC_CACHE_PREFER_SHARED: prefer larger shared memory and smaller L1 cache
 * - ::CU_FUNC_CACHE_PREFER_L1: prefer larger L1 cache and smaller shared memory
 * - ::CU_FUNC_CACHE_PREFER_EQUAL: prefer equal sized L1 cache and shared memory
 *
 * \param pconfig - Returned cache configuration
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cuFuncSetCacheConfig,
 * ::cudaDeviceGetCacheConfig
 */
CUresult cuCtxGetCacheConfig(CUfunc_cache *pconfig)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (pconfig == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    *pconfig = (CUfunc_cache)palContextGetCacheConfig(pCurrentCtx);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the CUDA context bound to the calling CPU thread.
 *
 * Returns in \p *pctx the CUDA context bound to the calling CPU thread.
 * If no context is bound to the calling CPU thread then \p *pctx is
 * set to NULL and ::CUDA_SUCCESS is returned.
 *
 * \param pctx - Returned context handle
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * \notefnerr
 *
 * \sa
 * ::cuCtxSetCurrent,
 * ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cudaGetDevice
 */
CUresult cuCtxGetCurrent(CUcontext *pctx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (pctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    *pctx = (CUcontext)(void*)palTlsThreadDataGetCurrentContext(pTlsThreadData);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the device ID for the current context
 *
 * Returns in \p *device the ordinal of the current context's device.
 *
 * \param device - Returned device ID for the current context
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cudaGetDevice
 */
CUresult cuCtxGetDevice(CUdevice *device)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (device == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    *device = palContextGetDeviceId(pCurrentCtx);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the execution affinity setting for the current context.
 *
 * Returns in \p *pExecAffinity the current value of \p type. The supported
 * ::CUexecAffinityType values are:
 * - ::CU_EXEC_AFFINITY_TYPE_SM_COUNT: number of SMs the context is limited to use.
 *
 * \param type          - Execution affinity type to query
 * \param pExecAffinity - Returned execution affinity
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNSUPPORTED_EXEC_AFFINITY
 * \notefnerr
 *
 * \sa
 * ::CUexecAffinityParam
 */
CUresult cuCtxGetExecAffinity(CUexecAffinityParam *pExecAffinity, CUexecAffinityType type)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextGetExecAffinity(pCurrentCtx, (palExecAffinityType)type, (palExecAffinityParam*)pExecAffinity);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get execution affinity parameters");
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the flags for the current context
 *
 * Returns in \p *flags the flags of the current context. See ::cuCtxCreate
 * for flag values.
 *
 * \param flags - Pointer to store flags of current context
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetCurrent,
 * ::cuCtxGetDevice,
 * ::cuCtxGetLimit,
 * ::cuCtxGetSharedMemConfig,
 * ::cuCtxGetStreamPriorityRange,
 * ::cuCtxSetFlags,
 * ::cudaGetDeviceFlags
 */
CUresult CUDAAPI cuCtxGetFlags(unsigned int *flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (flags == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    *flags = palContextGetFlags(pCurrentCtx);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the unique Id associated with the context supplied
 *
 * Returns in \p ctxId the unique Id which is associated with a given context.
 * The Id is unique for the life of the program for this instance of CUDA.
 * If context is supplied as NULL and there is one current, the Id of the
 * current context is returned.
 *
 * \param ctx - Context for which to obtain the Id
 * \param ctxId - Pointer to store the Id of the context
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_CONTEXT_IS_DESTROYED,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPushCurrent
 */
CUresult cuCtxGetId(CUcontext ctx, unsigned long long *ctxId)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (ctx == NULL)
    {
        pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
        if (pCurrentCtx == NULL)
        {
            CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
            return CUDA_ERROR_INVALID_CONTEXT;
        }
        else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
        {
            CU_DBG_PRINTF_ERROR("Context is destroyed.");
            return CUDA_ERROR_CONTEXT_IS_DESTROYED;
        }
    }
    else
    {
        pCurrentCtx = (palContext*)ctx;
        if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
        {
            CU_DBG_PRINTF_ERROR("Context is destroyed.");
            return CUDA_ERROR_CONTEXT_IS_DESTROYED;
        }
    }

    if (ctxId == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    *ctxId = palContextGetContextId(pCurrentCtx);

    return CUDA_SUCCESS;
}

/**
 * \brief Returns resource limits
 *
 * Returns in \p *pvalue the current size of \p limit.  The supported
 * ::CUlimit values are:
 * - ::CU_LIMIT_STACK_SIZE: stack size in bytes of each GPU thread.
 * - ::CU_LIMIT_PRINTF_FIFO_SIZE: size in bytes of the FIFO used by the
 *   ::printf() device system call.
 * - ::CU_LIMIT_MALLOC_HEAP_SIZE: size in bytes of the heap used by the
 *   ::malloc() and ::free() device system calls.
 * - ::CU_LIMIT_DEV_RUNTIME_SYNC_DEPTH: maximum grid depth at which a thread
 *   can issue the device runtime call ::cudaDeviceSynchronize() to wait on
 *   child grid launches to complete.
 * - ::CU_LIMIT_DEV_RUNTIME_PENDING_LAUNCH_COUNT: maximum number of outstanding
 *   device runtime launches that can be made from this context.
 * - ::CU_LIMIT_MAX_L2_FETCH_GRANULARITY: L2 cache fetch granularity.
 * - ::CU_LIMIT_PERSISTING_L2_CACHE_SIZE: Persisting L2 cache size in bytes
 *
 * \param limit  - Limit to query
 * \param pvalue - Returned size of limit
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNSUPPORTED_LIMIT
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cudaDeviceGetLimit
 */
CUresult cuCtxGetLimit(size_t *pvalue, CUlimit limit)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextGetLimit(pCurrentCtx, (palCtxLimit)limit, (palUint64*)pvalue);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get limit: %d", result);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns numerical values that correspond to the least and
 * greatest stream priorities.
 *
 * Returns in \p *leastPriority and \p *greatestPriority the numerical values that correspond
 * to the least and greatest stream priorities respectively. Stream priorities
 * follow a convention where lower numbers imply greater priorities. The range of
 * meaningful stream priorities is given by [\p *greatestPriority, \p *leastPriority].
 * If the user attempts to create a stream with a priority value that is
 * outside the meaningful range as specified by this API, the priority is
 * automatically clamped down or up to either \p *leastPriority or \p *greatestPriority
 * respectively. See ::cuStreamCreateWithPriority for details on creating a
 * priority stream.
 * A NULL may be passed in for \p *leastPriority or \p *greatestPriority if the value
 * is not desired.
 *
 * This function will return '0' in both \p *leastPriority and \p *greatestPriority if
 * the current context's device does not support stream priorities
 * (see ::cuDeviceGetAttribute).
 *
 * \param leastPriority    - Pointer to an int in which the numerical value for least
 *                           stream priority is returned
 * \param greatestPriority - Pointer to an int in which the numerical value for greatest
 *                           stream priority is returned
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa ::cuStreamCreateWithPriority,
 * ::cuStreamGetPriority,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cudaDeviceGetStreamPriorityRange
 */
CUresult cuCtxGetStreamPriorityRange(int *leastPriority, int *greatestPriority)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if ((leastPriority == NULL) || (greatestPriority == NULL))
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    // Get the least and greatest stream priorities for the current context.
    result = palContextGetStreamPriorityRange(pCurrentCtx,
                                              (palCtxStreamPriorityType*)leastPriority,
                                              (palCtxStreamPriorityType*)greatestPriority);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get stream priority range: %d", result);
        return (CUresult)result;
    }

    return result;
}

/**
 * \brief Pops the current CUDA context from the current CPU thread.
 *
 * Pops the current CUDA context from the CPU thread and passes back the
 * old context handle in \p *pctx. That context may then be made current
 * to a different CPU thread by calling ::cuCtxPushCurrent().
 *
 * If a context was current to the CPU thread before ::cuCtxCreate() or
 * ::cuCtxPushCurrent() was called, this function makes that context current to
 * the CPU thread again.
 *
 * \param pctx - Returned popped context handle
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuCtxPopCurrent_v2(CUcontext *pctx)
{
    palResult         result         = PAL_SUCCESS;
    palContext*       pCurrentCtx    = NULL;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (pctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palTlsThreadDataPopCurrentContext(pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to pop current context.");
        return (CUresult)result;
    }

    // Assign the old context handle to *pctx.
    *pctx = (CUcontext)(void*)pCurrentCtx;

    return (CUresult)result;
}

/**
 * \brief Pushes a context on the current CPU thread
 *
 * Pushes the given context \p ctx onto the CPU thread's stack of current
 * contexts. The specified context becomes the CPU thread's current context, so
 * all CUDA functions that operate on the current context are affected.
 *
 * The previous current context may be made current again by calling
 * ::cuCtxDestroy() or ::cuCtxPopCurrent().
 *
 * \param ctx - Context to push
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuCtxPushCurrent(CUcontext ctx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    if (ctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    result = palTlsThreadDataPushCurrentContext(pTlsThreadData, (palContext*)ctx);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to push current context.");
        return (CUresult)result;
    }

    return result;
}

/**
 * \brief Resets all persisting lines in cache to normal status.
 *
 * ::cuCtxResetPersistingL2Cache Resets all persisting lines in cache to normal
 * status. Takes effect on function return.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_NOT_SUPPORTED
 * \notefnerr
 *
 * \sa
 * ::CUaccessPolicyWindow
 */
CUresult cuCtxResetPersistingL2Cache(void)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextResetPersistingL2Cache(pCurrentCtx);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to reset persisting L2 cache: %d", result);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Sets the preferred cache configuration for the current context.
 *
 * On devices where the L1 cache and shared memory use the same hardware
 * resources, this sets through \p config the preferred cache configuration for
 * the current context. This is only a preference. The driver will use
 * the requested configuration if possible, but it is free to choose a different
 * configuration if required to execute the function. Any function preference
 * set via ::cuFuncSetCacheConfig() or ::cuKernelSetCacheConfig() will be preferred over this context-wide
 * setting. Setting the context-wide cache configuration to
 * ::CU_FUNC_CACHE_PREFER_NONE will cause subsequent kernel launches to prefer
 * to not change the cache configuration unless required to launch the kernel.
 *
 * This setting does nothing on devices where the size of the L1 cache and
 * shared memory are fixed.
 *
 * Launching a kernel with a different preference than the most recent
 * preference setting may insert a device-side synchronization point.
 *
 * The supported cache configurations are:
 * - ::CU_FUNC_CACHE_PREFER_NONE: no preference for shared memory or L1 (default)
 * - ::CU_FUNC_CACHE_PREFER_SHARED: prefer larger shared memory and smaller L1 cache
 * - ::CU_FUNC_CACHE_PREFER_L1: prefer larger L1 cache and smaller shared memory
 * - ::CU_FUNC_CACHE_PREFER_EQUAL: prefer equal sized L1 cache and shared memory
 *
 * \param config - Requested cache configuration
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cuFuncSetCacheConfig,
 * ::cudaDeviceSetCacheConfig,
 * ::cuKernelSetCacheConfig
 */
CUresult cuCtxSetCacheConfig(CUfunc_cache config)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextSetCacheConfig(pCurrentCtx, (palFuncCache)config);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to set cache config: %d", result);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Binds the specified CUDA context to the calling CPU thread
 *
 * Binds the specified CUDA context to the calling CPU thread.
 * If \p ctx is NULL then the CUDA context previously bound to the
 * calling CPU thread is unbound and ::CUDA_SUCCESS is returned.
 *
 * If there exists a CUDA context stack on the calling CPU thread, this
 * will replace the top of that stack with \p ctx.
 * If \p ctx is NULL then this will be equivalent to popping the top
 * of the calling CPU thread's CUDA context stack (or a no-op if the
 * calling CPU thread's CUDA context stack is empty).
 *
 * \param ctx - Context to bind to the calling CPU thread
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa
 * ::cuCtxGetCurrent,
 * ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cudaSetDevice
 */
CUresult cuCtxSetCurrent(CUcontext ctx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    result = palTlsThreadDataSetCurrentContext(pTlsThreadData, (palContext*)ctx);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to set current context.");
        return (CUresult)result;
    }

    return (CUresult)result;
}

/**
 * \brief Sets the flags for the current context
 *
 * Sets the flags for the current context overwriting previously set ones. See
 * ::cuDevicePrimaryCtxSetFlags for flag values.
 *
 * \param flags - Flags to set on the current context
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetCurrent,
 * ::cuCtxGetDevice,
 * ::cuCtxGetLimit,
 * ::cuCtxGetSharedMemConfig,
 * ::cuCtxGetStreamPriorityRange,
 * ::cuCtxGetFlags,
 * ::cudaGetDeviceFlags,
 * ::cuDevicePrimaryCtxSetFlags,
 */
CUresult cuCtxSetFlags(unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextSetFlags(pCurrentCtx, flags);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to set context flags.");
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Set resource limits
 *
 * Setting \p limit to \p value is a request by the application to update
 * the current limit maintained by the context. The driver is free to
 * modify the requested value to meet h/w requirements (this could be
 * clamping to minimum or maximum values, rounding up to nearest element
 * size, etc). The application can use ::cuCtxGetLimit() to find out exactly
 * what the limit has been set to.
 *
 * Setting each ::CUlimit has its own specific restrictions, so each is
 * discussed here.
 *
 * - ::CU_LIMIT_STACK_SIZE controls the stack size in bytes of each GPU thread.
 *   The driver automatically increases the per-thread stack size
 *   for each kernel launch as needed. This size isn't reset back to the
 *   original value after each launch. Setting this value will take effect
 *   immediately, and if necessary, the device will block until all preceding
 *   requested tasks are complete.
 *
 * - ::CU_LIMIT_PRINTF_FIFO_SIZE controls the size in bytes of the FIFO used
 *   by the ::printf() device system call. Setting ::CU_LIMIT_PRINTF_FIFO_SIZE
 *   must be performed before launching any kernel that uses the ::printf()
 *   device system call, otherwise ::CUDA_ERROR_INVALID_VALUE will be returned.
 *
 * - ::CU_LIMIT_MALLOC_HEAP_SIZE controls the size in bytes of the heap used
 *   by the ::malloc() and ::free() device system calls. Setting
 *   ::CU_LIMIT_MALLOC_HEAP_SIZE must be performed before launching any kernel
 *   that uses the ::malloc() or ::free() device system calls, otherwise
 *   ::CUDA_ERROR_INVALID_VALUE will be returned.
 *
 * - ::CU_LIMIT_DEV_RUNTIME_SYNC_DEPTH controls the maximum nesting depth of
 *   a grid at which a thread can safely call ::cudaDeviceSynchronize(). Setting
 *   this limit must be performed before any launch of a kernel that uses the
 *   device runtime and calls ::cudaDeviceSynchronize() above the default sync
 *   depth, two levels of grids. Calls to ::cudaDeviceSynchronize() will fail
 *   with error code ::cudaErrorSyncDepthExceeded if the limitation is
 *   violated. This limit can be set smaller than the default or up the maximum
 *   launch depth of 24. When setting this limit, keep in mind that additional
 *   levels of sync depth require the driver to reserve large amounts of device
 *   memory which can no longer be used for user allocations. If these
 *   reservations of device memory fail, ::cuCtxSetLimit() will return
 *   ::CUDA_ERROR_OUT_OF_MEMORY, and the limit can be reset to a lower value.
 *   This limit is only applicable to devices of compute capability < 9.0.
 *   Attempting to set this limit on devices of other compute capability
 *   versions will result in the error ::CUDA_ERROR_UNSUPPORTED_LIMIT being
 *   returned.
 *
 * - ::CU_LIMIT_DEV_RUNTIME_PENDING_LAUNCH_COUNT controls the maximum number of
 *   outstanding device runtime launches that can be made from the current
 *   context. A grid is outstanding from the point of launch up until the grid
 *   is known to have been completed. Device runtime launches which violate
 *   this limitation fail and return ::cudaErrorLaunchPendingCountExceeded when
 *   ::cudaGetLastError() is called after launch. If more pending launches than
 *   the default (2048 launches) are needed for a module using the device
 *   runtime, this limit can be increased. Keep in mind that being able to
 *   sustain additional pending launches will require the driver to reserve
 *   larger amounts of device memory upfront which can no longer be used for
 *   allocations. If these reservations fail, ::cuCtxSetLimit() will return
 *   ::CUDA_ERROR_OUT_OF_MEMORY, and the limit can be reset to a lower value.
 *   This limit is only applicable to devices of compute capability 3.5 and
 *   higher. Attempting to set this limit on devices of compute capability less
 *   than 3.5 will result in the error ::CUDA_ERROR_UNSUPPORTED_LIMIT being
 *   returned.
 *
 * - ::CU_LIMIT_MAX_L2_FETCH_GRANULARITY controls the L2 cache fetch granularity.
 *   Values can range from 0B to 128B. This is purely a performance hint and
 *   it can be ignored or clamped depending on the platform.
 *
 * - ::CU_LIMIT_PERSISTING_L2_CACHE_SIZE controls size in bytes available for
 *   persisting L2 cache. This is purely a performance hint and it can be
 *   ignored or clamped depending on the platform.
 *
 * \param limit - Limit to set
 * \param value - Size of limit
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_UNSUPPORTED_LIMIT,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSynchronize,
 * ::cudaDeviceSetLimit
 */
CUresult cuCtxSetLimit(CUlimit limit, size_t value)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextSetLimit(pCurrentCtx, (palCtxLimit)limit, (palUint64)value);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to set limit: %d", result);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Block for a context's tasks to complete
 *
 * Blocks until the device has completed all preceding requested tasks.
 * ::cuCtxSynchronize() returns an error if one of the preceding tasks failed.
 * If the context was created with the ::CU_CTX_SCHED_BLOCKING_SYNC flag, the
 * CPU thread will block until the GPU context has finished its work.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cudaDeviceSynchronize
 */
CUresult cuCtxSynchronize(void)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextSynchronize(pCurrentCtx);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to synchronize context: %d", result);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Increment a context's usage-count
 *
 * \deprecated
 *
 * Note that this function is deprecated and should not be used.
 *
 * Increments the usage count of the context and passes back a context handle
 * in \p *pctx that must be passed to ::cuCtxDetach() when the application is
 * done with the context. ::cuCtxAttach() fails if there is no context current
 * to the thread.
 *
 * Currently, the \p flags parameter must be 0.
 *
 * \param pctx  - Returned context handle of the current context
 * \param flags - Context attach flags (must be 0)
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxDetach,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuCtxAttach(CUcontext *pctx, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (flags != 0)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: flags must be 0");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if (pctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    palContextAttachRefCountIncrement(pCurrentCtx);

    *pctx = (CUcontext)(void*)pCurrentCtx;

    return CUDA_SUCCESS;
}

/**
 * \brief Decrement a context's usage-count
 *
 * \deprecated
 *
 * Note that this function is deprecated and should not be used.
 *
 * Decrements the usage count of the context \p ctx, and destroys the context
 * if the usage count goes to 0. The context must be a handle that was passed
 * back by ::cuCtxCreate() or ::cuCtxAttach(), and must be current to the
 * calling thread.
 *
 * \param ctx - Context to destroy
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetCacheConfig,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize
 */
CUresult cuCtxDetach(CUcontext ctx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (ctx == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: context handle is NULL");
        return CUDA_ERROR_INVALID_VALUE;
    }

    if ((CUcontext)(void*)pCurrentCtx != ctx)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: context handle does not match current context");
        return CUDA_ERROR_INVALID_CONTEXT;
    }

    result = palContextAttachRefCountDecrement(pCurrentCtx, pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to decrement context attach ref count.");
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Returns the current shared memory configuration for the current context.
 *
 * \deprecated
 *
 * This function will return in \p pConfig the current size of shared memory banks
 * in the current context. On devices with configurable shared memory banks,
 * ::cuCtxSetSharedMemConfig can be used to change this setting, so that all
 * subsequent kernel launches will by default use the new bank size. When
 * ::cuCtxGetSharedMemConfig is called on devices without configurable shared
 * memory, it will return the fixed bank size of the hardware.
 *
 * The returned bank configurations can be either:
 * - ::CU_SHARED_MEM_CONFIG_FOUR_BYTE_BANK_SIZE:  shared memory bank width is
 *   four bytes.
 * - ::CU_SHARED_MEM_CONFIG_EIGHT_BYTE_BANK_SIZE: shared memory bank width will
 *   eight bytes.
 *
 * \param pConfig - returned shared memory configuration
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cuCtxGetSharedMemConfig,
 * ::cuFuncSetCacheConfig,
 * ::cudaDeviceGetSharedMemConfig
 */
CUresult cuCtxGetSharedMemConfig(CUsharedconfig *pConfig)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    if (pConfig == NULL)
    {
        CU_DBG_PRINTF_ERROR("Invalid parameter: Specified NULL output pointer");
        return CUDA_ERROR_INVALID_VALUE;
    }

    *pConfig = (CUsharedconfig)palContextGetSharedMemConfig(pCurrentCtx);

    return CUDA_SUCCESS;
}

/**
 * \brief Sets the shared memory configuration for the current context.
 *
 * \deprecated
 *
 * On devices with configurable shared memory banks, this function will set
 * the context's shared memory bank size which is used for subsequent kernel
 * launches.
 *
 * Changed the shared memory configuration between launches may insert a device
 * side synchronization point between those launches.
 *
 * Changing the shared memory bank size will not increase shared memory usage
 * or affect occupancy of kernels, but may have major effects on performance.
 * Larger bank sizes will allow for greater potential bandwidth to shared memory,
 * but will change what kinds of accesses to shared memory will result in bank
 * conflicts.
 *
 * This function will do nothing on devices with fixed shared memory bank size.
 *
 * The supported bank configurations are:
 * - ::CU_SHARED_MEM_CONFIG_DEFAULT_BANK_SIZE: set bank width to the default initial
 *   setting (currently, four bytes).
 * - ::CU_SHARED_MEM_CONFIG_FOUR_BYTE_BANK_SIZE: set shared memory bank width to
 *   be natively four bytes.
 * - ::CU_SHARED_MEM_CONFIG_EIGHT_BYTE_BANK_SIZE: set shared memory bank width to
 *   be natively eight bytes.
 *
 * \param config - requested shared memory configuration
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 * \sa ::cuCtxCreate,
 * ::cuCtxDestroy,
 * ::cuCtxGetApiVersion,
 * ::cuCtxGetCacheConfig,
 * ::cuCtxGetDevice,
 * ::cuCtxGetFlags,
 * ::cuCtxGetLimit,
 * ::cuCtxPopCurrent,
 * ::cuCtxPushCurrent,
 * ::cuCtxSetLimit,
 * ::cuCtxSynchronize,
 * ::cuCtxGetSharedMemConfig,
 * ::cuFuncSetCacheConfig,
 * ::cudaDeviceSetSharedMemConfig
 */
CUresult cuCtxSetSharedMemConfig(CUsharedconfig config)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;
    palContext*       pCurrentCtx    = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    pCurrentCtx = palTlsThreadDataGetCurrentContext(pTlsThreadData);
    if (pCurrentCtx == NULL)
    {
        CU_DBG_PRINTF_ERROR("No context is current to the calling thread.");
        return CUDA_ERROR_INVALID_CONTEXT;
    }
    else if (palContextIsActive(pCurrentCtx) != PAL_TRUE)
    {
        CU_DBG_PRINTF_ERROR("Context is destroyed.");
        return CUDA_ERROR_CONTEXT_IS_DESTROYED;
    }

    result = palContextSetSharedMemConfig(pCurrentCtx, (palSharedConfig)config);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to set shared memory config: %d", result);
        return (CUresult)result;
    }

    return CUDA_SUCCESS;
}