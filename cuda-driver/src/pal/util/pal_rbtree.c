#include "pal_assert.h"
#include "pal_rbtree.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// =====================================================================================================================
// The internal default function for comparing key.
static palInt32 ipalRbTreeCompareKeyDefaultFunc(palRbTreeKeyType keyA, palRbTreeKeyType keyB)
{
	if (keyA < keyB)
	{
		return -1;
	}
	else if (keyA > keyB)
	{
		return 1;
	}
	else
	{
		return 0;
	}
}

// =====================================================================================================================
// Create new tree with given key compare function.
palResult palRbTreeCreate(palRbTree** ppRbTree, palRbTreeKeyCompareFunc func)
{
    palResult  result  = PAL_SUCCESS;
    palRbTree* pRbTree = NULL;

    if (ppRbTree == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid input parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppRbTree = NULL;

    pRbTree = (palRbTree*)malloc(sizeof(palRbTree));
    if (pRbTree == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for rbtree.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palRbTreeInitialize(pRbTree, func);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize rbtree.");
        free(pRbTree);
        return result;
    }

    *ppRbTree = pRbTree;

    return result;
}

// =====================================================================================================================
// Initialize a new tree (Created by caller) with given key compare function.
palResult palRbTreeInitialize(palRbTree* pRbTree, palRbTreeKeyCompareFunc func)
{
    palResult 	   result;
    palRbTreeNode* pNilNode;

    memset(pRbTree, 0, sizeof(palRbTree));

    pNilNode = (palRbTreeNode*)malloc(sizeof(palRbTreeNode));
    if (pNilNode == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for nil node.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

	memset(pNilNode, 0, sizeof(palRbTreeNode));

    pNilNode->color   = PAL_RBTREE_COLOR_TYPE_BLACK;  // The sentinel node is black.
    pNilNode->pLeft   =  pRbTree->pNil;
	pNilNode->pRight  =  pRbTree->pNil;
	pNilNode->pParent =  pRbTree->pNil;

    pRbTree->pNil  = pNilNode;
    pRbTree->pRoot = pRbTree->pNil;  // Initialize root node is empty

	pRbTree->compareKeyFunc = (func == NULL) ? ipalRbTreeCompareKeyDefaultFunc : func;

    return PAL_SUCCESS;
}

// =====================================================================================================================
// Clean up all nodes from the tree.
void palRbTreeClear(palRbTree* pRbTree)
{
	palRbTreeNode* pCurrent = NULL;
	palRbTreeNode* pEnd     = NULL;

	// Remove all of elements
	pCurrent = palRbTreeGetFirst(pRbTree);
	pEnd     = palRbTreeGetEnd(pRbTree);
	while (pCurrent != pEnd)
	{
		pCurrent = palRbTreeDelete(pRbTree, pCurrent);
	}
}

// =====================================================================================================================
// Deinitialize the tree (created by caller).
void palRbTreeDeinitialize(palRbTree* pRbTree)
{
    if (pRbTree == NULL)
    {
        return;
    }

	palRbTreeClear(pRbTree);

	free(pRbTree->pNil);

	memset(pRbTree, 0, sizeof(palRbTree));
}

// =====================================================================================================================
// Destroy all tree nodes and delete the rbtree handle.
void palRbTreeDestroy(palRbTree* pRbTree)
{
    palRbTreeDeinitialize(pRbTree);

    free(pRbTree);
}

// =====================================================================================================================
// Rotates the node to the left.
static void ipalRbTreeLeftRotate(palRbTree* pRbTree, palRbTreeNode* pNode)
{
	palRbTreeNode* pRight = pNode->pRight;

    // 1. Change the right pointer of the rotated node to point to the parent pointer of the left subtree of the
    // right node. And check if the left subtree of the rotated node is a leaf node.
	pNode->pRight = pRight->pLeft;
	if (pRight->pLeft != pRbTree->pNil)
	{
		pRight->pLeft->pParent = pNode;
	}

    // 2. Change the parent pointer of y to point to the left subtree of the parent node of x. And check if the
    // rotated node is the root node and check if the rotated node is the left or right subtree of its parent node.
	pRight->pParent = pNode->pParent;
	if (pNode->pParent == pRbTree->pNil)
    {
        pRbTree->pRoot = pRight;
    }
	else if (pNode == pNode->pParent->pLeft)
    {
        pNode->pParent->pLeft = pRight;
    }
	else
    {
        pNode->pParent->pRight = pRight;
    }

    // 3. Change the left pointer of the right node to point to the rotated node.
	pRight->pLeft = pNode;
	pNode->pParent = pRight;
}

// =====================================================================================================================
// Rotate the node from right.
static void ipalRbTreeRightRotate(palRbTree* pRbTree, palRbTreeNode* pNode)
{
	palRbTreeNode* pLeft = pNode->pLeft;

	pNode->pLeft = pLeft->pRight;
	if (pLeft->pRight != pRbTree->pNil)
	{
		pLeft->pRight->pParent = pNode;
	}

	pLeft->pParent = pNode->pParent;
	if (pNode->pParent == pRbTree->pNil)
    {
        pRbTree->pRoot = pLeft;
    }
	else if (pNode == pNode->pParent->pRight)
	{
        pNode->pParent->pRight = pLeft;
    }
	else
	{
        pNode->pParent->pLeft = pLeft;
    }

	pLeft->pRight = pNode;
	pNode->pParent = pLeft;
}

// =====================================================================================================================
// Fixup node color when insertion happened.
static void ipalRbTreeInsertFixup(palRbTree* pRbTree, palRbTreeNode* pFixup)
{
	palRbTreeNode* pUncle;

	if ((pRbTree == NULL) || (pFixup == NULL))
	{
		return;
	}

	// If the node is red, so its children is black
	while (pFixup->pParent->color == PAL_RBTREE_COLOR_TYPE_RED)
	{
		if (pFixup->pParent == pFixup->pParent->pParent->pLeft)
		{
			pUncle = pFixup->pParent->pParent->pRight;
			if (pUncle->color == PAL_RBTREE_COLOR_TYPE_RED)	// The right uncle node is red.
			{
				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pUncle->color = PAL_RBTREE_COLOR_TYPE_BLACK;

				pFixup->pParent->pParent->color = PAL_RBTREE_COLOR_TYPE_RED;

				pFixup = pFixup->pParent->pParent;
			}
			else  // Otherwise, the right uncle node is black.
			{
				if (pFixup == pFixup->pParent->pRight)
				{
					pFixup = pFixup->pParent;
					ipalRbTreeLeftRotate(pRbTree, pFixup);
				}

				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pFixup->pParent->pParent->color = PAL_RBTREE_COLOR_TYPE_RED;

				ipalRbTreeRightRotate(pRbTree, pFixup->pParent->pParent);
			}
		}
		else
		{
			pUncle = pFixup->pParent->pParent->pLeft;
			if (pUncle->color == PAL_RBTREE_COLOR_TYPE_RED)	// The left uncle node is red
			{
				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pUncle->color = PAL_RBTREE_COLOR_TYPE_BLACK;

				pFixup->pParent->pParent->color = PAL_RBTREE_COLOR_TYPE_RED;

				pFixup = pFixup->pParent->pParent;
			}
			else
			{
				if (pFixup == pFixup->pParent->pLeft)
				{
					pFixup = pFixup->pParent;
					ipalRbTreeRightRotate(pRbTree, pFixup);
				}

				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pFixup->pParent->pParent->color = PAL_RBTREE_COLOR_TYPE_RED;
				ipalRbTreeLeftRotate(pRbTree, pFixup->pParent->pParent);
			}
		}
	}

	pRbTree->pRoot->color = PAL_RBTREE_COLOR_TYPE_BLACK;
}

// =====================================================================================================================
// Insert a pair with key and value into the tree.
palResult palRbTreeInsert(palRbTree* pRbTree, const palRbTreeKeyType key, const palRbTreeValueType value)
{
	palInt32 compareRetVal = 0;

	palRbTreeNode* pCurrent = pRbTree->pRoot;
	palRbTreeNode* pParent  = pRbTree->pNil;
	palRbTreeNode* pNew		= NULL;

	if (pRbTree == NULL)
	{
		return PAL_ERROR_INVALID_VALUE;
	}

	while (pCurrent != pRbTree->pNil)
	{
		compareRetVal = pRbTree->compareKeyFunc(pCurrent->key, key);
		if (compareRetVal == 0)
		{
			// Update the value if key is same.
			pCurrent->value = value;
			return PAL_SUCCESS;
		}

		pParent = pCurrent;

		if (compareRetVal > 0)
		{
			pCurrent = pCurrent->pLeft;
		}
		else
		{
			pCurrent = pCurrent->pRight;
		}
	}

	pNew = (palRbTreeNode*)malloc(sizeof(palRbTreeNode));
	if (pNew == NULL)
	{
		PAL_DBG_PRINTF_ERROR("Failed to allocate new rbtree node.");
		return PAL_ERROR_OUT_OF_MEMORY;
	}

	pNew->key     = key;
	pNew->value   = value;
	pNew->pParent = pParent;
	pNew->pLeft   = pRbTree->pNil;
	pNew->pRight  = pRbTree->pNil;
	pNew->color   = PAL_RBTREE_COLOR_TYPE_RED;

	pRbTree->count++;

	if (pParent != pRbTree->pNil)
	{
		if (compareRetVal > 0)
		{
			pParent->pLeft = pNew;
		}
		else
		{
			pParent->pRight = pNew;
		}
	}
	else
	{
		pRbTree->pRoot = pNew;
	}

	ipalRbTreeInsertFixup(pRbTree, pNew);

	return PAL_SUCCESS;
}

// =====================================================================================================================
// Find key in the tree. Return end() node if not found.
palRbTreeNode* palRbTreeSearch(palRbTree* pRbTree, const palRbTreeKeyType key)
{
	palInt32 	   compareRetVal = 0;
	palRbTreeNode* pCurrent 	 = NULL;
	palRbTreeNode* pResult		 = NULL;

	if (pRbTree == NULL)
	{
		return NULL;
	}

	pCurrent = pRbTree->pRoot;
	pResult  = pRbTree->pNil;

	while (pCurrent != pRbTree->pNil)
	{
		compareRetVal = pRbTree->compareKeyFunc(pCurrent->key, key);
		if (compareRetVal == 0)
		{
			pResult = pCurrent;
			break;
		}

		if (compareRetVal < 0)
		{
			pCurrent = pCurrent->pRight;
		}
		else
		{
			pCurrent = pCurrent->pLeft;
		}
	}

	return pResult;
}

// =====================================================================================================================
// Find the greater equal node of rb-tree.
palRbTreeNode* palRbTreeFindGreaterEqual(palRbTree* pRbTree, const palRbTreeKeyType key)
{
	palInt32 	   compareRetVal = 0;
	palRbTreeNode* pCurrent 	 = NULL;
	palRbTreeNode* pResult		 = NULL;

	if (pRbTree == NULL)
	{
		return NULL;
	}

	pCurrent = pRbTree->pRoot;
	pResult  = pRbTree->pNil;

	while (pCurrent != pRbTree->pNil)
	{
		compareRetVal = pRbTree->compareKeyFunc(pCurrent->key, key);
		if (compareRetVal >= 0)
		{
			pResult  = pCurrent;
			pCurrent = pCurrent->pLeft;
		}
		else
		{
			pCurrent = pCurrent->pRight;
		}
	}

	return pResult;
}

// =====================================================================================================================
// Find the less equal node of rb-tree.
palRbTreeNode* palRbTreeFindLessEqual(palRbTree* pRbTree, const palRbTreeKeyType key)
{
	palInt32 	   compareRetVal = 0;
	palRbTreeNode* pCurrent 	 = NULL;
	palRbTreeNode* pResult		 = NULL;

	if (pRbTree == NULL)
	{
		return NULL;
	}

	pCurrent = pRbTree->pRoot;
	pResult  = pRbTree->pNil;

	while (pCurrent != pRbTree->pNil)
	{
		compareRetVal = pRbTree->compareKeyFunc(pCurrent->key, key);
		if (compareRetVal <= 0)
		{
			pResult  = pCurrent;
			pCurrent = pCurrent->pRight;
		}
		else
		{
			pCurrent = pCurrent->pLeft;
		}
	}

	return pResult;
}

// =====================================================================================================================
// Return first (smallest) node in the tree.
palRbTreeNode* palRbTreeGetFirst(palRbTree* pRbTree)
{
	palRbTreeNode* pCurrent;

	if (pRbTree->pRoot == pRbTree->pNil)
	{
		return pRbTree->pNil;
	}

	pCurrent = pRbTree->pRoot;
	while (pCurrent->pLeft != pRbTree->pNil)
	{
		pCurrent = pCurrent->pLeft;
	}

	return pCurrent;
}

// =====================================================================================================================
// Return last node in the tree.
palRbTreeNode* palRbTreeGetLast(palRbTree* pRbTree)
{
	palRbTreeNode* pLast;

	if (pRbTree->pRoot == pRbTree->pNil)
	{
		return pRbTree->pNil;
	}

	pLast = pRbTree->pRoot;
	while (pLast->pRight != pRbTree->pNil)
	{
		pLast = pLast->pRight;
	}

	return pLast;
}

// =====================================================================================================================
// Return the end() node in the tree.
palRbTreeNode* palRbTreeGetEnd(palRbTree* pRbTree)
{
	return pRbTree->pNil;
}

// =====================================================================================================================
// Returns previous smaller node in the tree.
palRbTreeNode* palRbTreeGetPrev(palRbTree* pRbTree, palRbTreeNode* pNode)
{
	palRbTreeNode* pParent;

	if ((pRbTree == NULL) || (pNode == NULL))
	{
		return NULL;
	}

	if (pNode == pRbTree->pNil)
	{
		return palRbTreeGetLast(pRbTree);
	}

	if (pNode->pLeft != pRbTree->pNil)
	{
		pNode = pNode->pLeft;
		while (pNode->pRight != pRbTree->pNil)
		{
			pNode = pNode->pRight;
		}
	}
	else
	{
		pParent = pNode->pParent;

		while ((pParent != pRbTree->pNil) && (pNode == pParent->pLeft))
		{
			pNode 	= pParent;
			pParent = pParent->pParent;
		}

		pNode = pParent;
	}

	return pNode;
}

// =====================================================================================================================
// Returns next node in the tree.
palRbTreeNode* palRbTreeGetNext(palRbTree* pRbTree, palRbTreeNode* pNode)
{
	palRbTreeNode* pParent;

	if ((pRbTree == NULL) || (pNode == NULL))
	{
		return NULL;
	}

	if (pNode == pRbTree->pNil)
	{
		return palRbTreeGetFirst(pRbTree);
	}

	if (pNode->pRight != pRbTree->pNil)
	{
		pNode = pNode->pRight;
		while (pNode->pLeft != pRbTree->pNil)
		{
			pNode = pNode->pLeft;
		}
	}
	else
	{
		pParent = pNode->pParent;

		while ((pParent != pRbTree->pNil) && (pNode == pParent->pRight))
		{
			pNode 	= pParent;
			pParent = pParent->pParent;
		}

		pNode = pParent;
	}

	return pNode;
}

// =====================================================================================================================
// Iterator points to the first element that is not less than the key. If no such element was found, the pas-the-end
// iterator end() will be returned.
palRbTreeNode* palRbTreeLowerBound(palRbTree* pRbTree, palRbTreeKeyType key)
{
	palInt32 	   compareRetVal;
	palRbTreeNode* pCurrent;
	palRbTreeNode* pResult;

	if (pRbTree == NULL)
	{
		return NULL;
	}

	pCurrent = pRbTree->pRoot;
	pResult  = pRbTree->pNil;

	while (pCurrent != pRbTree->pNil)
	{
		compareRetVal = pRbTree->compareKeyFunc(pCurrent->key, key);
		if (compareRetVal >= 0)
		{
			// Update the matched result node.
			pResult	 = pCurrent;
			pCurrent = pCurrent->pLeft;
		}
		else
		{
			pCurrent = pCurrent->pRight;
		}
	}

	return pResult;
}

// =====================================================================================================================
// Iterator points to the first element that is greater than the key. If no such element was found, the pas-the-end
// iterator end() will be returned.
palRbTreeNode* palRbTreeUpperBound(palRbTree* pRbTree, palRbTreeKeyType key)
{
	palInt32 	   compareRetVal;
	palRbTreeNode* pCurrent;
	palRbTreeNode* pResult;

	if (pRbTree == NULL)
	{
		return NULL;
	}

	pCurrent = pRbTree->pRoot;
	pResult  = pRbTree->pNil;

	while (pCurrent != pRbTree->pNil)
	{
		compareRetVal = pRbTree->compareKeyFunc(pCurrent->key, key);
		if (compareRetVal > 0)
		{
			// Update the matched result node.
			pResult	 = pCurrent;
			pCurrent = pCurrent->pLeft;
		}
		else
		{
			pCurrent = pCurrent->pRight;
		}
	}

	return pResult;
}

// =====================================================================================================================
// Fixup node colors when delete happened.
static void ipalRbTreeDeleteFixup(palRbTree* pRbTree, palRbTreeNode* pFixup)
{
	palRbTreeNode* pSibling;

	while ((pFixup != pRbTree->pRoot) && (pFixup->color == PAL_RBTREE_COLOR_TYPE_BLACK))
	{
		if (pFixup == pFixup->pParent->pLeft)
		{
			pSibling = pFixup->pParent->pRight;
			if (pSibling->color == PAL_RBTREE_COLOR_TYPE_RED)
			{
				pSibling->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_RED;

				ipalRbTreeLeftRotate(pRbTree, pFixup->pParent);
				pSibling = pFixup->pParent->pRight;
			}

			if ((pSibling->pLeft->color == PAL_RBTREE_COLOR_TYPE_BLACK) &&
				(pSibling->pRight->color == PAL_RBTREE_COLOR_TYPE_BLACK))
			{
				pSibling->color = PAL_RBTREE_COLOR_TYPE_RED;
				pFixup = pFixup->pParent;
			}
			else
			{
				if (pSibling->pRight->color == PAL_RBTREE_COLOR_TYPE_BLACK)
				{
					pSibling->pLeft->color = PAL_RBTREE_COLOR_TYPE_BLACK;
					pSibling->color = PAL_RBTREE_COLOR_TYPE_RED;

					ipalRbTreeRightRotate(pRbTree, pSibling);
					pSibling = pFixup->pParent->pRight;
				}

				pSibling->color = pFixup->pParent->color;
				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pSibling->pRight->color = PAL_RBTREE_COLOR_TYPE_BLACK;

				ipalRbTreeLeftRotate(pRbTree, pFixup->pParent);
				pFixup = pRbTree->pRoot;
			}

		}
		else
		{
			pSibling = pFixup->pParent->pLeft;
			if (pSibling->color == PAL_RBTREE_COLOR_TYPE_RED)
			{
				pSibling->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_RED;

				ipalRbTreeRightRotate(pRbTree, pFixup->pParent);
				pSibling = pFixup->pParent->pLeft;
			}

			if ((pSibling->pLeft->color == PAL_RBTREE_COLOR_TYPE_BLACK) &&
				(pSibling->pRight->color == PAL_RBTREE_COLOR_TYPE_BLACK))
			{
				pSibling->color = PAL_RBTREE_COLOR_TYPE_RED;
				pFixup = pFixup->pParent;
			}
			else
			{

				if (pSibling->pLeft->color == PAL_RBTREE_COLOR_TYPE_BLACK)
				{
					pSibling->pRight->color = PAL_RBTREE_COLOR_TYPE_BLACK;
					pSibling->color = PAL_RBTREE_COLOR_TYPE_RED;

					ipalRbTreeLeftRotate(pRbTree, pSibling);
					pSibling = pFixup->pParent->pLeft;
				}

				pSibling->color = pFixup->pParent->color;
				pFixup->pParent->color = PAL_RBTREE_COLOR_TYPE_BLACK;
				pSibling->pLeft->color = PAL_RBTREE_COLOR_TYPE_BLACK;

				ipalRbTreeRightRotate(pRbTree, pFixup->pParent);
				pFixup = pRbTree->pRoot;
			}
		}
	}

	pFixup->color = PAL_RBTREE_COLOR_TYPE_BLACK;
}

// =====================================================================================================================
// Delete the corresponding node of key from tree.
palRbTreeNode* palRbTreeDelete(palRbTree* pRbTree, palRbTreeNode* pToDelete)
{
	palRbTreeNode* pTarget = pRbTree->pNil;
	palRbTreeNode* pFixup = pRbTree->pNil;
	palRbTreeNode* pNext  = pRbTree->pNil;

	if ((pToDelete->pLeft == pRbTree->pNil) || (pToDelete->pRight == pRbTree->pNil))
	{
		pTarget = pToDelete;
	}
	else
	{
		pTarget = palRbTreeGetNext(pRbTree, pToDelete);
	}

	if (pTarget->pLeft != pRbTree->pNil)
	{
		pFixup = pTarget->pLeft;
	}
	else if (pTarget->pRight != pRbTree->pNil)
	{
		pFixup = pTarget->pRight;
	}

	pFixup->pParent = pTarget->pParent;
	if (pTarget->pParent == pRbTree->pNil)
	{
		pRbTree->pRoot = pFixup;
	}
	else if (pTarget == pTarget->pParent->pLeft)
	{
		pTarget->pParent->pLeft = pFixup;
	}
	else
	{
		pTarget->pParent->pRight = pFixup;
	}

	if (pTarget != pToDelete)
	{
		pToDelete->key   = pTarget->key;
		pToDelete->value = pTarget->value;
	}

	// Fixup
	if (pTarget->color == PAL_RBTREE_COLOR_TYPE_BLACK)
	{
		ipalRbTreeDeleteFixup(pRbTree, pFixup);
	}

	// Decrease the count of elements in the tree.
	pRbTree->count--;

	// Get the next node.
	pNext = palRbTreeGetNext(pRbTree, pTarget);

	// free it
	free(pTarget);

	return pNext;
}

// =====================================================================================================================
// Erase the node with the given key from the tree.
palResult palRbTreeErase(palRbTree* pRbTree, palRbTreeKeyType key)
{
	palRbTreeNode* pNode = palRbTreeSearch(pRbTree, key);
	if (pNode == pRbTree->pNil)
	{
		// The key was not found in the tree.
		return PAL_ERROR_NOT_FOUND;
	}

	// Delete the node from the tree.
	palRbTreeDelete(pRbTree, pNode);

	return PAL_SUCCESS;
}

// =====================================================================================================================
// Get the size of elements in the tree.
palUint64 palRbTreeGetSize(palRbTree* pRbTree)
{
	return (pRbTree != NULL) ? pRbTree->count : 0;
}

// =====================================================================================================================
// The in order traversal internal function for removing all elements.
static void ipalRbTreeTraverseIn(palRbTree* pRbTree, palRbTreeUserFunc func, void* pArg, palRbTreeNode* pNode)
{
	if (pNode == pRbTree->pNil)
	{
		return;
	}

	ipalRbTreeTraverseIn(pRbTree, func, pArg, pNode->pLeft);
	func(pNode, pArg);
	ipalRbTreeTraverseIn(pRbTree, func, pArg, pNode->pRight);
}

// =====================================================================================================================
// The post order traversal internal function for removing all elements.
static void ipalRbTreeTraversePost(palRbTree* pRbTree, palRbTreeUserFunc func, void* pArg, palRbTreeNode* pNode)
{
	if (pNode == pRbTree->pNil)
	{
		return;
	}

	ipalRbTreeTraversePost(pRbTree, func, pArg, pNode->pLeft);
	ipalRbTreeTraversePost(pRbTree, func, pArg, pNode->pRight);
	func(pNode, pArg);
}

// =====================================================================================================================
// The in order traversal internal function for removing all elements.
void palRbTreeTraverseInOrder(palRbTree* pRbTree, palRbTreeUserFunc func, void* pArg)
{
	if (pRbTree == NULL)
	{
		return;
	}

	ipalRbTreeTraverseIn(pRbTree, func, pArg, pRbTree->pRoot);
}

// =====================================================================================================================
// The post order traversal internal function for removing all elements.
void palRbTreeTraversePostOrder(palRbTree* pRbTree, palRbTreeUserFunc func, void* pArg)
{
	if (pRbTree == NULL)
	{
		return;
	}

	ipalRbTreeTraversePost(pRbTree, func, pArg, pRbTree->pRoot);
}

