/*
 ***********************************************************************************************************************
 *
 *  Copyright (c) 2014-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 *
 **********************************************************************************************************************/

#include "pal_assert.h"
#include "pal_hash.h"
#include "pal_list.h"
#include "pal_settingsfilemgr.h"

#include <ctype.h>

/// Returns a pointer to string with the lead spaces trimmed
///
/// @param [in] pStr String to edit
///
/// @return The modified string
static palInt8* ipalSkipLeadingSpaces(palInt8* pStr);

// =====================================================================================================================
palInt8* ipalSkipLeadingSpaces(palInt8* pStr)
{
    while ((*pStr != '\0') && isspace((palUint8)(*pStr)))
    {
        pStr++;
    }

    return pStr;
}

// =====================================================================================================================
palResult palSettingsFileMgrCreate(palSettingsFileMgr** ppSettingsFileMgr,  const palInt8* pSettingsFileName,
                                   const palInt8* pSettingsFilePath)
{
    palResult           result           = PAL_SUCCESS;
    palSettingsFileMgr* pSettingsFileMgr = NULL;

    if ((ppSettingsFileMgr == NULL) || (pSettingsFileName == NULL) || (pSettingsFilePath == NULL))
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    pSettingsFileMgr = (palSettingsFileMgr*)malloc(sizeof(palSettingsFileMgr));
    if (pSettingsFileMgr == NULL)
    {
        *ppSettingsFileMgr = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palSettingsFileMgrInitialize(pSettingsFileMgr, pSettingsFileName, pSettingsFilePath);
    if (result != PAL_SUCCESS)
    {
        free(pSettingsFileMgr);
        *ppSettingsFileMgr = NULL;
        return result;
    }

    *ppSettingsFileMgr = pSettingsFileMgr;

    return result;
}

// =====================================================================================================================
palResult palSettingsFileMgrInitialize(palSettingsFileMgr* pSettingsFileMgr, const palInt8* pSettingsFileName,
                                       const palInt8* pSettingsFilePath)
{
    palResult            result                   = PAL_SUCCESS;
    palInt8*             pEnvOverridenPath        = NULL;
    palInt8              fileAbsPath[512]         = {0};
    palInt8              fallbackFileAbsPath[512] = {0};
    palOsResult          osResult                 = PAL_OS_SUCCESS;
    palInt8              currLine[256]            = {0};
    size_t               lineLength               = 0;
    palResult            readResult               = PAL_SUCCESS;
    palUint32            idx                      = 0;
    palInt8*             pBuffer                  = NULL;
    palInt8*             pToken                   = NULL;
    palInt8*             pScope                   = NULL;
    palUint32            hashedName               = 0;
    palHashKeyType       hashKey                  = {0};
    palSettingValueInfo* pSettingValueInfo        = NULL;
    palSettingValueInfo* pSettingTempInfo         = NULL;

    if ((pSettingsFileMgr == NULL) || (pSettingsFileName == NULL) || (pSettingsFilePath == NULL))
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pSettingsFileMgr, 0, sizeof(palSettingsFileMgr));

    // Initialize the settings file manager.
    pSettingsFileMgr->pSettingsFileName = strdup(pSettingsFileName);
    if (pSettingsFileMgr->pSettingsFileName == NULL)
    {
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    if (palOsEnvGet("NOONE_CONFIG_DIR", pEnvOverridenPath, sizeof(pEnvOverridenPath)) != 0)
    {
        pSettingsFilePath = pEnvOverridenPath;
    }

    if (pSettingsFilePath != NULL)
    {
        // Open the settings file, if it exists.
        palOsSnprintf(&fileAbsPath[0],
                      sizeof(fileAbsPath),
#if defined(__unix__)
                      "%s/%s",
#else
                      "%s\\%s",
#endif
                      pSettingsFilePath,
                      pSettingsFileMgr->pSettingsFileName);

        if (palOsFileExists(&fileAbsPath[0]) == PAL_FALSE)
        {
            palOsSnprintf(&fallbackFileAbsPath[0],
                          sizeof(fallbackFileAbsPath),
#if defined(__unix__)
                          "%s/noonePalSettings.cfg",
#else
                          "%s\\noonePalSettings.cfg",
#endif
                          pSettingsFilePath);
            if (palOsFileExists(&fallbackFileAbsPath[0]) == PAL_FALSE)
            {
                free(pSettingsFileMgr->pSettingsFileName);
                pSettingsFileMgr->pSettingsFileName = NULL;
                return PAL_ERROR_FILE_NOT_FOUND;
            }
            else
            {
                osResult = palOsFileOpen(&fallbackFileAbsPath[0], PAL_OS_OPEN_READ,
                                         &pSettingsFileMgr->settingsFileHandle);
                result   = (osResult == PAL_OS_SUCCESS) ? PAL_SUCCESS : PAL_ERROR_OPERATING_SYSTEM;
            }
        }
        else
        {
            // Open the config file for read-only access
            osResult = palOsFileOpen(&fileAbsPath[0], PAL_OS_OPEN_READ, &pSettingsFileMgr->settingsFileHandle);
            result   = (osResult == PAL_OS_SUCCESS) ? PAL_SUCCESS : PAL_ERROR_OPERATING_SYSTEM;
        }
    }

    if (result == PAL_SUCCESS)
    {
        // Read the settings file one line at a time

        // An error is returned from ReadLine when EOF is encountered, so we can just loop until
        // we see an error of some kind
        while (readResult == PAL_SUCCESS)
        {
            // Read a line from the settings file
            readResult = palOsFileReadLine(pSettingsFileMgr->settingsFileHandle, &currLine[0], sizeof(currLine) - 1,
                                           &lineLength);
            currLine[lineLength] = '\0';

            if ((readResult == PAL_SUCCESS) && (lineLength > 0))
            {
                // Now parse the line
                idx = 0;

                // ignore leading whitespace
                while (isspace((palUint8)(currLine[idx])))
                {
                    idx++;
                }

                // ignore comment/empty lines
                if ((currLine[idx] == ';') || (currLine[idx] == '\0'))
                {
                    continue;
                }

                // A '#' not followed by a digit is also a comment
                // '#' followed by a digit is a hashed setting name
                if ((currLine[idx] == '#') && (isdigit(currLine[idx+1]) == false))
                {
                    continue;
                }

                // all other lines are key, value pairs. Split at the comma and add them to our map
                pBuffer = NULL;
                pToken = palOsStrtok(&currLine[idx], ", ", &pBuffer);
                if ((pToken != NULL) && (strlen(pToken) > 0))
                {
                    // Trim off the scope if it is present.
                    pScope = NULL;
                    if (strchr(pToken, ':') != NULL)
                    {
                        pScope = palOsStrtok(pToken, ":", &pToken);

                        // Handle strings like "Scope: Name"
                        if (pToken != NULL)
                        {
                            pToken = ipalSkipLeadingSpaces(pToken);
                        }
                    }

                    hashedName = 0;
                    // If the first character of the string is a # that indicates that the name
                    // string is an already hashed setting name. In that case just convert to UINT32
                    if (pToken[0] == '#')
                    {
                        palStringToValueType(&pToken[1], PAL_VALUE_TYPE_UINT32, sizeof(palUint32), &hashedName);
                    }
                    else
                    {
                        // Otherwise, calculate the hashed value for the setting name
                        memset(&hashKey, 0, sizeof(hashKey));
                        hashKey.stringKey = pToken;
                        hashedName = palHashComputeHashKey(PAL_HASH_TYPE_STRING, (const palHashKeyType*)&hashKey);
                    }

                    pToken = palOsStrtok(NULL, ",", &pBuffer);
                    if (pToken != NULL)
                    {
                        pToken = ipalSkipLeadingSpaces(pToken);

                        if (strlen(pToken) > 0)
                        {
                            // Add the setting name and value to the list.
                            pSettingValueInfo = (palSettingValueInfo*)malloc(sizeof(palSettingValueInfo));
                            if (pSettingValueInfo == NULL)
                            {
                                result = PAL_ERROR_OUT_OF_MEMORY;
                                break;
                            }

                            memset(pSettingValueInfo, 0, sizeof(palSettingValueInfo));

                            pSettingValueInfo->hashName = hashedName;
                            PAL_ASSERT(strlen(pToken) < sizeof(pSettingValueInfo->strValue));
                            strncpy(&pSettingValueInfo->strValue[0], pToken, sizeof(pSettingValueInfo->strValue));

                            if (pScope != NULL)
                            {
                                PAL_ASSERT(strlen(pScope) < sizeof(pSettingValueInfo->componentName));
                                strncpy(&pSettingValueInfo->componentName[0], pScope,
                                        sizeof(pSettingValueInfo->componentName));
                            }

                            PAL_LIST_INSERT_PREV_NEXT(pSettingsFileMgr->pSettingsListHead, pSettingValueInfo,
                                                      pSettingTempInfo, pPrev, pNext);
                        }
                    }
                }
            }

        }

        if (result != PAL_SUCCESS)
        {
            palSettingsFileMgrDeinitialize(pSettingsFileMgr);
        }
        else
        {
            // Close the settings file.
            palOsFileClose(pSettingsFileMgr->settingsFileHandle);
            pSettingsFileMgr->settingsFileHandle = NULL;
        }
    }

    return result;
}

// =====================================================================================================================
void palSettingsFileMgrDeinitialize(palSettingsFileMgr* pSettingsFileMgr)
{
    palSettingValueInfo* pSettingValueInfo = NULL;

    if (pSettingsFileMgr == NULL)
    {
        return;
    }

    // Free the settings list
    pSettingValueInfo = pSettingsFileMgr->pSettingsListHead;
    while (pSettingValueInfo != NULL)
    {
        pSettingValueInfo = pSettingsFileMgr->pSettingsListHead;
        if (pSettingValueInfo != NULL)
        {
            PAL_LIST_REMOVE_PREV_NEXT(pSettingsFileMgr->pSettingsListHead, pSettingValueInfo, pPrev, pNext);
            free(pSettingValueInfo);
        }
    }

    pSettingsFileMgr->pSettingsListHead = NULL;

    // Free the settings file name
    if (pSettingsFileMgr->pSettingsFileName != NULL)
    {
        free(pSettingsFileMgr->pSettingsFileName);
        pSettingsFileMgr->pSettingsFileName = NULL;
    }

    if (pSettingsFileMgr->settingsFileHandle != NULL)
    {
        // Close the settings file
        palOsFileClose(pSettingsFileMgr->settingsFileHandle);
    }

    memset(pSettingsFileMgr, 0, sizeof(palSettingsFileMgr));
}

// =====================================================================================================================
void palSettingsFileMgrDestroy(palSettingsFileMgr* pSettingsFileMgr)
{
    if (pSettingsFileMgr == NULL)
    {
        return;
    }

    palSettingsFileMgrDeinitialize(pSettingsFileMgr);
    free(pSettingsFileMgr);
}

// =====================================================================================================================
palBool palSettingsFileMgrGetValue(palSettingsFileMgr* pSettingsFileMgr, const palInt8* pSettingName, palValueType type,
                                   void* pValue, size_t bufferSz)
{
    palUint32      hashedName = 0;
    palHashKeyType hashKey    = {0};

    // If the first character of the string is a # that indicates that the name strings is the
    // already hashed setting name in string form. In that case just convert to UINT32
    if (pSettingName[0] == '#')
    {
        palStringToValueType(&pSettingName[1], PAL_VALUE_TYPE_UINT32,sizeof(palUint32), &hashedName);
    }
    else
    {
        // Otherwise, calculate the hashed value for the setting name by FNV1a hashing the string
        // and then converting the result to a 32-bit integer.
        hashKey.stringKey = (palInt8*)pSettingName;
        hashedName = palHashComputeHashKey(PAL_HASH_TYPE_STRING, (const palHashKeyType*)&hashKey);
    }

    return palSettingsFileMgrGetValueByHash(pSettingsFileMgr, hashedName, type, pValue, bufferSz);
}

// =====================================================================================================================
palBool palSettingsFileMgrGetValueByHash(palSettingsFileMgr* pSettingsFileMgr, palUint32 hashedName, palValueType type,
                                         void* pValue, size_t bufferSz)
{
    palSettingValueInfo* pSettingValueInfo = NULL;
    const palInt8*       pSettingValue     = NULL;
    bool                 foundValue        = false;

    // Get the value from the list
    pSettingValueInfo = pSettingsFileMgr->pSettingsListHead;
    while (pSettingValueInfo != NULL)
    {
        if (pSettingValueInfo->hashName == hashedName)
        {
            pSettingValue = &pSettingValueInfo->strValue[0];
            break;
        }

        pSettingValueInfo = pSettingValueInfo->pNext;
    }

    if (pSettingValue != NULL)
    {
        // Indicate we found the value being requested
        foundValue = true;
        // Then convert it to the correct type and return
        palStringToValueType(pSettingValue, type, bufferSz, pValue);
    }

    return foundValue;
}