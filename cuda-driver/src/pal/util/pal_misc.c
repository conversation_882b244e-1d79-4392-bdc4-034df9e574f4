#include "pal_assert.h"
#include "pal_misc.h"

#include <stdio.h>

// =====================================================================================================================
void palStringToValueType(const palInt8* pStrValue, palValueType type, size_t valueSize, void* pValue)
{
    switch (type)
    {
    case PAL_VALUE_TYPE_BOOLEAN:
        *((palBool*)pValue) = ((atoi(pStrValue)) ? PAL_TRUE : PAL_FALSE);
        break;
    case PAL_VALUE_TYPE_INT8:
        *((palInt8*)pValue) = (palInt8)(strtoll(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_UINT8:
        *((palUint8*)pValue) = (palUint8)(strtoull(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_INT16:
        *((palInt16*)pValue) = (palInt16)(strtoll(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_UINT16:
        *((palUint16*)pValue) = (palUint16)(strtoull(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_INT32:
        *((palInt32*)pValue) = (palInt32)(strtoll(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_UINT32:
        *((palUint32*)pValue) = (palUint32)(strtoull(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_INT64:
        *((palInt64*)pValue) = (palInt64)(strtoll(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_UINT64:
        *((palUint64*)pValue) = (palUint64)(strtoull(pStrValue, NULL, 0));
        break;
    case PAL_VALUE_TYPE_FLOAT:
        *((palFloat32*)pValue) = (palFloat32)(atof(pStrValue));
        break;
    case PAL_VALUE_TYPE_DOUBLE:
        *((palFloat64*)pValue) = (palFloat64)(atof(pStrValue));
        break;
    case PAL_VALUE_TYPE_STRING:
        palOsStrncpy((palInt8*)pValue, pStrValue, valueSize);
        break;
    case PAL_VALUE_TYPE_MAX:
        // Do nothing
        PAL_NEVER_CALLED_MSG("Invalid value type");
        break;
    }
}