/*
 ***********************************************************************************************************************
 *
 *  Copyright (c) 2017-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 *
 **********************************************************************************************************************/

#ifndef PAL_DBGPRINT_H_
#define PAL_DBGPRINT_H_

#include <assert.h>
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <execinfo.h>

#include "pal.h"
#include "pal_os.h"
#include "pal_types.h"

#if ENABLE_PRINTS_ASSERTS
// Specifies the category of a debug print.
// Driver developer can enable/disable each category separately through settings.
typedef enum palDbgPrintCategory_enum
{
    PAL_DBG_PRINT_CAT_ERROR_MSG = 0,   ///< Error messages.
    PAL_DBG_PRINT_CAT_WARN_MSG,        ///< Warning messages.
    PAL_DBG_PRINT_CAT_INFO_MSG,        ///< Generic informational messages.
    PAL_DBG_PRINT_CAT_COUNT
} palDbgPrintCategory;

/// Specifies the debug print mode: disabled, print to debugger, or print to file.
typedef enum palDbgPrintMode_enum
{
    PAL_DBG_PRINT_MODE_DISABLE = 0,    ///< Debug print is ignored.
    PAL_DBG_PRINT_MODE_PRINT,          ///< Debug print is routed to the debug window or stdout.
    PAL_DBG_PRINT_MODE_FILE,           ///< Debug print is routed to a file.
    PAL_DBG_PRINT_MODE_PRINT_CALLBACK, ///< Debug print is routed to the print callback only
    PAL_DBG_PRINT_MODE_COUNT
} palDbgPrintMode;

// Specifies the debug print mode: disabled, print to debugger, or print to file.
typedef enum palDbgPrintStyle_enum
{
    PAL_DBG_PRINT_STYLE_DEFAULT           = 0x0, ///< Normal mode: has a prefix and a CR-LF.
    PAL_DBG_PRINT_STYLE_NO_PREFIX         = 0x1, ///< Skip the prefix.
    PAL_DBG_PRINT_STYLE_NO_CRLF           = 0x2, ///< Skip the CR-LF.
    PAL_DBG_PRINT_STYLE_NO_PREFIX_NO_CRLF = 0x3  ///< Skip both the prefix and the CR-LF.
} palDbgPrintStyle;

// Definition for debug print callback.
typedef void (*palDbgPrintCallbackFunc)(void* pUserData, palDbgPrintCategory category, const palInt8* pText);

// Debug print callback struct that bundles the callback function and its userdata pointer
typedef struct palDbgPrintCallback_st
{
    palDbgPrintCallbackFunc pCallbackFunc;
    void*                   pUserData;
} palDbgPrintCallback;

/// @brief Generic debug printf function to be used when the caller wishes to specify the output category and style.
///
/// @param logMode The debug print mode.
/// @param pLogDirectory The directory where log files will be written.
/// @param pLogFileName The name of the log file to be used.
/// @return Success if successful, otherwise an appropriate error.
palResult palDbgPrintInitialize(palInt32 logMode, palInt8* pLogDirectory, palInt8* pLogFileName);

/// Generic debug printf function to be used when the caller wishes to specify the output category and style.  Clients
/// should use the PAL_DPF macro instead of calling this function directly.
///
/// @param [in] category Message category (e.g., CS dumps, SC output, etc.).
/// @param [in] style    Text output style (i.e., has prefix and/or CR-LF).
/// @param [in] isApiPrefix  PAL_TRUE if the API prefix should be used, PAL_FALSE otherwise.
/// @param [in] pFormat  Printf-style format string.
void palDbgPrintf(palDbgPrintCategory category, palDbgPrintStyle style, palBool isApiPrefix,
                  const palInt8* pFormat, ...) PRINTF_FORMAT(4, 0);

/// Generic printf function to be used when the caller wishes to specify the output category and style, and has
/// pre-started the variable arg list (va_list argument instead of ...).
///
/// @param [in] category Message category (e.g., CS dumps, SC output, etc.).
/// @param [in] style    Text output style (i.e., has prefix and/or CR-LF).
/// @param [in] isApiPrefix  PAL_TRUE if the API prefix should be used, PAL_FALSE otherwise.
/// @param [in] pFormat  Printf-style format string.
/// @param [in] argList  Variable argument list.
void palDbgPrintfV(palDbgPrintCategory category, palDbgPrintStyle style, palBool isApiPrefix, const palInt8* pFormat,
                   va_list argList) PRINTF_FORMAT(4, 0);

/// Sets the debug print mode (output to debugger, write to file, or disabled) for the specified category of messages.
///
/// Probably controlled by a setting and set during initialization.
///
/// @param [in] category Message category to control (e.g., CS dumps, SC output, etc.).
/// @param [in] mode     New mode to be used for this message category (print to file, etc.).
void palDbgPrintSetMode(palDbgPrintCategory category, palDbgPrintMode mode);

/// Opens a file that resides in the selected log directory.
///
/// This function exists in all build configurations.
///
/// @param [in,out] pFile     File object to represent the opened file.
/// @param [in]     pFilename Filename to open.
/// @param [in]     flags     ORed mask of FileAccessMode values specifying how this file will be accessed.
///
/// @returns Success if successful, otherwise an appropriate error.
palResult palDbgPrintOpenLogFile(palOsFileHandle* pFileHandle, const palInt8* pFileName, palUint32 flags);

/// Sets the global debug print callback.
///
/// @param [in] callback  Debug print callback struct that contains the callback function and a userdata pointer
void palDbgPrintSetPrintCallback(palDbgPrintCallback* pCallback);

/// Sets the global debug print log directory.
///
/// @param [in] pLogDirectory  Directory where log files will be written.
void palDbgPrintSetLogDirectory(const palInt8* pLogDirectory);

/// @brief Sets the global debug print log file name.
/// @param pLogFileName The name of the log file to be used.
void palDbgPrintSetLogFileName(const palInt8* pLogFileName);

/// Print back trace
void palDbgPrintPrintBacktrace(void);
#endif

/// PAL_ENABLE_LOGGING enables the new logging code. At this time, both, the current and new logging
/// code will be active for development purpose if both macros are enabled.
#if ENABLE_PRINTS_ASSERTS
#define PAL_DBG_PRINT_COLOR_RESET   "\x1B[0m"
#define PAL_DBG_PRINT_COLOR_RED     "\x1B[31m"
#define PAL_DBG_PRINT_COLOR_GREEN   "\x1B[32m"
#define PAL_DBG_PRINT_COLOR_YELLOW  "\x1B[33m"
#define PAL_DBG_PRINT_COLOR_BLUE    "\x1B[34m"
#define PAL_DBG_PRINT_COLOR_MAGENTA "\x1B[35m"
#define PAL_DBG_PRINT_COLOR_CYAN    "\x1B[36m"
#define PAL_DBG_PRINT_COLOR_WHITE   "\x1B[37m"
/// Debug printf macro.
#define PAL_DBG_PRINTF(_level, _stype, ...) palDbgPrintf(_level, _style, PAL_FALSE, __VA_ARGS__)

/// Debug info printf macro.
#define PAL_DBG_PRINTF_INFO(_pFormat, ...)                                                       \
    {                                                                                            \
        palDbgPrintf(PAL_DBG_PRINT_CAT_INFO_MSG, PAL_DBG_PRINT_STYLE_DEFAULT, PAL_FALSE,         \
                     PAL_DBG_PRINT_COLOR_GREEN _pFormat " (%s:%d:%s)" PAL_DBG_PRINT_COLOR_RESET, \
                     ##__VA_ARGS__, __FILE__, __LINE__, __func__);                               \
    }

/// Debug warning printf macro.
#define PAL_DBG_PRINTF_WARN(_pFormat, ...)                                                        \
    {                                                                                             \
        palDbgPrintf(PAL_DBG_PRINT_CAT_WARN_MSG, PAL_DBG_PRINT_STYLE_DEFAULT, PAL_FALSE,          \
                     PAL_DBG_PRINT_COLOR_YELLOW _pFormat " (%s:%d:%s)" PAL_DBG_PRINT_COLOR_RESET, \
                     ##__VA_ARGS__, __FILE__, __LINE__, __func__);                                \
    }

/// Debug error printf macro.
#define PAL_DBG_PRINTF_ERROR(_pFormat, ...)                                                    \
    {                                                                                          \
        palDbgPrintf(PAL_DBG_PRINT_CAT_ERROR_MSG, PAL_DBG_PRINT_STYLE_DEFAULT, PAL_FALSE,      \
                     PAL_DBG_PRINT_COLOR_RED _pFormat " (%s:%d:%s)" PAL_DBG_PRINT_COLOR_RESET, \
                     ##__VA_ARGS__, __FILE__, __LINE__, __func__);                             \
    }
#else
// Debug printf macro
#define PAL_DBG_PRINTF(...) ((void)0)
/// Debug info printf macro.
#define PAL_DBG_PRINTF_INFO(...) ((void)0)
/// Debug warning printf macro.
#define PAL_DBG_PRINTF_WARN(...) ((void)0)
/// Debug error printf macro.
#define PAL_DBG_PRINTF_ERROR(...) ((void)0)
#endif

#endif // PAL_DBGPRINT_H_