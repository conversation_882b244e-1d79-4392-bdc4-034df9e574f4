#include "pal_assert.h"
#include "pal_hash.h"

#include <stdio.h>
#include <string.h>

// =====================================================================================================================
// Fnv1a hash algorithm for 32-bit.
palUint32 palHashFnv1a32(const void* pData, size_t size)
{
    // Params taken from http://www.isthe.com/chongo/tech/comp/fnv/index.html#FNV-param
    palUint32 hash = 2166136261u;   // 32 bit offset_basis
    palUint32 fnv32Prime = 16777619u;   // 32 bit FNV_prime
    const palUint8* pBytes = (const palUint8*)pData;

    for (size_t i = 0; i < size; i++)
    {
        hash ^= pBytes[i];
        hash *= fnv32Prime;
    }

    return hash;
}

// =====================================================================================================================
// Fnv1a hash algorithm for 64-bit.
palUint64 palHashFnv1a64(const void* pData, size_t size)
{
    // Params taken from http://www.isthe.com/chongo/tech/comp/fnv/index.html#FNV-param
    palUint64 hash = 14695981039346656037ULL;   // 64 bit offset_basis
    palUint64 fnv64Prime = 1099511628211ULL;   // 64 bit FNV_prime
    const palUint8* pBytes = (const palUint8*)pData;

    for (size_t i = 0; i < size; i++)
    {
        hash ^= pBytes[i];
        hash *= fnv64Prime;
    }

    return hash;
}

// =====================================================================================================================
palUint32 palHashComputeHashKey(palHashType hashType, const palHashKeyType* pKey)
{
    switch (hashType)
    {
    case PAL_HASH_TYPE_STRING:
        return palHashFnv1a32(pKey->stringKey, strlen(pKey->stringKey));
    case PAL_HASH_TYPE_INTEGER:
        return palHashFnv1a32((const void*)&pKey->integerKey, sizeof(pKey->integerKey));
    case PAL_HASH_TYPE_BINARY:
        return palHashFnv1a32(pKey->binaryKey.pData, pKey->binaryKey.length);
    default:
        PAL_ASSERT_MSG(0, "Never be called!");
        return 0;
    }
}

// =====================================================================================================================
palBool palHashKeysEqual(palHashType hashType, const palHashKeyType* pKeyA, const palHashKeyType* pKeyB)
{
    switch (hashType)
    {
    case PAL_HASH_TYPE_STRING:
        return (strcmp(pKeyA->stringKey, pKeyB->stringKey) == 0) ? PAL_TRUE : PAL_FALSE;
    case PAL_HASH_TYPE_INTEGER:
        return (pKeyA->integerKey == pKeyB->integerKey) ? PAL_TRUE : PAL_FALSE;
    case PAL_HASH_TYPE_BINARY:
    {
        if (pKeyA->binaryKey.length == pKeyB->binaryKey.length)
        {
            return (memcmp(pKeyA->binaryKey.pData, pKeyB->binaryKey.pData, pKeyA->binaryKey.length) == 0) ?
                PAL_TRUE : PAL_FALSE;
        }
        else
        {
            return PAL_FALSE;
        }
    }
    default:
        PAL_ASSERT_MSG(0, "Never be called!");
        return PAL_FALSE;
    }
}

// =====================================================================================================================
palResult palHashCopyKey(palHashType hashType, palHashKeyType* pDest, const palHashKeyType* pSrc)
{
    switch (hashType)
    {
    case PAL_HASH_TYPE_STRING:
        pDest->stringKey = strdup(pSrc->stringKey);
        return (pDest->stringKey != NULL) ? PAL_SUCCESS : PAL_ERROR_OUT_OF_MEMORY;
    case PAL_HASH_TYPE_INTEGER:
        pDest->integerKey = pSrc->integerKey;
        return PAL_SUCCESS; // No pointer assignment, always successful
    case PAL_HASH_TYPE_BINARY:
    {
        pDest->binaryKey.pData = malloc(pSrc->binaryKey.length);
        if (pDest->binaryKey.pData != NULL)
        {
            memcpy(pDest->binaryKey.pData, pSrc->binaryKey.pData, pSrc->binaryKey.length);
            pDest->binaryKey.length = pSrc->binaryKey.length;
        }
        return (pDest->binaryKey.pData != NULL) ? PAL_SUCCESS : PAL_ERROR_OUT_OF_MEMORY;
    }
    default:
        PAL_ASSERT_MSG(0, "Invalid hash type!");
        return PAL_ERROR_INVALID_VALUE;
    }
}