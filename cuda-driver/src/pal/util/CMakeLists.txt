include(${PAL_SOURCE_DIR}/cmake/PalCodegen.cmake)

# Build static library libutil.a
add_library(util STATIC
    pal_assert.c
    pal_dbgprint.c
    pal_hash.c
    pal_hashmap.c
    pal_list.c
    pal_misc.c
    pal_rbtree.c
    pal_settingsfilemgr.c
    pal_vector.c
    ${PAL_SETTINGS_GENERATED_DIR}/g_pal_settings.c
)

# Generate the settings code
pal_setup_generated_code()

# The related implementation with OS
if(WIN32)
    target_sources(util PRIVATE os/windows/pal_os_windows.c)
    target_include_directories(util PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/os/windows)
elseif(UNIX)
    target_sources(util PRIVATE os/posix/pal_os_posix.c)
    target_include_directories(util PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/os/posix)
else()
    message(FATAL_ERROR "Unknown platform!")
endif()

# Include directories
target_include_directories(util PUBLIC
    ${PAL_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/os
)
