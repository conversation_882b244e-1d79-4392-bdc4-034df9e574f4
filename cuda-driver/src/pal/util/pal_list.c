#include "pal_list.h"

#include <stdlib.h>

// =====================================================================================================================
palResult palListAlloc(palListNode** ppList, palListNodeValueType value)
{
    palResult result = PAL_SUCCESS;

    if (ppList == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppList = (palListNode*)malloc(sizeof(palListNode));
    if (*ppList == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for list.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    memset(*ppList, 0, sizeof(palListNode));
    (*ppList)->value = value;

    return result;
}

// =====================================================================================================================
palResult palListDestroy(palListNode** ppList)
{
    palListNode* pTemp = NULL;

    if (ppList == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pTemp = (*ppList);
    while (pTemp != NULL)
    {
        palListNode* pNext = pTemp->pNext;
        free(pTemp);
        pTemp = pNext;
    }

    *ppList = NULL;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palListInsert(palListNode** ppList, palListNode* pNode)
{
    palListNode* pTemp = NULL;

    if ((ppList == NULL) || (pNode == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (*ppList == NULL)
    {
        *ppList = pNode;
        pNode->pPrev = NULL;
    }
    else
    {
        pTemp = *ppList;
        while (pTemp->pNext != NULL)
        {
            pTemp = pTemp->pNext;
        }
        pTemp->pNext = pNode;
        pNode->pPrev = pTemp;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palListRemove(palListNode** ppList, palListNode* pNode)
{
    if ((ppList == NULL) || (pNode == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (*ppList == NULL)
    {
        PAL_DBG_PRINTF_ERROR("List is empty.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pNode->pPrev != NULL)
    {
        pNode->pPrev->pNext = pNode->pNext;
    }
    else
    {
        *ppList = pNode->pNext;
    }

    if (pNode->pNext != NULL)
    {
        pNode->pNext->pPrev = pNode->pPrev;
    }

    free(pNode);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palListGetHead(palListNode* pList, palListNode** ppHead)
{
    if (pList == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppHead = pList;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palListGetTail(palListNode* pList, palListNode** ppTail)
{
    if (pList == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter.");
        return PAL_ERROR_INVALID_VALUE;
    }

    while (pList->pNext != NULL)
    {
        pList = pList->pNext;
    }

    *ppTail = pList;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palListGetSize(palListNode* pList, palUint64* pSize)
{
    palUint64 size = 0;

    if (pList == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter.");
        return PAL_ERROR_INVALID_VALUE;
    }

    while (pList != NULL)
    {
        size++;
        pList = pList->pNext;
    }

    *pSize = size;

    return PAL_SUCCESS;
}
