/*
 ***********************************************************************************************************************
 *
 *  Copyright (c) 2014-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 *
 **********************************************************************************************************************/
#ifndef PAL_SETTINGSFILEMGR_H_
#define PAL_SETTINGSFILEMGR_H_

#include "pal.h"
#include "pal_os.h"
#include "pal_misc.h"
#include "pal_structures.h"
#include "pal_types.h"

/**
 ***********************************************************************************************************************
 * @brief Parses a plain text config file filled with key/value pairs that describe user-desired driver settings.  The
 *        format of the target file should look like this:
 *
 *        ; Comment
 *        SettingName, StringValue
 *        AnotherSettingName, 1234
 *
 *        ; The following settings are pre-hashed.
 *        #0x9370a0c8, AnotherStringValue
 *
 *        After loading the file, a value can be retrieved by either specifying a setting string or hash value.
 ***********************************************************************************************************************
 */

// Describes a single { setting, value, componentName } triplet as loaded from a settings file.
typedef struct palSettingValueInfo_st
{
    // 32-bit hash of the setting name string.
    palUint32 hashName;

    // Value for this setting encoded as a C-style string.
    palInt8 strValue[512];

    // The name of the component
    palInt8 componentName[32];

    // Pointer to the next setting value info in the list.
    palSettingValueInfo* pNext;

    // Pointer to the previous setting value info in the list.
    palSettingValueInfo* pPrev;
} palSettingValueInfo;

struct palSettingsFileMgr_st
{
    // settings file name
    palInt8* pSettingsFileName;

    // The settings file path
    palOsFileHandle settingsFileHandle;

    // List of setting, value info parsed from the config file.
    palSettingValueInfo* pSettingsListHead;
};

/// @brief Create a settings file manager.
///
/// @param ppSettingsFileMgr The created settings file manager handle.
/// @param pSettingsFileName The settings file name.
/// @param pSettingsFilePath The settings file path.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
/// @note The caller is responsible for freeing the settings file manager handle.
palResult palSettingsFileMgrCreate(palSettingsFileMgr** ppSettingsFileMgr,  const palInt8* pSettingsFileName,
                                   const palInt8* pSettingsFilePath);

/// @brief Initializes the settings file manager.  Must be called before calling any other functions on this object.
///
/// @param pSettingsFileMgr The settings file manager handle.
/// @param pSettingsFileName The settings file name.
/// @param pSettingsFilePath The settings file path.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
/// @note The caller is responsible for freeing the settings file manager handle.
/// @note The caller is responsible for freeing the file content.
/// @note The caller is responsible for freeing the file name and file path.
palResult palSettingsFileMgrInitialize(palSettingsFileMgr* pSettingsFileMgr, const palInt8* pSettingsFileName,
                                       const palInt8* pSettingsFilePath);

/// @brief Deinitialize the settings file manager.
///
/// @param pSettingsFileMgr The settings file manager handle.
void palSettingsFileMgrDeinitialize(palSettingsFileMgr* pSettingsFileMgr);

/// @brief Destroy the settings file manager.
///
/// @param pSettingsFileMgr The settings file manager handle.
void palSettingsFileMgrDestroy(palSettingsFileMgr* pSettingsFileMgr);

/// Returns the value corresponding to the specified setting in this settings file.
    ///
    /// @param pSettingName C-style string specifying the setting name to get.
    /// @param type         Expected value type (e.g., int, float, string).  Specifies how the value specified
    ///                     in the file should be converted to the data written into pValue.
    /// @param pValue       Value as read from the settings file and converted to the specified type.
    /// @param bufferSz     If type is ValueType::Str, this value indicates the amount of available space at
    ///                     pValue.  If not enough space is available, the resulting string value will be truncated.
    ///
    /// @returns PAL_TRUE if the value was successfully found in the settings file and converted to the requested value
    ///          type; PAL_FALSE otherwise.
palBool palSettingsFileMgrGetValue(palSettingsFileMgr* pSettingsFileMgr, const palInt8* pSettingName, palValueType type,
                                   void* pValue, size_t bufferSz);

/// Returns the value corresponding to the specified setting in this settings file.  This method accepts a
/// pre-hashed settings string using the HashString() function defined in palInlineFuncs.h.
///
/// @param hashedName 32-bit hash result from calling HashString() on the setting name of interest.
/// @param type       Expected value type (e.g., int, float, string).  Specifies how the value specified
///                   in the file should be converted to the data written into pValue.
/// @param pValue     Value as read from the settings file and converted to the specified type.
/// @param bufferSz   If type is ValueType::Str, this value indicates the amount of available space at
///                   pValue.  If not enough space is available, the resulting string value will be
///                   truncated.
///
/// @returns PAL_TRUE if the value was successfully found in the settings file and converted to the requested value
///          type; PAL_FALSE otherwise.
palBool palSettingsFileMgrGetValueByHash(palSettingsFileMgr* pSettingsFileMgr, palUint32 hashedName, palValueType type,
                                         void* pValue, size_t bufferSz);

#endif // PAL_SETTINGSFILEMGR_H_