#include "pal_assert.h"
#include "pal_vector.h"

#include <stdlib.h>
#include <string.h>

// =====================================================================================================================
palResult palVectorCreate(palVector** ppVector, size_t elementSize, size_t initialCapacity,
                          palVectorDestructFunc destructFunc)
{
    palResult result = PAL_SUCCESS;
    palVector* pVector = NULL;

    if ((ppVector == NULL) || (elementSize == 0) || (initialCapacity == 0))
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    pVector = (palVector*)malloc(sizeof(palVector));
    if (pVector == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for palVector.");
        *ppVector = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    // Initialize the vector with the given parameters.
    result = palVectorInitialize(pVector, elementSize, initialCapacity, destructFunc);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize palVector.");
        free(pVector);
        *ppVector = NULL;
        return result;
    }

    *ppVector = pVector;

    return result;
}

// =====================================================================================================================
palResult palVectorInitialize(palVector* pVector, size_t elementSize, size_t initialCapacity,
                              palVectorDestructFunc destructFunc)
{
    if (pVector == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Initialize the vector properties.
    pVector->elementSize = elementSize;
    pVector->size        = 0;
    pVector->capacity    = initialCapacity;

    // Allocate memory for the vector data.
    pVector->pData = malloc(elementSize * initialCapacity);
    if (pVector->pData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for vector data.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    // Initialize the data to zero.
    memset(pVector->pData, 0, elementSize * initialCapacity);

    // Initialize the destruct function if provided.
    if (destructFunc != NULL)
    {
        // Store the destruct function in the vector structure.
        pVector->destructFunc = destructFunc;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorDeinitialize(palVector* pVector)
{
    if (pVector == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // If a destruct function is provided, call it for each element in the vector.
    if (pVector->destructFunc != NULL)
    {
        for (size_t i = 0; i < pVector->size; ++i)
        {
            pVector->destructFunc((palInt8*)pVector->pData + (i * pVector->elementSize));
        }
    }

    if (pVector->pData != NULL)
    {
        // Free the memory allocated for the vector data.
        free(pVector->pData);
        pVector->pData = NULL;
    }

    // Reset the vector properties.
    pVector->destructFunc = NULL;
    pVector->elementSize = 0;
    pVector->size        = 0;
    pVector->capacity    = 0;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorDestroy(palVector* pVector)
{
    if (pVector == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Deinitialize the vector.
    palResult result = palVectorDeinitialize(pVector);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to deinitialize palVector.");
        return result;
    }

    // Free the vector structure itself.
    free(pVector);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorPushBack(palVector* pVector, const void* pElement)
{
    if ((pVector == NULL) || (pElement == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or element pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the vector needs to be resized.
    if (pVector->size >= pVector->capacity)
    {
        size_t newCapacity = pVector->capacity * 2;
        palResult result = palVectorReserve(pVector, newCapacity);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to reserve memory for palVector.");
            return result;
        }
    }

    // Copy the element to the end of the vector.
    memcpy((palInt8*)pVector->pData + (pVector->size * pVector->elementSize), pElement, pVector->elementSize);
    pVector->size++;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorPopBack(palVector* pVector, void* pElement)
{
    if ((pVector == NULL) || (pElement == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or element pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the vector is empty.
    if (pVector->size == 0)
    {
        PAL_DBG_PRINTF_ERROR("Cannot pop from an empty vector.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Copy the last element to the provided pointer.
    memcpy(pElement, (palInt8*)pVector->pData + ((pVector->size - 1) * pVector->elementSize), pVector->elementSize);
    pVector->size--;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorGet(palVector* pVector, size_t index, void* pElement)
{
    if ((pVector == NULL) || (pElement == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or element pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the index is within bounds.
    if (index >= pVector->size)
    {
        PAL_DBG_PRINTF_ERROR("Index out of bounds.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Copy the element at the specified index to the provided pointer.
    memcpy(pElement, (palInt8*)pVector->pData + (index * pVector->elementSize), pVector->elementSize);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorSet(palVector* pVector, size_t index, const void* pElement)
{
    if ((pVector == NULL) || (pElement == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or element pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the index is within bounds.
    if (index >= pVector->size)
    {
        PAL_DBG_PRINTF_ERROR("Index out of bounds.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Copy the provided element to the specified index in the vector.
    memcpy((palInt8*)pVector->pData + (index * pVector->elementSize), pElement, pVector->elementSize);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorResize(palVector* pVector, size_t newSize)
{
    if (pVector == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the new size is valid.
    if (newSize == 0)
    {
        PAL_DBG_PRINTF_ERROR("Invalid new size.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the new size is greater than the current capacity.
    if (newSize > pVector->capacity)
    {
        // Reserve memory for the new size.
        palResult result = palVectorReserve(pVector, newSize);
        if (result != PAL_SUCCESS)
        {
            return result;
        }
    }

    // Resize the vector to the new size.
    pVector->size = newSize;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorReserve(palVector* pVector, size_t newCapacity)
{
    if (pVector == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the new capacity is valid.
    if (newCapacity <= pVector->capacity)
    {
        PAL_DBG_PRINTF_ERROR("New capacity must be greater than current capacity.");
        return PAL_SUCCESS;
    }

    // Allocate new memory for the vector data with the new capacity.
    void* pNewData = realloc(pVector->pData, pVector->elementSize * newCapacity);
    if (pNewData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for new vector data.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    // Update the vector properties.
    pVector->pData = pNewData;
    pVector->capacity = newCapacity;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorClear(palVector* pVector)
{
    if (pVector == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // If a destruct function is provided, call it for each element in the vector.
    if (pVector->destructFunc != NULL)
    {
        for (size_t i = 0; i < pVector->size; ++i)
        {
            pVector->destructFunc((palInt8*)pVector->pData + (i * pVector->elementSize));
        }
    }

    // Reset the size of the vector to zero.
    pVector->size = 0;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorGetSize(palVector* pVector, size_t* pSize)
{
    if ((pVector == NULL) || (pSize == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or size pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Return the size of the vector.
    *pSize = pVector->size;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorGetCapacity(palVector* pVector, size_t* pCapacity)
{
    if ((pVector == NULL) || (pCapacity == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or capacity pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Return the capacity of the vector.
    *pCapacity = pVector->capacity;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorGetData(palVector* pVector, void** ppData)
{
    if ((pVector == NULL) || (ppData == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or data pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Return the data pointer of the vector.
    *ppData = pVector->pData;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorGetElementSize(palVector* pVector, size_t* pElementSize)
{
    if ((pVector == NULL) || (pElementSize == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or element size pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Return the size of each element in the vector.
    *pElementSize = pVector->elementSize;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorGetElement(palVector* pVector, size_t index, void** ppElement)
{
    if ((pVector == NULL) || (ppElement == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or element pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the index is within bounds.
    if (index >= pVector->size)
    {
        PAL_DBG_PRINTF_ERROR("Index out of bounds.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Return the element at the specified index.
    *ppElement = (palInt8*)pVector->pData + (index * pVector->elementSize);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palVectorSetElement(palVector* pVector, size_t index, const void* pElement)
{
    if ((pVector == NULL) || (pElement == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid vector or element pointer.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the index is within bounds.
    if (index >= pVector->size)
    {
        PAL_DBG_PRINTF_ERROR("Index out of bounds.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Copy the provided element to the specified index in the vector.
    memcpy((palInt8*)pVector->pData + (index * pVector->elementSize), pElement, pVector->elementSize);

    return PAL_SUCCESS;
}