#ifndef PAL_LIST_H_
#define PAL_LIST_H_

#include "pal_assert.h"

#define PAL_LIST_INSERT_PREV_NEXT(head, node, temp, prev, next) \
    do                                                          \
    {                                                           \
        if ((head) == NULL)                                     \
        {                                                       \
            (head) = (node);                                    \
            (node)->next = NULL;                                \
            (node)->prev = NULL;                                \
            break;                                              \
        }                                                       \
        else                                                    \
        {                                                       \
            (temp) = (head);                                    \
            while ((temp)->next != NULL)                        \
            {                                                   \
                (temp) = (temp)->next;                          \
            }                                                   \
            (temp)->next = node;                                \
            (node)->prev = temp;                                \
        }                                                       \
    } while(0)

#define PAL_LIST_REMOVE_PREV_NEXT(head, node, prev, next) \
    do                                                    \
    {                                                     \
        if ((node)->prev != NULL)                         \
        {                                                 \
            (node)->prev->next = (node)->next;            \
        }                                                 \
        else                                              \
        {                                                 \
            (head) = (node)->next;                        \
        }                                                 \
        if ((node)->next != NULL)                         \
        {                                                 \
            (node)->next->prev = (node)->prev;            \
        }                                                 \
        (node)->prev = NULL;                              \
        (node)->next = NULL;                              \
    } while(0)

typedef void* palListNodeValueType;

typedef struct palListNode_st
{
    palListNodeValueType   value;
    struct palListNode_st* pPrev;
    struct palListNode_st* pNext;
} palListNode;

/// @brief Allocate a new list node.
///
/// @param ppList The created list handle.
/// @param value The value type of the list.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
/// @note The list is empty after creation.
palResult palListAlloc(palListNode** ppList, palListNodeValueType value);

/// @brief Destroy the list.
///
/// @param ppList The list handle.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
palResult palListDestroy(palListNode** ppList);

/// @brief Insert a node into the list.
///
/// @param ppList The list handle.
/// @param pNode The node to be insert.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
palResult palListInsert(palListNode** ppList, palListNode* pNode);

/// @brief Remove a node from the list.
///
/// @param ppList The list handle.
/// @param pNode The node to be removed.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
palResult palListRemove(palListNode** ppList, palListNode* pNode);

/// @brief Get the head of the list.
///
/// @param pList The list handle.
/// @param ppHead The head of the list.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
/// @note The head is the first node in the list.
palResult palListGetHead(palListNode* pList, palListNode** ppHead);

/// @brief Get the tail of the list.
///
/// @param pList The list handle.
/// @param ppTail The tail of the list.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
/// @note The tail is the last node in the list.
palResult palListGetTail(palListNode* pList, palListNode** ppTail);

/// @brief Get the size of the list.
///
/// @param pList The list handle.
/// @param pSize The size of the list.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*.
/// @note The size is the number of nodes in the list.
palResult palListGetSize(palListNode* pList, palUint64* pSize);

#endif // PAL_LIST_H_