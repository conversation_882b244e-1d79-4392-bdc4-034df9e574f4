/*
 ***********************************************************************************************************************
 *
 *  Copyright (c) 2017-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 *
 **********************************************************************************************************************/

#include "pal_assert.h"

#if ENABLE_PRINTS_ASSERTS

/// Defines the format of the global assertion control table (g_assertCatTable).
typedef struct ipalAssertTableEntry_st
{
    palBool        enable;   ///< Enable/disable this assertion category.
    const palInt8* pString;  ///< Assertion category name.
} ipalAssertTableEntry;

// Table of default values for each assertion category. This cannot be static since it is used by the PAL_ASSERT and
// PAL_ALERT macros.
ipalAssertTableEntry g_assertCatTable[PAL_ASSERT_CAT_COUNT] =
{
    // Debug breaks triggered by asserts/alerts are always disabled by default in non-debug builds.
#if ENABLE_DEBUG_BREAK
    { PAL_TRUE,  "Assert" },
#else
    { PAL_FALSE, "Assert" },
#endif
    { PAL_FALSE, "Alert"  },
};

// =====================================================================================================================
// Enables/disables the specified assert category.  Controlled by a setting and set during initialization.
void palAssertEnableAssertMode(
    palAssertCategory category, // Assert category to control (i.e., assert or alert).
    palBool           enable)   // Either enables or disables the specified assert category.
{
    PAL_ASSERT(category < PAL_ASSERT_CAT_COUNT);

    g_assertCatTable[category].enable = enable;
}

// =====================================================================================================================
// Returns true if the specified assert category is currently enabled.
bool palAssertIsCategoryEnabled(
    palAssertCategory category) // Assert category to check
{
    PAL_ASSERT(category < PAL_ASSERT_CAT_COUNT);

    return g_assertCatTable[category].enable;
}

#endif // ENABLE_PRINTS_ASSERTS