#include "pal_assert.h"
#include "pal_hashmap.h"

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

/// @brief Free the key if it is owned by the hashmap.
///
/// @param hashType The hash type.
/// @param pEntry The hash entry to free.
static void ipalHashMapFreeKeyIfOwned(palHashType hashType, palHashEntry* pEntry);

/// @brief Resize the hashmap to a new capacity.
///
/// @param pHashMap The hashmap to resize.
/// @param newCapacity The new capacity.
/// @return PAL_SUCCESS if successful, otherwise an error code.
/// @note This function will rehash all existing entries to the new capacity.
static palResult ipalHashMapResize(palHashMap* pHashMap, size_t newCapacity);

/// @brief Shrink the hashmap to a smaller capacity.
/// @param pHashMap The hashmap to shrink.
/// @return PAL_SUCCESS if successful, otherwise an error code.
/// @note This function will rehash all existing entries to the new capacity.
static palResult ipalHashMapShrink(palHashMap* pHashMap);

// =====================================================================================================================
static void ipalHashMapFreeKeyIfOwned(palHashType hashType, palHashEntry* pEntry)
{
    if (pEntry->isOwnedKey == PAL_TRUE)
    {
        free(pEntry->key.stringKey);
    }
    else if (hashType == PAL_HASH_TYPE_BINARY)
    {
        free(pEntry->key.binaryKey.pData);
        pEntry->key.binaryKey.length = 0;
    }

    pEntry->isOwnedKey = PAL_FALSE;
}

// =====================================================================================================================
palResult ipalHashMapResize(palHashMap* pHashMap, size_t newCapacity)
{
    palResult     result      = PAL_SUCCESS;
    palHashEntry* pNewEntries = NULL;
    size_t        newIndex    = 0;

    if (newCapacity == 0)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    pNewEntries = (palHashEntry*)malloc(newCapacity * sizeof(palHashEntry));
    if (pNewEntries == NULL)
    {
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    memset(pNewEntries, 0, newCapacity * sizeof(palHashEntry));

    for (size_t i = 0; i < pHashMap->capacity; ++i)
    {
        if (pHashMap->pEntries[i].isOccupied == PAL_TRUE)
        {
            // Rehash the entry to the new capacity
            size_t newIndex = palHashComputeHashKey(pHashMap->hashType, &pHashMap->pEntries[i].key) % newCapacity;
            while (pNewEntries[newIndex].isOccupied == PAL_TRUE)
            {
                // Linear probing to find the next available slot
                newIndex = (newIndex + 1) % newCapacity;
            }

            // Copy the entry to the new location
            result = palHashCopyKey(pHashMap->hashType, &pNewEntries[newIndex].key, &pHashMap->pEntries[i].key);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to copy key during resize.");
                free(pNewEntries);
                return result;
            }

            pNewEntries[newIndex].isOwnedKey = pHashMap->pEntries[i].isOwnedKey;
            pNewEntries[newIndex].value      = pHashMap->pEntries[i].value;
            pNewEntries[newIndex].isOccupied = PAL_TRUE;
        }
    }

    free(pHashMap->pEntries);
    pHashMap->pEntries = pNewEntries;
    pHashMap->capacity = newCapacity;

    return result;
}

// =====================================================================================================================
palResult ipalHashMapShrink(palHashMap* pHashMap)
{
    palResult result = PAL_SUCCESS;

    if (pHashMap->size < (pHashMap->capacity / 4))
    {
        size_t newCapacity = pHashMap->capacity / 2;
        result = ipalHashMapResize(pHashMap, newCapacity);
    }

    return result;
}

// =====================================================================================================================
palResult palHashMapCreate(palHashMap** ppHashMap, palHashType hashType, palUint64 capacity)
{
    palResult result     = PAL_SUCCESS;
    palHashMap* pHashMap = NULL;

    if ((ppHashMap == NULL) || (capacity == 0))
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    pHashMap = (palHashMap*)malloc(sizeof(palHashMap));
    if (pHashMap == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for hashmap.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    memset(pHashMap, 0, sizeof(palHashMap));

    result = palHashMapInitialize(pHashMap, hashType, capacity);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize hashmap.");
        free(pHashMap);
        return result;
    }

    *ppHashMap = pHashMap;

    return result;
}

// =====================================================================================================================
palResult palHashMapInitialize(palHashMap* pHashMap, palHashType hashType, palUint64 capacity)
{
    palResult result = PAL_SUCCESS;

    if (pHashMap == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    pHashMap->hashType = hashType;
    pHashMap->capacity = capacity;
    pHashMap->size     = 0;
    pHashMap->loadFactorThreshold = 0.75f;

    pHashMap->pEntries = (palHashEntry*)malloc(capacity * sizeof(palHashEntry));
    if (pHashMap->pEntries == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for hashmap entries.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    memset(pHashMap->pEntries, 0, capacity * sizeof(palHashEntry));

    return result;
}

// =====================================================================================================================
void palHashMapDeinitialize(palHashMap* pHashMap)
{
    if (pHashMap == NULL)
    {
        return;
    }

    for (size_t i = 0; i < pHashMap->capacity; ++i)
    {
        if (pHashMap->pEntries[i].isOccupied == PAL_TRUE)
        {
            // Free the key if it is owned by the hashmap
            ipalHashMapFreeKeyIfOwned(pHashMap->hashType, &pHashMap->pEntries[i]);
        }
    }

    free(pHashMap->pEntries);
    pHashMap->pEntries = NULL;
}

// =====================================================================================================================
void palHashMapDestroy(palHashMap* pHashMap)
{
    if (pHashMap == NULL)
    {
        return;
    }

    palHashMapDeinitialize(pHashMap);
    free(pHashMap);
}

// =====================================================================================================================
palResult palHashMapInsert(palHashMap* pHashMap, const palHashKeyType key, palHashMapValueType value, palBool ownsKey)
{
    palResult result     = PAL_SUCCESS;
    size_t index         = 0;
    size_t originalIndex = 0;

    if (pHashMap == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the hashmap needs to be resized.
    if (pHashMap->size >= (pHashMap->capacity * pHashMap->loadFactorThreshold))
    {
        result = ipalHashMapResize(pHashMap, pHashMap->capacity * 2);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to resize hashmap.");
            return result;
        }
    }

    // Check if the key already exists
    index         = palHashComputeHashKey(pHashMap->hashType, &key) % pHashMap->capacity;
    originalIndex = index;
    while (pHashMap->pEntries[index].isOccupied == PAL_TRUE)
    {
        // Check if the key matches the existing entry
        if (palHashKeysEqual(pHashMap->hashType, &pHashMap->pEntries[index].key, &key) == PAL_TRUE)
        {
            // Key matches, update value and ownership if needed
            ipalHashMapFreeKeyIfOwned(pHashMap->hashType, &pHashMap->pEntries[index]);
            result = palHashCopyKey(pHashMap->hashType, &pHashMap->pEntries[index].key, &key);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to copy key.");
                return result;
            }

            // Update the value and ownership
            pHashMap->pEntries[index].value      = value;
            pHashMap->pEntries[index].isOwnedKey = ownsKey;
            return PAL_SUCCESS;
        }

        // If the key does not match, continue searching
        // Move to the next slot (linear probing)
        index = (index + 1) % pHashMap->capacity;

        // If we have looped back to the original index, the hashmap is full.
        if (index == originalIndex)
        {
            PAL_DBG_PRINTF_ERROR("Hashmap is full, cannot insert new entry.");
            return PAL_ERROR_OUT_OF_MEMORY;
        }
    }

    // Insert the new entry
    result = palHashCopyKey(pHashMap->hashType, &pHashMap->pEntries[index].key, &key);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to copy key.");
        return result;
    }

    // Set the value and ownership.
    pHashMap->pEntries[index].value      = value;
    pHashMap->pEntries[index].isOwnedKey = ownsKey;
    pHashMap->pEntries[index].isOccupied = PAL_TRUE;

    pHashMap->size++;

    return result;
}

// =====================================================================================================================
void palHashMapErase(palHashMap* pHashMap, const palHashKeyType key)
{
    palUint32 index         = 0;
    size_t    originalIndex = 0;

    if (pHashMap == NULL)
    {
        return;
    }

    index         = palHashComputeHashKey(pHashMap->hashType, &key) % pHashMap->capacity;
    originalIndex = index;
    while (pHashMap->pEntries[index].isOccupied == PAL_TRUE)
    {
        // Check if the key matches the existing entry
        if (palHashKeysEqual(pHashMap->hashType, &pHashMap->pEntries[index].key, &key) == PAL_TRUE)
        {
            // Free the key if it is owned by the hashmap
            ipalHashMapFreeKeyIfOwned(pHashMap->hashType, &pHashMap->pEntries[index]);
            pHashMap->pEntries[index].isOccupied = PAL_FALSE;
            pHashMap->size--;
            return;
        }

        // Move to the next index (wrap around if necessary)
        index = (index + 1) % pHashMap->capacity;

        // Stop if we've looped back to the original index
        if (index == originalIndex)
        {
            break;
        }
    }
}

// =====================================================================================================================
void palHashMapClear(palHashMap* pHashMap)
{
    if (pHashMap == NULL)
    {
        return;
    }

    for (size_t i = 0; i < pHashMap->capacity; ++i)
    {
        if (pHashMap->pEntries[i].isOccupied == PAL_TRUE)
        {
            // Free the key if it is owned by the hashmap
            ipalHashMapFreeKeyIfOwned(pHashMap->hashType, &pHashMap->pEntries[i]);
            pHashMap->pEntries[i].isOccupied = PAL_FALSE;
            pHashMap->size--;
        }
    }
}

// =====================================================================================================================
palHashEntry* palHashMapFind(palHashMap* pHashMap, const palHashKeyType key, palHashMapValueType* pValue)
{
    palUint32 index         = 0;
    palUint32 originalIndex = 0;

    if (pHashMap == NULL)
    {
        return NULL;
    }

    index         = palHashComputeHashKey(pHashMap->hashType, &key) % pHashMap->capacity;
    originalIndex = index;
    while (pHashMap->pEntries[index].isOccupied == PAL_TRUE)
    {
        // Check if the current slot contains the desired key
        if (palHashKeysEqual(pHashMap->hashType, &pHashMap->pEntries[index].key, &key) == PAL_TRUE)
        {
            *pValue = pHashMap->pEntries[index].value;
            return &pHashMap->pEntries[index];
        }

        // Move to the next slot (linear probing)
        index = (index + 1) % pHashMap->capacity;

        // Stop if we've looped back to the starting index
        if (index == originalIndex)
        {
            *pValue = NULL;
            break;
        }
    }

    // Key not found.
    return NULL;
}

// =====================================================================================================================
palUint64 palHashMapGetSize(palHashMap* pHashMap)
{
    if (pHashMap == NULL)
    {
        return 0;
    }

    return pHashMap->size;
}

// =====================================================================================================================
palBool palHashMapIsEmpty(palHashMap* pHashMap)
{
    if (pHashMap == NULL)
    {
        return PAL_TRUE;
    }

    return (pHashMap->size == 0) ? PAL_TRUE : PAL_FALSE;
}

// =====================================================================================================================
void palHashMapTraverse(palHashMap* pHashMap, palHashMapUserFunc userFunc)
{
    if (pHashMap == NULL)
    {
        return;
    }

    for (size_t i = 0; i < pHashMap->capacity; ++i)
    {
        if (pHashMap->pEntries[i].isOccupied == PAL_TRUE)
        {
            userFunc(pHashMap->pEntries[i].key, pHashMap->pEntries[i].value);
        }
    }
}

// =====================================================================================================================
palResult palHashMapSetValue(palHashMap* pHashMap, const palHashKeyType key, palHashMapValueType value)
{
    palResult result        = PAL_SUCCESS;
    palUint32 index         = 0;
    palUint32 originalIndex = 0;
    palBool   found         = PAL_FALSE;

    if (pHashMap == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    index = palHashComputeHashKey(pHashMap->hashType, &key) % pHashMap->capacity;
    originalIndex = index;
    while (pHashMap->pEntries[index].isOccupied == PAL_TRUE)
    {
        // Check if the key matches the existing key
        if (palHashKeysEqual(pHashMap->hashType, &pHashMap->pEntries[index].key, &key) == PAL_TRUE)
        {
            pHashMap->pEntries[index].value = value;
            found = PAL_TRUE;
            break;
        }

        // Move to the next slot (linear probing)
        index = (index + 1) % pHashMap->capacity;

        // If we've looped back to the original index, the hashmap is full
        if (index == originalIndex)
        {
            return PAL_ERROR_OUT_OF_MEMORY; // Hashmap is full, cannot insert
        }
    }

    if (found == PAL_FALSE)
    {
        // Insert the new key-value pair
        result = palHashCopyKey(pHashMap->hashType, &pHashMap->pEntries[index].key, &key);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to copy key.");
            return result;
        }

        pHashMap->pEntries[index].value      = value;
        pHashMap->pEntries[index].isOwnedKey = pHashMap->hashType == PAL_HASH_TYPE_STRING ? PAL_TRUE : PAL_FALSE;
        pHashMap->pEntries[index].isOccupied = PAL_TRUE;
        pHashMap->size++;
    }

    return result;
}