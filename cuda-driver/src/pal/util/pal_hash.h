#ifndef PAL_HASH_H_
#define PAL_HASH_H_

#include "pal.h"
#include "pal_types.h"

/// @brief FNV-1a hash algorithm for 32-bit and 64-bit.
///
/// @param pData The data to hash.
/// @param size The size of the data.
/// @return The computed hash value.
palUint32 palHashFnv1a32(const void* pData, size_t size);

/// @brief FNV-1a hash algorithm for 64-bit.
/// @param pData The data to hash.
/// @param size The size of the data.
/// @return The computed hash value.
palUint64 palHashFnv1a64(const void* pData, size_t size);

typedef enum palHashType_enum
{
    PAL_HASH_TYPE_STRING  = 0x0,
    PAL_HASH_TYPE_INTEGER = 0x1,
    PAL_HASH_TYPE_BINARY  = 0x2,
    PAL_HASH_TYPE_MAX
} palHashType;

typedef struct palHashBinaryKeyType_st
{
    void*     pData;
    palUint64 length;
} palHashBinaryKeyType;

typedef union palHashKeyType_union
{
    palInt8*             stringKey;
    palUint64            integerKey;
    palHashBinaryKeyType binaryKey;
} palHashKeyType;

/// @brief Computes the hash key for the given key type.
///
/// @param hashType The type of the hash.
/// @param pKey The key to hash.
/// @return The computed hash key.
/// @note The caller is responsible for ensuring that the key is valid for the specified hash type.
palUint32 palHashComputeHashKey(palHashType hashType, const palHashKeyType* pKey);

/// @brief Compares two hash keys for equality.
///
/// @param hashType The type of the hash.
/// @param pKeyA The first key to compare.
/// @param pKeyB The second key to compare.
/// @return True if the keys match, false otherwise.
/// @note The caller is responsible for ensuring that the keys are valid for the specified hash type.
///       The comparison is based on the type of the hash and the data contained in the keys.
///       For string keys, the comparison is done using strcmp.
///       For integer keys, the comparison is done using the integer value.
///       For binary keys, the comparison is done using memcmp.
///       The caller should ensure that the keys are of the same type before calling this function.
palBool palHashKeysEqual(palHashType hashType, const palHashKeyType* pKeyA, const palHashKeyType* pKeyB);

/// @brief Computes the hash key for the given key type.
///
/// @param hashType The type of the hash.
/// @param pDest The destination key.
/// @param pSrc The source key.
/// @return Returns PAL_SUCCESS if the key was copied successfully, otherwise an error code.
/// @note The destination key must be allocated and have enough space to hold the source key.
/// @note The caller is responsible for ensuring that the keys are valid for the specified hash type.
///       The function copies the key data from the source to the destination.
palResult palHashCopyKey(palHashType hashType, palHashKeyType* pDest, const palHashKeyType* pSrc);

#endif // PAL_HASH_H_