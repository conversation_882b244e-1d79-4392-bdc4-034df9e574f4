#ifndef PAL_RBTREE_H_
#define PAL_RBTREE_H_

#include "pal.h"
#include "pal_structures.h"
#include "pal_types.h"

typedef void* palRbTreeKeyType;
typedef void* palRbTreeValueType;

typedef palInt32 (*palRbTreeKeyCompareFunc)(palRbTreeKeyType keyA, palRbTreeKeyType keyB);
typedef void (*palRbTreeUserFunc)(palRbTreeNode* pNode, void* pArg);

/// The enum of the red-black tree node color.
typedef enum palRbTreeColorType_enum
{
    PAL_RBTREE_COLOR_TYPE_RED,
    PAL_RBTREE_COLOR_TYPE_BLACK
} palRbTreeColorType;

/// The structure of the red-black tree node.
struct palRbTreeNode_st
{
    /// The key and value of the node.
    palRbTreeKeyType key;

    /// The value of the node.
    palRbTreeValueType value;

    /// The color of the node.
    palRbTreeColorType color;

    /// The left and right child of the node.
    palRbTreeNode* pLeft;

    /// The right child of the node.
    palRbTreeNode* pRight;

    /// The parent of the node.
    palRbTreeNode* pParent;
};

/// The structure of the red-black tree.
struct palRbTree_st
{
    /// The root of the tree.
    palRbTreeNode* pRoot;

    /// The nil node of the tree.
    palRbTreeNode* pNil;

    /// The number of nodes in the tree.
    palRbTreeKeyCompareFunc compareKeyFunc;

    /// The number of nodes in the tree.
    palUint64 count;
};

/// @brief Create new tree with given key compare function.
/// @param ppRbTree The created tree.
/// @param func The key compare function.
/// @return Return PAL_SUCCESS if the tree was created successfully, otherwise an error code.
/// @note The caller is responsible for destroying the tree using palRbTreeDestroy().
/// @note The tree is initialized with a nil node, which is used as a sentinel for the tree.
palResult palRbTreeCreate(palRbTree** ppRbTree, palRbTreeKeyCompareFunc func);

/// @brief Initialize a new tree (Created by caller) with given key compare function.
/// @param pRbTree The rbtree to initialize.
/// @param func The key compare function.
/// @return Return PAL_SUCCESS if the tree was initialized successfully, otherwise an error code.
/// @note The caller is responsible for destroying the tree using palRbTreeDeinitialize().
/// @note The tree is initialized with a nil node, which is used as a sentinel for the tree.
palResult palRbTreeInitialize(palRbTree* pRbTree, palRbTreeKeyCompareFunc func);

/// @brief Deinitialize the tree.
/// @param pRbTree The rbtree to deinitialize.
void palRbTreeDeinitialize(palRbTree* pRbTree);

/// @brief Destroy all tree nodes and delete the rbtree handle.
/// @param pRbTree The rbtree to destroy.
void palRbTreeDestroy(palRbTree* pRbTree);

/// @brief Clean up all nodes from the tree.
/// @param pRbTree The rbtree to clear.
/// @note The tree is not destroyed, only the nodes are deleted.
void palRbTreeClear(palRbTree* pRbTree);

/// @brief Insert a pair with key and value into the tree.
/// @param pRbTree The rbtree to insert into.
/// @param key The key to insert.
/// @param value The value to insert.
/// @note If the key already exists, the value is updated.
/// @return Return PAL_SUCCESS if the key was inserted successfully, otherwise an error code.
/// @note The tree is balanced after the insertion.
palResult palRbTreeInsert(palRbTree* pRbTree, const palRbTreeKeyType key, const palRbTreeValueType value);

/// @brief Find key in the tree. Return end() node if not found.
/// @param pRbTree The rbtree to search.
/// @param key The key to search for.
/// @return Return the node with the key if found, otherwise return the end() node.
/// @note The end() node is a sentinel node that indicates the end of the tree.
palRbTreeNode* palRbTreeSearch(palRbTree* pRbTree, const palRbTreeKeyType key);

/// @brief Find the greater equal node of rb-tree.
/// @param pRbTree The rbtree to search.
/// @param key The key to search for.
/// @return Return the greater equal node with the key, otherwise return the end() node.
/// @note The end() node is a sentinel node that indicates the end of the tree.
palRbTreeNode* palRbTreeFindGreaterEqual(palRbTree* pRbTree, const palRbTreeKeyType key);

/// @brief Find the less equal node of rb-tree.
/// @param pRbTree The rbtree to search.
/// @param key The key to search for.
/// @return Return the less equal node with the key, otherwise return the end() node.
/// @note The end() node is a sentinel node that indicates the end of the tree.
palRbTreeNode* palRbTreeFindLessEqual(palRbTree* pRbTree, const palRbTreeKeyType key);

/// @brief Return first (smallest) node in the tree.
/// @param pRbTree The rbtree to search.
/// @return Return the first node in the tree.
palRbTreeNode* palRbTreeGetFirst(palRbTree* pRbTree);

/// @brief Return last node in the tree.
/// @param pRbTree The rbtree to search.
/// @return Return the last node in the tree.
/// @note The last node is the largest node in the tree.
palRbTreeNode* palRbTreeGetLast(palRbTree* pRbTree);

/// @brief Return the end() node in the tree.
/// @param pRbTree The rbtree to search.
/// @return Return the end() node in the tree.
/// @note The end() node is a sentinel node that indicates the end of the tree.
palRbTreeNode* palRbTreeGetEnd(palRbTree* pRbTree);

/// @brief Returns previous smaller node in the tree.
/// @param pRbTree The rbtree to search.
/// @param pNode The node to search.
/// @return Return the previous smaller node in the tree.
palRbTreeNode* palRbTreeGetPrev(palRbTree* pRbTree, palRbTreeNode* pNode);

/// @brief Returns next node in the tree.
/// @param pRbTree The rbtree to search.
/// @param pNode The node to search.
/// @return Return the next node in the tree.
palRbTreeNode* palRbTreeGetNext(palRbTree* pRbTree, palRbTreeNode* pNode);

/// @brief Iterator points to the first element that is not less than the key.
/// @param pRbTree The rbtree to search.
/// @param key The key to search for.
/// @return Return the first element that is not less than the key.
/// @note If no such element was found, the past-the-end iterator end() will be returned.
palRbTreeNode* palRbTreeLowerBound(palRbTree* pRbTree, palRbTreeKeyType key);

/// @brief Iterator points to the first element that is greater than the key.
/// @param pRbTree The rbtree to search.
/// @param key The key to search for.
/// @return Return the first element that is greater than the key.
/// @note If no such element was found, the past-the-end iterator end() will be returned.
palRbTreeNode* palRbTreeUpperBound(palRbTree* pRbTree, palRbTreeKeyType key);

/// @brief Delete the corresponding node of key from tree.
/// @param pRbTree The rbtree to delete from.
/// @param pToDelete The delete node.
/// @return Return the next node.
/// @note The tree is balanced after the deletion.
/// @note The deleted node is destroyed.
palRbTreeNode* palRbTreeDelete(palRbTree* pRbTree, palRbTreeNode* pToDelete);

/// @brief Erase the node with the given key from the tree.
/// @param pRbTree The rbtree to erase from.
/// @param key The key to erase.
/// @return Return PAL_SUCCESS if the key was erased successfully, otherwise an error code.
palResult palRbTreeErase(palRbTree* pRbTree, palRbTreeKeyType key);

/// @brief Get the size of nodes in the tree.
/// @param pRbTree The rbtree to get the size from.
/// @return Return the size of nodes in the tree.
palUint64 palRbTreeGetSize(palRbTree* pRbTree);

/// @brief The in order traversal internal function for removing all elements.
/// @param pRbTree The rbtree to traverse.
/// @param func The user function to call for each node.
/// @param pArg The argument to pass to the user function.
/// @note The user function is called for each node in the tree.
void palRbTreeTraverseInOrder(palRbTree* pRbTree, palRbTreeUserFunc func, void* pArg);

/// @brief The post order traversal internal function for removing all elements.
/// @param pRbTree The rbtree to traverse.
/// @param func The user function to call for each node.
/// @param pArg The argument to pass to the user function.
/// @note The user function is called for each node in the tree.
void palRbTreeTraversePostOrder(palRbTree* pRbTree, palRbTreeUserFunc func, void* pArg);

// Call with node=variable of struct* with palRbTreeNode* as first elements,
// with type is the type of a pointer to that struct, the variable rbtree is
// the corresponding pointer to the red-black tree.
#define PAL_RBTREE_FOR(node, type, rbtree)                \
    for (node=(type)palRbTreeGetFirst(rbtree);            \
         (palRbTreeNode*)node != palRbTreeGetEnd(rbtree); \
         node = (type)palRbTreeGetNext(rbtree, (palRbTreeNode*)node))

#endif // PAL_RBTREE_H_