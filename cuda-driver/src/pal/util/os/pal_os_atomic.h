#ifndef PAL_OS_ATOMIC_H_
#define PAL_OS_ATOMIC_H_

#include <stdatomic.h>
#include <stdio.h>
#include <stdlib.h>

#include "pal_types.h"

// Load operations
// =====================================================================================================================
static inline palUint32 palOsAtomicLoadSeqCst32(volatile const _Atomic palUint32* ptr)
{
    return atomic_load_explicit(ptr, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint32 palOsAtomicLoadAcquire32(volatile const _Atomic palUint32* ptr)
{
    return atomic_load_explicit(ptr, __ATOMIC_ACQUIRE);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicLoadSeqCst64(volatile const _Atomic palUint64* ptr)
{
    return atomic_load_explicit(ptr, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicLoadAcquire64(volatile const _Atomic palUint64* ptr)
{
    return atomic_load_explicit(ptr, __ATOMIC_ACQUIRE);
}

// Store operations
// =====================================================================================================================
static inline void palOsAtomicStoreSeqCst32(volatile _Atomic palUint32* ptr, const palUint32 val)
{
    atomic_store_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline void palOsAtomicStoreRelease32(volatile _Atomic palUint32* ptr, const palUint32 val)
{
    atomic_store_explicit(ptr, val, __ATOMIC_RELEASE);
}

// =====================================================================================================================
static inline void palOsAtomicStoreSeqCst64(volatile _Atomic palUint64* ptr, const palUint64 val)
{
    atomic_store_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline void palOsAtomicStoreRelease64(volatile _Atomic palUint64* ptr, const palUint64 val)
{
    atomic_store_explicit(ptr, val, __ATOMIC_RELEASE);
}

// EXCHANGE operations
// Returns old value
// =====================================================================================================================
static inline palUint32 palOsAtomicExchangeSeqCst32(volatile _Atomic palUint32* ptr, const palUint32 val)
{
    return atomic_exchange_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint32 palOsAtomicExchangeAcqRel32(volatile _Atomic palUint32* ptr, const palUint32 val)
{
    return atomic_exchange_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicExchangeSeqCst64(volatile _Atomic palUint64* ptr, const palUint64 val)
{
    return atomic_exchange_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicExchangeAcqRel64(volatile _Atomic palUint64* ptr, const palUint64 val)
{
    return atomic_exchange_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

// WEAK CAS operations: use in loops
// Returns whether CAS succeeded
// May spuriously fail: http://en.cppreference.com/w/c/atomic/atomic_compare_exchange
// The memory ordering on the CAS failure is relaxed.
// =====================================================================================================================
static inline palInt32 palOsAtomicCompareExchangeWeakSeqCst32(volatile _Atomic palUint32* ptr, palUint32* pExpected,
                                                              const palUint32 desired)
{
    return atomic_compare_exchange_weak_explicit(ptr, pExpected, desired, __ATOMIC_SEQ_CST, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palInt32 palOsAtomicCompareExchangeWeakAcqRel32(volatile _Atomic palUint32* ptr, palUint32* pExpected,
                                                              const palUint32 desired)
{
    return atomic_compare_exchange_weak_explicit(ptr, pExpected, desired, __ATOMIC_ACQ_REL, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palInt64 palOsAtomicCompareExchangeWeakSeqCst64(volatile _Atomic palUint64* ptr, palUint64* pExpected,
                                                              const palUint64 desired)
{
    return atomic_compare_exchange_weak_explicit(ptr, pExpected, desired, __ATOMIC_SEQ_CST, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palInt64 palOsAtomicCompareExchangeWeakAcqRel64(volatile _Atomic palUint64* ptr, palUint64* pExpected,
                                                              const palUint64 desired)
{
    return atomic_compare_exchange_weak_explicit(ptr, pExpected, desired, __ATOMIC_ACQ_REL, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palInt32 palOsAtomicCompareExchangeStrongSeqCst32(volatile _Atomic palUint32* ptr, palUint32* pExpected,
                                                                const palUint32 desired)
{
    return atomic_compare_exchange_strong_explicit(ptr, pExpected, desired, __ATOMIC_SEQ_CST, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palInt32 palOsAtomicCompareExchangeStrongAcqRel32(volatile _Atomic palUint32* ptr, palUint32* pExpected,
                                                                const palUint32 desired)
{
    return atomic_compare_exchange_strong_explicit(ptr, pExpected, desired, __ATOMIC_ACQ_REL, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palInt64 palOsAtomicCompareExchangeStrongSeqCst64(volatile _Atomic palUint64* ptr, palUint64* pExpected,
                                                                const palUint64 desired)
{
    return atomic_compare_exchange_strong_explicit(ptr, pExpected, desired, __ATOMIC_SEQ_CST, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palInt64 palOsAtomicCompareExchangeStrongAcqRel64(volatile _Atomic palUint64* ptr, palUint64* pExpected,
                                                                const palUint64 desired)
{
    return atomic_compare_exchange_strong_explicit(ptr, pExpected, desired, __ATOMIC_ACQ_REL, __ATOMIC_ACQ_REL);
}

// INCREMENT operations
// Return old value
// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndIncrementSeqCst32(volatile _Atomic palUint32* ptr)
{
    return atomic_fetch_add_explicit(ptr, 1, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndIncrementAcqRel32(volatile _Atomic palUint32* ptr)
{
    return atomic_fetch_add_explicit(ptr, 1, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndIncrementSeqCst64(volatile _Atomic palUint64* ptr)
{
    return atomic_fetch_add_explicit(ptr, 1, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndIncrementAcqRel64(volatile _Atomic palUint64* ptr)
{
    return atomic_fetch_add_explicit(ptr, 1, __ATOMIC_ACQ_REL);
}

// DECREMENT operations
// Return old value
// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndDecrementSeqCst32(volatile _Atomic palUint32* ptr)
{
    return atomic_fetch_sub_explicit(ptr, 1, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndDecrementAcqRel32(volatile _Atomic palUint32* ptr)
{
    return atomic_fetch_sub_explicit(ptr, 1, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndDecrementSeqCst64(volatile _Atomic palUint64* ptr)
{
    return atomic_fetch_sub_explicit(ptr, 1, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndDecrementAcqRel64(volatile _Atomic palUint64* ptr)
{
    return atomic_fetch_sub_explicit(ptr, 1, __ATOMIC_ACQ_REL);
}

// Add operations
// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndAddSeqCst32(volatile _Atomic palUint32* ptr, palUint32 val)
{
    return atomic_fetch_add_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndAddAcqRel32(volatile _Atomic palUint32* ptr, palUint32 val)
{
    return atomic_fetch_add_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndAddSeqCst64(volatile _Atomic palUint64* ptr, palUint64 val)
{
    return atomic_fetch_add_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndAddAcqRel64(volatile _Atomic palUint64* ptr, palUint64 val)
{
    return atomic_fetch_add_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

// Sub operations
// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndSubSeqCst32(volatile _Atomic palUint32* ptr, palUint32 val)
{
    return atomic_fetch_sub_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndSubAcqRel32(volatile _Atomic palUint32* ptr, palUint32 val)
{
    return atomic_fetch_sub_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndSubSeqCst64(volatile _Atomic palUint64* ptr, palUint64 val)
{
    return atomic_fetch_sub_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndSubAcqRel64(volatile _Atomic palUint64* ptr, palUint64 val)
{
    return atomic_fetch_sub_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

// Or operations
// Returns old value
// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndOrSeqCst32(volatile _Atomic palUint32* ptr, palUint32 val)
{
    return atomic_fetch_or_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint32 palOsAtomicFetchAndOAcqRel32(volatile _Atomic palUint32* ptr, palUint32 val)
{
    return atomic_fetch_or_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndOrSeqCst64(volatile _Atomic palUint64* ptr, palUint64 val)
{
    return atomic_fetch_or_explicit(ptr, val, __ATOMIC_SEQ_CST);
}

// =====================================================================================================================
static inline palUint64 palOsAtomicFetchAndOrAcqRel64(volatile _Atomic palUint64* ptr, palUint64 val)
{
    return atomic_fetch_or_explicit(ptr, val, __ATOMIC_ACQ_REL);
}

#endif // PAL_OS_ATOMIC_H_