#include <dlfcn.h>
#include <errno.h>
#include <fcntl.h>
#include <limits.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/utsname.h>
#include <time.h>
#include <unistd.h>

#include "pal_assert.h"

// =====================================================================================================================
// This function sets an environment variable.
palInt32 palOsEnvSet(const char* pName, const char* pValue)
{
    return setenv(pName, pValue, 1);
}

// =====================================================================================================================
// This function gets the value of an environment variable.
palInt32 palOsEnvGet(const char* pName, char* pValue, size_t size)
{
    palInt32    result    = 0;
    const char* pEnvValue = getenv(pName);

    if (pEnvValue != NULL)
    {
        result = snprintf(pValue, size, "%s", pEnvValue);
    }

    return result;
}

// =====================================================================================================================
// This function is a wrapper for the standard sscanf function.
palInt32 palOsSscanf(const char* pInput, const char* pFormat, ...)
{
    palInt32 result = 0;
    va_list args;

    va_start(args, pFormat);
    result = vsscanf(pInput, pFormat, args);
    va_end(args);

    return result;
}

// =====================================================================================================================
// This function is a wrapper for the standard snprintf function.
palInt32 palOsSnprintf(char* pOutput, size_t bufSize, const char* pFormat, ...)
{
    palInt32 result = 0;
    va_list  args;

    va_start(args, pFormat);
    result = vsnprintf(pOutput, bufSize, pFormat, args);
    va_end(args);

    return result;
}

// =====================================================================================================================
// This function is a wrapper for the standard vsnprintf function.
palInt32 palOsVsnprintf(char* pOutput, size_t bufSize, const char* pFormat, va_list argList)
{
    return vsnprintf(pOutput, bufSize, pFormat, argList);
}

// =====================================================================================================================
// Opens a file with read permissions.
// The file is opened in binary mode. The file is created if it does not exist.
// The file is truncated to zero length if it exists.
// The file is opened in read-write mode if the PAL_OS_OPEN_PLUS flag is set.
// The file is opened in append mode if the PAL_OS_OPEN_APPEND flag is set.
// The file is opened in write mode if the PAL_OS_OPEN_WRITE flag is set.
// The file is opened in read mode if the PAL_OS_OPEN_READ flag is set.
// The file is opened in read-write mode if the PAL_OS_OPEN_PLUS flag is set.
palOsResult palOsFileOpen(const palInt8* pFilePath, palUint64 flags, palOsFileHandle* pStream)
{
    palInt8 mode[4] = {'\0', '\0', '\0', '\0'};
    palInt32 i = 0;

    if (flags & PAL_OS_OPEN_WRITE)
    {
        mode[i++] = 'w';
    }

    if (flags & PAL_OS_OPEN_READ)
    {
        mode[i++] = 'r';
    }

    if (flags & PAL_OS_OPEN_APPEND)
    {
        mode[i++] = 'a';
    }

    mode[i++] = 'b';

    if (flags & PAL_OS_OPEN_PLUS)
    {
        mode[i++] = '+';
    }

    *pStream = fopen(pFilePath, mode);
    if (*pStream == NULL)
    {
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
// Closes a file.
void palOsFileClose(palOsFileHandle stream)
{
    fclose(stream);
}

// =====================================================================================================================
// Writes a string to a file.
palOsResult palOsFileWrite(palOsFileHandle stream, const void* ptr, size_t size)
{
    if (fwrite(ptr, sizeof(char), size, stream) != size)
    {
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
// Reads a string from a file.
palOsResult palOsFileRead(palOsFileHandle stream, void* ptr, size_t size, size_t* pBytes)
{
    size_t count;

    count = fread(ptr, sizeof(char), size, stream);

    if (pBytes != NULL)
    {
        *pBytes = count;
    }

    if (count == size)
    {
        return PAL_OS_SUCCESS;
    }
    else if (feof(stream) == EOF)
    {
        return PAL_OS_EOF;
    }

    return PAL_OS_ERROR;
}

// =====================================================================================================================
// Reads a line from a file.
palOsResult palOsFileReadLine(palOsFileHandle stream, void* ptr, size_t size, size_t* pBytes)
{
    palInt32 ret = PAL_OS_SUCCESS;
    palInt8* pBuffer = (palInt8*)ptr;
    size_t bytesRead = 0;
    palInt32 c;

    if (feof(stream) == EOF)
    {
        return PAL_OS_EOF;
    }

    while (bytesRead < size)
    {
        c = fgetc(stream);
        if (c == '\n')
        {
            ret = PAL_OS_SUCCESS;
            break;
        }

        if (c == EOF)
        {
            ret = PAL_OS_SUCCESS;
            if (ferror(stream) != 0)
            {
                ret = PAL_OS_ERROR;
            }
            break;
        }

        pBuffer[bytesRead++] = (palInt8)c;
    }

    const size_t end = ((bytesRead < size) ? bytesRead : (size - 1));
    pBuffer[end] = '\0';

    if (pBytes != NULL)
    {
        *pBytes = bytesRead;
    }

    return ret;
}

// =====================================================================================================================
// Reads a character from a file.
palOsResult palOsFileGetc(palOsFileHandle stream, palInt8* pCharacter)
{
    palInt32 tmp;

    tmp = fgetc(stream);
    if (feof(stream) || tmp == EOF)
    {
        return PAL_OS_EOF;
    }

    *pCharacter = (palInt8)tmp;

    if (ferror(stream) != 0)
    {
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
// Gets the size of a file.
palInt64 palOsFileGetSize(const palInt8* pFilePath)
{
    struct stat buffer;

    if (stat(pFilePath, &buffer) != 0)
    {
        return (palInt64)0;
    }

    return (palInt64)buffer.st_size;
}

// =====================================================================================================================
// Sets the file position indicator for the stream to the given offset.
palOsResult palOsFileSeek(palOsFileHandle stream, palUint64 offset, palOsSeekFlags whence)
{
    palInt32 seek;

    switch (whence)
    {
    case PAL_OS_SEEK_SET:
        seek = SEEK_SET;
        break;
    case PAL_OS_SEEK_CUR:
        seek = SEEK_END;
        break;
    default:
        return PAL_OS_ERROR;
    }


    fseek(stream, offset, seek);
    if (feof(stream) == EOF)
    {
        return PAL_OS_EOF;
    }
    else if (ferror(stream) != 0)
    {
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
// Gets the current file position indicator for the stream.
palOsResult palOsFileTell(palOsFileHandle stream, palUint64* pPosition)
{
    *pPosition = (palUint64)ftell(stream);

    if (ferror(stream) != 0)
    {
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
// Flushes the output buffer of the stream.
palOsResult palOsFileFlush(palOsFileHandle stream)
{
    if (fflush(stream) != 0)
    {
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palBool palOsFileExists(const palInt8* pFilePath)
{
    return (access(pFilePath, F_OK) == 0) ? PAL_TRUE : PAL_FALSE;
}

// =====================================================================================================================
palOsPid palOsGetProcessId(void)
{
    return (palOsPid)getpid();
}

// =====================================================================================================================
palThreadId palOsGetThreadId(void)
{
    return pthread_self();
}

// =====================================================================================================================
/// Simple wrapper for strtok_s or strtok_r which provides a safe version of strtok.
palInt8* palOsStrtok(palInt8* str, const palInt8* delim, palInt8** buf)
{
    palInt8* pToken = NULL;

    PAL_ASSERT((delim != NULL) && (buf != NULL));

    pToken = strtok_r(str, delim, buf);

    return pToken;
}

// =====================================================================================================================
/// Performs a safe strcpy by requiring the destination buffer size.
void palOsStrncpy(palInt8* pDst, const palInt8* pSrc, size_t dstSize)
{
    PAL_ASSERT(pDst != NULL);
    PAL_ASSERT(pSrc != NULL);
    PAL_ALERT(strlen(pSrc) >= dstSize);

    if (dstSize > 0)
    {
        strncpy(pDst, pSrc, (dstSize - 1));
        pDst[dstSize - 1] = '\0';
    }
}

// =====================================================================================================================
// Performs a safe strncasecmp by requiring the length of the strings to compare.
palInt32 palOsStrncasecmp(const palInt8* pStr1, const palInt8* pStr2, size_t n)
{
    PAL_ASSERT((pStr1 != NULL) && (pStr2 != NULL));
    return (palInt32)strncasecmp((const char*)pStr1, (const char*)pStr2, n);
}

// =====================================================================================================================
void palOsSleep(palUint32 msec)
{
    struct timespec timeReq = {0};
    struct timespec timeRem = {0};
    palInt32        ret     = 0;

    timeReq.tv_sec  = msec / 1000;
    timeReq.tv_nsec = (msec % 1000) * 1000000;

    ret = nanosleep(&timeReq, &timeRem);

    // If interrupted by a non-blocked signal copy remaining time to the requested time.
    while (ret != 0 && errno == EINTR)
    {
        timeReq = timeRem;
        ret = nanosleep(&timeReq, &timeRem);
    }
}

// =====================================================================================================================
static void palOsThreadRelease(palOsThread* pThread)
{
    if (palOsAtomicFetchAndDecrementSeqCst32((volatile _Atomic palUint32*)(&pThread->refCount)) == 1)
    {
        memset(pThread, 0, sizeof(palOsThread));
        free(pThread);
    }
}

// =====================================================================================================================
static void* palOsPosixThreadStartFunc(void* pData)
{
    palOsThread* pThread = (palOsThread*)pData;

    // Call the user-specified thread process function
    pThread->returnValue = pThread->palOsUserStartFunc(pThread->pUserArgs);

    // release the thread
    palOsThreadRelease(pThread);

    return NULL;
}

// =====================================================================================================================
palInt32 palOsThreadCreate(palOsThread** ppThread, palInt32 (*pUserStartFunc)(void*), void* pUserArgs)
{
    palOsThread* pThread = NULL;
    palInt32 result = 0;

    if (ppThread == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters with thread handle.");
        return PAL_OS_ERROR;
    }

    *ppThread = NULL;

    // create the thread data
    pThread = (palOsThread*)malloc(sizeof(palOsThread));
    if (pThread == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create the thread handle.");
        return PAL_OS_ERROR;
    }

    memset(pThread, 0, sizeof(palOsThread));
    pThread->palOsUserStartFunc = pUserStartFunc;
    pThread->pUserArgs = pUserArgs;
    pThread->returnValue = -1;

    // Start the reference count at 2
    // - The thread itself has to decrement it once it has exited
    // - The thread must either be detached or joined
    pThread->refCount = 2;

    // Spawn the thread
    result = pthread_create(&pThread->thread, NULL, palOsPosixThreadStartFunc, pThread);
    if (result != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_create failed with %d.", result);
        free(pThread);
        return PAL_OS_ERROR;
    }

    *ppThread = pThread;

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
void palOsThreadJoin(palOsThread* pThread, palInt32* pRetCode)
{
    palInt32 result = 0;
    void* pExitCode = NULL;

    result = pthread_join(pThread->thread, &pExitCode);
    if (result != 0)
    {
        PAL_DBG_PRINTF_WARN("pthread_join failed with %d.", result);
        PAL_ASSERT(0);
    }
    PAL_ASSERT(pExitCode == NULL);

    if (pRetCode != NULL)
    {
        *pRetCode = pThread->returnValue;
    }

    palOsThreadRelease(pThread);
}

// =====================================================================================================================
void palOsThreadDetach(palOsThread* pThread)
{
    palInt32 result = 0;

    result = pthread_detach(pThread->thread);
    if (result != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_detach failed with %d.", result);
        PAL_ASSERT(0);
    }

    palOsThreadRelease(pThread);
}

// =====================================================================================================================
palBool palOsThreadIsCurrent(palOsThread* pThread)
{
    return (pthread_self() == pThread->thread) ? PAL_TRUE : PAL_FALSE;
}

// =====================================================================================================================
palBool palOsThreadHasExited(palOsThread* pThread)
{
    return (pthread_kill(pThread->thread, 0) == ESRCH) ? PAL_TRUE : PAL_FALSE;
}

// =====================================================================================================================
void palOsThreadYield(void)
{
    sched_yield();
}

// =====================================================================================================================
palInt32 palOsThreadSetName(const palInt8* pName)
{
    return prctl(PR_SET_NAME, pName, 0, 0, 0);
}

// =====================================================================================================================
palInt32 palOsThreadGetName(const palInt8* pName, size_t len)
{
    return prctl(PR_GET_NAME, pName, 0, 0, 0);
}

// =====================================================================================================================
palOsTlsEntry palOsTlsCreate(void (*pDestroyFunc)(void*))
{
    palInt32 status;
    palOsTlsEntry tlsEntry;

    status = pthread_key_create(&tlsEntry, pDestroyFunc);
    if (status != 0)
    {
        return 0;
    }

    return tlsEntry + 1;
}

// =====================================================================================================================
void palOsTlsDestroy(palOsTlsEntry tlsEntry)
{
    pthread_key_delete(tlsEntry - 1);
}

// =====================================================================================================================
void* palOsTlsGetValue(palOsTlsEntry tlsEntry)
{
    return pthread_getspecific(tlsEntry - 1);
}

// =====================================================================================================================
palUint32 palOsTlsSetValue(palOsTlsEntry tlsEntry, void* pValue)
{
    return pthread_setspecific(tlsEntry - 1, pValue);
}

// =====================================================================================================================
static palInt32 palOsMutexInitWithSharedFlag(palOsMutex* pMutex, palInt32 flag)
{
    palInt32 error;
    pthread_mutexattr_t attr;

    error = pthread_mutexattr_init(&attr);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to execute pthread_mutexattr_init: %s.", strerror(error));
        return error;
    }

    error = pthread_mutexattr_settype(&attr, PTHREAD_MUTEX_RECURSIVE_NP);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to execute pthread_mutexattr_settype: %s.", strerror(error));
        return error;
    }

    error = pthread_mutexattr_setpshared(&attr, flag);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to execute pthread_mutexattr_setpshared: %s.", strerror(error));
        return error;
    }

    error = pthread_mutex_init(pMutex, &attr);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to execute pthread_mutex_init: %s.", strerror(error));
        return error;
    }

    error = pthread_mutexattr_destroy(&attr);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to execute pthread_mutex_destroy: %s.", strerror(error));
        return error;
    }

    return error;
}

// =====================================================================================================================
palInt32 palOsMutexInit(palOsMutex* pMutex)
{
    return palOsMutexInitWithSharedFlag(pMutex, PTHREAD_PROCESS_PRIVATE);
}

// =====================================================================================================================
palInt32 palOsMutexSharedInit(palOsMutex* pMutex)
{
    return palOsMutexInitWithSharedFlag(pMutex, PTHREAD_PROCESS_SHARED);
}

// =====================================================================================================================
void palOsMutexDestroy(palOsMutex* pMutex)
{
    palInt32 error;

    error = pthread_mutex_destroy(pMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_WARN("pthread_mutex_destroy: %s.", strerror(error));
    }
}

// =====================================================================================================================
void palOsMutexLock(palOsMutex* pMutex)
{
    palInt32 error;

    error = pthread_mutex_lock(pMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_WARN("pthread_mutex_lock: %s.", strerror(error));
    }
}

// =====================================================================================================================
palInt32 palOsMutexTryLock(palOsMutex* pMutex)
{
    palInt32 error;

    error = pthread_mutex_trylock(pMutex);
    switch (error)
    {
    case 0:
        return PAL_OS_SUCCESS;
    case EBUSY:
        return PAL_OS_TIMEOUT;
    default:
        PAL_DBG_PRINTF_ERROR("pthread_mutex_trylock: %s.", strerror(error));
        return PAL_OS_ERROR;
    }
}

// =====================================================================================================================
void palOsMutexUnlock(palOsMutex* pMutex)
{
    palInt32 error;

    error = pthread_mutex_unlock(pMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_WARN("pthread_mutex_unlock: %s.", strerror(error));
    }
}

// =====================================================================================================================
// Executes the provided callback once by only one thread. Alll threads which call this with the same onceControl
// parameter will block until the callback has been completed.
palInt32 palOsCallOnce(palOsOnceControl* pOnceControl, void (*pInitRoutine)(void))
{
    palInt32 error;

    error = pthread_once(pOnceControl, pInitRoutine);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_once failed: %d.", error);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
static palInt32 palOsCondInitWithSharedFlag(palOsConditionVariable* pCv, palInt32 sharedFlag)
{
    palInt32 error;
    pthread_condattr_t condAttr;

    pthread_condattr_init(&condAttr);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_condattr_init failed: %s (%d).", strerror(error), error);
        return PAL_OS_ERROR;
    }

    error = pthread_condattr_setpshared(&condAttr, sharedFlag);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_condattr_setpshared failed: %s (%d).", strerror(error), error);
        return PAL_OS_ERROR;
    }

    error = pthread_cond_init(pCv, &condAttr);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_cond_init failed: %s (%d).", strerror(error), error);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palInt32 palOsCondInit(palOsConditionVariable* pCv)
{
    return palOsCondInitWithSharedFlag(pCv, PTHREAD_PROCESS_PRIVATE);
}

// =====================================================================================================================
palInt32 palOsCondSharedInit(palOsConditionVariable* pCv)
{
    return palOsCondInitWithSharedFlag(pCv, PTHREAD_PROCESS_SHARED);
}

// =====================================================================================================================
palInt32 palOsCondWait(palOsConditionVariable* pCv, palOsMutex* pMutex)
{
    return pthread_cond_wait(pCv, pMutex);
}

// =====================================================================================================================
palInt32 palOsCondTimedWait(palOsConditionVariable* pCv, palOsMutex* pMutex, palUint32 timeoutMs)
{
    palInt32 error;

    // Compute the absolute time for the wait function
    struct timespec tp;
    struct timeval tv;

    if (timeoutMs == PAL_OS_INFINITE_TIMEOUT)
    {
        error = palOsCondWait(pCv, pMutex);
    }
    else
    {
        if (timeoutMs == 0)
        {
            // Don't call gettimeofdat if we don't need to
            memset(&tp, 0, sizeof(tp));
        }
        else
        {
            error = gettimeofday(&tv, NULL);
            if (error != 0)
            {
                PAL_DBG_PRINTF_ERROR("gettimeofday failed: %s (%d).", strerror(errno), errno);
                return PAL_OS_ERROR;
            }

            tp.tv_sec = tv.tv_sec + timeoutMs / 1000;
            tp.tv_nsec = tv.tv_usec * 1000 + (timeoutMs % 1000) * 1000000;
            tp.tv_sec += tp.tv_nsec / 1000000000;
            tp.tv_nsec %= 1000000000;
        }

        error = pthread_cond_timedwait(pCv, pMutex, &tp);
        if (error == ETIMEDOUT)
        {
            PAL_DBG_PRINTF_ERROR("pthread_cond_timedwait timed out: %s (%d).", strerror(error), error);
            return PAL_OS_TIMEOUT;
        }
    }

    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_cond_[timed] wait failed: %s (%d).", strerror(error), error);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palInt32 palOsCondSignal(palOsConditionVariable* pCv)
{
    palInt32 error;

    error = pthread_cond_signal(pCv);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_cond_signal failed: %s (%d).", strerror(error), error);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palInt32 palOsCondBroadcast(palOsConditionVariable* pCv)
{
    palInt32 error;

    error = pthread_cond_broadcast(pCv);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_cond_broadcast failed: %s (%d).", strerror(error), error);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palInt32 palOsCondDestroy(palOsConditionVariable* pCv)
{
    palInt32 error;

    error = pthread_cond_destroy(pCv);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("pthread_cond_destroy failed: %s (%d).", strerror(error), error);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palInt32 palOsSemaphoreInit(palOsSemaphore* pSemaphore, palInt32 value)
{
    palInt32 error;

    error = sem_init(pSemaphore, 0, value);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("sem_init failed: %s (%d).", strerror(errno), errno);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palInt32 palOsSemaphoreWait(palOsSemaphore* pSemaphore)
{
    palInt32 error;

    while (1)
    {
        error = sem_wait(pSemaphore);
        if (error == 0)
        {
            return PAL_OS_SUCCESS;
        }
        else if ((error == -1) && (errno == EINTR))
        {
            PAL_DBG_PRINTF_WARN("sem_wait returned EINTR, interrupted by a signal.");
            return PAL_OS_TIMEOUT;
        }
        else
        {
            PAL_DBG_PRINTF_ERROR("sem_wait failed: %s (%d).", strerror(errno), errno);
            return PAL_OS_ERROR;
        }
    }
}

// =====================================================================================================================
palInt32 palOsSemaphoreTimedWait(palOsSemaphore* pSemaphore, palUint32 timeoutMs)
{
    palInt32 ret;

    // Compute the absolute time for the wait function
    struct timespec tp;
    struct timeval tv;

    if (timeoutMs == PAL_OS_INFINITE_TIMEOUT)
    {
        ret = palOsSemaphoreWait(pSemaphore);
    }
    else if (timeoutMs == 0)
    {
        ret = sem_trywait(pSemaphore);
        if (ret == 0)
        {
            return PAL_OS_SUCCESS;
        }
        else if ((ret == -1) && (errno == EINTR))
        {
            PAL_DBG_PRINTF_WARN("sem_trywait returned EINTR, interrupted by a signal.");
            return PAL_OS_TIMEOUT;
        }
        else
        {
            PAL_DBG_PRINTF_ERROR("sem_trywait failed: %s (%d).", strerror(errno), errno);
            return PAL_OS_ERROR;
        }
    }

    ret = gettimeofday(&tv, NULL);
    if (ret != 0)
    {
        PAL_DBG_PRINTF_ERROR("gettimeofday failed: %s (%d).", strerror(errno), errno);
        return PAL_OS_ERROR;
    }

    tp.tv_sec = tv.tv_sec + timeoutMs / 1000;
    tp.tv_nsec = tv.tv_usec * 1000 + (timeoutMs % 1000) * 1000000;
    tp.tv_sec += tp.tv_nsec / 1000000000;
    tp.tv_nsec %= 1000000000;

    while (1)
    {
        ret = sem_timedwait(pSemaphore, &tp);
        if (ret == 0)
        {
            return PAL_OS_SUCCESS;
        }
        else if ((ret == -1) && (errno == EINTR))
        {
            PAL_DBG_PRINTF_WARN("sem_timedwait returned EINTR, interrupted by a signal.");
            return PAL_OS_TIMEOUT;
        }
        else
        {
            PAL_DBG_PRINTF_ERROR("sem_timedwait failed with: %s (%d).", strerror(errno), errno);
            return PAL_OS_ERROR;
        }
    }

    // If we get here, it means that the semaphore timed out
    return PAL_OS_TIMEOUT;
}

// =====================================================================================================================
palInt32 palOsSemaphoreSignal(palOsSemaphore* pSemaphore)
{
    palInt32 ret;

    ret = sem_post(pSemaphore);
    if (ret != 0)
    {
        PAL_DBG_PRINTF_ERROR("sem_post failed: %s (%d).", strerror(errno), errno);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
palInt32 palOsSemaphoreDestroy(palOsSemaphore* pSemaphore)
{
    palInt32 ret;

    ret = sem_destroy(pSemaphore);
    if (ret != 0)
    {
        PAL_DBG_PRINTF_ERROR("sem_destroy failed: %s (%d).", strerror(errno), errno);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
void* palOsIpcSharedMemoryCreate(const palInt8* pFname, size_t size, palInt32* pFd)
{
    *pFd = shm_open(pFname, O_RDWR | O_CREAT, S_IRWXU | S_IRWXG | S_IRWXO);
    if (*pFd < 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to open shared memory segment.");
        return NULL;
    }

    int status = ftruncate(*pFd, size);
    if (status != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to truncate the file to the specified size.");
        return NULL;
    }

    return mmap(0, size, PROT_READ | PROT_WRITE, MAP_SHARED, *pFd, 0);
}

// =====================================================================================================================
void* palOsIpcSharedMemoryOpen(const palInt8* pFname, const palInt32 fd, size_t size)
{
    palInt32 handle = fd;

    if (pFname != NULL)
    {
        handle = shm_open(pFname, O_RDWR, S_IRWXU | S_IRWXG | S_IRWXO);
    }

    if (handle < 0)
    {
        PAL_DBG_PRINTF_ERROR("Invalid shared memory handle.");
        return NULL;
    }

    return mmap(0, size, PROT_READ | PROT_WRITE, MAP_SHARED, handle, 0);
}

// =====================================================================================================================
void palOsIpcSharedMemoryClose(const palInt8* pFname, const palInt32 fd, const void* ptr, size_t size)
{
    if (ptr != NULL)
    {
        munmap((void*)ptr, size);
    }

    if (fd != 0)
    {
        close(fd);
    }

    if (pFname != NULL)
    {
        shm_unlink(pFname);
    }
}

// =====================================================================================================================
palOsLibrary palOsLoadLibrary(const palInt8* pLibName)
{
    return dlopen(pLibName, RTLD_NOW | RTLD_LOCAL);
}

// =====================================================================================================================
palInt32 palOsUnloadLibrary(palOsLibrary lib)
{
    return dlclose(lib);
}

// =====================================================================================================================
void* palOsGetProcAddress(palOsLibrary lib, const palInt8* pSymbolName)
{
    return dlsym(lib, (const palInt8*)pSymbolName);
}

// =====================================================================================================================
palUint64 palOsGetPageSize(void)
{
    return sysconf(_SC_PAGESIZE);
}

// =====================================================================================================================
palUint64 palOsGetTotalPhysicalMemory(void)
{
    return sysconf(_SC_PAGESIZE) * sysconf(_SC_PHYS_PAGES);
}

// =====================================================================================================================
void* palOsMallocAligned(size_t size, size_t alignment)
{
    void* pPtr = NULL;

    if (posix_memalign(&pPtr, alignment, size) != 0)
    {
        return NULL;
    }

    return pPtr;


}

// =====================================================================================================================
void palOsFreeAligned(void* pPtr)
{
    free(pPtr);
}

// =====================================================================================================================
palInt32 palOsGetKernelVersion(palInt32* pKernelVersion, palInt32* pMajorVersion, palInt32* pMinorVersion)
{
    palInt32 result = 0;

    struct utsname unameData;

    if (uname(&unameData) != 0)
    {
        PAL_DBG_PRINTF_ERROR("uname failed: %s.", strerror(errno));
        return PAL_OS_ERROR;
    }

    *pKernelVersion = 0;
    *pMajorVersion  = 0;
    *pMinorVersion  = 0;
    result = sscanf(unameData.release, "%d.%d.%d", pKernelVersion, pMajorVersion, pMinorVersion);
    if (result != 3 && result != 2)
    {
        PAL_DBG_PRINTF_ERROR("Failed to parse kernel version: %s.", unameData.release);
        return PAL_OS_ERROR;
    }

    return PAL_OS_SUCCESS;
}

// =====================================================================================================================
static void* palOsVirtualMemoryAllocInternal(void* pAddress, palUint64 size, palOsVirtualMemoryAllocFlags allocFlags,
                                             palOsVirtualMemoryAccessFlags accessFlags, void* pBaseAddress,
                                             void* pLimit, size_t alignment)
{
    void*    pMapped = NULL;
    palInt32 flags   = 0;
    palInt32 proto   = 0;

    PAL_ASSERT(size != 0);

    if (allocFlags == PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_COMMIT)
    {
        PAL_ASSERT_MSG(pAddress != NULL,
                       "pAddress must not be NULL when allocating with PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_COMMIT.");
    }

    // Convert the access flags to the appropriate mmap flags
    switch (allocFlags)
    {
    case PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE:
    {
        PAL_ASSERT(accessFlags == PAL_OS_VIRTUAL_MEMORY_ACCESS_NONE);
        proto = PROT_NONE;
        flags = MAP_PRIVATE | MAP_ANONYMOUS;
        break;
    }
    case PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_COMMIT:
    {
        PAL_ASSERT(accessFlags == PAL_OS_VIRTUAL_MEMORY_ACCESS_DEFAULT);
        proto = PROT_READ | PROT_WRITE;
        // Committed reservations must be shared to avoid bad interactions between COW and pinned memory.
        flags = MAP_SHARED | MAP_ANONYMOUS | MAP_FIXED;
        break;
    }
    case PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE_AND_COMMIT:
    {
        PAL_ASSERT(accessFlags == PAL_OS_VIRTUAL_MEMORY_ACCESS_DEFAULT);
        proto = PROT_READ | PROT_WRITE;
        // Committed reservations must be shared to avoid bad interactions between COW and pinned memory.
        flags = MAP_SHARED | MAP_ANONYMOUS;
        break;
    }
    case PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE_WITH_STRONG_HINT:
    {
        PAL_ASSERT(accessFlags == PAL_OS_VIRTUAL_MEMORY_ACCESS_NONE);
        proto = PROT_NONE;
        // NOTE: MAP_FIXED_NOREPLACE is only available on linux 4.17+.
        flags = MAP_PRIVATE | MAP_ANONYMOUS | MAP_FIXED_NOREPLACE;
        break;
    }
    default:
        break;
    }

    // Map the memory
    pMapped = mmap(pAddress, size, proto, flags, -1, 0);
    if (pMapped == MAP_FAILED)
    {
        PAL_DBG_PRINTF_ERROR("mmap failed: %s (%d).", strerror(errno), errno);
        return NULL;
    }

    return pMapped;
}

// =====================================================================================================================
void* palOsVirtualMemoryAlloc(void* pAddress, palUint64 size, palOsVirtualMemoryAllocFlags allocFlags,
                              palOsVirtualMemoryAccessFlags accessFlags)
{
    if (pAddress != NULL)
    {
        return palOsVirtualMemoryAllocInternal(pAddress, size, allocFlags, accessFlags, pAddress,
                                               (void*)((palUint64)pAddress + size), 1);
    }
    else
    {
        // If the address is NULL then we allow a VA allocation from anywhere in address space of the process.
        return palOsVirtualMemoryAllocInternal(pAddress, size, allocFlags, accessFlags, 0, (void*)-1, 1);
    }
}

// =====================================================================================================================
void palOsVirtualMemoryFree(void* pAddress, size_t size, palOsVirtualMemoryFreeFlags freeFlags)
{
    PAL_ASSERT(size != 0);

    switch (freeFlags)
    {
    case PAL_OS_VIRTUAL_MEMORY_FREE_DECOMMIT:
    {
        void* pMapped = mmap(pAddress, size, PROT_NONE, MAP_PRIVATE | MAP_ANONYMOUS | MAP_FIXED, -1, 0);
        if (pMapped != pAddress)
        {
            PAL_ASSERT_MSG(0, "mmap with PROT_NONE failed to decommit memory at %p.", pAddress);
        }
        break;
    }
    case PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE:
    {
        // Unmap the memory
        if (munmap(pAddress, size) != 0)
        {
            PAL_DBG_PRINTF_ERROR("munmap failed: %s (%d).", strerror(errno), errno);
        }
        break;
    }
    default:
        PAL_ASSERT_MSG(0, "Invalid free flags specified.");
        break;
    }
}