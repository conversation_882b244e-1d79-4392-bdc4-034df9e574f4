#ifndef PAL_OS_POSIX_H_
#define PAL_OS_POSIX_H_

#include <memory.h>
#include <pthread.h>
#include <semaphore.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <stdint.h>
#include <sched.h>
#include <unistd.h>

#include "pal_types.h"

typedef pthread_key_t palOsTlsEntry;

typedef pthread_mutex_t palOsMutex;

typedef pthread_cond_t palOsConditionVariable;

typedef sem_t palOsSemaphore;

typedef FILE* palOsFileHandle;

typedef pid_t palOsPid;

typedef pthread_t palThreadId;

typedef palInt32 palOsFileDescriptor;

typedef pthread_once_t palOsOnceControl;

typedef struct palOsThread_st
{
    palInt32 (*palOsUserStartFunc)(void*);

    void*     pUserArgs;
    palInt32  returnValue;

    pthread_t thread;

    // reference count (start at 2, for join/detach and thread exit)
    palUint32 refCount;
} palOsThread;

typedef void* palOsLibrary;

#define PAL_OS_ONCE_INIT PTHREAD_ONCE_INIT

#define PAL_OS_TLS_OUT_OF_INDEXES 0

#if defined(__x86_64__)
#define palOsDebugBreak() asm volatile("int $0x3" : : : "memory")
#endif

#endif // PAL_OS_POSIX_H_