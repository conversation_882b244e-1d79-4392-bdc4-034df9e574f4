#ifndef PAL_OS_H_
#define PAL_OS_H_

/* Platform specific types and #defines */
#if defined(_WIN32)
// Add windows exclusive header file.
/* Ensure proper CRT prototype of isatty() is included */
#include <io.h>
#else // UNIX
#include "pal_os_posix.h"
#endif

#include "pal_os_atomic.h"

#if defined(_WIN32)
#define PRINTF_FORMAT(m, n)
#elif defined(__GNUC__) // GCC
#define PRINTF_FORMAT(m, n) __attribute__((format(gnu_printf, (m), (n))))
#else // all other compilers
#define PRINTF_FORMAT(m, n)
#endif

/// This enum defines the result codes for the PAL OS functions.
typedef enum palOsResult_enum
{
    PAL_OS_SUCCESS = 0,
    PAL_OS_ERROR   = -1,
    PAL_OS_TIMEOUT = -2,
    PAL_OS_EOF     = -3
} palOsResult;

/// Seek flags for file operations.
/// These flags are used to specify the position in the file where the read/write operations will occur.
/// The flags are used in the palOsFileSeek function to determine the starting point for the seek operation.
typedef enum palOsSeekFlags_enum
{
    PAL_OS_SEEK_SET = 0, // The beginning position of the file.
    PAL_OS_SEEK_CUR = 1, // The current position of the file pointer.
    PAL_OS_SEEK_END = 2  // The end position of the file.
} palOsSeekFlags;

typedef enum palOsVirtualMemoryAllocFlags_enum
{
    // Reserve memory but do not commit it
    PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE                  = 0,
    // Commit an already-reserved address
    PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_COMMIT                   = 1,
    // Reserve memory and commit it
    PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE_AND_COMMIT       = 2,
    // Reserve memory at a fixed address without clobber
    // This flag exploits mmap flag MAP_FIXED_NO_REPLACE which is only available on Linux 4.17+.
    // This flag falls back to PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE on older kernels.
    PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE_WITH_STRONG_HINT = 3,
} palOsVirtualMemoryAllocFlags;

typedef enum palOsVirtualMemoryAccessFlags_enum
{
    // Read-write access
    PAL_OS_VIRTUAL_MEMORY_ACCESS_DEFAULT      = 0,
    // Disable CPU caching
    PAL_OS_VIRTUAL_MEMORY_ACCESS_UNCACHED     = 1,
    // Enable write-combine
    PAL_OS_VIRTUAL_MEMORY_ACCESS_WRITECOMBINE = 2,
    // No access (for reservations)
    PAL_OS_VIRTUAL_MEMORY_ACCESS_NONE         = 3,
} palOsVirtualMemoryAccessFlags;

typedef enum palOsVirtualMemoryFreeFlags_enum
{
    // Decommit already-committed memory
    PAL_OS_VIRTUAL_MEMORY_FREE_DECOMMIT = 0,
    // Free (and decommit if needed) reserved or committed memory
    PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE  = 1,
} palOsVirtualMemoryFreeFlags;

// The default buffer length for environment variable.
#define PAL_OS_ENV_VAR_LENGTH 1024

// The infinite timeout value
#define PAL_OS_INFINITE_TIMEOUT ((palUint32)~0)

// File operations

/** Opens a file with read permissions. **/
#define PAL_OS_OPEN_READ 0x1

/** Opens a file with write permissions **/
#define PAL_OS_OPEN_WRITE 0x2

/** Open file in append mode. Implies WRITE. **/
#define PAL_OS_OPEN_APPEND 0x4

/** Open file in read-write plus mode. Implies '+'. **/
#define PAL_OS_OPEN_PLUS 0x8

/// @brief This function sets an environment variable.
///
/// @param [in] pName The name of the environment variable to set.
/// @param [in] pValue The value to set the environment variable to.
///
/// @return PAL_SUCCESS if the environment variable was set successfully, or an error code if it was not.
palInt32 palOsEnvSet(const char* pName, const char* pValue);

/// @brief This function gets the value of an environment variable.
///
/// @param [in] pName The name of the environment variable to get.
/// @param [out] pValue The buffer to store the value of the environment variable.
/// @param [in] size The size of the buffer.
///
/// @return The number of characters written to the buffer, or an error code if the environment variable was not found.
palInt32 palOsEnvGet(const char* pName, char* pValue, size_t size);

/// @brief This function is a wrapper for the standard sscanf function.
///
/// @param [in] pInput The input string to scan.
/// @param [in] pFormat The format string.
/// @param ... The arguments to store the scanned values.
/// @return The number of input items successfully matched and assigned.
palInt32 palOsSscanf(const char* pInput, const char* pFormat, ...);

/// @brief This function is a wrapper for the standard snprintf function.
///
/// @param [out] pOutput The buffer to store the formatted string.
/// @param [in] bufSize The size of the buffer.
/// @param [in] pFormat The format string.
///
/// @return The number of characters written to the buffer.
palInt32 palOsSnprintf(char* pOutput, size_t bufSize, const char* pFormat, ...) PRINTF_FORMAT(3, 0);

/// @brief This function is a wrapper for the standard vsnprintf function.
///
/// @param [out] pOutput The buffer to store the formatted string.
/// @param [in] bufSize The size of the buffer.
/// @param [in] pFormat The format string.
/// @param [in] argList The list of arguments.
///
/// @return The number of characters written to the buffer.
palInt32 palOsVsnprintf(char* pOutput, size_t bufSize, const char* pFormat, va_list argList) PRINTF_FORMAT(3, 0);

/// @brief Opens a file with read permissions.
///
/// @param pFilePath The path to the file.
/// @param flags The flags to open the file with.
/// @param pStream The file handle to store the opened file.
///
/// @return Returns PAL_OS_SUCCESS if the file was opened successfully, or an error code if it was not.
palOsResult palOsFileOpen(const palInt8* pFilePath, palUint64 flags, palOsFileHandle* pStream);

/// @brief Closes a file.
///
/// @param stream The file handle to close.
void palOsFileClose(palOsFileHandle stream);

/// @brief Writes a string to a file.
///
/// @param stream The file handle to store the opened file.
/// @param ptr The string to write.
/// @param size The size of the string.
///
/// @return Returns PAL_OS_SUCCESS if the string was written successfully, or an error code if it was not.
palOsResult palOsFileWrite(palOsFileHandle stream, const void* ptr, size_t size);

/// @brief Reads a string from a file.
///
/// @param stream The file handle to store the opened file.
/// @param ptr The buffer to store the read string.
/// @param size The size of the buffer.
/// @param pBytes The number of bytes read.
///
/// @return Returns PAL_OS_SUCCESS if the string was read successfully, or an error code if it was not.
palOsResult palOsFileRead(palOsFileHandle stream, void* ptr, size_t size, size_t* pBytes);

/// @brief Reads a line from a file.
///
/// @param stream The file handle to store the opened file.
/// @param ptr The buffer to store the read line.
/// @param size The size of the buffer.
/// @param pBytes The number of bytes read.
///
/// @return Returns PAL_OS_SUCCESS if the line was read successfully, or an error code if it was not.
palOsResult palOsFileReadLine(palOsFileHandle stream, void* ptr, size_t size, size_t* pBytes);

/// @brief Reads a character from a file.
///
/// @param stream The file handle to store the opened file.
/// @param pCharacter The character read from the file.
///
/// @return Returns PAL_OS_SUCCESS if the character was read successfully, or an error code if it was not.
palOsResult palOsFileGetc(palOsFileHandle stream, palInt8* pCharacter);

/// @brief Gets the size of a file.
///
/// @param pFilePath The path to the file.
///
/// @return Returns the size of the file in bytes, or 0 if the file does not exist.
palInt64 palOsFileGetSize(const palInt8* pFilePath);

/// @brief Sets the file position indicator for the stream to the given offset.
///
/// @param stream The file handle to store the opened file.
/// @param offset The offset to set the file position indicator to.
/// @param whence The reference point for the offset.
///
/// @return Returns PAL_OS_SUCCESS if the file position indicator was set successfully, or an error code if it was not.
palOsResult palOsFileSeek(palOsFileHandle stream, palUint64 offset, palOsSeekFlags whence);

/// @brief Gets the current file position indicator for the stream.
///
/// @param stream The file handle to store the opened file.
/// @param pPosition The current position of the file pointer.
///
/// @return Returns PAL_OS_SUCCESS if the file position indicator was retrieved successfully, or an error code if it was not.
palOsResult palOsFileTell(palOsFileHandle stream, palUint64* pPosition);

/// @brief Flushes the file stream.
///
/// @param stream The file handle to store the opened file.
///
/// @return Returns PAL_OS_SUCCESS if the file stream was flushed successfully, or an error code if it was not.
palOsResult palOsFileFlush(palOsFileHandle stream);

/// @brief Checks if a file exists.
///
/// @param pFilePath The path to the file.
///
/// @return Returns PAL_TRUE  if the file exists, PAL_FALSE otherwise.
palBool palOsFileExists(const palInt8* pFilePath);

/// @brief Gets the process ID of the current process.
///
/// @return Returns the process ID of the current process.
/// @note This function is not thread-safe.
palOsPid palOsGetProcessId(void);

/// @brief Gets the thread ID of the current thread.
///
/// @return Returns the thread ID of the current thread.
/// @note This function is not thread-safe.
palThreadId palOsGetThreadId(void);

/// @brief Simple wrapper for strtok_s or strtok_r which provides a safe version of strtok.
///
/// @param str The string to tokenize.
/// @param delim The delimiters to use for tokenization.
/// @param buf The buffer to store the token.
///
/// @return Returns the next token from the string, or NULL if there are no more tokens.
/// @note This function is not thread-safe.
palInt8* palOsStrtok(palInt8* str, const palInt8* delim, palInt8** buf);

/// @brief Performs a safe strcpy by requiring the destination buffer size.
///
/// @param pDst The destination string.
/// @param pSrc The source string to be copied into destination.
/// @param dstSize The size of the destination buffer in bytes.
/// @note This function is not thread-safe.
void palOsStrncpy(palInt8* pDst, const palInt8* pSrc, size_t dstSize);

/// @brief Performs a safe strncasecmp by requiring the length of the strings to compare.
///
/// @param pStr1 The first string to compare.
/// @param pStr2 The second string to compare.
/// @param n The number of characters to compare.
/// @return Returns 0 if the strings are equal, a positive value if pStr1 is greater than pStr2, or a negative value if pStr1 is less than pStr2.
palInt32 palOsStrncasecmp(const palInt8* pStr1, const palInt8* pStr2, size_t n);

/// @brief Sleeps for the specified number of milliseconds.
/// @param msec The number of milliseconds to sleep.
/// @note This function is not thread-safe.
void palOsSleep(palUint32 msec);

/// @brief Creates a thread.
///
/// @param ppThread The thread handle.
/// @param pUserStartFunc The function to be executed by the thread.
/// @param pUserArgs The arguments to be passed to the thread function.
/// @return Returns PAL_OS_SUCCESS if the thread was created successfully, or an error code if it was not.
palInt32 palOsThreadCreate(palOsThread** ppThread, palInt32 (*pUserStartFunc)(void*), void* pUserArgs);

/// @brief Joins a thread.
///
/// @param pThread The thread handle.
/// @param pRetCode The return code of the thread.
void palOsThreadJoin(palOsThread* pThread, palInt32* pRetCode);

/// @brief Detaches a thread.
///
/// @param pThread The thread handle.
void palOsThreadDetach(palOsThread* pThread);

/// @brief Checks if the current thread is the same as the specified thread.
///
/// @param pThread The thread handle.
/// @return Returns PAL_TRUE if the current thread is the same as the specified thread, PAL_FALSE otherwise.
palBool palOsThreadIsCurrent(palOsThread* pThread);

/// @brief Checks if the specified thread has exited.
///
/// @param pThread The thread handle.
/// @return Returns PAL_TRUE if the specified thread has exited, PAL_FALSE otherwise.
palBool palOsThreadHasExited(palOsThread* pThread);

/// @brief Yields the current thread, allowing other threads to run.
void palOsThreadYield(void);

/// @brief Sets the name of the current thread.
///
/// @param pName The name to set for the current thread.
/// @return Returns PAL_OS_SUCCESS if the name was set successfully, or an error code if it was not.
palInt32 palOsThreadSetName(const palInt8* pName);

/// @brief Gets the name of the current thread.
///
/// @param pName The buffer to store the name of the current thread.
/// @param len The length of the buffer.
/// @return Returns PAL_OS_SUCCESS if the name was retrieved successfully, or an error code if it was not.
palInt32 palOsThreadGetName(const palInt8* pName, size_t len);

/// @brief Creates a thread-local storage (TLS) entry.
///
/// @param pDestroyFunc The function to be called when the TLS entry is destroyed.
palOsTlsEntry palOsTlsCreate(void (*pDestroyFunc)(void*));

/// @brief Sets the value of the TLS entry.
///
/// @param tlsEntry The TLS entry.
/// @param pValue The value to set for the TLS entry.
void palOsTlsDestroy(palOsTlsEntry tlsEntry);

/// @brief Gets the value of the TLS entry.
///
/// @param tlsEntry The TLS entry.
/// @return Returns the value of the TLS entry.
void* palOsTlsGetValue(palOsTlsEntry tlsEntry);

/// @brief Sets the value of the TLS entry.
///
/// @param tlsEntry The TLS entry.
/// @param pValue The value to set for the TLS entry.
/// @return Returns the old value of the TLS entry.
palUint32 palOsTlsSetValue(palOsTlsEntry tlsEntry, void* pValue);

/// @brief Initializes a mutex.
///
/// @param pMutex The mutex to initialize.
/// @return Returns PAL_OS_SUCCESS if the mutex was initialized successfully, or an error code if it was not.
palInt32 palOsMutexInit(palOsMutex* pMutex);

/// @brief Initializes a shared mutex.
///
/// @param pMutex The mutex to initialize.
/// @return Returns PAL_OS_SUCCESS if the mutex was initialized successfully, or an error code if it was not.
palInt32 palOsMutexSharedInit(palOsMutex* pMutex);

/// @brief Destroys a mutex.
///
/// @param pMutex The mutex to destroy.
void palOsMutexDestroy(palOsMutex* pMutex);

/// @brief Locks a mutex.
///
/// @param pMutex The mutex to lock.
void palOsMutexLock(palOsMutex* pMutex);

/// @brief Tries to lock a mutex.
///
/// @param pMutex The mutex to try to lock.
/// @return Returns PAL_OS_SUCCESS if the mutex was locked successfully, PAL_OS_TIMEOUT if the mutex is already locked, or an error code if it was not.
palInt32 palOsMutexTryLock(palOsMutex* pMutex);

/// @brief Unlocks a mutex.
///
/// @param pMutex The mutex to unlock.
void palOsMutexUnlock(palOsMutex* pMutex);

/// @brief Performs a callOnce operation.
///
/// @param pOnceControl The control structure for the callOnce operation.
/// @param pInitRoutine The initialization routine to be called only once.
/// @return Returns PAL_OS_SUCCESS if the callOnce operation was successful, or an error code if it was not.
palInt32 palOsCallOnce(palOsOnceControl* pOnceControl, void (*pInitRoutine)(void));

/// @brief Initializes a condition variable.
///
/// @param pCv The condition variable to initialize.
/// @return Returns PAL_OS_SUCCESS if the condition variable was initialized successfully, or an error code if it was not.
palInt32 palOsCondInit(palOsConditionVariable* pCv);

/// @brief Initializes a shared condition variable.
///
/// @param pCv The condition variable to initialize.
/// @return Returns PAL_OS_SUCCESS if the condition variable was initialized successfully, or an error code if it was not.
palInt32 palOsCondSharedInit(palOsConditionVariable* pCv);

/// @brief Waits on a condition variable.
///
/// @param pCv The condition variable to wait on.
/// @param pMutex The mutex to unlock while waiting.
/// @return Returns PAL_OS_SUCCESS if the wait operation was successful, or an error code if it was not.
palInt32 palOsCondWait(palOsConditionVariable* pCv, palOsMutex* pMutex);

/// @brief Waits on a condition variable with a timeout.
///
/// @param pCv The condition variable to wait on.
/// @param pMutex The mutex to unlock while waiting.
/// @param timeoutMs The timeout in milliseconds.
/// @return Returns PAL_OS_SUCCESS if the wait operation was successful, PAL_OS_TIMEOUT if the timeout occurred, or an error code if it was not.
palInt32 palOsCondTimedWait(palOsConditionVariable* pCv, palOsMutex* pMutex, palUint32 timeoutMs);

/// @brief Signals a condition variable.
///
/// @param pCv The condition variable to signal.
/// @return Returns PAL_OS_SUCCESS if the signal operation was successful, or an error code if it was not.
palInt32 palOsCondSignal(palOsConditionVariable* pCv);

/// @brief Broadcasts a signal to all threads waiting on a condition variable.
///
/// @param pCv The condition variable to broadcast to.
/// @return Returns PAL_OS_SUCCESS if the broadcast operation was successful, or an error code if it was not.
palInt32 palOsCondBroadcast(palOsConditionVariable* pCv);

/// @brief Destroys a condition variable.
///
/// @param pCv The condition variable to destroy.
/// @return Returns PAL_OS_SUCCESS if the condition variable was destroyed successfully, or an error code if it was not.
palInt32 palOsCondDestroy(palOsConditionVariable* pCv);

/// @brief Initializes a semaphore.
///
/// @param pSemaphore The semaphore to initialize.
/// @param value The initial value of the semaphore.
/// @return Returns PAL_OS_SUCCESS if the semaphore was initialized successfully, or an error code if it was not.
palInt32 palOsSemaphoreInit(palOsSemaphore* pSemaphore, palInt32 value);

/// @brief Waits on a semaphore.
///
/// @param pSemaphore The semaphore to wait on.
/// @return Returns PAL_OS_SUCCESS if the wait operation was successful, PAL_OS_TIMEOUT if the timeout occurred, or an error code if it was not.
palInt32 palOsSemaphoreWait(palOsSemaphore* pSemaphore);

/// @brief Waits on a semaphore with a timeout.
///
/// @param pSemaphore The semaphore to wait on.
/// @param timeoutMs The timeout in milliseconds.
/// @return Returns PAL_OS_SUCCESS if the wait operation was successful, PAL_OS_TIMEOUT if the timeout occurred, or an error code if it was not.
palInt32 palOsSemaphoreTimedWait(palOsSemaphore* pSemaphore, palUint32 timeoutMs);

/// @brief Signals a semaphore.
///
/// @param pSemaphore The semaphore to signal.
/// @return Returns PAL_OS_SUCCESS if the signal operation was successful, or an error code if it was not.
palInt32 palOsSemaphoreSignal(palOsSemaphore* pSemaphore);

/// @brief Destroys a semaphore.
///
/// @param pSemaphore The semaphore to destroy.
/// @return Returns PAL_OS_SUCCESS if the semaphore was destroyed successfully, or an error code if it was not.
palInt32 palOsSemaphoreDestroy(palOsSemaphore* pSemaphore);

/// @brief Creates a shared memory segment.
///
/// @param pFname The name of the shared memory segment.
/// @param size The size of the shared memory segment.
/// @param pFd The file descriptor for the shared memory segment.
/// @return Returns a pointer to the shared memory segment, or NULL if the creation failed.
void* palOsIpcSharedMemoryCreate(const palInt8* pFname, size_t size, palInt32* pFd);

/// @brief Opens a shared memory segment.
///
/// @param pFname The name of the shared memory segment.
/// @param fd The file descriptor for the shared memory segment.
/// @param size The size of the shared memory segment.
/// @return Returns a pointer to the shared memory segment, or NULL if the opening failed.
void* palOsIpcSharedMemoryOpen(const palInt8* pFname, const palInt32 fd, size_t size);

/// @brief Closes a shared memory segment.
///
/// @param pFname The name of the shared memory segment.
/// @param fd The file descriptor for the shared memory segment.
/// @param ptr The pointer to the shared memory segment.
/// @param size The size of the shared memory segment.
void palOsIpcSharedMemoryClose(const palInt8* pFname, const palInt32 fd, const void* ptr, size_t size);

/// @brief Loads a shared library.
///
/// @param pLibName The name of the library to load.
/// @return Returns a handle to the loaded library, or NULL if the loading failed.
palOsLibrary palOsLoadLibrary(const palInt8* pLibName);

/// @brief Unloads a shared library.
///
/// @param lib The handle to the library to unload.
/// @return Returns PAL_SUCCESS if the library was unloaded successfully, or an error code if it was not.
palInt32 palOsUnloadLibrary(palOsLibrary lib);

/// @brief Gets the address of a symbol in a shared library.
///
/// @param lib The handle to the library.
/// @param pSymbolName The name of the symbol to get the address of.
/// @return Returns the address of the symbol, or NULL if the symbol was not found.
void* palOsGetProcAddress(palOsLibrary lib, const palInt8* pSymbolName);

/// @brief Gets the page size of the system.
///
/// @return Returns the page size in bytes.
palUint64 palOsGetPageSize(void);

/// @brief Gets the total physical memory of the system.
///
/// @return Returns the total physical memory in bytes.
palUint64 palOsGetTotalPhysicalMemory(void);

/// @brief Allocates aligned memory.
///
/// @param size The size of the memory to allocate.
/// @param alignment The alignment of the memory to allocate.
/// @return Returns a pointer to the allocated memory, or NULL if the allocation failed.
void* palOsMallocAligned(size_t size, size_t alignment);

/// @brief Frees aligned memory.
///
/// @param ptr The pointer to the memory to free.
/// @note This function does not check if the pointer is NULL before freeing.
void palOsFreeAligned(void* ptr);

/// @brief Gets the kernel version of the operating system.
///
/// @param pKernelVersion Pointer to store the kernel version.
/// @param pMajorVersion Pointer to store the major version.
/// @param pMinorVersion Pointer to store the minor version.
/// @return Returns PAL_OS_SUCCESS if the kernel version was retrieved successfully, or an error code if it was not.
palInt32 palOsGetKernelVersion(palInt32* pKernelVersion, palInt32* pMajorVersion, palInt32* pMinorVersion);

/// @brief Allocates virtual memory.
///
/// @param pAddress The address to allocate memory at, or NULL to let the OS choose.
/// @param size The size of the memory to allocate.
/// @param allocFlags The allocation flags to use.
/// @param accessFlags The access flags to use.
/// @return Returns a pointer to the allocated memory, or NULL if the allocation failed.
void* palOsVirtualMemoryAlloc(void* pAddress, palUint64 size, palOsVirtualMemoryAllocFlags allocFlags,
                              palOsVirtualMemoryAccessFlags accessFlags);

/// @brief Frees virtual memory.
///
/// @param pAddress The starting address of the memory to free.
/// @param size The size of the memory region to free, in bytes.
/// @param freeFlags Flags that specify options for freeing the memory.
/// @note The behavior of this function depends on the platform and the provided flags.
void palOsVirtualMemoryFree(void* pAddress, size_t size, palOsVirtualMemoryFreeFlags freeFlags);

#endif // PAL_OS_H_