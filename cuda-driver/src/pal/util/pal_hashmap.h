#ifndef PAL_HASHMAP_H_
#define PAL_HASHMAP_H_

#include "pal_hash.h"

#define PAL_HASHMAP_DEFAULT_INITIAL_CAPACITY 16
#define PAL_HASHMAP_LOAD_FACTOR_THRESHOLD 0.75

typedef void* palHashMapValueType;
typedef void (*palHashMapUserFunc)(const palHashKeyType key, const palHashMapValueType value);

typedef struct palHashEntry_st
{
    palHashKeyType key;

    palHashMapValueType value;

    // Check if this location has been occupied
    palBool isOccupied : 1;

    // Indicates that the hash owns the string key or binary key and
    // should free() it when deleting the entry.
    palBool isOwnedKey : 1;
} palHashEntry;

typedef struct palHashMap_st
{
    // The vector of entries
    palHashEntry* pEntries;

    // The maximum capacity size that can be loaded
    palUint64 capacity;

    // The actual hash entry number.
    palUint64 size;

    // The load factor threshold
    palFloat32 loadFactorThreshold;

    // The hash type.
    palHashType hashType;
} palHashMap;

/// @brief Create a hashmap object
///
/// @param ppHashMap The created hashmap handle.
/// @param hashType The hash type
/// @param capacity The initial capacity size that can be loaded.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*
palResult palHashMapCreate(palHashMap** ppHashMap, palHashType hashType, palUint64 capacity);

/// @brief Initialize the hashmap (Created by caller)
///
/// @param pHashMap The created hashmap handle.
/// @param hashType The hash type
/// @param capacity The initial capacity size that can be loaded.
/// @return Return PAL_SUCCESS if success, otherwise return PAL_ERROR_*
palResult palHashMapInitialize(palHashMap* pHashMap, palHashType hashType, palUint64 capacity);

/// @brief Deinitialize the hashmap (created by caller)
///
/// @param pHashMap The hashmap handle.
void palHashMapDeinitialize(palHashMap* pHashMap);

/// @brief Destroy all entries and delete the hashmap handle.
///
/// @param pHashMap The hashmap handle.
void palHashMapDestroy(palHashMap* pHashMap);

/// @brief Inserts a pair into the hashmap
///
/// @param pHashMap The hashmap handle.
/// @param key The input key
/// @param value The input value
/// @param ownsKey It indicates if the key owns to the hashmap
/// @return Return PAL_SUCCESS if success, return PAL_ERROR_OUT_OF_MEMORY if internal allocation fails with out of
///         memory, return PAL_ERROR_INVALID_VALUE if insertion fails.
palResult palHashMapInsert(palHashMap* pHashMap, const palHashKeyType key, palHashMapValueType value, palBool ownsKey);

/// @brief Remove an element from hashmap
///
/// @param pHashMap The hashmap handle.
/// @param key The input key.
void palHashMapErase(palHashMap* pHashMap, const palHashKeyType key);

/// @brief Remove all elements from hashmap
///        - Note the owning some keys which need to be free()d
///
/// @param pHashMap The hashmap handle.
void palHashMapClear(palHashMap* pHashMap);

/// @brief Find all elements from hashmap
///
/// @param pHashMap The hashmap handle.
/// @param key The corresponding key.
/// @param pValue The output value.
/// @return Return the corresponding palHashEntry
palHashEntry* palHashMapFind(palHashMap* pHashMap, const palHashKeyType key, palHashMapValueType* pValue);

/// @brief Get the size of hashmap
///
/// @param pHashMap The hashmap handle.
/// @return Return the size of the hashmap.
palUint64 palHashMapGetSize(palHashMap* pHashMap);

/// @brief Check if the hashmap is empty
///
/// @param pHashMap The hashmap handle.
/// @return Return PAL_TRUE if it is empty, PAL_FALSE otherwise.
palBool palHashMapIsEmpty(palHashMap* pHashMap);

/// @brief Traverse each entry of the hashmap and execute the user's callback function
///
/// @param pHashMap The hashmap handle.
/// @param userFunc The user callback function
void palHashMapTraverse(palHashMap* pHashMap, palHashMapUserFunc userFunc);

/// @brief Find an element with key equivalent to 'key', and update the value of the corresponding key.
///
/// @param pHashMap The hashmap handle.
/// @param key The corresponding key.
/// @param value The input new value.
/// @return Return PAL_SUCCESS if success, return PAL_ERROR_OUT_OF_MEMORY if internal allocation fails with out of
///         memory, return PAL_ERROR_INVALID_VALUE if insertion fails.
///         - Note the owning some keys which need to be free()d
///         - Note the value is not owned by the hashmap, so it should be free()d by the caller
palResult palHashMapSetValue(palHashMap* pHashMap, const palHashKeyType key, palHashMapValueType value);

#endif // PAL_HASHMAP_H_