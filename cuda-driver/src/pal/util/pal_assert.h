/*
 ***********************************************************************************************************************
 *
 *  Copyright (c) 2017-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 *
 **********************************************************************************************************************/

#ifndef PAL_ASSERT_H_
#define PAL_ASSERT_H_

#include <signal.h>

#include "pal_dbgprint.h"
#include "pal_misc.h"

/// OS-independent macro to force a break into the debugger.
#define PAL_DEBUG_BREAK() raise(SIGTRAP);

/// This macro is only useful on MSVC builds. It has no meaning for other builds.
# define PAL_ANALYSIS_ASSUME(_expr) ((void)0)

#if ENABLE_PRINTS_ASSERTS
/// Specifies how severe an triggered assert (or alert) is.
///
/// Both asserts and alerts can print out a debug string and break into the debugger.  Asserts are to be used to verify
/// the known, assumed state of the program at any time.  Alerts are to be used to notify the developer of a _possible_,
/// but unexpected condition such as memory allocation failure, an OS call failure, or an application behavior that is
/// known to be slow.
typedef enum palAssertCategory_enum
{
    PAL_ASSERT_CAT_ASSERT = 0,
    PAL_ASSERT_CAT_ALERT,
    PAL_ASSERT_CAT_COUNT
} palAssertCategory;

/// Enables/disables the specified assert category.
///
/// Probably controlled by a setting and set during initialization.
///
/// @param [in] category  Assert category to enable/disable (asserts or alerts).
/// @param [in] enable    True to enable the specified assert category, false to disable it.
extern void palAssertEnableAssertMode(palAssertCategory category, palBool enable);

/// Returns true if the specified assert category is enabled and false otherwise.
///
/// @param [in] category  Assert category to check
extern palBool palAssertIsCategoryEnabled(palAssertCategory category);

/// Prints an error message with the specified reason via the debug print system. A debug break will also be triggered
/// if they're currently enabled for asserts.
///
/// @note This version of assert inlines an 'int 3' every time it is used so that each occurrence can be zapped
///       independently.  This macro cannot be used in assignment operations.
#define PAL_TRIGGER_ASSERT(_pFormat, ...)                                  \
    do {                                                                   \
        PAL_DBG_PRINTF_ERROR(_pFormat, ##__VA_ARGS__);                     \
        if (palAssertIsCategoryEnabled(PAL_ASSERT_CAT_ASSERT) == PAL_TRUE) \
        {                                                                  \
            PAL_DEBUG_BREAK();                                             \
        }                                                                  \
    } while (0)

/// If the expression evaluates to false, then it calls the PAL_TRIGGER_ASSERT macro with an error message with the
/// specified reason.
///
/// @note This assert should not be used in constant evaluated contexts (e.g., constexpr functions).
//
// This previously said:
//    if (_expr_eval == false) [[unlikely]]
//    {
//        PAL_TRIGGER_ASSERT(...);
//    }
// However there is a bug in the initial gcc implementation of [[unlikely]] that means you cannot
// attach it to a compound statement. So:
// 1. we ignore PAL coding standards and don't use a compound statement;
// 2. we don't use [[unlikely]] as the expansion of PAL_TRIGGER_ASSERT already has one.
#define PAL_ASSERT_MSG(_expr, _pReasonFmt, ...)                                                       \
    do {                                                                                              \
        const palBool _expr_eval = (palBool)(_expr);                                                  \
        if (_expr_eval == PAL_FALSE)                                                                  \
        {                                                                                             \
            PAL_TRIGGER_ASSERT("Assertion failed: %s | Reason: " _pReasonFmt, #_expr, ##__VA_ARGS__); \
        }                                                                                             \
        PAL_ANALYSIS_ASSUME(_expr_eval);                                                              \
    } while (0)

/// Calls the PAL_ASSERT_MSG macro with a generic reason string
#define PAL_ASSERT(_expr) PAL_ASSERT_MSG(_expr, "%s", "Unknown")

/// If the expression evaluates to true, then a warning message with the specified reason will be printed via the
/// debug print system. A debug break will also be triggered if they're currently enabled for alerts.
///
/// @note This is the opposite polarity of asserts.  The assert macro _asserts_ that the specified condition is true.
///       While the alert macro _alerts_ the developer if the specified condition is true.
///
/// This macro should be used in places where an assert is inappropriate because an error condition is _possible_, but
/// not typically expected.  For example, asserting that an OS call succeeded should be avoided since there cannot be an
/// assumption that it will succeed.  Nonetheless, a developer may want to be alerted immediately and dropped into the
/// debugger when such a failure occurs.
#define PAL_TRIGGER_ALERT(_pFormat, ...)                                  \
    do {                                                                  \
        PAL_DBG_PRINTF_ERROR(_pFormat, ##__VA_ARGS__);                    \
        if (palAssertIsCategoryEnabled(PAL_ASSERT_CAT_ALERT) == PAL_TRUE) \
        {                                                                 \
            PAL_DEBUG_BREAK();                                            \
        }                                                                 \
    } while (0)

//
// This previously said:
//    if (_expr) [[unlikely]]
//    {
//        PAL_TRIGGER_ASSERT(...);
//    }
// However there is a bug in the initial gcc implementation of [[unlikely]] that means you cannot
// attach it to a compound statement. So:
// 1. we ignore PAL coding standards and don't use a compound statement;
// 2. we don't use [[unlikely]] as the expansion of PAL_TRIGGER_ASSERT already has one.
#define PAL_ALERT_MSG(_expr, _pReasonFmt, ...)                                                      \
    do {                                                                                            \
        if (_expr)                                                                                  \
        {                                                                                           \
            PAL_TRIGGER_ALERT("Alert triggered: %s | Reason: " _pReasonFmt, #_expr, ##__VA_ARGS__); \
        }                                                                                           \
    } while (0)

/// Calls the PAL_ALERT_MSG macro with a generic reason string
#define PAL_ALERT(_expr) PAL_ALERT_MSG(_expr, "%s", "Unknown")

/// Convenience macro that asserts if something has not been implemented.
#define PAL_NOT_IMPLEMENTED_MSG(_pReasonFmt, ...) \
    PAL_TRIGGER_ASSERT("Not Implemented! | Reason: " _pReasonFmt, ##__VA_ARGS__)
#define PAL_NOT_IMPLEMENTED() PAL_NOT_IMPLEMENTED_MSG("%s", "Unknown")

/// Convenience macro that asserts if an area of code that shouldn't be executed is reached.
#define PAL_NEVER_CALLED_MSG(_pReasonFmt, ...) \
    PAL_TRIGGER_ASSERT("Code should never be called! | Reason: " _pReasonFmt, ##__VA_ARGS__)
#define PAL_NEVER_CALLED() PAL_NEVER_CALLED_MSG("%s", "Unknown")

#else

#define PAL_ASSERT(_expr)                    PAL_ANALYSIS_ASSUME(_expr)
#define PAL_ASSERT_MSG(_expr, ...)           PAL_ANALYSIS_ASSUME(_expr)
#define PAL_ALERT(_expr)                     ((void)0)
#define PAL_ALERT_MSG(_expr, ...)            ((void)0)
#define PAL_NOT_IMPLEMENTED()                ((void)0)
#define PAL_NOT_IMPLEMENTED_MSG(...)         ((void)0)
#define PAL_NEVER_CALLED()                   ((void)0)
#define PAL_NEVER_CALLED_MSG(...)            ((void)0)
#endif // ENABLE_PRINTS_ASSERTS

#endif // PAL_ASSERT_H_