#ifndef PAL_MISC_H_
#define PAL_MISC_H_

#include "pal_types.h"

#include <stddef.h>

#if !defined(likely)
#define likely(x) __builtin_expect(!!(x), 1)
#endif
#if !defined(unlikely)
#define unlikely(x) __builtin_expect(!!(x), 0)
#endif

#define PAL_MAX(a, b) (((a) > (b)) ? (a) : (b))
#define PAL_MIN(a, b) (((a) < (b)) ? (a) : (b))

#define PAL_ALIGN(x, alignment) (((x) + ((alignment) - 1ULL)) & ~((alignment) - 1ULL))

/// Describes a value type, primarily used for loading settings values.
typedef enum palValueType_enum
{
    PAL_VALUE_TYPE_BOOLEAN = 0, ///< Boolean type.
    PAL_VALUE_TYPE_INT8,        ///< 8-bit signed integer type.
    PAL_VALUE_TYPE_UINT8,       ///< 8-bit unsigned integer type.
    PAL_VALUE_TYPE_INT16,       ///< 16-bit signed integer type.
    PAL_VALUE_TYPE_UINT16,      ///< 16-bit unsigned integer type.
    PAL_VALUE_TYPE_INT32,       ///< 32-bit signed integer type.
    PAL_VALUE_TYPE_UINT32,      ///< 32-bit unsigned integer type.
    PAL_VALUE_TYPE_INT64,       ///< 64-bit signed integer type.
    PAL_VALUE_TYPE_UINT64,      ///< 64-bit unsigned integer type.
    PAL_VALUE_TYPE_FLOAT,       ///< 32-bit floating point type.
    PAL_VALUE_TYPE_DOUBLE,      ///< 64-bit floating point type.
    PAL_VALUE_TYPE_STRING,      ///< String type.
    PAL_VALUE_TYPE_MAX,         ///< Maximum value type.
} palValueType;

/// Converts a raw string value to the correct data type.
/// @param pStrValue [in] Setting value in string form.
/// @param type [in] Data type of the value being converted.
/// @param valueSize [in] Size of pValue buffer.
/// @param pValue [out] Converted setting value buffer.
/// @note This function is used to convert string values to their corresponding data types.
///       The function will parse the string and convert it to the specified type.
void palStringToValueType(const palInt8* pStrValue, palValueType type, size_t valueSize,
                          void* pValue);


#endif // PAL_MISC_H_