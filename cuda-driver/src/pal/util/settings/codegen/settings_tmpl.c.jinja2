#include "{{ CodeGenHeader }}"
#include "pal_settingsfilemgr.h"

// =====================================================================================================================
palResult {{ CreateFuncName }}({{ SettingsStructName }}** p{{ ParamName }}, palSettingsFileMgr* pSettingsFileMgr)
{
    {{ SettingsStructName }}* {{ ParamName }};

    {{ ParamName }} = ({{ SettingsStructName}}*)malloc(sizeof({{ SettingsStructName }}));
    if ({{ ParamName }} == NULL)
    {
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    {{ InitFuncName }}({{ ParamName }});
    {{ OverrideByUserEnvFuncName }}({{ ParamName }});

    if (pSettingsFileMgr != NULL)
    {
        {{ OverrideBySettingsFileFuncName }}({{ ParamName }}, pSettingsFileMgr);
    }

    {{ OverrideByAppProfiledFuncName }}({{ ParamName }});

    *p{{ ParamName }} = {{ ParamName }};

    return PAL_SUCCESS;
}

// =====================================================================================================================
void {{ InitFuncName }}({{ SettingsStructName }}* {{ ParamName }})
{
    if ({{ ParamName }} == NULL)
    {
        return;
    }

{% for setting in Settings %}
    {{ setting|setup_default('') }}
{% endfor %}
}

// =====================================================================================================================
void {{ OverrideByUserEnvFuncName }}({{ SettingsStructName }}* {{ ParamName }})
{
    char settingValueVar[PAL_OS_ENV_VAR_LENGTH] = "";

    if ({{ ParamName }} == NULL)
    {
        return;
    }

{% for setting in Settings %}
    {{ setting|override_by_user_environment('') }}
{% endfor %}
}

// =====================================================================================================================
void {{ OverrideBySettingsFileFuncName }}({{ SettingsStructName }}* {{ ParamName }}, palSettingsFileMgr* pSettingsFileMgr)
{
    palInt8 settingValueVar[PAL_OS_ENV_VAR_LENGTH] = "";

    if (({{ ParamName }} == NULL) || (pSettingsFileMgr == NULL) || (pSettingsFileMgr->pSettingsListHead == NULL))
    {
        return;
    }

{% for setting in Settings %}
    {{ setting|override_by_settings_file('') }}
{% endfor %}
}

// =====================================================================================================================
void {{ OverrideByAppProfiledFuncName }}({{ SettingsStructName }}* {{ ParamName }})
{
    if ({{ ParamName }} == NULL)
    {
        return;
    }

    // Do nothing now.
}

// =====================================================================================================================
void {{ DestroyFuncName }}({{ SettingsStructName }}* {{ ParamName }})
{
    memset({{ ParamName }}, 0, sizeof({{ SettingsStructName }}));
    free({{ ParamName }});
}