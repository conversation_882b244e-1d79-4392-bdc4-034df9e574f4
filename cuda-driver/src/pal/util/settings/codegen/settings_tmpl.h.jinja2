#ifndef {{ HeaderGuard }}
#define {{ HeaderGuard }}

#include <assert.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
{% for header in IncludeHeaders %}
#include "{{ header }}"
{%- endfor %}

// =====================================================================================================================
// Macros
#define PAL_SETTINGS_MAX_PATH_SIZE 256
#define PAL_SETTINGS_MAX_FILE_NAME_SIZE 256
#define PAL_SETTINGS_MAX_MISC_STRING_SIZE 256

// =====================================================================================================================
// Enums
{%- for enum in Enums %}
typedef enum pal{{ enum.Name }}_enum
{
    {%- for value in enum.Values %}
    {{ value.Name }} = {{ value.Value }},
    {%- endfor %}
} pal{{ enum.Name }};
{%- endfor %}

// =====================================================================================================================
// The structure of settings
typedef struct {{ SettingsStructName }}_st
{
{%- for setting in Settings %}
    {%- if setting.Type == "string" %}
    {{ setting.VariableType }} {{ setting.Name }}[{{setting.StringLength}}];
    {%- else %}
    {{ setting.VariableType }} {{ setting.Name }};
    {%- endif %}
{% endfor %}
} {{ SettingsStructName }};

palResult {{ CreateFuncName }}({{ SettingsStructName }}** p{{ ParamName }}, palSettingsFileMgr* pSettingsFileMgr);
void {{ InitFuncName }}({{ SettingsStructName }}* {{ ParamName }});
void {{ OverrideByUserEnvFuncName }}({{ SettingsStructName }}* {{ ParamName }});
void {{ OverrideBySettingsFileFuncName }}({{ SettingsStructName }}* {{ ParamName }}, palSettingsFileMgr* pSettingsFileMgr);
void {{ OverrideByAppProfiledFuncName }}({{ SettingsStructName }}* {{ ParamName }});
void {{ DestroyFuncName }}({{ SettingsStructName }}* {{ ParamName }});

#endif // {{ HeaderGuard }}