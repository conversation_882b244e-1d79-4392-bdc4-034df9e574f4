##
 #######################################################################################################################
 #
 #  Copyright (c) 2022-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 #
 #  Permission is hereby granted, free of charge, to any person obtaining a copy
 #  of this software and associated documentation files (the "Software"), to deal
 #  in the Software without restriction, including without limitation the rights
 #  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 #  copies of the Software, and to permit persons to whom the Software is
 #  furnished to do so, subject to the following conditions:
 #
 #  The above copyright notice and this permission notice shall be included in all
 #  copies or substantial portions of the Software.
 #
 #  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 #  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 #  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 #  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 #  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 #  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 #  SOFTWARE.
 #
 #######################################################################################################################

import argparse
import io
import time
import sys
import json
import copy
import random
from os import path
from jinja2 import Environment as JinjaEnv, FileSystemLoader as JinjaLoader

def fnv1a(bytes: bytes):
    fnv_prime = 0x01000193
    hval = 0x811C9DC5
    uint32Mod = 0xFFFFFFFF

    for byte in bytes:
        hval = int(byte) ^ hval
        hval = hval * fnv_prime
        hval = hval & uint32Mod

    return hval

def setup_default(setting: dict, group_name: str) -> str:
    """A Jinja filter that renders settings default value assignment. """

    def assign_line(setting: dict, platform: str, group_name: str) -> str:
        setting_name = setting["VariableName"]
        if len(group_name) != 0:
            setting_name = f"{group_name}.{setting_name}"

        default = setting["Defaults"][platform]
        default_str = ""
        enum_name = setting.get("enum", None)
        is_string = False
        if enum_name:
            if isinstance(default, str):
                default_str = f"{enum_name}::{default}"
            elif isinstance(default, int):
                default_str = str(default)
            else:
                raise ValueError(
                    f"{setting_name} is an Enum. Its default value must be either "
                    f"an integer or a string representing one of the values of its "
                    f"Enum type, but it's {type(default)}."
                )

        elif isinstance(default, bool):
            default_str = "true" if default else "false"

        elif isinstance(default, int):
            flags = setting.get("Flags", None)
            if flags and "isHex" in flags:
                default_str = hex(default)
            else:
                default_str = str(default)

        elif isinstance(default, str):
            # setting_type = setting["Defaults"]["Type"]
            setting_type = setting["Type"]
            if setting_type == "float":
                default_str = default
                if default_str[-1:] != "f":
                    default_str += "f"
            elif setting_type in ["uint64", "uint32"]:
                default_str = default
            else:
                default_str = default.replace("\\", "\\\\")
                default_str = f'"{default_str}"'
                is_string = True

        elif isinstance(default, float):
            default_str = f"{str(default)}f"

        else:
            raise ValueError(
                f'Invalid type of default value for the setting "{setting_name}"'
            )

        result = ""
        if is_string:
            string_len = setting["StringLength"]
            # result += f'static_asser({string_len} >= sizeof({default_str}), "The string setting ({setting_name}) length execeeds the max {string_len}.");\n'
            result += f'strncpy(pSettings->{setting_name}, {default_str}, {string_len} - 1);\n'
            result += f"\tpSettings->{setting_name}[{string_len} - 1] = '\\0';\n"
        else:
            result += f"pSettings->{setting_name} = {default_str};\n"

        return result

    defaults = setting["Defaults"]

    default_win = defaults.get("Windows", None)
    default_linux = defaults.get("Linux", None)

    result = ""
    if (default_win is not None) and (default_linux is not None):
        result += "#if defined(_WIN32)\n"
        result += "\t" + assign_line(setting, "Windows", group_name)
        result += "\t" + "#elif (__unix__)\n"
        result += "\t" + assign_line(setting, "Linux", group_name)
        result += "\t" + "#else\n"
        result += "\t" + assign_line(setting, "Default", group_name)
        result += "\t" + "#endif\n"
    elif default_win is not None:
        result += "#if defined(_WIN32)\n"
        result += "\t" + assign_line(setting, "Windows", group_name)
        result += "\t" + "#else\n"
        result += "\t" + assign_line(setting, "Default", group_name)
        result += "\t" + "#endif\n"
    elif default_linux is not None:
        result += "#if defined(__unix__)\n"
        result += "\t" + assign_line(setting, "Linux", group_name)
        result += "\t" + "#else\n"
        result += "\t" + assign_line(setting, "Default", group_name)
        result += "\t" + "#endif\n"
    else:
        result += assign_line(setting, "Default", group_name)

    return result

def override_by_user_environment(setting: dict, group_name: str) -> str:
    """A Jinja filter that renders settings value by user environment variables."""

    def override_line(seting: dict, platform: str, group_name: str) -> str:
        setting_name = setting["VariableName"]
        env_name = setting_name
        if len(group_name) != 0:
            setting_name = f"{group_name}.{setting_name}"

        print("env_name: ", env_name)

        is_string = False
        type_str = setting["Type"]
        flags = setting.get("Flags", None)
        convert_str_to_init = ""

        if flags and "IsBitMask" in flags:
            assert (
                type_str == "uint32" or type_str == "uint64"
            ), f'Because setting "{setting_name}" is a bitmask, its "Type" must be "uint32" or "uint64".'
            if type_str == "uint32":
                variable_type = "uint32_t"
                convert_str_to_init = "atoi"
            else:
                variable_type = "uint64_t"
                convert_str_to_init = "atoll"
        elif type_str == "string":
            is_string = True
            variable_type = "char"
            if flags and flags.get("IsDir", False):
                setting["StringLength"] = "PAL_SETTINGS_MAX_PATH_SIZE"
            if flags and flags.get("IsFile", False):
                setting["StringLength"] = "PAL_SETTINGS_MAX_FILE_NAME_SIZE"
            else:
                setting["StringLength"] = "PAL_SETTINGS_MAX_MISC_STRING_SIZE"
        elif type_str.startswith("int32"):
            variable_type = "int32_t"
            convert_str_to_init = "atoi"
        elif type_str.startswith("uint32"):
            variable_type = "uint32_t"
            convert_str_to_init = "atoi"
        elif type_str == "float":
            variable_type = "float"
            convert_str_to_init = "atof"
        elif type_str == "bool":
            variable_type = "bool"
            convert_str_to_init = "atoi"
        elif type_str == "uint64":
            variable_type = "uint64_t"
            convert_str_to_init = "atoll"
        elif type_str == "int64":
            variable_type = "int64_t"
            convert_str_to_init = "atoll"
        else:
            print("Invalid type_str: ", type_str)
            raise ValueError(f'Setting {setting_name} has invalid "Type" field')

        result = ""
        result += f'if (palOsEnvGet("{env_name}", settingValueVar, PAL_OS_ENV_VAR_LENGTH) != 0)\n'
        result += "\t" + f'{{\n'

        if is_string:
            string_len = setting["StringLength"]
            result += "\t" + f'\tstrncpy(pSettings->{setting_name}, settingValueVar, {string_len});\n'
        else:
            result += "\t" + f'\tpSettings->{setting_name} = ({variable_type}){convert_str_to_init}(settingValueVar);\n'

        result += "\t" + f'}}\n'
        result += "\t" + f'memset(settingValueVar, 0, PAL_OS_ENV_VAR_LENGTH);\n'

        return result

    return override_line(setting, "Default", group_name)

def override_by_settings_file(setting: dict, group_name: str) -> str:
    """A Jinja filter that renders settings value by settings file."""

    def override_line(setting:dict, platform: str, group_name: str) -> str:
        setting_name = setting["VariableName"]
        env_name = setting_name
        if len(group_name) != 0:
            setting_name = f"{group_name}.{setting_name}"

        # print("env_name: ", env_name)

        is_string = False
        type_str = setting["Type"]
        flags = setting.get("Flags", None)
        settings_value_type = ""

        if flags and "IsBitmask" in flags:
            assert (
                type_str == "uint32" or type_str == "uint64"
            ), f'Because setting "{setting_name}" is a bitmask, its "Type" must be "uint32" or "uint64".'
            if type_str == "uint32":
                variable_type = "uint32_t"
                settings_value_type = "PAL_VALUE_TYPE_UINT32"
            else:
                variable_type = "uint64_t"
                settings_value_type = "PAL_VALUE_TYPE_UINT64"
        elif type_str == "string":
            is_string = True
            variable_type = "char"
            settings_value_type = "PAL_VALUE_TYPE_STRING"
            if flags and flags.get("IsDir", False):
                setting["StringLength"] = "PAL_SETTINGS_MAX_PATH_SIZE"
            elif flags and flags.get("IsFile", False):
                setting["StringLength"] = "PAL_SETTINGS_MAX_FILE_NAME_SIZE"
            else:
                setting["StringLength"] = "PAL_SETTINGS_MAX_MISC_STRING_SIZE"
        elif type_str.startswith("int32"):
            variable_type = "int32_t"
            settings_value_type = "PAL_VALUE_TYPE_INT32"
        elif type_str.startswith("uint32"):
            variable_type = "uint32_t"
            settings_value_type = "PAL_VALUE_TYPE_UINT32"
        elif type_str == "float":
            variable_type = "float"
            settings_value_type = "PAL_VALUE_TYPE_FLOAT"
        elif type_str == "bool":
            variable_type = "bool"
            settings_value_type = "PAL_VALUE_TYPE_BOOLEAN"
        elif type_str == "int64":
            variable_type = "int64_t"
            settings_value_type = "PAL_VALUE_TYPE_INT64"
        elif type_str == "uint64":
            variable_type = "uint64_t"
            settings_value_type = "PAL_VALUE_TYPE_UINT64"
        else:
            print("Invalid type_str: ", type_str)
            raise ValueError(f'Setting {setting_name} has invalid "Type" field.')

        result = ""
        result += f'if (palSettingsFileMgrGetValue(pSettingsFileMgr, "{env_name}", {settings_value_type}, (void*)settingValueVar, PAL_OS_ENV_VAR_LENGTH) == PAL_TRUE)\n'
        result += "\t" + f'{{\n'

        if is_string:
            string_len = setting["StringLength"]
            result += "\t" + f'\tstrncpy(pSettings->{setting_name}, settingValueVar, {string_len});\n'
        else:
            result += "\t" + f'\tpSettings->{setting_name} = (({variable_type}*)settingValueVar)[0];\n'

        result += "\t" + f'}}\n'
        result += "\t" + f'memset(settingValueVar, 0, PAL_OS_ENV_VAR_LENGTH);\n'

        return result

    return override_line(setting, "Default", group_name)

def validate_settings_name(name: str):
    """Valid a "name" field in YAML object.

    The generated C variable names are based on 'name'. So we need to make
    sure they can compile in C.

    A name must start with an alphabetic letter and can only contain
    alphanumeric characters, plus underscore.
    """
    if not name[0].isalpha():
        raise ValueError(f'"{name}" does not start with an alphabetic letter.')
    name_cleaned = name.replace("_", "")
    if not name_cleaned.isalnum():
        raise ValueError(
            f'"{name}" contains character(s) other than alphanumeric or underscore (_).'
        )

def gen_setting_name_hashes(settings: list):
    """Generate name hash for each setting.

    And add it as 'NameHash' field. This function also validates setting names
    and checks for duplicates.
    """

    setting_name_set = set() # used to detect duplicate names
    setting_name_hash_map = dict() # used to detect hashing collision

    for setting in settings:
        name = setting["Name"]
        validate_settings_name(name)

        assert name not in setting_name_set, f'Duplicate setting name: "{name}".'
        setting_name_set.add(name)

        if "Structure" in setting:
            subsetting_name_set = set() # used to detect duplicate in subsettings.

            for subsetting in setting["Structure"]:
                subsetting_name = subsetting["Name"]
                validate_settings_name(subsetting_name)

                assert (
                    subsetting_name not in subsetting_name_set
                ), f'Duplicate subsetting name "{subsetting_name}" found in Structure "{name}".'
                subsetting_name_set.add(subsetting_name)

                full_name = f"{name}.{subsetting_name}"
                name_hash = fnv1a(bytes(full_name.encode(encoding="utf-8")))

                if name_hash in setting_name_hash_map:
                    colliding_hash_name = setting_name_hash_map[name_hash]
                    raise ValueError(
                        f"Hash collision detected between setting names: {full_name}, {colliding_hash_name}"
                    )
                else:
                    subsetting["NameHash"] = name_hash
                    setting_name_hash_map[name_hash] = full_name
        else:
            name_hash = fnv1a(bytes(name.encode(encoding="utf-8")))
            if name_hash in setting_name_hash_map:
                colliding_hash_name = setting_name_hash_map[name_hash]
                raise ValueError(
                    f"Hash collision detected between setting names: {name}, {colliding_hash_name}"
                )
            else:
                setting["NameHash"] = name_hash
                setting_name_hash_map[name_hash] = name

def prepare_enums(settings_root: dict):
    """Prepare enums from the top-level enum list and individual settings.

    Return a list of all unique enums. ValueError exception is raised when
    duplicate enums are found.
    """

    def validate_enum_reference(setting: dict, enums_list):
        valid_values = setting.get("ValidValues", None)
        setting_type = setting["Type"]
        if setting_type == "enum":
            assert valid_values, f'Setting {setting["Name"]} is of enum type, but misses "ValidValues" field.'

            valid_values_name = valid_values.get("Name", None)
            values = valid_values.get("Values", None)
            if not values:
                # ValidValues doesn't define an enum, make sure it references an item from the top-level "Enums" list.
                if all(enum["Name"] != valid_values_name for enum in enums_list):
                    raise ValueError(
                        f'Setting {setting["Name"]} references an enum that does not exist in the top-level "Enums" list'
                    )

        if valid_values:
            # Since we extracted ValidValues from inside individual settings to the top-level "Enums" list, we can
            # remove them from within settings, so that all settings reference enums from the top-level Enums list,
            # making it easier for tools to parse.
            valid_values_name = valid_values.get("Name", None)
            if valid_values_name:
                del setting["ValidValues"]
                setting["EnumReference"] = valid_values_name

    def extract_enum(setting: dict, enums_list, enum_names_unique):
        """Extract enum definitions from individual settings to the top-level Enums list."""

        valid_values = setting.get("ValidValues", None)
        if valid_values and valid_values.get("IsEnum", False):
            name = valid_values.get("Name", None)
            assert (
                name is not None
            ), 'ValidValues in the setting "{}" does not have a "Name" field'.format(
                setting["Name"]
            )

            if "Values" in valid_values:
                # Presence of "Values" field means it's enum definition. Check name duplication.
                skip_gen = valid_values.get("SkipGen", False)
                if (skip_gen == False) and (name in enum_names_unique):
                    setting_name = setting["Name"]
                    raise ValueError(
                        f'The enum name "{name}" in the setting "{setting_name}" already exists.'
                    )

                enums_list.append(valid_values)
                enum_names_unique.add(name)
            else:
                # No "Values" means this is a reference to an enum defined in top-level "Enums" list.
                if name not in enum_names_unique:
                    raise ValueError(
                        f'The enum name "{name}" in the setting "{setting_name}" does not reference any item '
                         'in the top-level "Enums" list.'
                    )

    top_level_enums = settings_root.get("Enums", [])
    top_level_enum_names = [enum["Name"] for enum in top_level_enums]

    enum_names_unique = set(top_level_enum_names)

    if len(top_level_enum_names) != len(enum_names_unique):
        duplicates = [x for x in enum_names_unique if top_level_enum_names.count(x) > 1]
        raise ValueError(f"Duplicate Enum names: {duplicates}")

    # Extract "ValidValues" from individual settings and append them to the top-level "Enums" list.
    for setting in settings_root["Settings"]:
        subsettings = setting.get("Structure", None)
        if subsettings:
            for subs in subsettings:
                extract_enum(subs, top_level_enums, enum_names_unique)
        else:
            extract_enum(setting, top_level_enums, enum_names_unique)

    for setting in settings_root["Settings"]:
        # If the setting is an enum, validate it.
        if "Structure" in setting:
            for subsetting in setting["Structure"]:
                validate_enum_reference(subsetting, top_level_enums)
        else:
            validate_enum_reference(setting, top_level_enums)

    if "Enums" not in settings_root:
        settings_root["Enums"] = top_level_enums

    for enum in top_level_enums:
        values = enum["Values"]

        # Convert enum values from hex string to int.
        for value_item in values:
            value = value_item["Value"]
            if isinstance(value, str):
                if value.startswith("0x") or value.startswith("0X"):
                    value_item["Value"] = int(value, base=16)
                else:
                    # If an enum value references other values in the enum, expand them.
                    for v in values:
                        if v["Name"] in value:
                            value = value.replace(v["Name"], str(v["Value"]))
                    try:
                        # Some clients use math expressions, try to evaluate them:
                        value_item["Value"] = eval(value)
                    except:
                        raise ValueError(f'Enum {enum["Name"]} contains value ({value_item["Name"]}) that is of invalid format. '
                                         'The valid formats are: 1. an integer 2. a string starting with "0x"/"0X" '
                                         '3. a string of math expression 4. a string containing previously defined enum values.')

        enum_base = "uint32_t"

        if "Is64Bit" in enum:
            enum_base = "uint64_t"
            print('[SettingsCodeGen][WARNING] "Is64Bit" field is deprecated, please use "EnumSize". '
                  'For example: `"EnumSize": 64`.', file=sys.stderr)
        else:
            enum_size = enum.get("EnumSize", 32)
            if enum_size == 8:
                enum_base = "char"
            elif enum_size == 16:
                enum_base = "uint16_t"
            elif enum_size == 32:
                enum_base = "uint32_t"
            elif enum_size == 64:
                enum_base = "uint64_t"
            else:
                raise ValueError(f'Enum {enum["Name"]} contains invalid "EnumSize" value ({enum_size}). '
                                  'Valid values are: 8, 16, 32, 64.')

        enum["EnumBase"] = enum_base

def add_variable_type(setting: dict):
    """Add a "VariableType" field to `setting`, used for generating C variable type."""
    setting_name = setting["Name"]
    variable_type = ""

    type_str = setting["Type"]
    flags = setting.get("Flags", None)

    if type_str == "enum":
        variable_type = setting["EnumReference"]
    elif flags and "IsBitmask" in flags:
        assert (
            type_str == "uint32" or type_str == "uint64"
        ), f'Because setting "{setting_name}" is a bitmask, its "Type" must be "uint32" or "uint64".'
        if type_str == "uint32":
            variable_type = "uint32_t"
        else:
            variable_type = "uint64_t"
    elif type_str == "string":
        variable_type = "char"
        if flags and flags.get("IsDir", False):
            setting["StringLength"] = "PAL_SETTINGS_MAX_PATH_SIZE"
        elif flags and flags.get("IsFile", False):
            setting["StringLength"] = "PAL_SETTINGS_MAX_FILE_NAME_SIZE"
        else:
            setting["StringLength"] = "PAL_SETTINGS_MAX_MISC_STRING_SIZE"
    elif type_str.startswith("int32"):
        variable_type = "int32_t"
    elif type_str.startswith("uint32"):
        variable_type = "uint32_t"
    elif type_str.startswith("float"):
        variable_type = "float"
    elif type_str.startswith("bool"):
        variable_type = "bool"
    elif type_str.startswith("int64"):
        variable_type = "int64_t"
    elif type_str.startswith("uint64"):
        variable_type = "uint64_t"
    else:
        raise ValueError(f'Setting "{setting_name}" has invalid "Type" field.')

    defaults = setting.get("Defaults", [])
    if (len(defaults) == 0) and (type_str != "string"):
        variable_type = f"DevDriver::Optional<{variable_type}>"
        setting["IsOptional"] = True

    setting["VariableType"] = variable_type

def gen_variable_name(name: str) -> str:
    """Generate C variable names.
    Try our best to convert to camelCase style.

    For example:
    "peerMemoryEnabled" -> "peerMemoryEnabled"
    "risSharpness" -> "risSharpness"
    "tfq" -> "tfq"
    "vpeForceTFCalculation" -> "vpeForceTfCalculation"
    "enableLLPC" -> "enableLlpc"
    """
    var_name = ""

    upper_case_start = -1
    upper_case_len = 0
    unprocessed_char_index = 0
    for char_index in range(len(name)):
        # Find a sequence of uppercase letters.
        if name[char_index].isupper():
            if upper_case_start == -1:

                # Process prior letters which should all be lowercase.
                if char_index > 0:
                    var_name += name[unprocessed_char_index : char_index]
                    unprocessed_char_index = char_index

                upper_case_start = char_index

            upper_case_len += 1
        else:
            if upper_case_len > 0:
                # Process previous sequence of uppercase letters.
                upper_case_end = upper_case_start + upper_case_len

                if upper_case_start == 0:
                    if upper_case_len == 1:
                        var_name += name[upper_case_start].lower()
                    else:
                        var_name += name[upper_case_start : upper_case_end - 1].lower()
                        var_name += name[upper_case_end - 1]
                else:
                    if upper_case_len == 1:
                        var_name += name[upper_case_start]
                    elif upper_case_len == 2:
                        if name[char_index].isalpha():
                            var_name += name[upper_case_start : upper_case_end]
                        else:
                            var_name += name[upper_case_start]
                            var_name += name[upper_case_start + 1].lower()
                    else:
                        # Lowercase the letters in the middle.
                        var_name += name[upper_case_start]
                        var_name += name[upper_case_start + 1 : upper_case_end - 1].lower()
                        var_name += name[upper_case_end - 1]

                unprocessed_char_index = upper_case_start + upper_case_len
                upper_case_start = -1
                upper_case_len = 0

    if unprocessed_char_index < len(name):
        # Process anything left at the end.
        if upper_case_len > 0:
            if upper_case_start == 0:
                var_name += name[upper_case_start:].lower()
            else:
                var_name += name[upper_case_start]
                if upper_case_len > 1:
                    var_name += name[upper_case_start + 1:].lower()
        else:
            var_name += name[unprocessed_char_index:]

    return var_name

def prepare_settings_list(settings: dict, top_level_enum_list: list):
    def convert_enum_value(setting: dict, top_level_enum_list: list, value: str):
        enum_ref = setting["EnumReference"]  # "EnumReference" should have been added in prepare_enum().
        enum = next(enum for enum in top_level_enum_list if enum["Name"] == enum_ref)

        enum_value_names = [value_name.strip() for value_name in value.split("|")]
        enum_value_int = 0
        for value_name in enum_value_names:
            # If the default value has a scope on it, remove it before looking up the value:
            if "::" in value_name:
                value_name = value_name.split("::")[-1]

            # value["Value"] should have already been converted to integer in prepare_enum().
            enum_value = next((value["Value"] for value in enum["Values"] if value["Name"] == value_name), None)
            if enum_value is None:
                raise ValueError(f'Setting {setting["Name"]} is bitmask, enum or has enum values. Its default value is a string, '
                                 f'but the string does not match any one or combination of values of its referenced enum {enum_ref}.'
                                 f'Only enum value names and \'|\' are allowed in the string.')
            else:
                enum_value_int |= enum_value

        return enum_value_int

    def validate_fields(setting: dict, top_level_enum_list: list):
        if "VariableName" in setting:
            raise ValueError(f'"VariableName" field is not allowed in the setting {setting["Name"]}. '
                             'The generated C++ variable name of this setting will be derived from the "Name" field.')

        if "DependsOn" in setting:
            raise ValueError(f'"DependsOn" field found in the setting {setting["Name"]}. It is deprecated and no longer allowed.')

        defaults = setting.get("Defaults", None)
        if defaults:
            assert "Default" in defaults, f'Setting {setting["Name"]}\'s "Defaults" field is missing the '\
                                           'platform-agnostic "Default" field.'
            assert "WinDefault" not in defaults, f'"WinDefault" field found in the setting {setting["Name"]}. '\
                                                  'It is deprecated, please use "Windows" instead.'
            assert "LnxDefault" not in defaults, f'"LnxDefault" field found in the setting {setting["Name"]}. '\
                                                  'It is deprecated, please use "Linux" instead.'

            setting_type = setting["Type"]
            for platform, value in defaults.items():
                if setting_type == "enum" or setting_type.startswith("int") or setting_type.startswith("uint"):
                    if isinstance(value, str):
                        is_bitmask = setting.get("Flags", {}).get("IsBitmask", False)
                        if value.startswith("0x") or value.startswith("0X"):
                            # Convert hex strings to integers.
                            defaults[platform] = int(value, base=16)
                        elif not is_bitmask and setting_type != "enum":
                            if "EnumReference" in setting:
                                raise ValueError(f'Non-bitmask setting {setting["Name"]} references an enum. '
                                                  'But its "Type" is not "enum".')
                            else:
                                try:
                                    # This could be a math expressions, so evaluate it:
                                    defaults[platform] = eval(value)
                                except SyntaxError:
                                    # Syntax error. Assume that this string is useful to the driver instead.
                                    # For example, this string could be the name of a constant variable defined in a driver
                                    # header file (the header file is #included in the generated settings cpp file).
                                    defaults[platform] = value
                        else:
                            if is_bitmask or setting_type == "enum":
                                # Convert default values from string of enum value names to integers.
                                defaults[platform] = convert_enum_value(setting, top_level_enum_list, value)
                            else:
                                raise ValueError(f'Setting {setting["Name"]} is of type {setting_type}. Its default '
                                                  'values can only be integers or strings starting with "0x".')

    for setting in settings:
        structure = setting.get("Structure", None)
        scope = setting.get("Scope", None)
        if structure:
            for subsetting in structure:
                validate_fields(subsetting, top_level_enum_list)
                subsetting["VariableName"] = gen_variable_name(subsetting["Name"])
                add_variable_type(subsetting)

                if "Scope" not in subsetting:
                    subsetting["Scope"] = scope
        else:
            validate_fields(setting, top_level_enum_list)
            add_variable_type(setting)

        setting["VariableName"] = gen_variable_name(setting["Name"])

def prepare_settings_meta(settings_root: dict, codegen_header: str):
    """Prepare settings meta data for code generation.

    Meta data are anything that's not under the "Settings" field in the YAML.

    settings_root:
        The root of Settings JSON object.

    codegen_header:
        The name of the auto-generated header file by this script.
    """

    settings_root["NumSettings"] = len(settings_root["Settings"])

    settings_root["CodeGenHeader"] = codegen_header

    component_name = settings_root["ComponentName"]
    validate_settings_name(component_name)
    settings_root["ComponentNameLower"] = component_name[0].lower() + component_name[1:]

def main():
    timer_start = time.monotonic()

    parser = argparse.ArgumentParser(
        description="Generate Settings files from a JSON files."
    )

    parser.add_argument(
        "-i",
        "--input",
        required=True,
        help="The path to a Settings JSON file.",
    )

    parser.add_argument(
        "-g",
        "--generated-filename",
        required=True,
        help="The name for both generated header and source files.\n"
        "The final names will be prefixed with 'g_' and suffixed with '.h'\n"
        "or '.c' for the final filenames.",
    )

    parser.add_argument(
        "-o",
        "--outdir",
        required=True,
        help="The directory to put the generated files.",
    )

    parser.add_argument(
        "--settings-struct-name",
        required=False,
        help="The name of the generated C struct that contains all settings' definitions specified in the input JSON"
        " file. By default, it's `<ComponentName>Settings`.",
    )

    parser.add_argument(
        "--include-headers",
        nargs="*",
        default=[],
        help="C header files that the generated settings header file needs to '#include'. For example, a header file containing an existing enum definition.",
    )

    args = parser.parse_args()
    print("args:", args)

    assert not args.generated_filename.endswith(
        ".h"
    ) and not args.generated_filename.endswith(
        ".c"
    ), 'The argument "generated-filename" should not contain extension'

    if path.exists(args.outdir) == False:
        print("The directory does not exist.\n")
        return

    g_header_path = path.join(args.outdir, f"g_{args.generated_filename}.h")
    g_source_path = path.join(args.outdir, f"g_{args.generated_filename}.c")
    print("g_header_path: ", g_header_path)
    print("g_source_path: ", g_source_path)

    running_script_dir = sys.path[0]
    # print("running_script_dir: ", running_script_dir)

    with open(args.input) as file:
        settings_root = json.load(file)

    settings_root["SettingsStructName"] = (
        args.settings_struct_name
        if (args.settings_struct_name)
        else settings_root["ComponentName"] + "Settings"
    )

    gen_setting_name_hashes(settings_root["Settings"])

    prepare_enums(settings_root)

    prepare_settings_list(settings_root["Settings"], settings_root["Enums"])

    prepare_settings_meta(
        settings_root,
        f"g_{args.generated_filename}.h")

    settings_root["IncludeHeaders"] = args.include_headers
    settings_root["HeaderGuard"] = "G_PAL_SETTINGS_H_"
    settings_root["CreateFuncName"] = "palSettingsCreate"
    settings_root["InitFuncName"] = "palSettingsInitDefaultSettings"
    settings_root["OverrideByUserEnvFuncName"] = "palSettingsOverrideByUserEnvironment"
    settings_root["OverrideBySettingsFileFuncName"] = "palRuntimeSettingsOverrideBySettingsFile"
    settings_root["OverrideByAppProfiledFuncName"] = "palSettingsOverrideByAppProfiled"
    settings_root["DestroyFuncName"] = "palSettingsDestroy"
    settings_root["ParamName"] = "pSettings"

    # Show formatted settings json data
    # print("settings_root:\n", json.dumps(settings_root, indent=4, ensure_ascii=False))

    # Set up Jinja Environment
    jinja_env = JinjaEnv(
        keep_trailing_newline=True,
        # Jinja2 templates should be in the same directory as this script.
        loader=JinjaLoader(running_script_dir),
    )

    jinja_env.filters["setup_default"] = setup_default
    jinja_env.filters["override_by_user_environment"] = override_by_user_environment
    jinja_env.filters["override_by_settings_file"] = override_by_settings_file

    header_template = jinja_env.get_template("settings_tmpl.h.jinja2")

    # Trun on global trim-spaces flag for the source file template.
    jinja_env.trim_blocks = True
    jinja_env.lstrip_blocks = True

    source_template = jinja_env.get_template("settings_tmpl.c.jinja2")

    # Render template.
    with open(g_header_path, "w") as generated_file:
        generated_file.write(header_template.render(settings_root))

    with open(g_source_path, "w") as generated_file:
        generated_file.write(source_template.render(settings_root))

    execution_time = time.monotonic() - timer_start

    # Comment out printing to avoid polluting compilation output. But leave it here so we can easily check timing during development.
    print("Settings C code generated successfully, in {} milliseconds.".format(execution_time * 1000))

    return 0

if __name__ == "__main__":
    sys.exit(main())