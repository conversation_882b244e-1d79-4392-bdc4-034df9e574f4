{"ComponentName": "pal", "Tags": ["General", "Printing and Logging", "Memory Management"], "Enums": [{"Name": "FeatureEnableMode", "IsEnum": true, "Values": [{"Name": "FeatureDefault", "Value": 0, "Description": "Default, use application setting"}, {"Name": "FeatureForceEnable", "Value": 1, "Description": "Force Enable"}, {"Name": "FeatureForceDisable", "Value": 2, "Description": "Force Disable Default Value"}]}], "Settings": [{"ValidValues": {"Values": [{"Description": "Disable all logs.", "Value": 0}, {"Description": "Enable logging routed to the debug window or stdout.", "Value": 1}, {"Description": "Enable logging routed to a file.", "Value": 2}, {"Description": "Enable logging routed to the print callback only.", "Value": 3}]}, "Name": "enableLogging", "Description": "Enable logging to log app execution status.", "Tags": ["Printing and Logging"], "Defaults": {"Default": 1}, "Type": "uint32", "Scope": "CUDA Driver"}, {"Name": "loggingFileName", "Description": "Filename of the file to log debug information. If the file name is empty, the message will be output to stderr.", "Tags": ["Printing and Logging"], "Flags": {"IsFile": true}, "Defaults": {"Default": "logInfo.txt"}, "Type": "string", "Scope": "CUDA Driver"}, {"Name": "loggingDirectory", "Description": "Relative director where logging dumps are placed. Relative to the path in the CUDA_DEBUG_DIR environment variable. If that env var isn't set, the location is platform dependent", "Tags": ["Printing and Logging"], "Flags": {"IsDir": true}, "Defaults": {"Default": "./", "Windows": ".\\", "Linux": "./"}, "Type": "string", "Scope": "CUDA Driver"}, {"Name": "enableDebugAssert", "Description": "Enable debug assert.", "Tags": ["Printing and Logging"], "Defaults": {"Default": false}, "Type": "bool", "Scope": "CUDA Driver"}, {"Name": "enableUnifiedAddressing", "Description": "Enable unified addressing.", "Tags": ["Memory Management"], "Defaults": {"Default": true}, "Type": "bool", "Scope": "CUDA Driver"}]}