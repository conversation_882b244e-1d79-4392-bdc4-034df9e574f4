#ifndef PAL_VECTOR_H
#define PAL_VECTOR_H

#include "pal.h"
#include "pal_types.h"
#include "pal_structures.h"

#include <stddef.h>

// Forward declaration of the palVector struct.
typedef void (*palVectorDestructFunc)(void*);

struct palVector_st
{
    // data pointer to the array of elements
    void* pData;

    // The size of each element in the vector.
    // This is used to calculate the offset of each element in the array.
    // For example, if the element size is 4 bytes, the first element is at offset 0,
    size_t elementSize;

    // The number of elements in the vector.
    size_t size;

    // The capacity of the vector.
    size_t capacity;

    // The function to call when destructing elements in the vector.
    // This is used to free any resources associated with the elements.
    // For example, if the elements are pointers to dynamically allocated memory,
    // this function would be used to free that memory.
    palVectorDestructFunc destructFunc;
};

/// @brief Create a new vector.
///
/// @param ppVector The address of the pointer to the vector.
/// @param elementSize The size of each element in the vector.
/// @param initialCapacity The initial capacity of the vector.
/// @param destructFunc The function to call when destructing elements in the vector.
/// @return PAL_SUCCESS if the vector was created successfully, or an error code if there was a problem.
palResult palVectorCreate(palVector** ppVector, size_t elementSize, size_t initialCapacity,
                          palVectorDestructFunc destructFunc);

/// @brief Initialize an existing vector.
///
/// @param pVector The pointer to the vector.
/// @param elementSize The size of each element in the vector.
/// @param initialCapacity The initial capacity of the vector.
/// @param destructFunc The function to call when destructing elements in the vector.
/// @return PAL_SUCCESS if the vector was initialized successfully, or an error code if there was a problem.
palResult palVectorInitialize(palVector* pVector, size_t elementSize, size_t initialCapacity,
                              palVectorDestructFunc destructFunc);

/// @brief Deinitialize a vector.
///
/// @param pVector The pointer to the vector.
/// @return PAL_SUCCESS if the vector was deinitialized successfully, or an error code if there was a problem.
palResult palVectorDeinitialize(palVector* pVector);

/// @brief Destroy a vector.
///
/// @param pVector The pointer to the vector.
/// @return PAL_SUCCESS if the vector was destroyed successfully, or an error code if there was a problem.
palResult palVectorDestroy(palVector* pVector);

/// @brief Push an element to the back of the vector.
///
/// @param pVector The pointer to the vector.
/// @param pElement The element to push.
/// @return PAL_SUCCESS if the element was pushed successfully, or an error code if there was a problem.
/// @note The vector will automatically resize if it is full.
palResult palVectorPushBack(palVector* pVector, const void* pElement);

/// @brief Pop an element from the back of the vector.
///
/// @param pVector The pointer to the vector.
/// @param pElement The address to store the popped element.
/// @return PAL_SUCCESS if the element was popped successfully, or an error code if there was a problem.
/// @note The function will return an error if the vector is empty; no resizing will occur.
/// @note The popped element will be removed from the vector.
/// @note The popped element will be destructed if a destruct function is provided.
palResult palVectorPopBack(palVector* pVector, void* pElement);

/// @brief Get an element from the vector.
///
/// @param pVector The pointer to the vector.
/// @param index The index of the element to get.
/// @param pElement The address to store the element.
/// @return PAL_SUCCESS if the element was retrieved successfully, or an error code if there was a problem.
palResult palVectorGet(palVector* pVector, size_t index, void* pElement);

/// @brief Set an element in the vector.
///
/// @param pVector The pointer to the vector.
/// @param index The index of the element to set.
/// @param pElement The element to set.
/// @return PAL_SUCCESS if the element was set successfully, or an error code if there was a problem.
palResult palVectorSet(palVector* pVector, size_t index, const void* pElement);

/// @brief Get an element from the vector at a specific index.
///
/// @param pVector The pointer to the vector.
/// @param newSize The new size of the vector.
/// @return PAL_SUCCESS if the vector was resized successfully, or an error code if there was a problem.
palResult palVectorResize(palVector* pVector, size_t newSize);

/// @brief Reserve space for a specific number of elements in the vector.
///
/// @param pVector The pointer to the vector.
/// @param newCapacity The new capacity of the vector.
/// @return PAL_SUCCESS if the vector was resized successfully, or an error code if there was a problem.
palResult palVectorReserve(palVector* pVector, size_t newCapacity);

/// @brief Clear the vector.
///
/// @param pVector The pointer to the vector.
/// @return PAL_SUCCESS if the vector was cleared successfully, or an error code if there was a problem.
palResult palVectorClear(palVector* pVector);

/// @brief Get the size of the vector.
///
/// @param pVector The pointer to the vector.
/// @param pSize The address to store the size of the vector.
/// @return PAL_SUCCESS if the size was retrieved successfully, or an error code if there was a problem.
palResult palVectorGetSize(palVector* pVector, size_t* pSize);

/// @brief Get the capacity of the vector.
///
/// @param pVector The pointer to the vector.
/// @param pCapacity The address to store the capacity of the vector.
/// @return PAL_SUCCESS if the capacity was retrieved successfully, or an error code if there was a problem.
palResult palVectorGetCapacity(palVector* pVector, size_t* pCapacity);

/// @brief Get the data pointer of the vector.
///
/// @param pVector The pointer to the vector.
/// @param ppData The address to store the data pointer.
/// @return PAL_SUCCESS if the data pointer was retrieved successfully, or an error code if there was a problem.
palResult palVectorGetData(palVector* pVector, void** ppData);

/// @brief Get the size of each element in the vector.
///
/// @param pVector The pointer to the vector.
/// @param pElementSize The address to store the size of each element.
/// @return PAL_SUCCESS if the element size was retrieved successfully, or an error code if there was a problem.
palResult palVectorGetElementSize(palVector* pVector, size_t* pElementSize);

/// @brief Get an element from the vector at a specific index.
///
/// @param pVector The pointer to the vector.
/// @param index The index of the element to get.
/// @param ppElement The address to store the element.
/// @return PAL_SUCCESS if the element was retrieved successfully, or an error code if there was a problem.
/// @note The element will not be destructed.
palResult palVectorGetElement(palVector* pVector, size_t index, void** ppElement);

/// @brief Set an element in the vector at a specific index.
///
/// @param pVector The pointer to the vector.
/// @param index The index of the element to set.
/// @param pElement The element to set.
/// @return PAL_SUCCESS if the element was set successfully, or an error code if there was a problem.
palResult palVectorSetElement(palVector* pVector, size_t index, const void* pElement);

#endif // PAL_VECTOR_H