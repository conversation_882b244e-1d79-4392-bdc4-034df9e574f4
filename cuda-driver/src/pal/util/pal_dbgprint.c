/*
 ***********************************************************************************************************************
 *
 *  Copyright (c) 2017-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is
 *  furnished to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in all
 *  copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 *  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 *  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 *  SOFTWARE.
 *
 **********************************************************************************************************************/

#include "pal_dbgprint.h"
#include "pal_os.h"

#include <assert.h>
#include <regex.h>
#include <string.h>

#if ENABLE_PRINTS_ASSERTS
/// Global callback used to report debug print messages.
/// If the pointer is set to a valid callback, the callback will be called every time a debug message is printed
palDbgPrintCallback g_dbgPrintCallback = { NULL, NULL };

#if defined(__unix__)
// Directory where log files will be written.
static palInt8 g_dbgLogDirectoryStr[FILENAME_MAX] = "/tmp/";
#endif

// The default log file name.
static palInt8 g_dbgLogFileNameStr[FILENAME_MAX] = "palLog.txt";

// Entry in the global table of debug print targets. Defines the debug category, output mode and prefix string to use.
typedef struct palDbgPrintTarget_st
{
    palDbgPrintMode outputMode;  // Print to debugger, print to file, or disabled.
    const palInt8*  pPrefix;     // Prefix to add to each debug print.
} palDbgPrintTarget;

// Global table of information for each debug print category.
static palDbgPrintTarget g_dbgPrintTable[PAL_DBG_PRINT_MODE_COUNT] =
{
    { PAL_DBG_PRINT_MODE_PRINT, "Error"},
    { PAL_DBG_PRINT_MODE_PRINT, "Warn"},
    { PAL_DBG_PRINT_MODE_PRINT, "Info"},
    { PAL_DBG_PRINT_MODE_PRINT, "Callback"},
};

static void ipalDbgPrintOutputString(const palDbgPrintTarget* pTarget, palDbgPrintCategory category,
                                     const palInt8* pString);
static palResult ipalDbgPrintBuildString(palInt8* pOutBuf, size_t bufSize, const palDbgPrintTarget* pTarget,
                                         palDbgPrintStyle style, palBool isApiPrefix, const palInt8* pFormat,
                                         va_list argList) PRINTF_FORMAT(6, 0);
static void ipalDbgPrintfVImpl(palDbgPrintCategory category, palDbgPrintStyle style, palBool isApiPrefix,
                               const palInt8* pFormat, va_list argList) PRINTF_FORMAT(4, 0);

// =====================================================================================================================
// Sends the specified log string (pString) to the appropriate output (i.e., file or debugger, configured in the target
// argument).
static void ipalDbgPrintOutputString(const palDbgPrintTarget* pTarget, palDbgPrintCategory category,
                                     const palInt8* pString)
{
    palResult result;
    palOsFileHandle hLogFileHandle;

    assert(pTarget != NULL);

    switch (pTarget->outputMode)
    {
    case PAL_DBG_PRINT_MODE_PRINT:
    {
        // Otherwise, send the string to stderr.
        fputs(pString, stderr);

        // If there's a valid callback function, then invoke it with the current message.
        if (g_dbgPrintCallback.pCallbackFunc != NULL)
        {
            g_dbgPrintCallback.pCallbackFunc(g_dbgPrintCallback.pUserData, category, pString);
        }

        break;
    }
    case PAL_DBG_PRINT_MODE_PRINT_CALLBACK:
    {
        // Only output to the debug callback and avoid other debug output.

        // If there's a valid callback function, then invoke it with the current message.
        if (g_dbgPrintCallback.pCallbackFunc != NULL)
        {
            g_dbgPrintCallback.pCallbackFunc(g_dbgPrintCallback.pUserData, category, pString);
        }

        break;
    }
    case PAL_DBG_PRINT_MODE_FILE:
    {
        result = palDbgPrintOpenLogFile(&hLogFileHandle, g_dbgLogFileNameStr, PAL_OS_OPEN_APPEND);
        if (result == PAL_SUCCESS)
        {
            palOsFileWrite(hLogFileHandle, pString, strlen(pString));

            // Close the opened file.
            palOsFileClose(hLogFileHandle);
        }
        break;
    }
    case PAL_DBG_PRINT_MODE_DISABLE:
    default:
        // For performance, the debug print methods should avoid formatting strings
        // and attempting to output the string earlier on than this.
        // PAL_NEVER_CALLED();
        assert(0);
        break;
    }
}

// =====================================================================================================================
// Assembles a log string in the specified output buffer.  Will return ErrorInvalidMemorySize if the destination buffer
// is not large enough.
static palResult ipalDbgPrintBuildString(palInt8* pOutBuf, size_t bufSize, const palDbgPrintTarget* pTarget,
                                         palDbgPrintStyle style, palBool isApiPrefix, const palInt8* pFormat,
                                         va_list argList)
{
    const palInt8* const pApiPrefixString = "NOONE-CUDA: ";
    const palInt8* const pPalPrefixString = "NOONE-PAL: ";
#if defined(_WIN32)
    const char* const pEndString = "\r\n";
#elif defined(__unix__)
    const char* const pEndString = "\n";
#endif
    size_t length = strlen(pOutBuf);
    palInt8 pExtraBuf[128] = {'\0'};
    pOutBuf[0] = '\0';

    // Add the prefix string, if requested
    if ((style & PAL_DBG_PRINT_STYLE_NO_PREFIX) == 0)
    {
        // Compute the length of the destination string to prevent buffer overruns.
        length = strlen(pOutBuf);
        if (isApiPrefix == PAL_TRUE)
        {
            strncat(pOutBuf, pApiPrefixString, bufSize - length - 1);
        }
        else
        {
            strncat(pOutBuf, pPalPrefixString, bufSize - length - 1);
        }

        length = strlen(pOutBuf);
        strncat(pOutBuf, pTarget->pPrefix, bufSize - length - 1);

        length = strlen(pOutBuf);
        strncat(pOutBuf, ": ", bufSize - length - 1);

        length = strlen(pOutBuf);
        palOsSnprintf(pExtraBuf, 128, "[pid:%-5d tid: %-5d] ", palOsGetProcessId(), palOsGetThreadId());
        strncat(pOutBuf, pExtraBuf, bufSize - length - 1);
    }

    length = strlen(pOutBuf);
    palOsVsnprintf(pOutBuf + length, bufSize - length, pFormat, argList);

    // Add the CR/LF, if requested.
    if ((style & PAL_DBG_PRINT_STYLE_NO_CRLF) == 0)
    {
        length = strlen(pOutBuf);
        strncat(pOutBuf, pEndString, bufSize - length - 1);
    }

    length = strlen(pOutBuf);
    assert(length < bufSize);

    // Assume that if the final string length is bufSize-1 then some stuff was truncated.
    return (length == (bufSize - 1)) ? PAL_ERROR_OUT_OF_MEMORY : PAL_SUCCESS;
}

// =====================================================================================================================
// Assembles a log string and sends it to the desired output target.  Common implementation shared by the rest of the
// debug print functions.
static void ipalDbgPrintfVImpl(palDbgPrintCategory category, palDbgPrintStyle style, palBool isApiPrefix,
                               const palInt8* pFormat, va_list argList)
{
    // Look up the debug print target based on the category
    const palDbgPrintTarget* pTarget = &g_dbgPrintTable[category];
    const size_t bufferLength        = 1024;
    const size_t largeBufferLength   = 1024 * 1024;

    palInt8 buffer[bufferLength];
    palInt8* pOutputBuf = &buffer[0];
    palInt8* pLargeBuf  = NULL;

    if (ipalDbgPrintBuildString(pOutputBuf, bufferLength, pTarget, style, isApiPrefix, pFormat, argList) != PAL_SUCCESS)
    {
        pLargeBuf = (palInt8*)malloc(largeBufferLength * sizeof(palInt8));
        if (pLargeBuf != NULL)
        {
            pOutputBuf = pLargeBuf;
            ipalDbgPrintBuildString(pOutputBuf, largeBufferLength, pTarget, style, isApiPrefix, pFormat, argList);
        }
    }

    ipalDbgPrintOutputString(pTarget, category, pOutputBuf);

    if (pLargeBuf != NULL)
    {
        free(pLargeBuf);
    }
}

// =====================================================================================================================
// Removes all ANSI color codes from the string. This is used to remove the color codes from the string before
// sending it to the debugger or file.
static void ipalDbgPrintStripColorCodes(const char* pSrc, char* pDst, size_t dstSize)
{
    const char* pStr         = pSrc;
    size_t      outputLength = 0;
    palInt32    regcompRet;
    regex_t     regex;

    // Compile the regular expression
    regcompRet = regcomp(&regex, "\x1B\\[[0-9;]*m", REG_EXTENDED);
    if (regcompRet != 0)
    {
        // On regex compilation failure, simply copy as much as fits in pDst.
        size_t srcLen = strlen(pSrc);
        if (srcLen >= dstSize)
        {
            srcLen = dstSize - 1;
        }
        memcpy(pDst, pSrc, srcLen);
        pDst[srcLen] = '\0';

        return;
    }

    regmatch_t match;
    while (*pSrc && (outputLength + 1 < dstSize)) // Reserve space for '\0'
    {
        if (regexec(&regex, pSrc, 1, &match, 0) == 0 && match.rm_so == 0)
        {
            // If a color code is found at current position, skip it.
            pSrc += match.rm_eo;
        }
        else
        {
            // Copy one character
            *pDst++ = *pSrc++;
            outputLength++;
        }
    }
    *pDst = '\0';

    regfree(&regex);
}

// =====================================================================================================================
// Generic debug printf function to be used when the caller wishes to specify the output category and style.
void palDbgPrintf(palDbgPrintCategory category, palDbgPrintStyle style, palBool isApiPrefix, const palInt8* pFormat,
                  ...)
{
    assert(category < PAL_DBG_PRINT_CAT_COUNT);

    if (g_dbgPrintTable[category].outputMode != PAL_DBG_PRINT_MODE_DISABLE)
    {
        va_list argList;
        va_start(argList, pFormat);

        if (g_dbgPrintTable[category].outputMode == PAL_DBG_PRINT_MODE_FILE)
        {
            // If the output mode is set to print callback, we need to strip the color codes
            // before sending it to the callback.
            char  strippedString[1024];
            char* pOutputBuf = &strippedString[0];
            memset(pOutputBuf, 0, sizeof(strippedString));
            ipalDbgPrintStripColorCodes(pFormat, pOutputBuf, sizeof(strippedString));
            ipalDbgPrintfVImpl(category, style, isApiPrefix, pOutputBuf, argList);
        }
        else
        {
            ipalDbgPrintfVImpl(category, style, isApiPrefix, pFormat, argList);
        }

        va_end(argList);
    }
}

// =====================================================================================================================
// Assembles a log string and sends it to the desired output target.  This method accepts a pre-initialized va_list
// parameter instead of a direct variable argument list, and is used when printing out messages on behalf of SC.
void palDbgPrintfV(palDbgPrintCategory category, palDbgPrintStyle style, palBool isApiPrefix, const palInt8* pFormat,
                   va_list argList)
{
    assert(category < PAL_DBG_PRINT_CAT_COUNT);

    if (g_dbgPrintTable[category].outputMode != PAL_DBG_PRINT_MODE_DISABLE)
    {
        if (g_dbgPrintTable[category].outputMode == PAL_DBG_PRINT_MODE_FILE)
        {
            // If the output mode is set to print callback, we need to strip the color codes
            // before sending it to the callback.
            char  strippedString[1024];
            char* pOutputBuf = &strippedString[0];
            memset(pOutputBuf, 0, sizeof(strippedString));
            ipalDbgPrintStripColorCodes(pFormat, pOutputBuf, sizeof(strippedString));
            ipalDbgPrintfVImpl(category, style, isApiPrefix, pOutputBuf, argList);
        }
        else
        {
            ipalDbgPrintfVImpl(category, style, isApiPrefix, pFormat, argList);
        }
    }
}

// =====================================================================================================================
palResult palDbgPrintInitialize(palInt32 logMode, palInt8* pLogDirectory, palInt8* pLogFileName)
{
    // Set the default log directory and file name.
    memset(g_dbgLogDirectoryStr, 0, sizeof(g_dbgLogDirectoryStr));
    memset(g_dbgLogFileNameStr, 0, sizeof(g_dbgLogFileNameStr));

    if ((logMode < PAL_DBG_PRINT_MODE_DISABLE) || (logMode >= PAL_DBG_PRINT_MODE_COUNT))
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pLogDirectory != NULL)
    {
        strncpy(g_dbgLogDirectoryStr, pLogDirectory, sizeof(g_dbgLogDirectoryStr) - 1);
    }

    if (pLogFileName != NULL)
    {
        strncpy(g_dbgLogFileNameStr, pLogFileName, sizeof(g_dbgLogFileNameStr) - 1);
    }

    // Set the logging type.
    for (int i = 0; i < PAL_DBG_PRINT_CAT_COUNT; i++)
    {
        palDbgPrintSetMode((palDbgPrintCategory)i, (palDbgPrintMode)logMode);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
// Sets the debug print mode (output to debugger, write to file, disabled) for the specified category of messages.
// Probably controlled by setting and set during initialization.
void palDbgPrintSetMode(
    palDbgPrintCategory category, // Message category to control (e.g., CS dumps, SC output, etc.).
    palDbgPrintMode     mode)     // New mode to be used for this message category.
{
    assert(category < PAL_DBG_PRINT_CAT_COUNT);

    g_dbgPrintTable[category].outputMode = mode;
}

// =====================================================================================================================
// Opens a file called "pFilename" that resides in the "LogDirectoryStr" directory.  This function exists in all build
// configurations.
palResult palDbgPrintOpenLogFile(palOsFileHandle* pFileHandle, const palInt8* pFileName, palUint32 flags)
{
    palResult result = PAL_SUCCESS;
    palInt8 fullyQualifiedFilename[FILENAME_MAX];

    palOsSnprintf(&fullyQualifiedFilename[0], FILENAME_MAX, "%s%s", g_dbgLogDirectoryStr, pFileName);
    if (palOsFileOpen(fullyQualifiedFilename, (palUint64)flags, pFileHandle) != PAL_OS_SUCCESS)
    {
        result = PAL_ERROR_FILE_NOT_FOUND;
    }
    assert(result == PAL_SUCCESS);

    return result;
}

// =====================================================================================================================
// Sets the global debug print callback.
void palDbgPrintSetPrintCallback(palDbgPrintCallback* pCallback)
{
    g_dbgPrintCallback.pCallbackFunc = pCallback->pCallbackFunc;
    g_dbgPrintCallback.pUserData     = pCallback->pUserData;
}

// =====================================================================================================================
void palDbgPrintSetLogDirectory(const palInt8* pLogDirectory)
{
    assert(pLogDirectory != NULL);
    assert(strlen(pLogDirectory) < sizeof(g_dbgLogDirectoryStr));

    memset(g_dbgLogDirectoryStr, 0, sizeof(g_dbgLogDirectoryStr));
    strncpy(g_dbgLogDirectoryStr, pLogDirectory, sizeof(g_dbgLogDirectoryStr) - 1);
}

// =====================================================================================================================
void palDbgPrintSetLogFileName(const palInt8* pLogFileName)
{
    assert(pLogFileName != NULL);
    assert(strlen(pLogFileName) < sizeof(g_dbgLogFileNameStr));

    memset(g_dbgLogFileNameStr, 0, sizeof(g_dbgLogFileNameStr));
    strncpy(g_dbgLogFileNameStr, pLogFileName, sizeof(g_dbgLogFileNameStr) - 1);
}

// =====================================================================================================================
// Print backtrace
void palDbgPrintPrintBacktrace(void)
{
    static size_t BT_BUF_SIZE = 100;
    palInt32 j, nptrs;
    void* pBuffer[BT_BUF_SIZE];
    palInt8** ppStrings;

    nptrs = backtrace(pBuffer, BT_BUF_SIZE);
    printf("backtrace() return %d address\n", nptrs);

    ppStrings = backtrace_symbols(pBuffer, nptrs);
    if (ppStrings == NULL)
    {
        perror("backtrace_symbols error!");
        exit(EXIT_FAILURE);
    }

    for (j = 0; j < nptrs; j++)
    {
        printf("%s\n", ppStrings[j]);
    }

    free(ppStrings);
}

#endif