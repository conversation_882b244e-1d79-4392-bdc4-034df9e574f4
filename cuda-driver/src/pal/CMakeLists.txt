add_library(pal STATIC)

set(PAL_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR})
message(STATUS "PAL_SOURCE_DIR: ${PAL_SOURCE_DIR}")

set(PAL_SETTINGS_GENERATED_DIR ${CMAKE_BINARY_DIR}/src/pal/util/settings)
message(STATUS "PAL_SETTINGS_GENERATED_DIR: ${PAL_SETTINGS_GENERATED_DIR}")

# Add subdirectories to build static library libutil.a and libthunk.a
add_subdirectory(util)
add_subdirectory(thunk)

# Build the static library libpal.a
add_subdirectory(core)

# Link the static library libutil.a and libthunk.a
target_link_libraries(pal PUBLIC util thunk)

# Add the include file path.
target_include_directories(pal PUBLIC
    ${PAL_SOURCE_DIR}
    ${PAL_SOURCE_DIR}/core
    ${PAL_SOURCE_DIR}/thunk
    ${PAL_SOURCE_DIR}/util
    ${PAL_SETTINGS_GENERATED_DIR}
)
