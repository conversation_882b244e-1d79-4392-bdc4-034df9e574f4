#ifndef PAL_TLSTHREADMANAGER_H_
#define PAL_TLSTHREADMANAGER_H_

#include "pal.h"
#include "pal_os.h"
#include "pal_structures.h"
#include "pal_types.h"

typedef enum palTlsThreadManagerStatus_enum
{
    PAL_TLS_THREAD_MANAGER_STATUS_NOT_INITIALIZED = 0,
    PAL_TLS_THREAD_MANAGER_STATUS_INITIALIZED,
    PAL_TLS_THREAD_MANAGER_STATUS_DESTROYED
} palTlsThreadManagerStatus;

struct palTlsThreadManager_st
{
    // Pointer to the global manager
    palGlobalManager* pGlobalManager;

    // Status of the thread manager
    palTlsThreadManagerStatus status;

    // The mutex for the thread manager
    palOsMutex mutex;

    // The OS-specific key used to look up a thread's data
    // - the data pointed to by this key is a palTlsThreadData which has the whole
    //   context stack, mutex lists, and a bunch of other things
    // - this structure gets freed as thread-exit.
    palOsTlsEntry tlsEntry;

    // Entry used as a flag to indicate that the thread data is being destroyed
    palOsTlsEntry tlsEntryInThreadDataDestruction;

    // The key used to look up the top of the context stack
    // - Storing the top of the context stack after destruction works around this.
    palOsTlsEntry tlsEntryTopCtx;

    // The thread data List
    palTlsThreadData* pThreadDataList;

    // The thread counter
    palUint64 threadIdCounter;
};

/// @brief Create a thread manager
///
/// @param ppThreadManager The thread manager to create
/// @param pGlobalManager The global manager to use
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palTlsThreadManagerCreate(palTlsThreadManager** ppThreadManager, palGlobalManager* pGlobalManager);

/// @brief Initialize a thread manager
///
/// @param pThreadManager The thread manager to initialize
/// @param pGlobalManager The global manager to use
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palTlsThreadManagerInitialize(palTlsThreadManager* pThreadManager, palGlobalManager* pGlobalManager);

/// @brief Deinitialize a thread manager
///
/// @param pThreadManager The thread manager to deinitialize
void palTlsThreadManagerDeinitialize(palTlsThreadManager* pThreadManager);

/// @brief Destroy a thread manager
///
/// @param pThreadManager The thread manager to destroy
void palTlsThreadManagerDestroy(palTlsThreadManager* pThreadManager);

/// @brief Get the current thread data object
///
/// @param ppThreadData The thread data object to get
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palTlsThreadManagerGetCurrentThreadData(palTlsThreadManager* pThreadManager, palTlsThreadData** ppThreadData);

#endif // PAL_TLSTHREADMANAGER_H_