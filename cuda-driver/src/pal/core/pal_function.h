#ifndef PAL_FUNCTION_H_
#define PAL_FUNCTION_H_

#include "pal.h"
#include "pal_types.h"
#include "pal_structures.h"

typedef enum palFuncCache_enum
{
    PAL_FUNC_CACHE_PREFER_NONE   = 0x00, /**< no preference for shared memory or L1 (default) */
    PAL_FUNC_CACHE_PREFER_SHARED = 0x01, /**< prefer larger shared memory and smaller L1 cache */
    PAL_FUNC_CACHE_PREFER_L1     = 0x02, /**< prefer larger L1 cache and smaller shared memory */
    PAL_FUNC_CACHE_PREFER_EQUAL  = 0x03  /**< prefer equal sized L1 cache and shared memory */
} palFuncCache;




#endif // PAL_FUNCTION_H_