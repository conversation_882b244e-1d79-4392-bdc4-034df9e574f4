#ifndef PAL_CONTEXT_H_
#define PAL_CONTEXT_H_

#include "pal.h"
#include "pal_device.h"
#include "pal_function.h"
#include "pal_os.h"
#include "pal_structures.h"
#include "pal_types.h"

typedef enum palCtxApiVersion_enum
{
    PAL_CTX_API_VERSION_DEFAULT = 0,
    PAL_CTX_API_VERSION_v3010   = 3010,
    PAL_CTX_API_VERSION_v3020   = 3020,
} palCtxApiVersion;

/**
 *
 * Shared memory configurations
 */
typedef enum palSharedConfig_enum
{
    PAL_SHARED_MEM_CONFIG_DEFAULT_BANK_SIZE    = 0x00, /**< set default shared memory bank size */
    PAL_SHARED_MEM_CONFIG_FOUR_BYTE_BANK_SIZE  = 0x01, /**< set shared memory bank width to four bytes */
    PAL_SHARED_MEM_CONFIG_EIGHT_BYTE_BANK_SIZE = 0x02  /**< set shared memory bank width to eight bytes */
} palSharedConfig;

typedef enum palCtxStreamPriorityType_enum
{
    PAL_CTX_STREAM_PRIORITY_TYPE_LOW    = -1,
    PAL_CTX_STREAM_PRIORITY_TYPE_NORMAL = 0,
    PAL_CTX_STREAM_PRIORITY_TYPE_HIGH   = 1,
    PAL_CTX_STREAM_PRIORITY_TYPE_MAX
} palCtxStreamPriorityType;

typedef enum palFlushGPUDirectRDMAWritesScope_enum
{
    PAL_FLUSH_GPU_DIRECT_RDMA_WRITES_TO_OWNER       = 100, /**< Blocks until remote writes are visible to the CUDA device context owning the data. */
    PAL_FLUSH_GPU_DIRECT_RDMA_WRITES_TO_ALL_DEVICES = 200  /**< Blocks until remote writes are visible to all CUDA device contexts. */
} palFlushGPUDirectRDMAWritesScope;

typedef enum palFlushGPUDirectRDMAWritesTarget_enum {
    PAL_FLUSH_GPU_DIRECT_RDMA_WRITES_TARGET_CURRENT_CTX = 0 /**< Sets the target for ::cuFlushGPUDirectRDMAWrites() to the currently active CUDA device context. */
} palFlushGPUDirectRDMAWritesTarget;

struct palContextCreateInfo_st
{
    // The flag to indicate if the context is primary context
    palBool isPrimary;

    // The context creation flags
    palUint32 flags;

    // The execution affinity parameters
    palExecAffinityParam* pExecAffinityParams;

     // Number of execution affinity parameters
    palInt32 numParams;
};

typedef enum palContextStatus_enum
{
    PAL_CONTEXT_STATUS_NOT_INITIALIZED = 0,
    PAL_CONTEXT_STATUS_INITIALIZING,
    PAL_CONTEXT_STATUS_INITIALIZED,
    PAL_CONTEXT_STATUS_DEINITIALIZING,
    PAL_CONTEXT_STATUS_MAX
} palContextStatus;

typedef enum palCtxFlags_enum
{
    PAL_CTX_SCHED_AUTO           = 0x00, /**< Automatic scheduling */
    PAL_CTX_SCHED_SPIN           = 0x01, /**< Set spin as default scheduling */
    PAL_CTX_SCHED_YIELD          = 0x02, /**< Create a primary context */
    PAL_CTX_SCHED_BLOCKING_SYNC  = 0x04, /**< Set blocking synchronization as default scheduling */
    PAL_CTX_BLOCKING_SYNC        = 0x04, /**< Set blocking synchronization as default scheduling,
                                              \deprecated This flag was deprecated as of CUDA 4.0 */
    PAL_CTX_SCHED_MASK           = 0x07, /**< Mask for scheduling flags */
    PAL_CTX_MAP_HOST             = 0x08, /**< Set CUDA to support mapped pinned allocations.
                                              This flag must be set in order to allocate pinned
                                              host memory that is accessible to the GPU */
    PAL_CTX_LMEM_RESIZE_TO_MAX   = 0x10, /**< Keep local memory allocation after launch */
    PAL_CTX_COREDUMP_ENABLE      = 0x20, /**< Enable core dump for the context */
    PAL_CTX_USER_COREDUMP_ENABLE = 0x40, /**< Enable user core dump for the context */
    PAL_CTX_SYNC_MEMOPS          = 0x80, /**< Synchronize memory operations in the context */
    PAL_CTX_FLAGS_MASK           = 0xFF, /**< Mask for context flags */
} palCtxFlags;

typedef enum palCtxLimit_enum
{
    PAL_CTX_LIMIT_STACK_SIZE                       = 0x00, /**< GPU thread stack size */
    PAL_CTX_LIMIT_PRINTF_FIFO_SIZE                 = 0x01, /**< GPU printf FIFO size */
    PAL_CTX_LIMIT_MALLOC_HEAP_SIZE                 = 0x02, /**< GPU malloc heap size */
    PAL_CTX_LIMIT_DEV_RUNTIME_SYNC_DEPTH           = 0x03, /**< GPU device runtime launch synchronize depth */
    PAL_CTX_LIMIT_DEV_RUNTIME_PENDING_LAUNCH_COUNT = 0x04, /**< GPU device runtime pending launch count */
    PAL_CTX_LIMIT_MAX_L2_FETCH_GRANULARITY         = 0x05, /**< A value between 0 and 128 that indicates the maximum fetch granularity of L2 (in Bytes). This is a hint */
    PAL_CTX_LIMIT_PERSISTING_L2_CACHE_SIZE         = 0x06, /**< A size in bytes for L2 persisting lines cache size */
    PAL_CTX_LIMIT_MAX
} palCtxLimit;

struct palContextPersistentState_st
{
    // The context status.
    palContextStatus status;

    // The device handle
    palDevice* pDevice;

    // The context id
    palUint64 contextId;

    // The flag to indicate if the context is primary context.
    palBool isPrimary;

    // This mutex is used to protect the context state operations.
    palOsMutex mutex;

    // The context flags;
    palUint32 flags;

    // This is equal to the number of times that this context appears in
    // any context stack plus one if the context state is initialized.
    // If this drops to zero then the persistent state will be freed.
    volatile _Atomic palUint32 threadCtxStackRefCount;

    // Reference count used to destroy via cuCtxAttach/cuCtxDetach
    volatile _Atomic palUint32 attachRefCount;
};

struct palContext_st
{
    // The context persistent state
    palContextPersistentState persistentState;

    // The device context handle
    void* pKmdContext;

    // The low priority of stream
    palCtxStreamPriorityType lowPriority;

    // The high priority of stream
    palCtxStreamPriorityType highPriority;

    // The api version
    palUint32 apiVersion;

    // The context current flags;
    palUint32 currentFlags;

    // The context function cache preference
    palFuncCache funcCache;

    // The context shared memory configuration
    palSharedConfig sharedConfig;

    // The execution affinity type
    palExecAffinityType execAffinityType;

    // The execution affinity parameters
    palExecAffinityParam execAffinityParam;

    // The memory manager for the context
    palMemoryManager* pMemoryManager;

    // The next context in the list
    struct palContext_st* pNext;

    // The previous context in the list
    struct palContext_st* pPrev;
};

/// @brief Create a context
///
/// @param ppCtx The context to create
/// @param pDevice The device to use
/// @param pCreateInfo The context creation info
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palContextCreate(palContext** ppCtx, palDevice* pDevice, palContextCreateInfo* pCreateInfo);

/// @brief Initialize the persistent state of the context.
///
/// @param pCtx The context to initialize.
/// @param pDevice The device to use.
/// @param isPrimary Flag to indicate if the context is primary.
/// @return PAL_SUCCESS on success, or an error code on failure.
palResult palContextPersistentStateInitialize(palContext* pCtx, palDevice* pDevice, palBool isPrimary);

/// @brief Initialize a context
///
/// @param pCtx The context to initialize
/// @param pDevice The device to use
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palContextInitialize(palContext* pCtx, palDevice* pDevice, palContextCreateInfo* pCreateInfo);

/// @brief Deinitialize the persistent state of the context.
///
/// @param pCtx The context to deinitialize.
void palContextPersistentStateDeinitialize(palContext* pCtx);

/// @brief Deinitialize a context
///
/// @param pCtx The context to deinitialize
void palContextDeinitialize(palContext* pCtx);

/// @brief Destroy a context
///
/// @param pCtx The context to destroy
void palContextDestroy(palContext* pCtx);

/// @brief Increment the reference count for a context
///
/// @param pCtx The context to get the device from
void palContextThreadRefCountIncrement(palContext* pCtx);

/// @brief Decrement the reference count for a context
///
/// @param pCtx The context to get the device from
void palContextThreadRefCountDecrement(palContext* pCtx);

/// @brief Increment the attach reference count for a context
///
/// @param pCtx The context to increment the attach reference count for
void palContextAttachRefCountIncrement(palContext* pCtx);

/// @brief Decrement the attach reference count for a context
///
/// @param pCtx The context to decrement the attach reference count for
/// @param pTlsThreadData The thread data to use for popping the context if the attach reference count reaches zero
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palContextAttachRefCountDecrement(palContext* pCtx, palTlsThreadData* pTlsThreadData);

/// @brief Check if a context is active
///
/// @param pCtx The context to check
/// @return Returns PAL_TRUE if the context is active, PAL_FALSE otherwise
palBool palContextIsActive(palContext* pCtx);

/// @brief Get the device associated with a context
///
/// @param pCtx The context to get the device from
/// @return Returns the device handle associated with the context
palDevice* palContextGetDevice(palContext* pCtx);

/// @brief Get the execution affinity of a context
///
/// @param pCtx The context to get the execution affinity from
/// @param execAffinityType Pointer to store the execution affinity type
/// @param pExecAffinityParams Pointer to store the execution affinity parameters

/// @return PAL_SUCCESS on success, or an error code on failure
palResult palContextGetExecAffinity(palContext* pCtx, palExecAffinityType execAffinityType,
                                    palExecAffinityParam* pExecAffinityParams);

/// @brief Update the flags of a context
///
/// @param pCtx The context to update the flags for
/// @param flags The flags to update
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palContextSetFlags(palContext* pCtx, palUint32 flags);

/// @brief Get the api version of a context
///
/// @param pCtx The context to get the api version from
/// @return Returns the api version of the context
palCtxApiVersion palContextGetApiVersion(palContext* pCtx);

/// @brief Set the cache configuration for a context
///
/// @param pCtx The context to set the cache configuration for
/// @param funcCache The cache configuration to set
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palContextSetCacheConfig(palContext* pCtx, palFuncCache funcCache);

/// @brief Get the current shared memory configuration for the context
/// @param pCtx The context to get the shared memory configuration from
/// @return Returns the current shared memory configuration for the context
palFuncCache palContextGetCacheConfig(palContext* pCtx);

/// @brief Get the device ID of a context
///
/// @param pCtx The context to get the device ID from
/// @return Returns the device ID of the context
palInt32 palContextGetDeviceId(palContext* pCtx);

/// @brief Get the flags of a context
///
/// @param pCtx The context to get the flags from
/// @return Returns the flags of the context
palUint32 palContextGetFlags(palContext* pCtx);

/// @brief Get the context ID of a context
///
/// @param pCtx The context to get the context ID from
/// @return Returns the context ID of the context
palUint64 palContextGetContextId(palContext* pCtx);

/// @brief Set a limit value for a context
/// @param pCtx The context to set the limit for
/// @param limit The limit to set
/// @param value The value to set for the limit
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palContextSetLimit(palContext* pCtx, palCtxLimit limit, palUint64 value);

/// @brief Get a limit value for a context
/// @param pCtx The context to get the limit from
/// @param limit The limit to get
/// @param pValue Pointer to store the limit value
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palContextGetLimit(palContext* pCtx, palCtxLimit limit, palUint64* pValue);

/// @brief Get the stream priority range of a context
///
/// @param pCtx The context to get the stream priority from
/// @param pLowPriority Pointer to store the low priority stream type
/// @param pHighPriority Pointer to store the high priority stream type
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palContextGetStreamPriorityRange(palContext* pCtx,
                                           palCtxStreamPriorityType* pLowPriority,
                                           palCtxStreamPriorityType* pHighPriority);

/// @brief Synchronize a context
///
/// @param pCtx The context to synchronize
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palContextSynchronize(palContext* pCtx);

/// @brief Reset the persisting L2 cache of a context
///
/// @param pCtx The context to reset the persisting L2 cache for
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palContextResetPersistingL2Cache(palContext* pCtx);

/// @brief Set the shared memory configuration for a context
/// @param pCtx The context to set the shared memory configuration for
/// @param sharedConfig The shared memory configuration to set
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palContextSetSharedMemConfig(palContext* pCtx, palSharedConfig sharedConfig);

/// @brief Get the shared memory configuration of a context
///
/// @param pCtx The context to get the shared memory configuration from
/// @return Returns the shared memory configuration of the context
palSharedConfig palContextGetSharedMemConfig(palContext* pCtx);

/// @brief Flush GPU Direct RDMA writes for a context
///
/// @param pCtx The context to flush GPU Direct RDMA writes for
/// @param target The target for the flush operation
/// @param scope The scope of the flush operation
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palContextFlushGPUDirectRDMAWrites(palContext* pCtx,
                                             palFlushGPUDirectRDMAWritesTarget target,
                                             palFlushGPUDirectRDMAWritesScope scope);

#endif // PAL_CONTEXT_H_