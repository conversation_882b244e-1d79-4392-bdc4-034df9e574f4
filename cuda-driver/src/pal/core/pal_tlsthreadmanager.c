#include "pal_assert.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * Initialize and tear-down functions.
 * - Each of these verify that they are called only once,
 * - These roll their own locks rather than use the common Lock/Unlock functions.
 */
#if defined(_WIN32)
//#TODO:
#else
static void palTlsThreadDataDestructorPosix(void* pData)
{
    palTlsThreadData* pTlsThreadData = (palTlsThreadData*)pData;
    if (pTlsThreadData == NULL)
    {
        return;
    }

    palOsMutexLock(&pTlsThreadData->pThreadManager->mutex);
    if (pTlsThreadData->pThreadManager->status == PAL_TLS_THREAD_MANAGER_STATUS_INITIALIZED)
    {
        palTlsThreadDataDestroy(pTlsThreadData);
    }
    palOsMutexUnlock(&pTlsThreadData->pThreadManager->mutex);
}
#endif // POSIX

// =====================================================================================================================
palResult palTlsThreadManagerCreate(palTlsThreadManager** ppThreadManager, palGlobalManager* pGlobalManager)
{
    palResult            result         = PAL_SUCCESS;
    palTlsThreadManager* pThreadManager = NULL;

    if ((ppThreadManager == NULL) || (pGlobalManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        *ppThreadManager = NULL;
        return PAL_ERROR_INVALID_VALUE;
    }

    pThreadManager = (palTlsThreadManager*)malloc(sizeof(palTlsThreadManager));
    if (pThreadManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for thread manager.");
        *ppThreadManager = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palTlsThreadManagerInitialize(pThreadManager, pGlobalManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize thread manager.");
        free(pThreadManager);
        *ppThreadManager = NULL;
        return result;
    }

    *ppThreadManager = pThreadManager;

    return result;
}

// =====================================================================================================================
palResult palTlsThreadManagerInitialize(palTlsThreadManager* pThreadManager, palGlobalManager* pGlobalManager)
{
    palResult result = PAL_SUCCESS;
    palInt32  error  = 0;

    if ((pThreadManager == NULL) || (pGlobalManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pThreadManager->status == PAL_TLS_THREAD_MANAGER_STATUS_INITIALIZED)
    {
        PAL_DBG_PRINTF_INFO("Thread manager is already initialized.");
        return PAL_SUCCESS;
    }

    memset(pThreadManager, 0, sizeof(palTlsThreadManager));

    // Initialize the thread manager
    pThreadManager->pGlobalManager = pGlobalManager;

    error = palOsMutexInit(&pThreadManager->mutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize mutex.");
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    pThreadManager->tlsEntry = palOsTlsCreate(palTlsThreadDataDestructorPosix);
    if (pThreadManager->tlsEntry == PAL_OS_TLS_OUT_OF_INDEXES)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory to initialize tls entry for thread's data.");
        palOsMutexDestroy(&pThreadManager->mutex);
        memset(pThreadManager, 0, sizeof(palTlsThreadManager));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    pThreadManager->tlsEntryTopCtx = palOsTlsCreate(NULL);
    if (pThreadManager->tlsEntryTopCtx == PAL_OS_TLS_OUT_OF_INDEXES)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory to initialize tls entry for top context.");
        palOsTlsDestroy(pThreadManager->tlsEntry);
        palOsMutexDestroy(&pThreadManager->mutex);
        memset(pThreadManager, 0, sizeof(palTlsThreadManager));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    pThreadManager->tlsEntryInThreadDataDestruction = palOsTlsCreate(NULL);
    if (pThreadManager->tlsEntryInThreadDataDestruction == PAL_OS_TLS_OUT_OF_INDEXES)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory to initialize tls entry for thread data destruction.");
        palOsTlsDestroy(pThreadManager->tlsEntryTopCtx);
        palOsTlsDestroy(pThreadManager->tlsEntry);
        palOsMutexDestroy(&pThreadManager->mutex);
        memset(pThreadManager, 0, sizeof(palTlsThreadManager));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    pThreadManager->status = PAL_TLS_THREAD_MANAGER_STATUS_INITIALIZED;

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palTlsThreadManagerDeinitialize(palTlsThreadManager* pThreadManager)
{
    if (pThreadManager->status == PAL_TLS_THREAD_MANAGER_STATUS_NOT_INITIALIZED)
    {
        pThreadManager->status = PAL_TLS_THREAD_MANAGER_STATUS_DESTROYED;
        return;
    }
    else if (pThreadManager->status == PAL_TLS_THREAD_MANAGER_STATUS_DESTROYED)
    {
        return;
    }

    PAL_ASSERT(pThreadManager->status == PAL_TLS_THREAD_MANAGER_STATUS_INITIALIZED);

    // Deinitialize the thread manager

    palOsMutexLock(&pThreadManager->mutex);

    // Walk the list of threads that had not exited and destroy them.
    while (pThreadManager->pThreadDataList != NULL)
    {
        // Destroy the thread data
        palTlsThreadDataDestroy(pThreadManager->pThreadDataList);
    }

    // Destroy TLS Keys
    palOsTlsDestroy(pThreadManager->tlsEntryInThreadDataDestruction);
    palOsTlsDestroy(pThreadManager->tlsEntryTopCtx);
    palOsTlsDestroy(pThreadManager->tlsEntry);

    pThreadManager->status = PAL_TLS_THREAD_MANAGER_STATUS_DESTROYED;

    palOsMutexUnlock(&pThreadManager->mutex);

    // Destroy the mutex
    palOsMutexDestroy(&pThreadManager->mutex);

    memset(pThreadManager, 0, sizeof(palTlsThreadManager));
}

// =====================================================================================================================
void palTlsThreadManagerDestroy(palTlsThreadManager* pThreadManager)
{
    if (pThreadManager == NULL)
    {
        return;
    }

    // Deinitialize the thread manager
    palTlsThreadManagerDeinitialize(pThreadManager);

    free(pThreadManager);
}

// =====================================================================================================================
palResult palTlsThreadManagerGetCurrentThreadData(palTlsThreadManager* pThreadManager, palTlsThreadData** ppThreadData)
{
    palResult         result      = PAL_SUCCESS;
    palTlsThreadData* pThreadData = NULL;

    if ((pThreadManager == NULL) || (ppThreadData == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if TLS thread manager has been initialized.
    switch (pThreadManager->status)
    {
    case PAL_TLS_THREAD_MANAGER_STATUS_NOT_INITIALIZED:
        return PAL_ERROR_NOT_INITIALIZED;
    case PAL_TLS_THREAD_MANAGER_STATUS_INITIALIZED:
        break;
    case PAL_TLS_THREAD_MANAGER_STATUS_DESTROYED:
        return PAL_ERROR_DEINITIALIZED;
    default:
        PAL_ASSERT(0);
        break;
    }

    // Look for existing structure in TLS if we haven't done so already.
    pThreadData = (palTlsThreadData*)palOsTlsGetValue(pThreadManager->tlsEntry);
    if (pThreadData != NULL)
    {
        *ppThreadData = pThreadData;
        return PAL_SUCCESS;
    }

    // If we are after the TLS has been destroyed return destroyed handle.
    if (palOsTlsGetValue(pThreadManager->tlsEntryInThreadDataDestruction) == (void*)1)
    {
        // The TLS thread data has been destroyed
        // - return PAL_TLS_THREAD_DATA_DESTROYED;
        *ppThreadData = PAL_TLS_THREAD_DATA_DESTROYED;
        PAL_DBG_PRINTF_ERROR("The TLS thread data has been destroyed.");
        return PAL_SUCCESS;
    }

    // If lazy initialization is allowed, allocate one
    result = palTlsThreadDataCreate(&pThreadData, pThreadManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create thread data.");
        *ppThreadData = NULL;
        return result;
    }

    *ppThreadData = pThreadData;

    return PAL_SUCCESS;
}