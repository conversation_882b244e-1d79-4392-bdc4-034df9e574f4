#ifndef PAL_MEMORYOBJECT_H_
#define PAL_MEMORYOBJECT_H_

#include "pal.h"
#include "pal_memoryflags.h"
#include "pal_types.h"
#include "pal_structures.h"

/**
* Defines the allocation types available
*/
typedef enum palMemAllocationType_enum
{
    PAL_MEM_ALLOCATION_TYPE_INVALID = 0x0,

    /** This allocation type is 'pinned', i.e. cannot migrate from its current
      * location while the application is actively using it
      */
    PAL_MEM_ALLOCATION_TYPE_PINNED  = 0x1,
    PAL_MEM_ALLOCATION_TYPE_MAX     = 0x7FFFFFFF
} palMemAllocationType;

/**
 * Flags for specifying particular handle types
 */
typedef enum palMemAllocationHandleType_enum
{
    PAL_MEM_HANDLE_TYPE_NONE                  = 0x0,  /**< Does not allow any export mechanism. > */
    PAL_MEM_HANDLE_TYPE_POSIX_FILE_DESCRIPTOR = 0x1,  /**< Allows a file descriptor to be used for exporting. Permitted only on POSIX systems. (int) */
    PAL_MEM_HANDLE_TYPE_WIN32                 = 0x2,  /**< Allows a Win32 NT handle to be used for exporting. (HANDLE) */
    PAL_MEM_HANDLE_TYPE_WIN32_KMT             = 0x4,  /**< Allows a Win32 KMT handle to be used for exporting. (D3DKMT_HANDLE) */
    PAL_MEM_HANDLE_TYPE_FABRIC                = 0x8,  /**< Allows a fabric handle to be used for exporting. (CUmemFabricHandle)*/
    PAL_MEM_HANDLE_TYPE_MAX                   = 0x7FFFFFFF
} palMemAllocationHandleType;

/**
 * Specifies the type of location
 */
typedef enum palMemLocationType_enum
{
    PAL_MEM_LOCATION_TYPE_INVALID           = 0x0,
    PAL_MEM_LOCATION_TYPE_DEVICE            = 0x1,  /**< Location is a device location, thus id is a device ordinal */
    PAL_MEM_LOCATION_TYPE_HOST              = 0x2,   /**< Location is host, id is ignored */
    PAL_MEM_LOCATION_TYPE_HOST_NUMA         = 0x3,  /**< Location is a host NUMA node, thus id is a host NUMA node id */
    PAL_MEM_LOCATION_TYPE_HOST_NUMA_CURRENT = 0x4,  /**< Location is a host NUMA node of the current thread, id is ignored */
    PAL_MEM_LOCATION_TYPE_MAX               = 0x7FFFFFFF
} palMemLocationType;

/**
 * Specifies a memory location.
 */
typedef struct palMemLocation_st
{
    palMemLocationType type; /**< Specifies the location type, which modifies the meaning of id. */
    int id;                  /**< identifier for a given this location's ::palMemLocationType. */
} palMemLocation;

/**
 * If set, host memory is portable between CUDA contexts.
 * Flag for ::cuMemHostAlloc()
 */
#define PAL_MEMHOSTALLOC_PORTABLE        0x01

/**
 * If set, host memory is mapped into CUDA address space and
 * ::cuMemHostGetDevicePointer() may be called on the host pointer.
 * Flag for ::cuMemHostAlloc()
 */
#define PAL_MEMHOSTALLOC_DEVICEMAP       0x02

/**
 * If set, host memory is allocated as write-combined - fast to write,
 * faster to DMA, slow to read except via SSE4 streaming load instruction
 * (MOVNTDQA).
 * Flag for ::cuMemHostAlloc()
 */
#define PAL_MEMHOSTALLOC_WRITECOMBINED   0x04

/**
 * If set, host memory is portable between CUDA contexts.
 * Flag for ::cuMemHostRegister()
 */
#define PAL_MEMHOSTREGISTER_PORTABLE     0x01

/**
 * If set, host memory is mapped into CUDA address space and
 * ::cuMemHostGetDevicePointer() may be called on the host pointer.
 * Flag for ::cuMemHostRegister()
 */
#define PAL_MEMHOSTREGISTER_DEVICEMAP    0x02

/**
 * If set, the passed memory pointer is treated as pointing to some
 * memory-mapped I/O space, e.g. belonging to a third-party PCIe device.
 * On Windows the flag is a no-op.
 * On Linux that memory is marked as non cache-coherent for the GPU and
 * is expected to be physically contiguous. It may return
 * ::CUDA_ERROR_NOT_PERMITTED if run as an unprivileged user,
 * ::CUDA_ERROR_NOT_SUPPORTED on older Linux kernel versions.
 * On all other platforms, it is not supported and ::CUDA_ERROR_NOT_SUPPORTED
 * is returned.
 * Flag for ::cuMemHostRegister()
 */
#define PAL_MEMHOSTREGISTER_IOMEMORY     0x04

/**
* If set, the passed memory pointer is treated as pointing to memory that is
* considered read-only by the device.  On platforms without
* ::CU_DEVICE_ATTRIBUTE_PAGEABLE_MEMORY_ACCESS_USES_HOST_PAGE_TABLES, this flag is
* required in order to register memory mapped to the CPU as read-only.  Support
* for the use of this flag can be queried from the device attribute
* ::CU_DEVICE_ATTRIBUTE_READ_ONLY_HOST_REGISTER_SUPPORTED.  Using this flag with
* a current context associated with a device that does not have this attribute
* set will cause ::cuMemHostRegister to error with ::CUDA_ERROR_NOT_SUPPORTED.
*/
#define PAL_MEMHOSTREGISTER_READ_ONLY    0x08

/**
 * Memory Attach Flags
 */
typedef enum palMemAttachFlags_enum
{
    PAL_MEM_ATTACH_GLOBAL = 0x1, /**< Memory can be accessed by any stream on any device */
    PAL_MEM_ATTACH_HOST   = 0x2, /**< Memory cannot be accessed by any stream on any device */
    PAL_MEM_ATTACH_SINGLE = 0x4  /**< Memory can only be accessed by a single stream on the associated device */
} palMemAttachFlags;

typedef struct palMemoryObjectCreateInfo_st
{
    // The memory chunk to create the memory object from.
    palMemoryChunk* pMemoryChunk;

    // The size of the memory object.
    palUint64 size;

    // The memory flags for the memory object.
    palMemoryFlags memFlags;

    // The host flags
    palUint32 hostFlags;

    // The host pointer for the memory object.
    void* pHostPtr;
} palMemoryObjectCreateInfo;

struct palMemoryObject_st
{
    // The corresponding chunk handle.
    palMemoryChunk* pMemoryChunk;

    // The context handle.
    palContext* pContext;

    // The size of the memory object.
    palUint64 size;

    // The actual size of the memory object.
    palUint64 actualSize;

    // The memory flags
    palMemoryFlags memFlags;

    // The user flags
    palUint32 hostFlags;

    // The ref count for the memory block.
    volatile _Atomic palUint32 refCount;
};

/// @brief Create a memory object
///
/// @param ppMemoryObject Pointer to the memory object to create
/// @param pContext The context to associate with the memory object
/// @param pCreateInfo The creation info for the memory object
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryObjectCreate(palMemoryObject** ppMemoryObject, palContext* pContext,
                                palMemoryObjectCreateInfo* pCreateInfo);

/// @brief Retain a memory object (increment the reference count)
///
/// @param pMemoryObject The memory object to retain
void palMemoryObjectRetain(palMemoryObject* pMemoryObject);

/// @brief Release a memory object (decrement the reference count and destroy if it reaches zero)
///
/// @param pMemoryObject The memory object to release
void palMemoryObjectRelease(palMemoryObject* pMemoryObject);

/// @brief Get the offset of a device pointer within a memory object
///
/// @param pMemoryObject Pointer to the memory object
/// @param devicePtr The device pointer to get the offset for
/// @return The offset of the device pointer within the memory object
palUint64 palMemoryObjectGetOffsetOf(palMemoryObject* pMemoryObject, palUint64 devicePtr);

/// @brief Get the device pointer of a memory object
///
/// @param pMemoryObject Pointer to the memory object
/// @return The device pointer of the memory object
palUint64 palMemoryObjectGetDevicePtr(palMemoryObject* pMemoryObject);

/// @brief Get the host pointer of a memory object
///
/// @param pMemoryObject Pointer to the memory object
/// @return The host pointer of the memory object
void* palMemoryObjectGetHostPtr(palMemoryObject* pMemoryObject);

/// @brief Get the size of a memory object
///
/// @param pMemoryObject Pointer to the memory object
/// @return The size of the memory object
palUint64 palMemoryObjectGetSize(palMemoryObject* pMemoryObject);

/// @brief Get the memory flags of a memory object
///
/// @param pMemoryObject Pointer to the memory object
/// @return The memory flags of the memory object
palMemoryFlags* palMemoryObjectGetMemoryFlags(palMemoryObject* pMemoryObject);

/// @brief Get the host flags of a memory object
///
/// @param pMemoryObject Pointer to the memory object
/// @return The host flags of the memory object
palUint32 palMemoryObjectGetHostFlags(palMemoryObject* pMemoryObject);

/// @brief Get the memory manager associated with a memory object
///
/// @param pMemoryObject Pointer to the memory object
/// @return The memory manager associated with the memory object
palMemoryManager* palMemoryObjectGetMemoryManager(palMemoryObject* pMemoryObject);

/// @brief Get the handle for an address range of memory object
///
/// @param pMemoryObject The memory object to get the handle for
/// @param devicePtr The corresponding device pointer for the address range
/// @param size The size of the address range
/// @param handleType The type of handle to get
/// @param flags The flags for the handle, Reserved for future use
/// @param pHandle Pointer to store the handle (dma_buf fd or other handle type)
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryObjectGetHandleForAddressRange(palMemoryObject* pMemoryObject, palUint64 devicePtr, palUint64 size,
                                                  palMemRangeHandleType handleType, palUint64 flags, palUint64* pHandle);

#endif // PAL_MEMORYOBJECT_H_