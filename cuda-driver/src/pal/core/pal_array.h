#ifndef PAL_ARRAY_H_
#define PAL_ARRAY_H_

#include "pal.h"
#include "pal_types.h"
#include "pal_structures.h"

/**
 * Array formats
 */
typedef enum palArrayFormat_enum
{
    PAL_AD_FORMAT_UNSIGNED_INT8  = 0x01, /**< Unsigned 8-bit integers */
    PAL_AD_FORMAT_UNSIGNED_INT16 = 0x02, /**< Unsigned 16-bit integers */
    PAL_AD_FORMAT_UNSIGNED_INT32 = 0x03, /**< Unsigned 32-bit integers */
    PAL_AD_FORMAT_SIGNED_INT8    = 0x08, /**< Signed 8-bit integers */
    PAL_AD_FORMAT_SIGNED_INT16   = 0x09, /**< Signed 16-bit integers */
    PAL_AD_FORMAT_SIGNED_INT32   = 0x0a, /**< Signed 32-bit integers */
    PAL_AD_FORMAT_HALF           = 0x10, /**< 16-bit floating point */
    PAL_AD_FORMAT_FLOAT          = 0x20, /**< 32-bit floating point */
    PAL_AD_FORMAT_NV12           = 0xb0, /**< 8-bit YUV planar format, with 4:2:0 sampling */
    PAL_AD_FORMAT_UNORM_INT8X1   = 0xc0, /**< 1 channel unsigned 8-bit normalized integer */
    PAL_AD_FORMAT_UNORM_INT8X2   = 0xc1, /**< 2 channel unsigned 8-bit normalized integer */
    PAL_AD_FORMAT_UNORM_INT8X4   = 0xc2, /**< 4 channel unsigned 8-bit normalized integer */
    PAL_AD_FORMAT_UNORM_INT16X1  = 0xc3, /**< 1 channel unsigned 16-bit normalized integer */
    PAL_AD_FORMAT_UNORM_INT16X2  = 0xc4, /**< 2 channel unsigned 16-bit normalized integer */
    PAL_AD_FORMAT_UNORM_INT16X4  = 0xc5, /**< 4 channel unsigned 16-bit normalized integer */
    PAL_AD_FORMAT_SNORM_INT8X1   = 0xc6, /**< 1 channel signed 8-bit normalized integer */
    PAL_AD_FORMAT_SNORM_INT8X2   = 0xc7, /**< 2 channel signed 8-bit normalized integer */
    PAL_AD_FORMAT_SNORM_INT8X4   = 0xc8, /**< 4 channel signed 8-bit normalized integer */
    PAL_AD_FORMAT_SNORM_INT16X1  = 0xc9, /**< 1 channel signed 16-bit normalized integer */
    PAL_AD_FORMAT_SNORM_INT16X2  = 0xca, /**< 2 channel signed 16-bit normalized integer */
    PAL_AD_FORMAT_SNORM_INT16X4  = 0xcb, /**< 4 channel signed 16-bit normalized integer */
    PAL_AD_FORMAT_BC1_UNORM      = 0x91, /**< 4 channel unsigned normalized block-compressed (BC1 compression) format */
    PAL_AD_FORMAT_BC1_UNORM_SRGB = 0x92, /**< 4 channel unsigned normalized block-compressed (BC1 compression) format with sRGB encoding*/
    PAL_AD_FORMAT_BC2_UNORM      = 0x93, /**< 4 channel unsigned normalized block-compressed (BC2 compression) format */
    PAL_AD_FORMAT_BC2_UNORM_SRGB = 0x94, /**< 4 channel unsigned normalized block-compressed (BC2 compression) format with sRGB encoding*/
    PAL_AD_FORMAT_BC3_UNORM      = 0x95, /**< 4 channel unsigned normalized block-compressed (BC3 compression) format */
    PAL_AD_FORMAT_BC3_UNORM_SRGB = 0x96, /**< 4 channel unsigned normalized block-compressed (BC3 compression) format with sRGB encoding*/
    PAL_AD_FORMAT_BC4_UNORM      = 0x97, /**< 1 channel unsigned normalized block-compressed (BC4 compression) format */
    PAL_AD_FORMAT_BC4_SNORM      = 0x98, /**< 1 channel signed normalized block-compressed (BC4 compression) format */
    PAL_AD_FORMAT_BC5_UNORM      = 0x99, /**< 2 channel unsigned normalized block-compressed (BC5 compression) format */
    PAL_AD_FORMAT_BC5_SNORM      = 0x9a, /**< 2 channel signed normalized block-compressed (BC5 compression) format */
    PAL_AD_FORMAT_BC6H_UF16      = 0x9b, /**< 3 channel unsigned half-float block-compressed (BC6H compression) format */
    PAL_AD_FORMAT_BC6H_SF16      = 0x9c, /**< 3 channel signed half-float block-compressed (BC6H compression) format */
    PAL_AD_FORMAT_BC7_UNORM      = 0x9d, /**< 4 channel unsigned normalized block-compressed (BC7 compression) format */
    PAL_AD_FORMAT_BC7_UNORM_SRGB = 0x9e  /**< 4 channel unsigned normalized block-compressed (BC7 compression) format with sRGB encoding */
} palArrayFormat;

/// @brief Check if the specified array format and number of channels are valid.
///
/// @param format The array format to check.
/// @param numChannels The number of channels in the array format.
/// @return Returns PAL_SUCCESS if the format and number of channels are valid, otherwise returns an error code.
palResult palArrayCheckFormatAndNumChannels(palArrayFormat format, palUint32 numChannels);

/// @brief Get the size in bytes of the specified array format and number of channels.
///
/// @param format The array format.
/// @param numChannels The number of channels in the array format.
/// @return Returns the size in bytes of the specified array format and number of channels.
/// If the format or number of channels is invalid, it returns 0.
palUint64 palArrayGetElementSize(palArrayFormat format, palUint32 numChannels);

#endif // PAL_ARRAY_H_