#ifndef PAL_MEMORYPOOL_H_
#define PAL_MEMORYPOOL_H_

#include "pal_memoryobject.h"

typedef enum palMemoryPoolType_enum
{
    PAL_MEMORY_POOL_TYPE_DEFAULT = 0, /**< Default memory pool type */
    PAL_MEMORY_POOL_TYPE_CUSTOM,      /**< Custom memory pool type */
    PAL_MEMORY_POOL_TYPE_GRAPH,       /**< Graph memory pool type */
    PAL_MEMORY_POOL_TYPE_MAX          /**< Maximum memory pool type */
} palMemoryPoolType;

typedef struct palMemoryPoolProps_st
{
    palMemAllocationType allocType;         /**< Allocation type. Currently must be specified as CU_MEM_ALLOCATION_TYPE_PINNED */
    palMemAllocationHandleType handleTypes; /**< Handle types that will be supported by allocations from the pool. */
    palMemLocation location;                /**< Location where allocations should reside. */
    /**
     * Windows-specific LPSECURITYATTRIBUTES required when
     * ::CU_MEM_HANDLE_TYPE_WIN32 is specified.  This security attribute defines
     * the scope of which exported allocations may be transferred to other
     * processes.  In all other cases, this field is required to be zero.
     */
    void* pWin32SecurityAttributes;
    size_t maxSize;             /**< Maximum pool size. When set to 0, defaults to a system dependent value. */
    unsigned char reserved[56]; /**< reserved for future use, must be 0 */
} palMemoryPoolProps;

struct palMemoryPool_st
{
    // The primary memory manager handle.
    palMemoryManager* pMemoryManager;

    // The type of the memory pool.
    palMemoryPoolType poolType;

    // The mutex for protecting the memory block map.
    palOsMutex memoryBlockMutex;

    // The used memory block map for the pool (key: VirtAddress, value: palMemoryBlock*).
    palRbTree* pUsedMemoryBlockMap;

    // The free memory block map for the pool (key: VirtAddress, value: palMemoryBlock*)
    palRbTree* pFreeMemoryBlockMap;
};

/// @brief Create a memory pool
///
/// @param ppMemoryPool Pointer to the memory pool to create
/// @param pMemoryManager The memory manager to associate with the memory pool
/// @param poolType The type of the memory pool to create
/// @param pProps The properties for the memory pool
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolCreate(palMemoryPool** ppMemoryPool, palMemoryManager* pMemoryManager,
                              palMemoryPoolType poolType, const palMemoryPoolProps* pProps);

/// @brief Initialize a memory pool
///
/// @param pMemoryPool The memory pool to initialize
/// @param pMemoryManager The memory manager to associate with the memory pool
/// @param poolType The type of the memory pool to initialize
/// @param pProps The properties for the memory pool
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolInitialize(palMemoryPool* pMemoryPool, palMemoryManager* pMemoryManager,
                                  palMemoryPoolType poolType, const palMemoryPoolProps* pProps);

/// @brief Deinitialize a memory pool
///
/// @param pMemoryPool The memory pool to deinitialize
void palMemoryPoolDeinitialize(palMemoryPool* pMemoryPool);

/// @brief Destroy a memory pool
///
/// @param pMemoryPool The memory pool to destroy
void palMemoryPoolDestroy(palMemoryPool* pMemoryPool);

/// @brief Add a free memory block to the memory pool
///
/// @param pMemoryPool The memory pool to add the free memory block to
/// @param pMemoryBlock The memory block to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolAddFreeMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock);

/// @brief Remove a free memory block from the memory pool
///
/// @param pMemoryPool The memory pool to remove the memory block from
/// @param pMemoryBlock The memory block to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolRemoveFreeMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock);

/// @brief Get a free memory block from the memory pool
///
/// @param pMemoryPool The memory pool to get the free memory block from
/// @param size The size of the memory block to get
/// @param alignment The alignment size of the memory block to get
/// @param ppMemoryBlock Pointer to store the free memory block
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolGetFreeMemoryBlock(palMemoryPool* pMemoryPool, palUint64 size, palUint64 alignment,
                                          palMemoryBlock** ppMemoryBlock);

/// @brief Add a used memory block to the memory pool
/// Note: This function will move the memory block from the free memory block map to the used memory block map.
///
/// @param pMemoryPool The memory pool to add the used memory block to
/// @param pMemoryBlock The memory block to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolAddUsedMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock);

/// @brief Remove a used memory block from the memory pool
/// Note: This function will move the memory block from the used memory block map to the free memory block map.
///
/// @param pMemoryPool The memory pool to remove the memory block from
/// @param pMemoryBlock The memory block to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolRemoveUsedMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock);

/// @brief Get a free memory chunk from a used memory block in the memory pool
///
/// @param pMemoryPool The memory pool to get the free memory chunk from
/// @param size The size of the memory chunk to get
/// @param alignment The alignment size of the memory chunk to get
/// @param memFlags The memory flags for the memory chunk to get
/// @param ppMemoryChunk Pointer to store the free memory chunk
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolGetFreeMemoryChunkFromUsedMemoryBlock(palMemoryPool* pMemoryPool,
                                                             palUint64 size,
                                                             palUint64 alignment,
                                                             palMemoryFlags memFlags,
                                                             palMemoryChunk** ppMemoryChunk);

/// @brief Get a free memory chunk from a free memory block in the memory pool
///
/// @param pMemoryPool The memory pool to get the free memory chunk from
/// @param size The size of the memory chunk to get
/// @param alignment The alignment size of the memory chunk to get
/// @param memFlags The memory flags for the memory chunk to get
/// @param ppMemoryChunk Pointer to store the free memory chunk
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryPoolGetFreeMemoryChunkFromFreeMemoryBlock(palMemoryPool* pMemoryPool,
                                                             palUint64 size,
                                                             palUint64 alignment,
                                                             palMemoryFlags memFlags,
                                                             palMemoryChunk** ppMemoryChunk);

/// @brief Create a memory object in the memory pool
///
/// @param pMemoryPool The memory pool to get the free memory chunk from
/// @param pContext The context associated with the memory object
/// @param size The size of the memory object to create
/// @param memFlags The memory flags for the memory object
/// @param hostFlags The host flags for the memory object
/// @param pHostPtr The host pointer for the memory object (optional)
/// @param ppMemoryObject Pointer to store the created memory object
palResult palMemoryPoolCreateMemoryObject(palMemoryPool* pMemoryPool, palContext* pContext, palUint64 size,
                                          palMemoryFlags memFlags, palUint64 hostFlags, void* pHostPtr,
                                          palMemoryObject** ppMemoryObject);

#endif // PAL_MEMORYPOOL_H_