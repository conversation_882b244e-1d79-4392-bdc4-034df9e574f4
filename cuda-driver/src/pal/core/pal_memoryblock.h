#ifndef PAL_MEMORYBLOCK_H_
#define PAL_MEMORYBLOCK_H_

#include "pal.h"
#include "pal_memoryflags.h"
#include "pal_os_atomic.h"
#include "pal_structures.h"
#include "pal_types.h"

// The alignment size for memory blocks.
#define PAL_MEMORY_BLOCK_ALIGNMENT_SIZE 4096

typedef struct palMemoryBlockCreateInfo_st
{
    // The parent memory object (heap or pool) for the memory block.
    palBool isMemoryHeap;

    // The context for the memory block.
    // This is used to associate the memory block with a specific context.
    palContext* pContext;

    // The size of the memory block.
    palUint64 size;

    // The alignment of the memory block.
    palUint64 alignment;

    // The memory flags for the memory block.
    palMemoryFlags memFlags;

    // The host pointer for the memory block, if applicable.
    void* pHostPtr;
} palMemoryBlockCreateInfo;

struct palMemoryBlock_st
{
    union
    {
        // The corresponding heap handle.
        palMemoryHeap* pMemoryHeap;

        // The corresponding pool handle.
        palMemoryPool* pMemoryPool;
    };

    // The flag to indicate if the memory block is from a heap or a pool.
    palBool isMemoryHeap;

    // The context for the memory block.
    // This is used to associate the memory block with a specific context.
    palContext* pContext;

    // The size of the memory block.
    palUint64 size;

    // The aligned size of the memory block.
    palUint64 alignment;

    // The memory flags for the memory block.
    palMemoryFlags memFlags;

    // The base virtual address of the memory block.
    palUint64 deviceVirtAddress;

    // The host virtual address for the memory block.
    void* pHostVirtualAddress;

    union
    {
        // The device physical memory object of the memory block.
        void* pPhysicalMemoryObject;

        // The host pinned memory object for host mapping to device.
        void* pHostPinnedMemoryObject;

        // The host pageable memory registered handle for host mapping to device.
        void* pHostRegisteredMemoryObject;
    };

    // The device mapping of the memory block for VA-to-PA.
    void* pPhysicalMemoryDeviceMapping;

    // The host mapping handle for CPU access.
    void* pPhysicalMemoryHostMapping;

    // The offset of registered pageable memory, if applicable.
    palUint64 pageableMemoryOffset;

    // The flag to indicate if the memory block is used.
    palBool isUsed;

    // The mutex for protecting the memory chunk.
    palOsMutex memoryChunkMutex;

    // The free memory chunk map container for the memory block.
    palRbTree* pFreeMemoryChunkMap;

    // The used memory chunk map container for the memory block.
    palRbTree* pUsedMemoryChunkMap;

    // The ref count for the memory block.
    volatile _Atomic palUint32 refCount;
};

/// @brief Create a memory block
/// @param ppMemoryBlock Pointer to the memory block to create
/// @param pMemoryParent Pointer to the parent memory object (heap or pool)
/// @param pCreateInfo Pointer to the memory block creation info
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryBlockCreate(palMemoryBlock** ppMemoryBlock, void* pMemoryParent,
                               palMemoryBlockCreateInfo* pCreateInfo);

/// @brief Retain a memory block (increment the reference count)
///
/// @param pMemoryBlock Pointer to the memory block to retain
void palMemoryBlockRetain(palMemoryBlock* pMemoryBlock);

/// @brief Release a memory block (decrement the reference count)
/// Note: When the reference count reaches zero, the memory block will be destroyed.
///
/// @param pMemoryBlock Pointer to the memory block to release
void palMemoryBlockRelease(palMemoryBlock* pMemoryBlock);

/// @brief Add a free memory chunk to the memory block
/// @param pMemoryBlock Pointer to the memory block
/// @param pMemoryChunk Pointer to the memory chunk to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryBlockAddFreeMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk);

/// @brief Remove a free memory chunk from the memory block
///
/// @param pMemoryBlock Pointer to the memory block
/// @param pMemoryChunk Pointer to the memory chunk to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryBlockRemoveFreeMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk);

/// @brief Get a best fit free memory chunk from the memory block
///
/// @param pMemoryBlock Pointer to the memory block
/// @param size The size of the memory chunk to get
/// @param alignment The alignment size of the memory chunk to get
/// @param ppMemoryChunk Pointer to store the free memory chunk
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryBlockGetFreeMemoryChunk(palMemoryBlock* pMemoryBlock, palUint64 size, palUint64 alignment,
                                           palMemoryChunk** ppMemoryChunk);

/// @brief Add a used memory chunk to the memory block
/// Note: This function will move the memory chunk from the free memory chunk map to the used memory chunk map.
///
/// @param pMemoryBlock Pointer to the memory block
/// @param pMemoryChunk Pointer to the memory chunk to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryBlockAddUsedMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk);

/// @brief Remove a used memory chunk from the memory block
/// Note: This function will move the memory chunk from the used memory chunk map to the free memory chunk map.
///
/// @param pMemoryBlock Pointer to the memory block
/// @param pMemoryChunk Pointer to the memory chunk to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryBlockRemoveUsedMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk);

/// @brief Merge adjacent free memory chunks in the memory block
///
/// @param pMemoryBlock Pointer to the memory block
/// @param pMemoryChunk Pointer to the memory chunk to merge with adjacent free chunks
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryBlockMergeFreeMemoryChunks(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk);

#endif // PAL_MEMORYBLOCK_H_