#ifndef PAL_MEMORYFLAGS_H_
#define PAL_MEMORYFLAGS_H_

/*
 * This file is for defining memory flags used in the PAL (Platform Abstraction Layer).
 * These flags are used to specify various properties of memory allocations.
 * Each flag corresponds to a specific memory attribute or behavior.
 */

typedef enum palMemoryLocationType_enum
{
    PAL_MEMORY_LOCATION_TYPE_HOST   = 0, /**< Memory is located on the host */
    PAL_MEMORY_LOCATION_TYPE_DEVICE = 1, /**< Memory is located on the device */
    PAL_MEMORY_LOCATION_TYPE_MAX    = 2  /**< Maximum value for memory location */
} palMemoryLocationType;

typedef enum palMemoryHostCacheType_enum
{
    PAL_MEMORY_HOST_CACHE_TYPE_INVALID       = 0, /**< Invalid caching behavior */
    PAL_MEMORY_HOST_CACHE_TYPE_UNCACHED      = 1, /**< Uncached memory */
    PAL_MEMORY_HOST_CACHE_TYPE_CACHED        = 2, /**< Cached memory */
    PAL_MEMORY_HOST_CACHE_TYPE_WRITECOMBINED = 3, /**< Write-combined caching */
    PAL_MEMORY_HOST_CACHE_TYPE_MAX           = 4  /**< Maximum value for host cache type */
} palMemoryHostCacheType;

typedef enum palMemoryDeviceCacheType_enum
{
    PAL_MEMORY_DEVICE_CACHE_TYPE_DEFAULT  = 0, /**< Use the default caching behavior */
    PAL_MEMORY_DEVICE_CACHE_TYPE_UNCACHED = 1, /**< Uncached memory */
    PAL_MEMORY_DEVICE_CACHE_TYPE_CACHED   = 2, /**< Cached memory */
    PAL_MEMORY_DEVICE_CACHE_TYPE_MAX      = 3  /**< Maximum value for device cache type */
} palMemoryDeviceCacheType;

typedef enum palMemoryType_enum
{
    PAL_MEMORY_TYPE_GENERAL        = 0, /**< General memory type */
    PAL_MEMORY_TYPE_COMMAND_BUFFER = 1, /**< Command buffer memory */
    PAL_MEMORY_TYPE_FIFO_BUFFER    = 2, /**< FIFO buffer memory */
    PAL_MEMORY_TYPE_EVENT          = 3, /**< Event memory */
    PAL_MEMORY_TYPE_FENCE          = 4, /**< Fence memory */
    PAL_MEMORY_TYPE_MANAGED        = 5, /**< Managed memory type */
    PAL_MEMORY_TYPE_MAX            = 6  /**< Maximum value for memory type */
} palMemoryType;

typedef enum palMemoryLayoutType_enum
{
    PAL_MEMORY_LAYOUT_TYPE_LINEAR = 0, /**< Linear memory layout */
    PAL_MEMORY_LAYOUT_TYPE_PITCH  = 1, /**< Pitched memory layout */
    PAL_MEMORY_LAYOUT_TYPE_MAX    = 2  /**< Maximum value for memory layout type */
} palMemoryLayoutType;

typedef enum palMemoryHostMapType_enum
{
    PAL_MEMORY_HOST_MAP_TYPE_NONE    = 0, /**< No host mapping */
    PAL_MEMORY_HOST_MAP_TYPE_HOST_VA = 1, /**< Host mapping to virtual address */
    PAL_MEMORY_HOST_MAP_TYPE_MAX     = 2  /**< Maximum value for host map type */
} palMemoryHostMapType;

typedef enum palMemoryDeviceMapType_enum
{
    PAL_MEMORY_DEVICE_MAP_TYPE_NONE      = 0, /**< No device mapping */
    PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA = 1, /**< Device mapping to virtual address */
    PAL_MEMORY_DEVICE_MAP_TYPE_MAX       = 2  /**< Maximum value for device map type */
} palMemoryDeviceMapType;

typedef enum palMemoryOwnerType_enum
{
    PAL_MEMORY_OWNER_TYPE_NONE    = 0, /**< No owner */
    PAL_MEMORY_OWNER_TYPE_USER    = 1, /**< Memory owned by the user */
    PAL_MEMORY_OWNER_TYPE_DRIVER  = 2, /**< Memory owned by the driver */
    PAL_MEMORY_OWNER_TYPE_MAX     = 3  /**< Maximum value for memory owner type */
} palMemoryOwnerType;

typedef enum palMemoryPageSizeType_enum
{
    PAL_MEMORY_PAGE_SIZE_TYPE_4K = 0,  /**< 4KB page size */
    PAL_MEMORY_PAGE_SIZE_TYPE_2M = 1,  /**< 2MB page size */
    PAL_MEMORY_PAGE_SIZE_TYPE_MAX = 2, /**< Maximum value for page size type */
} palMemoryPageSizeType;

typedef enum palMemoryAccessFlags_enum
{
    PAL_MEMORY_ACCESS_FLAGS_READ_WRITE = 0, /**< Read and write access */
    PAL_MEMORY_ACCESS_FLAGS_READ_ONLY  = 1, /**< Read-only access */
    PAL_MEMORY_ACCESS_FLAGS_MAX        = 2  /**< Maximum value for memory access flags */
} palMemoryAccessFlags;

typedef enum palMemoryVisibilityType_enum
{
    PAL_MEMORY_VISIBILITY_TYPE_GLOBAL = 0, /**< Memory is globally visible to all streams on the device */
    PAL_MEMORY_VISIBILITY_TYPE_HOST   = 1, /**< Memory is not visible to any stream on the device */
    PAL_MEMORY_VISIBILITY_TYPE_SINGLE = 2, /**< Memory is visible to a single stream on the device */
    PAL_MEMORY_VISIBILITY_TYPE_MAX    = 3  /**< Maximum value for memory visibility type */
} palMemoryVisibilityType;

typedef enum palMemoryAllocApiType_enum
{
    PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC                  = 0, /**< Memory allocated from cuMemAlloc() */
    PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC             = 1, /**< Memory allocated from cuMemHostAlloc() */
    PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC_PORTABLE    = 2, /**< Memory allocated from cuMemHostAlloc() with portable flag */
    PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_MANAGED          = 3, /**< Memory allocated from cuMemAllocManaged() */
    PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_PITCH            = 4, /**< Memory allocated from cuMemAllocPitch() */
    PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER          = 5, /**< Memory registered from cuMemHostRegister() */
    PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER_PORTABLE = 6, /**< Memory registered from cuMemHostRegister() with portable flag */
    PAL_MEMORY_ALLOC_API_TYPE_MAX                        = 7  /**< Maximum value for memory allocation api type */
} palMemoryAllocApiType;

typedef enum palMemorySharingType_enum
{
    PAL_MEMORY_SHARING_TYPE_NONE            = 0, /**< No sharing */
    PAL_MEMORY_SHARING_TYPE_MEMORY_OBJECT   = 1, /**< Memory is shared by a memory object from another CUDA contexts */
    PAL_MEMORY_SHARING_TYPE_EXTERNAL_HANDLE = 2, /**< Memory is shared by another driver API (e.g. GL, Vulkan) */
    PAL_MEMORY_SHARING_TYPE_HOST_ADDRESS    = 3, /**< Memory is shared by host address */
    PAL_MEMORY_SHARING_TYPE_MAX             = 4  /**< Maximum value for memory sharing type */
} palMemorySharingType;

/**
* Specifies the handle type for address range
*/
typedef enum palMemRangeHandleType_enum
{
    PAL_MEM_RANGE_HANDLE_TYPE_DMA_BUF_FD = 0x1,
    PAL_MEM_RANGE_HANDLE_TYPE_MAX        = 0x7FFFFFFF
} palMemRangeHandleType;

typedef struct palMemoryFlags_st
{
    palMemoryLocationType    location;        /**< Location of the memory (host or device) */
    palMemoryHostCacheType   hostCacheType;   /**< Cache type for host memory */
    palMemoryDeviceCacheType deviceCacheType; /**< Cache type for device memory */
    palMemoryType            type;            /**< Type of memory (general, command buffer, etc.) */
    palMemoryLayoutType      layout;          /**< Memory layout type */
    palBool                  isPortable;      /**< Indicates if the memory is portable */
    palBool                  isManaged;       /**< Indicates if the memory is managed */
    palBool                  isIomem;         /**< Indicates if the memory is I/O memory */
    palMemoryHostMapType     hostMapType;     /**< Host mapping type */
    palMemoryDeviceMapType   deviceMapType;   /**< Device mapping type */
    palMemoryOwnerType       ownerType;       /**< Owner type of the memory */
    palMemoryPageSizeType    pageSizeType;    /**< Page size type for the memory */
    palMemoryAccessFlags     accessFlags;     /**< Access flags for the memory */
    palMemoryVisibilityType  visibilityType;  /**< Visibility type of the memory */
    palBool                  hasSubAllocator; /**< Indicates if the memory has sub-allocator */
    palMemoryAllocApiType    allocApiType;    /**< Memory allocation api type */
    palMemorySharingType     sharedType;      /**< Shared type for the memory */
} palMemoryFlags;

palBool palMemoryFlagsIsEqual(palMemoryFlags* pMemFlags1, palMemoryFlags* pMemFlags2);

#endif // PAL_MEMORYFLAGS_H_