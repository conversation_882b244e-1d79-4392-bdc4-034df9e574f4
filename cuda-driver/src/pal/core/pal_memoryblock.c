#include "pal_assert.h"
#include "pal_context.h"
#include "pal_device.h"
#include "pal_memoryblock.h"
#include "pal_memorychunk.h"
#include "pal_memoryheap.h"
#include "pal_memorymanager.h"
#include "pal_memorypool.h"
#include "pal_rbtree.h"

/// @brief The internal initialization of the memory block
///
/// @param pMemoryBlock Pointer to the memory block to initialize
/// @param pMemoryParent Pointer to the parent memory object (heap or pool)
/// @param pCreateInfo Pointer to the memory block creation info
/// @return Returns PAL_SUCCESS on success, or an error code on failure
static palResult palMemoryBlockInitializeInternal(palMemoryBlock* pMemoryBlock, void* pMemoryParent,
                                                  palMemoryBlockCreateInfo* pCreateInfo);

/// @brief The internal deinitialization of the memory block
///
/// @param pMemoryBlock Pointer to the memory block to deinitialize
static void palMemoryBlockDeinitializeInternal(palMemoryBlock* pMemoryBlock);

// =====================================================================================================================
palResult palMemoryBlockCreate(palMemoryBlock** ppMemoryBlock, void* pMemoryParent,
                               palMemoryBlockCreateInfo* pCreateInfo)
{
    palResult       result       = PAL_SUCCESS;
    palMemoryBlock* pMemoryBlock = NULL;

    if ((ppMemoryBlock == NULL) || (pMemoryParent == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory block creation.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppMemoryBlock = NULL;

    pMemoryBlock = (palMemoryBlock*)malloc(sizeof(palMemoryBlock));
    if (pMemoryBlock == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for memory block.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    // Initialize the internal members of memory block.
    result = palMemoryBlockInitializeInternal(pMemoryBlock, pMemoryParent, pCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory block.");
        free(pMemoryBlock);
        return result;
    }

    *ppMemoryBlock = pMemoryBlock;

    return result;
}

// ====================================================================================================================
palResult palMemoryBlockInitializeInternal(palMemoryBlock* pMemoryBlock, void* pMemoryParent,
                                           palMemoryBlockCreateInfo* pCreateInfo)
{
    palResult                result              = PAL_SUCCESS;
    palInt32                 error               = 0;
    palContext*              pContext            = NULL;
    palDevice*               pDevice             = NULL;
    palUint64                flags               = 0;
    palMemoryChunk*          pDefaultMemoryChunk = NULL;
    palMemoryChunkCreateInfo chunkCreateInfo     = {0};

    if ((pMemoryBlock == NULL) || (pMemoryParent == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory block creation.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pMemoryBlock, 0, sizeof(palMemoryBlock));

    pContext = pCreateInfo->pContext;
    PAL_ASSERT(pContext != NULL);
    pDevice = palContextGetDevice(pContext);
    PAL_ASSERT(pDevice != NULL);

    pMemoryBlock->isMemoryHeap = pCreateInfo->isMemoryHeap;

    // Set the parent heap or pool.
    if (pCreateInfo->isMemoryHeap == PAL_TRUE)
    {
        pMemoryBlock->pMemoryHeap = (palMemoryHeap*)pMemoryParent;
    }
    else
    {
        pMemoryBlock->pMemoryPool = (palMemoryPool*)pMemoryParent;
    }

    pMemoryBlock->pContext  = pContext;
    pMemoryBlock->size      = pCreateInfo->size;
    pMemoryBlock->alignment = pCreateInfo->alignment;

    if (pCreateInfo->pHostPtr != NULL)
    {
        pMemoryBlock->pHostVirtualAddress = pCreateInfo->pHostPtr;
    }

    memcpy(&pMemoryBlock->memFlags, &pCreateInfo->memFlags, sizeof(palMemoryFlags));

    error = palOsMutexInit(&pMemoryBlock->memoryChunkMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize mutex for memory block.");
        memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    // Initialize the free memory chunk map container.
    result = palRbTreeCreate(&pMemoryBlock->pFreeMemoryChunkMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create free memory chunk map.");
        palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
        memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palRbTreeCreate(&pMemoryBlock->pUsedMemoryChunkMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create used memory chunk map.");
        palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
        palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
        memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    // Allocate the virtual memory.
    pMemoryBlock->deviceVirtAddress = (palUint64)palOsVirtualMemoryAlloc(NULL, pMemoryBlock->size,
                                                                         PAL_OS_VIRTUAL_MEMORY_ALLOC_FLAGS_RESERVE,
                                                                         PAL_OS_VIRTUAL_MEMORY_ACCESS_NONE);
    if (pMemoryBlock->deviceVirtAddress == 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate virtual memory for memory block.");
        palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
        palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
        palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
        memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
        return result;
    }

    if (pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC ||
        pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_PITCH ||
        pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_MANAGED)
    {
        // Default flags for device physical memory allocation.
        flags = 0;

        // Allocate the physical memory.
        result = palDevicePhysicalMemoryCreate(pDevice, pContext, pMemoryBlock->size, flags,
                                               &pMemoryBlock->pPhysicalMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to allocate physical memory for memory block.");
            palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                                   PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
            palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
            palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
            palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
            memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
            return result;
        }

        if (pMemoryBlock->memFlags.deviceMapType == PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA)
        {
            // Default flags for device physical memory mapping.
            flags = 0;

            // Map the device physical memory object to the device virtual address space.
            result = palDevicePhysicalMemoryDeviceMap(pDevice, pContext, pMemoryBlock->deviceVirtAddress,
                                                      pMemoryBlock->pPhysicalMemoryObject, pMemoryBlock->size,
                                                      flags, &pMemoryBlock->pPhysicalMemoryDeviceMapping);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to map device memory for memory block.");
                palDevicePhysicalMemoryDestroy(pDevice, pContext, pMemoryBlock->pPhysicalMemoryObject);
                palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                                       PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
                palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
                palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
                palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
                memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
                return result;
            }
        }

        if (pMemoryBlock->memFlags.hostMapType == PAL_MEMORY_HOST_MAP_TYPE_HOST_VA)
        {
            // Default flags for device physical memory host mapping.
            flags = 0;

            // Map the device physical memory object to the host virtual address space.
            result = palDevicePhysicalMemoryHostMap(pDevice, pContext, pMemoryBlock->pPhysicalMemoryObject,
                                                    pMemoryBlock->size, flags,
                                                    &pMemoryBlock->pPhysicalMemoryHostMapping,
                                                    &pMemoryBlock->pHostVirtualAddress);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to unmap device physical memory to host for memory block.");
                if (pMemoryBlock->memFlags.deviceMapType == PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA)
                {
                    palDevicePhysicalMemoryDeviceUnmap(pDevice, pContext, pMemoryBlock->pPhysicalMemoryDeviceMapping);
                }
                palDevicePhysicalMemoryDestroy(pDevice, pContext, pMemoryBlock->pPhysicalMemoryObject);
                palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                                       PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
                palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
                palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
                palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
                memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
                return result;
            }
        }
    }
    else if (pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC ||
             pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC_PORTABLE)
    {
        // Allocate the host pinned memory.
        result = palDeviceHostPinnedMemoryCreate(pDevice, pContext, pMemoryBlock->size, flags,
                                                 &pMemoryBlock->pHostPinnedMemoryObject,
                                                 &pMemoryBlock->pHostVirtualAddress);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to allocate host pinned memory for memory block.");
            palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                                   PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
            palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
            palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
            palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
            memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
            return result;
        }

        if (pMemoryBlock->memFlags.deviceMapType == PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA)
        {
            // Set to 1 for pinned memory object mapping to device.
            flags = 1;

            // Map the host pinned memory object to the device virtual address space.
            result = palDevicePhysicalMemoryDeviceMap(pDevice, pMemoryBlock->pContext, pMemoryBlock->deviceVirtAddress,
                                                      pMemoryBlock->pHostPinnedMemoryObject, pMemoryBlock->size,
                                                      flags, &pMemoryBlock->pPhysicalMemoryDeviceMapping);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to map host pinned memory to device for memory block.");
                palDeviceHostPinnedMemoryDestroy(pDevice, pMemoryBlock->pContext,
                                                 pMemoryBlock->pHostPinnedMemoryObject);
                palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                                       PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
                palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
                palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
                palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
                memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
                return result;
            }
        }
    }
    else if (pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER ||
            pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER_PORTABLE)
    {
        // Register the host pageable memory.
        result = palDeviceHostPageableMemoryRegister(pDevice, pContext, pMemoryBlock->pHostVirtualAddress,
                                                     pMemoryBlock->size, flags,
                                                     &pMemoryBlock->pHostRegisteredMemoryObject,
                                                     &pMemoryBlock->pageableMemoryOffset);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to register host pageable memory for memory block.");
            palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                                   PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
            palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
            palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
            palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
            memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
            return result;
        }

        // Map the host pageable memory to the device.
        if (pMemoryBlock->memFlags.deviceMapType == PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA)
        {
            // Set to 2 for registered pageable memory object mapping to device.
            flags = 2;

            // Map the registered pageable memory object to the device virtual address space.
            result = palDevicePhysicalMemoryDeviceMap(pDevice, pContext, pMemoryBlock->deviceVirtAddress,
                                                      pMemoryBlock->pHostRegisteredMemoryObject, pMemoryBlock->size,
                                                      flags, &pMemoryBlock->pPhysicalMemoryDeviceMapping);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to map host pageable memory to device for memory block.");
                palDeviceHostPageableMemoryUnregister(pDevice, pContext, pMemoryBlock->pHostVirtualAddress,
                                                      pMemoryBlock->pHostRegisteredMemoryObject);
                palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                                       PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
                palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
                palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
                palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);
                memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
                return result;
            }
        }
    }

    // Create the default memory chunk for the entire memory block.
    chunkCreateInfo.deviceVirtAddress = pMemoryBlock->deviceVirtAddress + pMemoryBlock->pageableMemoryOffset;
    chunkCreateInfo.pHostVirtAddress  = (pMemoryBlock->pHostVirtualAddress != NULL)
                                        ? (void*)((uintptr_t)pMemoryBlock->pHostVirtualAddress +
                                                  pMemoryBlock->pageableMemoryOffset)
                                        : NULL;
    chunkCreateInfo.offset            = 0;
    chunkCreateInfo.size              = pMemoryBlock->size;
    chunkCreateInfo.alignmentSize     = PAL_MEMORY_CHUNK_ALIGNMENT_SIZE;
    result = palMemoryChunkCreate(&pDefaultMemoryChunk, pMemoryBlock, &chunkCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create default memory chunk for memory block.");
        palMemoryBlockDeinitializeInternal(pMemoryBlock);
        return result;
    }

    if (pCreateInfo->isMemoryHeap == PAL_TRUE)
    {
        result = palMemoryHeapAddFreeMemoryBlock(pMemoryBlock->pMemoryHeap, pMemoryBlock);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to add memory block to the heap.");
            palMemoryBlockDeinitializeInternal(pMemoryBlock);
            return result;
        }
    }
    else
    {
        result = palMemoryPoolAddFreeMemoryBlock(pMemoryBlock->pMemoryPool, pMemoryBlock);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to add memory block to the pool.");
            palMemoryBlockDeinitializeInternal(pMemoryBlock);
            return result;
        }
    }

    // Initialize the ref count to 1.
    palOsAtomicStoreSeqCst32(&pMemoryBlock->refCount, 1);

    return result;
}

// =====================================================================================================================
void palMemoryBlockDeinitializeInternal(palMemoryBlock* pMemoryBlock)
{
    palResult   result = PAL_SUCCESS;
    palContext* pContext = NULL;
    palDevice*  pDevice = NULL;

    if (pMemoryBlock->isUsed == PAL_TRUE)
    {
        PAL_DBG_PRINTF_ERROR("Failed to deinitialize memory block since it is still in use.");
        return;
    }

    pContext = pMemoryBlock->pContext;
    pDevice = palContextGetDevice(pContext);
    PAL_ASSERT(pDevice != NULL);

    // Remove the memory block from the parent heap or pool.
    if (pMemoryBlock->isMemoryHeap != PAL_FALSE)
    {
        // Remove the memory block from the free memory block map in the heap.
        result = palMemoryHeapRemoveFreeMemoryBlock(pMemoryBlock->pMemoryHeap, pMemoryBlock);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to remove memory block from the heap.");
            // Handle the error as needed, e.g., log it or return an error code.
            return;
        }
    }
    else
    {
        // Remove the memory block from the free memory block map in the pool.
        result = palMemoryPoolRemoveFreeMemoryBlock(pMemoryBlock->pMemoryPool, pMemoryBlock);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to remove memory block from the pool.");
            // Handle the error as needed, e.g., log it or return an error code.
            return;
        }
    }

    // Destroy the memory chunk map container.
    palOsMutexLock(&pMemoryBlock->memoryChunkMutex);
    if (pMemoryBlock->pUsedMemoryChunkMap != NULL)
    {
        // TODO: Clear the memory chunk map if needed.

        // Destroy the memory chunk map tree.
        palRbTreeDestroy(pMemoryBlock->pUsedMemoryChunkMap);
        pMemoryBlock->pUsedMemoryChunkMap = NULL;
    }

    if (pMemoryBlock->pFreeMemoryChunkMap != NULL)
    {
        // TODO: Clear the memory chunk map if needed.

        // Destroy the memory chunk map tree.
        palRbTreeDestroy(pMemoryBlock->pFreeMemoryChunkMap);
        pMemoryBlock->pFreeMemoryChunkMap = NULL;
    }
    palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
    palOsMutexDestroy(&pMemoryBlock->memoryChunkMutex);

    // Unmap the device mapping from device first.
    if ((pMemoryBlock->pPhysicalMemoryDeviceMapping != NULL) &&
        (pMemoryBlock->memFlags.deviceMapType == PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA))
    {
        palDevicePhysicalMemoryDeviceUnmap(pDevice, pContext, pMemoryBlock->pPhysicalMemoryDeviceMapping);
        pMemoryBlock->pPhysicalMemoryDeviceMapping = NULL;
    }

    if (pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC ||
        pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_PITCH ||
        pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_MANAGED)
    {
        // If the memory is mapped to host, unmap it from host first.
        if (pMemoryBlock->memFlags.hostMapType == PAL_MEMORY_HOST_MAP_TYPE_HOST_VA)
        {
            if (pMemoryBlock->pPhysicalMemoryHostMapping != NULL)
            {
                palDevicePhysicalMemoryHostUnmap(pDevice, pContext, pMemoryBlock->pPhysicalMemoryHostMapping);
                pMemoryBlock->pPhysicalMemoryHostMapping = NULL;
                pMemoryBlock->pHostVirtualAddress = NULL;
            }
        }

        // Release the physical memory.
        if (pMemoryBlock->pPhysicalMemoryObject != NULL)
        {
            palDevicePhysicalMemoryDestroy(pDevice, pContext, pMemoryBlock->pPhysicalMemoryObject);
            pMemoryBlock->pPhysicalMemoryObject = NULL;
        }
    }
    else if (pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC ||
            pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC_PORTABLE)
    {
        // Release the host pinned memory.
        if (pMemoryBlock->pHostPinnedMemoryObject != NULL)
        {
            palDeviceHostPinnedMemoryDestroy(pDevice, pContext, pMemoryBlock->pHostPinnedMemoryObject);
            pMemoryBlock->pHostPinnedMemoryObject = NULL;
            pMemoryBlock->pHostVirtualAddress = NULL;
        }
    }
    else if (pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER ||
            pMemoryBlock->memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER_PORTABLE)
    {
        // Unregister the host pageable memory.
        if (pMemoryBlock->pHostRegisteredMemoryObject != NULL)
        {
            palDeviceHostPageableMemoryUnregister(pDevice, pContext, pMemoryBlock->pHostVirtualAddress,
                                                  pMemoryBlock->pHostRegisteredMemoryObject);
            pMemoryBlock->pHostRegisteredMemoryObject = NULL;
            pMemoryBlock->pHostVirtualAddress = NULL;
            pMemoryBlock->pageableMemoryOffset = 0;
        }
    }

    if (pMemoryBlock->deviceVirtAddress != 0)
    {
        palOsVirtualMemoryFree((void*)pMemoryBlock->deviceVirtAddress, pMemoryBlock->size,
                               PAL_OS_VIRTUAL_MEMORY_FREE_RELEASE);
        pMemoryBlock->deviceVirtAddress = 0;
    }

    // Clear the memory block structure.
    memset(pMemoryBlock, 0, sizeof(palMemoryBlock));
}

// =====================================================================================================================
void palMemoryBlockRetain(palMemoryBlock* pMemoryBlock)
{
    PAL_ASSERT(pMemoryBlock != NULL);

    // Increment the reference count atomically.
    palOsAtomicFetchAndIncrementAcqRel32(&pMemoryBlock->refCount);
}

// =====================================================================================================================
void palMemoryBlockRelease(palMemoryBlock* pMemoryBlock)
{
    palResult result = PAL_SUCCESS;
    palUint32 oldRefCount;

    if (pMemoryBlock == NULL)
    {
        return;
    }

    // Decrement the reference count atomically.
    oldRefCount = palOsAtomicFetchAndDecrementAcqRel32(&pMemoryBlock->refCount);
    PAL_ASSERT(oldRefCount > 0);

    // Don't free memory block if it is not the last reference - that is, refCount > 0
    if (oldRefCount != 1)
    {
        return;
    }

    palMemoryBlockDeinitializeInternal(pMemoryBlock);

    // Free the memory block handle.
    free(pMemoryBlock);
}

// =====================================================================================================================
palResult palMemoryBlockAddFreeMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryBlock != NULL);
    PAL_ASSERT(pMemoryChunk != NULL);

    palOsMutexLock(&pMemoryBlock->memoryChunkMutex);
    result = palRbTreeInsert(pMemoryBlock->pFreeMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory chunk to the free memory chunk map tree.");
        palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
        return result;
    }
    palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryBlockRemoveFreeMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryBlock != NULL);
    PAL_ASSERT(pMemoryChunk != NULL);

    palOsMutexLock(&pMemoryBlock->memoryChunkMutex);
    pNode = palRbTreeSearch(pMemoryBlock->pFreeMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryBlock->pFreeMemoryChunkMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory chunk not found in the free memory chunk map tree.");
        palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory chunk from the tree.
    palRbTreeDelete(pMemoryBlock->pFreeMemoryChunkMap, pNode);
    palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryBlockGetFreeMemoryChunk(palMemoryBlock* pMemoryBlock, palUint64 size, palUint64 alignment,
                                           palMemoryChunk** ppMemoryChunk)
{
    palResult       result              = PAL_SUCCESS;
    palRbTreeNode*  pNode               = NULL;
    palMemoryChunk* pCurrMemoryChunk    = NULL;
    palMemoryChunk* pBestFitMemoryChunk = NULL;

    PAL_ASSERT(pMemoryBlock != NULL);
    PAL_ASSERT(ppMemoryChunk != NULL);

    *ppMemoryChunk = NULL;

    palOsMutexLock(&pMemoryBlock->memoryChunkMutex);
    pNode = palRbTreeGetFirst(pMemoryBlock->pFreeMemoryChunkMap);
    while (pNode != palRbTreeGetEnd(pMemoryBlock->pFreeMemoryChunkMap))
    {
        pCurrMemoryChunk = (palMemoryChunk*)pNode->value;

        // Check if the memory chunk is large enough and aligned correctly
        if ((pCurrMemoryChunk->size >= size) && (pCurrMemoryChunk->deviceVirtAddress % alignment == 0) &&
            (pCurrMemoryChunk->alignmentSize == alignment))
        {
            if ((pBestFitMemoryChunk == NULL) || (pCurrMemoryChunk->size < pBestFitMemoryChunk->size))
            {
                pBestFitMemoryChunk = pCurrMemoryChunk;
            }
        }

        // Move to the next node
        pNode = palRbTreeGetNext(pMemoryBlock->pFreeMemoryChunkMap, pNode);
    }
    palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);

    if (pBestFitMemoryChunk == NULL)
    {
        PAL_DBG_PRINTF_ERROR("No suitable free memory chunk found for size %llu and alignment %llu.",
                             size, alignment);
        return PAL_ERROR_NOT_FOUND;
    }

    *ppMemoryChunk = pBestFitMemoryChunk;

    // If we found a suitable memory chunk, we can return success.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryBlockAddUsedMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryBlock != NULL);
    PAL_ASSERT(pMemoryChunk != NULL);

    palOsMutexLock(&pMemoryBlock->memoryChunkMutex);
    // Search for the memory chunk in the free memory chunk map tree.
    pNode = palRbTreeSearch(pMemoryBlock->pFreeMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryBlock->pFreeMemoryChunkMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory chunk not found in the free memory chunk map tree.");
        palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory chunk from the free memory chunk map tree.
    palRbTreeDelete(pMemoryBlock->pFreeMemoryChunkMap, pNode);

    // Insert the memory chunk into the used memory chunk map tree.
    result = palRbTreeInsert(pMemoryBlock->pUsedMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory chunk to the used memory chunk map tree.");
        // If insertion fails, we need to reinsert the memory chunk back to the free memory chunk map.
        palRbTreeInsert(pMemoryBlock->pFreeMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress,
                        (palRbTreeValueType)pMemoryChunk);
        // Unlock the mutex before returning.
        palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
        return result;
    }

    // Mark the memory chunk as used.
    pMemoryChunk->isUsed = PAL_TRUE;

    palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryBlockRemoveUsedMemoryChunk(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryBlock != NULL);
    PAL_ASSERT(pMemoryChunk != NULL);

    palOsMutexLock(&pMemoryBlock->memoryChunkMutex);

    // Search for the memory chunk in the used memory chunk map tree.
    pNode = palRbTreeSearch(pMemoryBlock->pUsedMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryBlock->pUsedMemoryChunkMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory chunk not found in the used memory chunk map tree.");
        palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory chunk from the used memory chunk map tree.
    palRbTreeDelete(pMemoryBlock->pUsedMemoryChunkMap, pNode);

    // Insert the memory chunk into the free memory chunk map tree.
    result = palRbTreeInsert(pMemoryBlock->pFreeMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory chunk to the free memory chunk map tree.");
        // If insertion fails, we need to reinsert the memory chunk back to the used memory chunk map.
        palRbTreeInsert(pMemoryBlock->pUsedMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress,
                        (palRbTreeValueType)pMemoryChunk);
        // Unlock the mutex before returning.
        palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
        return result;
    }

    // Mark the memory chunk as not used.
    pMemoryChunk->isUsed = PAL_FALSE;

    if (pMemoryBlock->memFlags.hasSubAllocator == PAL_TRUE)
    {
        // If the memory block supports sub-allocation, try to merge with adjacent free memory chunks if possible.
        result = palMemoryBlockMergeFreeMemoryChunks(pMemoryBlock, pMemoryChunk);
    }

    palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);

    return result;
}

// =====================================================================================================================
palResult palMemoryBlockMergeFreeMemoryChunks(palMemoryBlock* pMemoryBlock, palMemoryChunk* pMemoryChunk)
{
    palResult      result      = PAL_SUCCESS;
    palRbTreeNode* pTargetNode = NULL;
    palRbTreeNode* pPrevNode   = NULL;
    palRbTreeNode* pNextNode   = NULL;

    PAL_ASSERT(pMemoryBlock != NULL);
    PAL_ASSERT(pMemoryChunk != NULL);

    palOsMutexLock(&pMemoryBlock->memoryChunkMutex);

    // Search for the memory chunk in the free memory chunk map tree.
    pTargetNode = palRbTreeSearch(pMemoryBlock->pFreeMemoryChunkMap, (palRbTreeKeyType)pMemoryChunk->deviceVirtAddress);
    if (pTargetNode == palRbTreeGetEnd(pMemoryBlock->pFreeMemoryChunkMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory chunk not found in the free memory chunk map tree.");
        palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Try to merge with adjacent free memory chunks if possible.

    // If the previous or next node is not the end of the tree, we can attempt to merge.
    pPrevNode = palRbTreeGetPrev(pMemoryBlock->pFreeMemoryChunkMap, pTargetNode);
    if (pPrevNode != palRbTreeGetEnd(pMemoryBlock->pFreeMemoryChunkMap))
    {
        result = palMemoryChunkMerge((palMemoryChunk*)pPrevNode->value, pMemoryChunk, &pMemoryChunk);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_INFO("Failed to merge with previous memory chunk.");
        }
    }

    // If the next node is not the end of the tree, we can attempt to merge.
    pNextNode = palRbTreeGetNext(pMemoryBlock->pFreeMemoryChunkMap, pTargetNode);
    if (pNextNode != palRbTreeGetEnd(pMemoryBlock->pFreeMemoryChunkMap))
    {
        result = palMemoryChunkMerge(pMemoryChunk, (palMemoryChunk*)pNextNode->value, &pMemoryChunk);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to merge with next memory chunk.");
        }
    }

    palOsMutexUnlock(&pMemoryBlock->memoryChunkMutex);

    return PAL_SUCCESS;
}

// ====================================================================================================================
palResult palMemoryBlockUnregisterHostPageableMemory(palMemoryBlock* pMemoryBlock)
{
    palResult   result   = PAL_SUCCESS;
    palContext* pContext = NULL;
    palDevice*  pDevice  = NULL;

    PAL_ASSERT(pMemoryBlock != NULL);

    if (pMemoryBlock->pPhysicalMemoryObject == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Memory block has no registered pageable memory.");
        return PAL_ERROR_HOST_MEMORY_NOT_REGISTERED;
    }

    pContext = pMemoryBlock->pContext;
    pDevice  = pContext->persistentState.pDevice;

    result = palDeviceHostPageableMemoryUnregister(pDevice, pContext, pMemoryBlock->pHostVirtualAddress,
                                                   pMemoryBlock->pHostRegisteredMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to unregister pageable memory for memory block.");
        return result;
    }

    pMemoryBlock->pPhysicalMemoryObject = NULL;

    return PAL_SUCCESS;
}