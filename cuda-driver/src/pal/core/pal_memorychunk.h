#ifndef PAL_MEMORYCHUNK_H_
#define PAL_MEMORYCHUNK_H_

#include "pal.h"
#include "pal_types.h"
#include "pal_structures.h"

///< The alignment size for memory chunks.
#define PAL_MEMORY_CHUNK_ALIGNMENT_SIZE 256

typedef struct palMemoryChunkCreateInfo_st
{
    // The device virtual address of the memory chunk.
    palUint64 deviceVirtAddress;

    // The host virtual address for the memory chunk, if applicable.
    void* pHostVirtAddress;

    // The offset of the chunk within the memory block.
    palUint64 offset;

    // The size of the chunk.
    palUint64 size;

    // The alignment size of the chunk.
    palUint64 alignmentSize;
} palMemoryChunkCreateInfo;

struct palMemoryChunk_st
{
    // The corresponding block handle.
    palMemoryBlock* pMemoryBlock;

    // The device virtual address of the chunk.
    palUint64 deviceVirtAddress;

    // The host virtual address for the chunk, if applicable.
    void* pHostVirtAddress;

    // The offset of the chunk within the memory block.
    palUint64 offset;

    // The size of the chunk.
    palUint64 size;

    // The chunk alignment size.
    palUint64 alignmentSize;

    // The flag to indicate if the chunk is used.
    palBool isUsed;
};

/// @brief Create a memory chunk
///
/// @param ppMemoryChunk Pointer to the memory chunk to create
/// @param pMemoryBlock Pointer to the memory block to create the chunk in
/// @param pCreateInfo Pointer to the memory chunk creation info
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryChunkCreate(palMemoryChunk** ppMemoryChunk, palMemoryBlock* pMemoryBlock,
                               palMemoryChunkCreateInfo* pCreateInfo);

/// @brief Initialize a memory chunk
///
/// @param pMemoryChunk Pointer to the memory chunk to initialize
/// @param pMemoryBlock Pointer to the memory block to initialize the chunk in
/// @param pCreateInfo Pointer to the memory chunk creation info
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryChunkInitialize(palMemoryChunk* pMemoryChunk, palMemoryBlock* pMemoryBlock,
                                   palMemoryChunkCreateInfo* pCreateInfo);

/// @brief Deinitialize a memory chunk
///
/// @param pMemoryChunk Pointer to the memory chunk to deinitialize
void palMemoryChunkDeinitialize(palMemoryChunk* pMemoryChunk);

/// @brief Destroy a memory chunk
///
/// @param pMemoryChunk Pointer to the memory chunk to destroy
void palMemoryChunkDestroy(palMemoryChunk* pMemoryChunk);

/// @brief Split a free memory chunk into two chunks
///
/// @param pMemoryChunk Pointer to the memory chunk to split
/// @param size The size of the new memory chunk
/// @param ppSplitMemoryChunk Pointer to store the splitted memory chunk
/// @param ppRemainderMemoryChunk Pointer to store the remaining memory chunk
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryChunkSplit(palMemoryChunk* pMemoryChunk, palUint64 size, palMemoryChunk** ppSplitMemoryChunk,
                              palMemoryChunk** ppRemainderMemoryChunk);

/// @brief Merge two free memory chunks into one
///
/// @param pMemoryChunk1 Pointer to the first memory chunk
/// @param pMemoryChunk2 Pointer to the second memory chunk
/// @param ppMergedMemoryChunk Pointer to store the merged memory chunk
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryChunkMerge(palMemoryChunk* pMemoryChunk1, palMemoryChunk* pMemoryChunk2,
                              palMemoryChunk** ppMergedMemoryChunk);

#endif // PAL_MEMORYCHUNK_H_