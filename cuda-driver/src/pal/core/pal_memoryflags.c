#include "pal_assert.h"
#include "pal_memoryflags.h"

#include <string.h>

// =====================================================================================================================
palBool palMemoryFlagsIsEqual(palMemoryFlags* pMemFlags1, palMemoryFlags* pMemFlags2)
{
    if ((pMemFlags1 == NULL) || (pMemFlags2 == NULL))
    {
        return PAL_FALSE;
    }

    return (memcmp(pMemFlags1, pMemFlags2, sizeof(palMemoryFlags)) == 0) ? PAL_TRUE : PAL_FALSE;
}