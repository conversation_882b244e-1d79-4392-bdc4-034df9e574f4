#include "pal_assert.h"
#include "pal_context.h"
#include "pal_list.h"
#include "pal_os.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/// @brief Set the minimum stack size for the context stack.
///
/// @param ppThreadData The thread data object to create
/// @param minStackSize The minimum stack size to set
/// @return PAL_SUCCESS on success, or an error code on failure
static palResult ipalTlsThreadDataSetMinStackSize(palTlsThreadData* pThreadData, palUint32 minStackSize);

// =====================================================================================================================
palResult palTlsThreadDataCreate(palTlsThreadData** ppThreadData, palTlsThreadManager* pThreadManager)
{
    palResult         result      = PAL_SUCCESS;
    palTlsThreadData* pThreadData = NULL;

    if ((ppThreadData == NULL) || (pThreadManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pThreadData = (palTlsThreadData*)malloc(sizeof(palTlsThreadData));
    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for thread data.");
        *ppThreadData = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palTlsThreadDataInitialize(pThreadData, pThreadManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize thread data.");
        free(pThreadData);
        *ppThreadData = NULL;
        return result;
    }

    *ppThreadData = pThreadData;

    return result;
}

// =====================================================================================================================
palResult palTlsThreadDataInitialize(palTlsThreadData* pThreadData, palTlsThreadManager* pThreadManager)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTmpThreadData = NULL;

    if ((pThreadData == NULL) || (pThreadManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pThreadData, 0, sizeof(palTlsThreadData));

    pThreadData->pThreadManager = pThreadManager;

    pThreadData->threadId = palOsAtomicFetchAndIncrementSeqCst64((_Atomic palUint64*)&pThreadManager->threadIdCounter);

    // By default, the thread is allowed to make api call.
    pThreadData->allowedToMakeApiCalls = PAL_TRUE;

    // Insert into the TLS list.
    palOsMutexLock(&pThreadManager->mutex);
    PAL_LIST_INSERT_PREV_NEXT(pThreadManager->pThreadDataList, pThreadData, pTmpThreadData, pPrev, pNext);
    palOsMutexUnlock(&pThreadManager->mutex);

    palOsTlsSetValue(pThreadManager->tlsEntry, (void*)pThreadData);

    return result;
}

// =====================================================================================================================
void palTlsThreadDataDeinitialize(palTlsThreadData* pThreadData)
{
    palTlsThreadManager* pThreadManager = NULL;
    palContext*          pCtx           = NULL;

    if ((pThreadData == NULL) || (pThreadData == PAL_TLS_THREAD_DATA_DESTROYED))
    {
        return;
    }

    pThreadManager = pThreadData->pThreadManager;

    palOsMutexLock(&pThreadManager->mutex);

    // Set the thread data to be destroyed
    palOsTlsSetValue(pThreadManager->tlsEntryInThreadDataDestruction, (void*)1);

    if (pThreadData->ctxStackSize > 0)
    {
        pCtx = pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1];
    }
    palOsTlsSetValue(pThreadManager->tlsEntryTopCtx, pCtx);

    // Pop all contexts (pop will decr refcount)
    while (pThreadData->ctxStackSize > 0)
    {
        // Remove the top of the stack
        pCtx = pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1];
        pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1] = NULL;
        pThreadData->ctxStackSize -= 1;

        // Decrement its thread reference count (potentially freeing it)
        palContextThreadRefCountDecrement(pCtx);
    }

    // Remove from the global lists
    PAL_LIST_REMOVE_PREV_NEXT(pThreadManager->pThreadDataList, pThreadData, pPrev, pNext);

    // Blow away the data.
    memset(pThreadData->ppCtxStack, 0, pThreadData->ctxStackArraySize * sizeof(palContext*));
    free(pThreadData->ppCtxStack);

    memset(pThreadData, 0, sizeof(palTlsThreadData));

    palOsMutexUnlock(&pThreadManager->mutex);
}

// =====================================================================================================================
void palTlsThreadDataDestroy(palTlsThreadData* pThreadData)
{
    if (pThreadData == NULL)
    {
        return;
    }

    palTlsThreadDataDeinitialize(pThreadData);

    free(pThreadData);
}

// =====================================================================================================================
static palResult ipalTlsThreadDataSetMinStackSize(palTlsThreadData* pThreadData, palUint32 minStackSize)
{
    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid thread data handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pThreadData->ctxStackArraySize < minStackSize)
    {
        palUint32    newCtxStackArraySize = PAL_MAX(16, 2 * pThreadData->ctxStackArraySize);
        palContext** ppNewCtxStack        = (palContext**)malloc(newCtxStackArraySize * sizeof(palContext*));
        if (ppNewCtxStack == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Failed to allocate memory for new context stack");
            return PAL_ERROR_OUT_OF_MEMORY;
        }

        memset(ppNewCtxStack, 0, newCtxStackArraySize * sizeof(palContext*));

        // Copy the old stack to the new stack.
        if (pThreadData->ctxStackArraySize > 0)
        {
            memcpy(ppNewCtxStack, pThreadData->ppCtxStack, pThreadData->ctxStackArraySize * sizeof(palContext*));
        }

        // Free the old stack.
        free(pThreadData->ppCtxStack);

        pThreadData->ppCtxStack        = ppNewCtxStack;
        pThreadData->ctxStackArraySize = newCtxStackArraySize;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palContext* palTlsThreadDataGetCurrentContext(palTlsThreadData* pThreadData)
{
    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid thread data handle.");
        return NULL;
    }

    if (pThreadData == PAL_TLS_THREAD_DATA_DESTROYED)
    {
        return (palContext*)palOsTlsGetValue(pThreadData->pThreadManager->tlsEntryTopCtx);
    }

    if (pThreadData->ctxStackSize == 0)
    {
        return NULL;
    }

    return pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1];
}

// =====================================================================================================================
palResult palTlsThreadDataPushCurrentContext(palTlsThreadData* pThreadData, palContext* pContext)
{
    palResult result = PAL_SUCCESS;

    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid thread data handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pThreadData == PAL_TLS_THREAD_DATA_DESTROYED)
    {
        PAL_DBG_PRINTF_ERROR("Thread data is destroyed.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if (pContext == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid context handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    result = ipalTlsThreadDataSetMinStackSize(pThreadData, pThreadData->ctxStackSize + 1);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to set minimum stack size.");
        return result;
    }

    // Increment the thread reference count for the context.
    palContextThreadRefCountIncrement(pContext);

    pThreadData->ctxStackSize += 1;
    pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1] = pContext;

    return result;
}

// =====================================================================================================================
palResult palTlsThreadDataPopCurrentContext(palTlsThreadData* pThreadData)
{
    palContext* pCtx = NULL;

    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid thread data handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pThreadData == PAL_TLS_THREAD_DATA_DESTROYED)
    {
        PAL_DBG_PRINTF_ERROR("Thread data is destroyed.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if (pThreadData->ctxStackSize > 0)
    {
        // Remove the top context
        pCtx = pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1];
        pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1] = NULL;
        pThreadData->ctxStackSize -= 1;

        // Decrement the thread reference count for the context.
        palContextThreadRefCountDecrement(pCtx);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palTlsThreadDataSetCurrentContext(palTlsThreadData* pThreadData, palContext* pContext)
{
    palResult   result  = PAL_SUCCESS;
    palContext* pOldCtx = NULL;

    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid thread data handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pThreadData == PAL_TLS_THREAD_DATA_DESTROYED)
    {
        PAL_DBG_PRINTF_ERROR("Thread data is destroyed.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    result = ipalTlsThreadDataSetMinStackSize(pThreadData, pThreadData->ctxStackSize + 1);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to set minimum stack size.");
        return result;
    }

    // Get the old top of the stack and early-out for no-ops.
    if (pThreadData->ctxStackSize > 0)
    {
        pOldCtx = pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1];
    }

    if (pOldCtx == pContext)
    {
        return PAL_SUCCESS;
    }

    // Retain the new context and release the old context
    if (pContext != NULL)
    {
        // Increment the thread reference count for the new context.
        palContextThreadRefCountIncrement(pContext);
    }

    if (pOldCtx != NULL)
    {
        // Decrement the thread reference count for the old context.
        palContextThreadRefCountDecrement(pOldCtx);
    }

    // Set the top of the stack to the new context.
    if (pThreadData->ctxStackSize == 0)
    {
        pThreadData->ctxStackSize = 1;
    }

    pThreadData->ppCtxStack[pThreadData->ctxStackSize - 1] = pContext;
    if (pContext == NULL)
    {
        pThreadData->ctxStackSize -= 1;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palBool palTlsThreadDataIsAllowedToMakeApiCalls(palTlsThreadData* pThreadData)
{
    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid thread data handle.");
        return PAL_FALSE;
    }

    if (pThreadData == PAL_TLS_THREAD_DATA_DESTROYED)
    {
        PAL_DBG_PRINTF_ERROR("Thread data is destroyed.");
        return PAL_TRUE;
    }

    return pThreadData->allowedToMakeApiCalls;
}

// =====================================================================================================================
void palTlsThreadDataSetAllowedToMakeApiCalls(palTlsThreadData* pThreadData, palBool allowedToMakeApiCalls)
{
    if (pThreadData == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid thread data handle.");
        return;
    }

    if (pThreadData == PAL_TLS_THREAD_DATA_DESTROYED)
    {
        PAL_DBG_PRINTF_ERROR("Thread data is destroyed.");
        return;
    }

    pThreadData->allowedToMakeApiCalls = allowedToMakeApiCalls;
}