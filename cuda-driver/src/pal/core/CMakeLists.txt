# Build core module as the part of the static library.
target_sources(pal PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_array.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_asynccleanup.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_context.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_device.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_devicemanager.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_event.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_fence.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_function.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_globalmanager.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_graph.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_init.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_memoryblock.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_memorychunk.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_memoryflags.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_memoryheap.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_memorymanager.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_memoryobject.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_memorypool.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_stream.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_tlsthreaddata.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_tlsthreadmanager.c
    ${CMAKE_CURRENT_SOURCE_DIR}/pal_uvamanager.c
)

# Add the including file path
target_include_directories(pal PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src/pal
    ${CMAKE_SOURCE_DIR}/src/pal/thunk
    ${CMAKE_SOURCE_DIR}/src/pal/util
)
