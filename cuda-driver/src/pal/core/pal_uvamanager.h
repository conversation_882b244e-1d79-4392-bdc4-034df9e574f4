#ifndef PAL_UVAMANAGER_H_
#define PAL_UVAMANAGER_H_

#include "pal.h"
#include "pal_os.h"
#include "pal_memoryflags.h"
#include "pal_structures.h"
#include "pal_types.h"

struct palUvaManager_st
{
    // The global manager associated with the UVA manager.
    palGlobalManager* pGlobalManager;

    // The mutex for protecting the UVA manager.
    palOsMutex uvaManagerMutex;

    // The memory object map for the UVA manager.
    // The corresponding <key, value> pair is <virtAddress, palMemoryObject*>
    palRbTree* pMemoryObjectDeviceMap;

    // The memory object map for the host.
    // The corresponding <key, value> pair is <pHost, palMemoryObject*>
    palRbTree* pMemoryObjectHostMap;

    // The mutex for protecting the memory object map.
    palOsMutex memoryObjectMutex;

    // Flag to indicate if unified addressing is enabled
    palBool enableUnifiedAddressing;
};

/// @brief Create a UVA manager
///
/// @param ppUvaManager Pointer to the UVA manager to create
/// @param pGlobalManager The global manager to use
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerCreate(palUvaManager** ppUvaManager, palGlobalManager* pGlobalManager);

/// @brief Initialize the UVA manager
/// @param pUvaManager The UVA manager to initialize
/// @param pGlobalManager The global manager to use
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerInitialize(palUvaManager* pUvaManager, palGlobalManager* pGlobalManager);

/// @brief Deinitialize the UVA manager
///
/// @param pUvaManager The UVA manager to deinitialize
void palUvaManagerDeinitialize(palUvaManager* pUvaManager);

/// @brief Destroy the UVA manager
///
/// @param pUvaManager The UVA manager to destroy
void palUvaManagerDestroy(palUvaManager* pUvaManager);

/// @brief Add a memory object to the UVA manager
///
/// @param pUvaManager The UVA manager to add the memory object to
/// @param pMemoryObject The memory object to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerAddMemoryObject(palUvaManager* pUvaManager, palMemoryObject* pMemoryObject);

/// @brief Remove a memory object from the UVA manager
///
/// @param pUvaManager The UVA manager to remove the memory object from
/// @param pMemoryObject The memory object to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerRemoveMemoryObject(palUvaManager* pUvaManager, palMemoryObject* pMemoryObject);

/// @brief Get a memory object from the UVA manager
///
/// @param pUvaManager The UVA manager to get the memory object from
/// @param ptr The pointer to the memory object
/// @param ppMemoryObject Pointer to store the memory object
/// @return Returns PAL_SUCCESS on success, or PAL_ERROR_NOT_FOUND on failure
palResult palUvaManagerGetMemoryObject(palUvaManager* pUvaManager, const void* ptr, palMemoryObject** ppMemoryObject);

/// @brief Allocate memory in the UVA manager
///
/// @param pUvaManager The UVA manager to allocate memory from
/// @param pContext The context to use for the allocation
/// @param bytesize The size of the memory to allocate
/// @param pDevicePtr Pointer to store the device pointer of the allocated memory
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemAlloc(palUvaManager* pUvaManager, palContext* pContext, size_t bytesize, palUint64* pDevicePtr);

/// @brief Allocate managed memory in the UVA manager
///
/// @param pUvaManager The UVA manager to allocate memory from
/// @param pContext The context to use for the allocation
/// @param bytesize The size of the memory to allocate
/// @param flags The flags for the allocation
/// @param pDevicePtr Pointer to store the device pointer of the allocated memory
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemAllocManaged(palUvaManager* pUvaManager, palContext* pContext, size_t bytesize,
                                       palUint32 flags, palUint64* pDevicePtr);

/// @brief Free memory in the UVA manager
///
/// @param pUvaManager The UVA manager to free memory from
/// @param pContext The context to use for the deallocation
/// @param devicePtr The device pointer of the memory to free
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemFree(palUvaManager* pUvaManager, palContext* pContext, palUint64 devicePtr);

/// @brief Allocate host pinned memory in the UVA manager
///
/// @param pUvaManager The UVA manager to allocate memory from
/// @param pContext The context to use for the allocation
/// @param bytesize The size of the memory to allocate
/// @param hostFlags The host flags for the allocation
/// @param ppHostPtr Pointer to store the host pointer of the allocated memory
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemHostAlloc(palUvaManager* pUvaManager, palContext* pContext, size_t bytesize,
                                    palUint32 hostFlags, void** ppHostPtr);

/// @brief Free host pinned memory in the UVA manager
///
/// @param pUvaManager The UVA manager to free memory from
/// @param pContext The context to use for the deallocation
/// @param pHostPtr The host pointer of the memory to free
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemFreeHost(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr);

/// @brief Get the device pointer for a given host pointer
///
/// @param pUvaManager The UVA manager to use
/// @param pContext The context to use
/// @param pHostPtr The host pointer to get the device pointer for
/// @param hostFlags The host flags for the allocation
/// @param pDevicePtr Pointer to store the device pointer
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemHostGetDevicePointer(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr,
                                               palUint32 hostFlags, palUint64* pDevicePtr);

/// @brief Retrieve handle for an address range.
///
/// @param pUvaManager The UVA manager to use
/// @param devicePtr The device pointer of the memory object
/// @param size The size of the memory object
/// @param handleType The type of handle to retrieve
/// @param flags The flags for the handle retrieval
/// @param pHandle Pointer to the handle to retrieve
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemGetHandleForAddressRange(palUvaManager* pUvaManager, palUint64 devicePtr, palUint64 size,
                                                   palMemRangeHandleType handleType, palUint64 flags, void* pHandle);

/// @brief Get the host flags for a given host pointer
///
/// @param pUvaManager The UVA manager to use
/// @param pContext The context to use
/// @param pHostPtr The host pointer to get the host flags for
/// @param pHostFlags Pointer to store the host flags
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemHostGetFlags(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr,
                                       palUint32* pHostFlags);

/// @brief Get the address range of a memory object in the UVA manager
/// @param pUvaManager The UVA manager to use
/// @param pContext The context to use
/// @param devicePtr The device pointer of the memory object
/// @param pBase Pointer to store the base address of the memory object
/// @param pSize Pointer to store the size of the memory object
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemGetAddressRange(palUvaManager* pUvaManager, palContext* pContext, palUint64 devicePtr,
                                          palUint64* pBase, size_t* pSize);

/// @brief Register host memory in the UVA manager
///
/// @param pUvaManager The UVA manager to use
/// @param pContext The context to use
/// @param pHostPtr The host pointer to register
/// @param size The size of the memory to register
/// @param flags The flags for the registration
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemHostRegister(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr,
                                       size_t size, palUint32 flags);

/// @brief Unregister host memory in the UVA manager
///
/// @param pUvaManager The UVA manager to use
/// @param pContext The context to use
/// @param pHostPtr The host pointer to unregister
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palUvaManagerMemHostUnregister(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr);
#endif // PAL_UVAMANAGER_H_