#include "g_pal_settings.h"
#include "pal_assert.h"
#include "pal_devicemanager.h"
#include "pal_globalmanager.h"
#include "pal_settingsfilemgr.h"
#include "pal_thunk.h"
#include "pal_tlsthreadmanager.h"
#include "pal_uvamanager.h"

palGlobalManager g_globalManager = {0};

// =====================================================================================================================
palResult palGlobalManagerInitialize(void)
{
    palResult result                                            = PAL_SUCCESS;
    palInt8   settingsFileName[PAL_SETTINGS_MAX_FILE_NAME_SIZE] = {0};
    palInt8   settingsFilePath[PAL_SETTINGS_MAX_PATH_SIZE]      = {0};

    if (palOsAtomicLoadSeqCst32(&g_globalManager.status) == PAL_GLOBAL_MANAGER_STATUS_INITIALIZED)
    {
        return PAL_SUCCESS;
    }

    memset(&g_globalManager, 0, sizeof(g_globalManager));

    // Create the runtime settings file manager
    // The settings file name and path are hardcoded for now.
    snprintf(settingsFileName, PAL_SETTINGS_MAX_FILE_NAME_SIZE, "%s", "nooneCudaSettings.cfg");
    snprintf(settingsFilePath, PAL_SETTINGS_MAX_PATH_SIZE, "%s", "/etc/noone");

    result = palSettingsFileMgrCreate(&g_globalManager.pSettingsFileMgr, settingsFileName, settingsFilePath);
    if (result != PAL_SUCCESS)
    {
        if (result == PAL_ERROR_FILE_NOT_FOUND)
        {
            // Settings file not found.
        }
        else
        {
            // Failed to create settings file manager.;
            return result;
        }
    }

    // Create the settings.
    result = palSettingsCreate(&g_globalManager.pSettings, g_globalManager.pSettingsFileMgr);
    if (result != PAL_SUCCESS)
    {
        if (g_globalManager.pSettingsFileMgr != NULL)
        {
            palSettingsFileMgrDestroy(g_globalManager.pSettingsFileMgr);
            g_globalManager.pSettingsFileMgr = NULL;
        }
        memset(&g_globalManager, 0, sizeof(g_globalManager));
        return result;
    }

#if ENABLE_PRINTS_ASSERTS
    // Initialize the debug print system.
    result = palDbgPrintInitialize(g_globalManager.pSettings->enableLogging,
                                   g_globalManager.pSettings->loggingDirectory,
                                   g_globalManager.pSettings->loggingFileName);
    if (result != PAL_SUCCESS)
    {
        palSettingsDestroy(g_globalManager.pSettings);
        if (g_globalManager.pSettingsFileMgr != NULL)
        {
            palSettingsFileMgrDestroy(g_globalManager.pSettingsFileMgr);
            g_globalManager.pSettingsFileMgr = NULL;
        }
        memset(&g_globalManager, 0, sizeof(g_globalManager));
        return result;
    }

    // Enable the assert mode based on the settings.
    palAssertEnableAssertMode(PAL_ASSERT_CAT_ASSERT, (palBool)g_globalManager.pSettings->enableDebugAssert);
#endif

    result = palThunkInitialize();
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize the PAL Thunk layer.");
        palSettingsDestroy(g_globalManager.pSettings);
        if (g_globalManager.pSettingsFileMgr != NULL)
        {
            palSettingsFileMgrDestroy(g_globalManager.pSettingsFileMgr);
            g_globalManager.pSettingsFileMgr = NULL;
        }
        memset(&g_globalManager, 0, sizeof(g_globalManager));
        return result;
    }

    // Create the device manager
    result = palDeviceManagerCreate(&g_globalManager.pDeviceManager, &g_globalManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create device manager.");
        palThunkDeinitialize();
        palSettingsDestroy(g_globalManager.pSettings);
        if (g_globalManager.pSettingsFileMgr != NULL)
        {
            palSettingsFileMgrDestroy(g_globalManager.pSettingsFileMgr);
            g_globalManager.pSettingsFileMgr = NULL;
        }
        memset(&g_globalManager, 0, sizeof(g_globalManager));
        return result;
    }

    // Create the tls thread manager
    result = palTlsThreadManagerCreate(&g_globalManager.pThreadManager, &g_globalManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create thread manager.");
        palThunkDeinitialize();
        palDeviceManagerDestroy(g_globalManager.pDeviceManager);
        palSettingsDestroy(g_globalManager.pSettings);
        if (g_globalManager.pSettingsFileMgr != NULL)
        {
            palSettingsFileMgrDestroy(g_globalManager.pSettingsFileMgr);
            g_globalManager.pSettingsFileMgr = NULL;
        }
        memset(&g_globalManager, 0, sizeof(g_globalManager));
        return result;
    }

    // Create the UVA manager
    result = palUvaManagerCreate(&g_globalManager.pUvaManager, &g_globalManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create UVA manager.");
        palTlsThreadManagerDestroy(g_globalManager.pThreadManager);
        palDeviceManagerDestroy(g_globalManager.pDeviceManager);
        palThunkDeinitialize();
        palSettingsDestroy(g_globalManager.pSettings);
        if (g_globalManager.pSettingsFileMgr != NULL)
        {
            palSettingsFileMgrDestroy(g_globalManager.pSettingsFileMgr);
            g_globalManager.pSettingsFileMgr = NULL;
        }
        memset(&g_globalManager, 0, sizeof(g_globalManager));
        return result;
    }

    palOsAtomicStoreSeqCst32(&g_globalManager.status, PAL_GLOBAL_MANAGER_STATUS_INITIALIZED);

    return result;
}

// =====================================================================================================================
void palGlobalManagerDeinitialize(void)
{
    if (palOsAtomicLoadSeqCst32(&g_globalManager.status) == PAL_GLOBAL_MANAGER_STATUS_INITIALIZED)
    {
        // Deinitialize the UVA manager
        palUvaManagerDeinitialize(g_globalManager.pUvaManager);

        // Destroy the tls thread manager
        palTlsThreadManagerDestroy(g_globalManager.pThreadManager);

        // Destroy the device manager
        palDeviceManagerDestroy(g_globalManager.pDeviceManager);

        // Deinitialize the thunk layer
        palThunkDeinitialize();

        // Destroy the settings
        palSettingsDestroy(g_globalManager.pSettings);

        // Destroy the settings file manager
        palSettingsFileMgrDestroy(g_globalManager.pSettingsFileMgr);

        memset(&g_globalManager, 0, sizeof(g_globalManager));

        palOsAtomicStoreSeqCst32(&g_globalManager.status, PAL_GLOBAL_MANAGER_STATUS_DEINITIALIZED);
    }
}

// =====================================================================================================================
palResult palGlobalManagerGetStatus(void)
{
    palGlobalManagerStatus status;

    status = (palGlobalManagerStatus)palOsAtomicLoadSeqCst32(&g_globalManager.status);
    if (status == PAL_GLOBAL_MANAGER_STATUS_NOT_INITIALIZED)
    {
        PAL_DBG_PRINTF_ERROR("Global manager is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }
    else if (status == PAL_GLOBAL_MANAGER_STATUS_DEINITIALIZED)
    {
        PAL_DBG_PRINTF_ERROR("Global manager is deinitialized.");
        return PAL_ERROR_DEINITIALIZED;
    }

    return PAL_SUCCESS;
}