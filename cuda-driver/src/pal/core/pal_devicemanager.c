#include "pal_assert.h"
#include "pal_context.h"
#include "pal_devicemanager.h"
#include "pal_globalmanager.h"
#include "pal_thunk.h"

#include <ctype.h>

/// @brief formats the UUID string to match the expected format "gpu-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".
/// The UUID is expected to be a 16-byte array.
/// @param pOutUuid Pointer to the output buffer where the formatted UUID will be stored.
/// @param pUuid Pointer to the 16-byte UUID array to format.
/// @note The output buffer must be at least PAL_THUNK_MAX_DEVICE_UUID_LENGTH bytes long.
static void formatUuid(char* pOutUuid, const palInt8* pUuid)
{
    // Format the UUID string to match the expected format "gpu-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
    snprintf(pOutUuid, PAL_THUNK_MAX_DEVICE_UUID_LENGTH,
             "gpu-%02x%02x%02x%02x-%02x%02x-%02x%02x-%02x%02x-%02x%02x%02x%02x%02x%02x",
             pUuid[0], pUuid[1], pUuid[2], pUuid[3],
             pUuid[4], pUuid[5],
             pUuid[6], pUuid[7],
             pUuid[8], pUuid[9],
             pUuid[10], pUuid[11], pUuid[12], pUuid[13], pUuid[14], pUuid[15]);
}

/// @brief Checks if the given environment variable string is a valid number.
/// A valid number consists only of digits and is not empty.
///
/// @param pStr The string to check.
/// @return Returns PAL_TRUE if the string is a valid number, PAL_FALSE otherwise.
static palBool isValidNumber(const palInt8* pStr)
{
    if (pStr == NULL || *pStr == '\0')
    {
        return PAL_FALSE;
    }

    for (const palInt8* p = pStr; *p != '\0'; ++p)
    {
        if (!isdigit((unsigned char)*p))
        {
            return PAL_FALSE;
        }
    }

    return PAL_TRUE;
}

/// @brief Checks if the given UUID is valid. The UUID should be in the format
/// "gpu-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx", where 'x' is a hexadecimal digit.
///
/// @param pUuid The UUID to check.
/// @return Returns PAL_TRUE if the UUID is valid, PAL_FALSE otherwise.
static palBool isValidUuid(const palInt8* pUuid)
{
    // Check if the UUID is in the format "gpu-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
    if ((pUuid == NULL) || (strlen(pUuid) != PAL_THUNK_MAX_DEVICE_UUID_LENGTH) || (strncmp(pUuid, "gpu-", 4) != 0))
    {
        return PAL_FALSE;
    }

    // Check if the rest of the UUID is valid hexadecimal characters
    for (int i = 4; i < PAL_THUNK_MAX_DEVICE_UUID_LENGTH - 1; ++i)
    {
        if (!isxdigit((unsigned char)pUuid[i]) && pUuid[i] != '-')
        {
            return PAL_FALSE;
        }
    }

    return PAL_TRUE;
}

/// @brief Parses the CUDA_VISIBLE_DEVICES environment variable to determine the number of cuda visible devices.
/// The function checks if the devices are specified by UUID or by index.
/// If the devices are specified by UUID, it validates each UUID.
/// If the devices are specified by index, it counts the number of indices.
/// The function updates the cudaVisibleDeviceCount in the device manager structure.
/// @param pDeviceManager Pointer to the device manager structure.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
static palResult ipalDeviceManagerParseCudaVisibleDeviceCount(palDeviceManager* pDeviceManager)
{
    palResult result                        = PAL_SUCCESS;
    palInt8   strBuf[PAL_OS_ENV_VAR_LENGTH] = {0};
    palInt32  deviceIndex                   = 0;
    palInt8*  pSaveptr                      = NULL;
    palInt8*  pToken                        = NULL;
    palBool   firstTokenCheck               = PAL_TRUE;

    PAL_ASSERT(pDeviceManager != NULL);

    palOsStrncpy(strBuf, pDeviceManager->cudaVisibleDevices, PAL_OS_ENV_VAR_LENGTH);
    strBuf[PAL_OS_ENV_VAR_LENGTH - 1] = '\0'; // Ensure null-termination

    pDeviceManager->cudaVisibleDeviceCount = 0;

    // Restart splitting the string
    pToken = palOsStrtok(strBuf, ",", &pSaveptr);
    while ((pToken != NULL) && pDeviceManager->cudaVisibleDeviceCount < pDeviceManager->openedDeviceCount)
    {
        while ((pToken != NULL) && isspace((palUint8)*pToken))
        {
            pToken++; // Skip leading whitespace
        }

        if (firstTokenCheck == PAL_TRUE)
        {
            // If this is the first token, we need to check if it is a UUID or an index.
            // Check whether the first token of CUDA_VISIBLE_DEVICES is in GPU UUID format
            pDeviceManager->isUuid = (pToken != NULL && palOsStrncasecmp(pToken, "gpu-", 4) == 0) ? PAL_TRUE : PAL_FALSE;

            // Set the firstTokenCheck to false to avoid checking again
            firstTokenCheck = PAL_FALSE;
        }
        else
        {
            if (pDeviceManager->isUuid != (palOsStrncasecmp(pToken, "gpu-", 4) == 0))
            {
                PAL_DBG_PRINTF_ERROR("Inconsistent CUDA_VISIBLE_DEVICES format: mixed UUID and index.");
                break; // Mixed formats are not allowed
            }
        }

        if (pDeviceManager->isUuid)
        {
            // If the token is a UUID, we need to check if it is valid.
            if (isValidUuid(pToken) == PAL_TRUE)
            {
                palOsStrncpy(pDeviceManager->ppVisibleDeviceUuids[pDeviceManager->cudaVisibleDeviceCount++],
                             pToken,
                             PAL_THUNK_MAX_DEVICE_UUID_LENGTH);
            }
            else
            {
                PAL_DBG_PRINTF_ERROR("Invalid UUID format: %s", pToken);
                break; // Exit the loop on invalid UUID
            }
        }
        else
        {
            // If the token is an index, we need to check if it is a valid index.
            if (isValidNumber(pToken) == PAL_TRUE)
            {
                deviceIndex = atoi(pToken);
                if (deviceIndex < 0 || deviceIndex >= pDeviceManager->openedDeviceCount)
                {
                    PAL_DBG_PRINTF_ERROR("Device index out of range: %d", deviceIndex);
                    break; // Exit the loop on out-of-range index
                }

                // Store the index in the visible device indices array
                pDeviceManager->pVisibleDeviceIndices[pDeviceManager->cudaVisibleDeviceCount++] = deviceIndex;
            }
            else
            {
                PAL_DBG_PRINTF_ERROR("Invalid device index: %s", pToken);
                break; // Exit the loop on invalid index
            }
        }

        pToken = palOsStrtok(NULL, ",", &pSaveptr);
    }

    if (pDeviceManager->cudaVisibleDeviceCount == 0)
    {
        PAL_DBG_PRINTF_ERROR("No valid devices found in CUDA_VISIBLE_DEVICES.");
        return PAL_ERROR_NO_DEVICE;
    }

    return PAL_SUCCESS;
}

/// @brief Calculates the visible device indices based on the CUDA_VISIBLE_DEVICES environment variable.
/// It checks for duplicates if the devices are specified by index, or validates each UUID if specified by UUID.
/// The function updates the visibleDeviceCount and pVisibleDeviceIndices in the device manager structure.
///
/// @param pDeviceManager Pointer to the device manager structure.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
static palResult ipalDeviceManagerCalculateVisibleDeviceIndices(palDeviceManager* pDeviceManager)
{
    palBool* pExistedHash = NULL;
    palInt32 gpuIndex     = -1;

    PAL_ASSERT(pDeviceManager != NULL);

    pDeviceManager->visibleDeviceCount = 0;

    pExistedHash = malloc(pDeviceManager->openedDeviceCount * sizeof(palBool));
    if (pExistedHash == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for device existence hash.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }
    memset(pExistedHash, 0, pDeviceManager->openedDeviceCount * sizeof(palBool));

    if (pDeviceManager->isUuid == PAL_FALSE)
    {
        // If the devices are specified by index, check for duplicates
        for (palInt32 i = 0; i < pDeviceManager->cudaVisibleDeviceCount; ++i)
        {
            gpuIndex = pDeviceManager->pVisibleDeviceIndices[i];
            if ((gpuIndex < 0) || (gpuIndex >= pDeviceManager->openedDeviceCount))
            {
                // Invalid index
                PAL_DBG_PRINTF_ERROR("Invalid device index %d found in CUDA_VISIBLE_DEVICES.", gpuIndex);
                free(pExistedHash);
                return PAL_ERROR_INVALID_DEVICE;
            }

            if (pExistedHash[gpuIndex] == PAL_TRUE)
            {
                PAL_DBG_PRINTF_ERROR("Duplicate device indices found in CUDA_VISIBLE_DEVICES.");
                free(pExistedHash);
                return PAL_ERROR_INVALID_DEVICE; // Duplicate index found
            }

            // Increment the visible device count.
            pDeviceManager->visibleDeviceCount++;

            // Mark this index as seen
            pExistedHash[gpuIndex] = PAL_TRUE;
        }
    }
    else
    {
        // If the devices are specified by UUID, validate each UUID
        for (palInt32 i = 0; i < pDeviceManager->cudaVisibleDeviceCount; ++i)
        {
            for (palInt32 j = 0; j < pDeviceManager->openedDeviceCount; ++j)
            {
                char FormattedUuid[PAL_THUNK_MAX_DEVICE_UUID_LENGTH] = {0};
                formatUuid(FormattedUuid, pDeviceManager->ppOpenedDevices[j]->pDeviceProperties->uuid);

                // Compare the UUIDs in a case-insensitive manner
                // and assign the index if a match is found.
                if (palOsStrncasecmp(pDeviceManager->ppVisibleDeviceUuids[i], FormattedUuid,
                                     PAL_THUNK_MAX_DEVICE_UUID_LENGTH) == 0)
                {
                    gpuIndex = pDeviceManager->ppOpenedDevices[j]->gpuId;
                    if (pExistedHash[gpuIndex] == PAL_TRUE)
                    {
                        PAL_DBG_PRINTF_ERROR("Duplicate UUIDs found in CUDA_VISIBLE_DEVICES.");
                        free(pExistedHash);
                        return PAL_ERROR_INVALID_DEVICE;
                    }

                    // Store the GPU index in the visible device indices array
                    pDeviceManager->pVisibleDeviceIndices[i] = gpuIndex;

                    // Increment the visible device count.
                    pDeviceManager->visibleDeviceCount++;

                    // Mark this GPU index as seen
                    pExistedHash[gpuIndex] = PAL_TRUE;

                    // Exit the inner loop once a match is found
                    break;
                }
            }
        }
    }

    free(pExistedHash);

    if (pDeviceManager->visibleDeviceCount == 0)
    {
        PAL_DBG_PRINTF_ERROR("No valid visible devices found in CUDA_VISIBLE_DEVICES.");
        return PAL_ERROR_NO_DEVICE;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceManagerCreate(palDeviceManager** ppDeviceManager, palGlobalManager* pGlobalManager)
{
    palResult         result         = PAL_SUCCESS;
    palDeviceManager* pDeviceManager = NULL;

    if ((ppDeviceManager == NULL) || (pGlobalManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        *ppDeviceManager = NULL;
        return PAL_ERROR_INVALID_VALUE;
    }

    pDeviceManager = (palDeviceManager*)malloc(sizeof(palDeviceManager));
    if (pDeviceManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for device manager.");
        *ppDeviceManager = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palDeviceManagerInitialize(pDeviceManager, pGlobalManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize device manager.");
        free(pDeviceManager);
        *ppDeviceManager = NULL;
        return result;
    }

    *ppDeviceManager = pDeviceManager;

    return result;
}

// =====================================================================================================================
palResult palDeviceManagerInitialize(palDeviceManager* pDeviceManager, palGlobalManager* pGlobalManager)
{
    palResult result = PAL_SUCCESS;
    palInt32  error  = 0;

    if ((pDeviceManager == NULL) || (pGlobalManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pDeviceManager, 0, sizeof(palDeviceManager));

    pDeviceManager->pGlobalManager = pGlobalManager;

    error = palOsMutexInit(&pDeviceManager->activeContextsMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize mutex for active contexts.");
        memset(pDeviceManager, 0, sizeof(palDeviceManager));
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    result = palThunkDeviceGetCount(&pDeviceManager->openedDeviceCount);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to get device count.");
        palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
        memset(pDeviceManager, 0, sizeof(palDeviceManager));
        return result;
    }

    pDeviceManager->ppOpenedDevices = (palDevice**)malloc(pDeviceManager->openedDeviceCount * sizeof(palDevice*));
    if (pDeviceManager->ppOpenedDevices == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for devices array.");
        palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
        memset(pDeviceManager, 0, sizeof(palDeviceManager));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    memset(pDeviceManager->ppOpenedDevices, 0, pDeviceManager->openedDeviceCount * sizeof(palDevice*));

    for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
    {
        result = palDeviceCreate(&pDeviceManager->ppOpenedDevices[i], pDeviceManager, i);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to initialize the device handle %p.", pDeviceManager->ppOpenedDevices[i]);

            // Cleanup already opened devices.
            for (palInt32 j = 0; j < i; ++j)
            {
                palDeviceDestroy(pDeviceManager->ppOpenedDevices[j]);
                pDeviceManager->ppOpenedDevices[j] = NULL;
            }

            // Free the devices array and mutex.
            free(pDeviceManager->ppOpenedDevices);
            palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
            memset(pDeviceManager, 0, sizeof(palDeviceManager));
            return result;
        }
    }

    // Allocate memory for visible device indices.
    pDeviceManager->pVisibleDeviceIndices = (palInt32*)malloc(pDeviceManager->openedDeviceCount * sizeof(palInt32));
    if (pDeviceManager->pVisibleDeviceIndices == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for visible device indices.");
        // Cleanup already opened devices.
        for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
        {
            palDeviceDestroy(pDeviceManager->ppOpenedDevices[i]);
            pDeviceManager->ppOpenedDevices[i] = NULL;
        }
        free(pDeviceManager->ppOpenedDevices);
        palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
        memset(pDeviceManager, 0, sizeof(palDeviceManager));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    memset(pDeviceManager->pVisibleDeviceIndices, 0, pDeviceManager->openedDeviceCount * sizeof(palInt32));

    // Allocate memory for visible device UUIDs.
    pDeviceManager->ppVisibleDeviceUuids = (palInt8**)malloc(pDeviceManager->openedDeviceCount * sizeof(palInt8*));
    if (pDeviceManager->ppVisibleDeviceUuids == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for visible device UUIDs.");
        free(pDeviceManager->pVisibleDeviceIndices);
        // Cleanup already opened devices.
        for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
        {
            palDeviceDestroy(pDeviceManager->ppOpenedDevices[i]);
            pDeviceManager->ppOpenedDevices[i] = NULL;
        }
        free(pDeviceManager->ppOpenedDevices);
        palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
        memset(pDeviceManager, 0, sizeof(palDeviceManager));
        return PAL_ERROR_OUT_OF_MEMORY;
    }
    memset(pDeviceManager->ppVisibleDeviceUuids, 0, pDeviceManager->openedDeviceCount * sizeof(palInt8*));

    for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
    {
        pDeviceManager->ppVisibleDeviceUuids[i] = (palInt8*)malloc(PAL_THUNK_MAX_DEVICE_UUID_LENGTH * sizeof(palInt8));
        if (pDeviceManager->ppVisibleDeviceUuids[i] == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Failed to allocate memory for visible device UUIDs.");
            // Cleanup already opened devices.
            for (palInt32 j = 0; j < i; ++j)
            {
                free(pDeviceManager->ppVisibleDeviceUuids[j]);
            }

            free(pDeviceManager->ppVisibleDeviceUuids);
            free(pDeviceManager->pVisibleDeviceIndices);
            // Cleanup already opened devices.
            for (palInt32 j = 0; j < pDeviceManager->openedDeviceCount; ++j)
            {
                palDeviceDestroy(pDeviceManager->ppOpenedDevices[j]);
                pDeviceManager->ppOpenedDevices[j] = NULL;
            }
            free(pDeviceManager->ppOpenedDevices);
            palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
            memset(pDeviceManager, 0, sizeof(palDeviceManager));
            return PAL_ERROR_OUT_OF_MEMORY;
        }

        memset(pDeviceManager->ppVisibleDeviceUuids[i], 0, PAL_THUNK_MAX_DEVICE_UUID_LENGTH * sizeof(palInt8));
    }

    // Get the CUDA_VISIBLE_DEVICES environment variable value.
    if (palOsEnvGet("CUDA_VISIBLE_DEVICES", pDeviceManager->cudaVisibleDevices,
                    sizeof(pDeviceManager->cudaVisibleDevices)) != 0)
    {
        PAL_DBG_PRINTF_INFO("CUDA_VISIBLE_DEVICES: %s", pDeviceManager->cudaVisibleDevices);

        // If CUDA_VISIBLE_DEVICES is set, we need to parse it to determine the visible devices.
        result = ipalDeviceManagerParseCudaVisibleDeviceCount(pDeviceManager);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to parse CUDA_VISIBLE_DEVICES.");
            for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
            {
                free(pDeviceManager->ppVisibleDeviceUuids[i]);
                pDeviceManager->ppVisibleDeviceUuids[i] = NULL;
            }
            free(pDeviceManager->ppVisibleDeviceUuids);
            free(pDeviceManager->pVisibleDeviceIndices);
            // Cleanup already opened devices.
            for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
            {
                palDeviceDestroy(pDeviceManager->ppOpenedDevices[i]);
                pDeviceManager->ppOpenedDevices[i] = NULL;
            }
            free(pDeviceManager->ppOpenedDevices);
            palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
            memset(pDeviceManager, 0, sizeof(palDeviceManager));
            return result;
        }

        // Calculate the visible device indices based on the parsed CUDA_VISIBLE_DEVICES.
        result = ipalDeviceManagerCalculateVisibleDeviceIndices(pDeviceManager);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to parse CUDA_VISIBLE_DEVICES.");
            free(pDeviceManager->ppVisibleDeviceUuids);
            free(pDeviceManager->pVisibleDeviceIndices);
            // Cleanup already opened devices.
            for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
            {
                palDeviceDestroy(pDeviceManager->ppOpenedDevices[i]);
                pDeviceManager->ppOpenedDevices[i] = NULL;
            }
            free(pDeviceManager->ppOpenedDevices);
            palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
            memset(pDeviceManager, 0, sizeof(palDeviceManager));
            return result;
        }
    }
    else
    {
        // If CUDA_VISIBLE_DEVICES is not set, we consider all devices as visible.
        pDeviceManager->visibleDeviceCount = pDeviceManager->openedDeviceCount;
        pDeviceManager->isUuid = PAL_FALSE; // Default to index-based visibility

        for (palInt32 i = 0; i < pDeviceManager->visibleDeviceCount; ++i)
        {
            pDeviceManager->pVisibleDeviceIndices[i] = i;
        }
    }

    // Allocate memory for visible devices array.
    pDeviceManager->ppVisibleDevices = (palDevice**)malloc(pDeviceManager->visibleDeviceCount * sizeof(palDevice*));
    if (pDeviceManager->ppVisibleDevices == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for visible devices array.");
        for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
        {
            free(pDeviceManager->ppVisibleDeviceUuids[i]);
            pDeviceManager->ppVisibleDeviceUuids[i] = NULL;
        }
        free(pDeviceManager->ppVisibleDeviceUuids);
        free(pDeviceManager->pVisibleDeviceIndices);
        // Cleanup already opened devices.
        for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
        {
            palDeviceDestroy(pDeviceManager->ppOpenedDevices[i]);
            pDeviceManager->ppOpenedDevices[i] = NULL;
        }
        free(pDeviceManager->ppOpenedDevices);
        palOsMutexDestroy(&pDeviceManager->activeContextsMutex);
        memset(pDeviceManager, 0, sizeof(palDeviceManager));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    // Initialize the visible devices array.
    for (palInt32 i = 0; i < pDeviceManager->visibleDeviceCount; ++i)
    {
        palInt32 deviceMapIdx = pDeviceManager->pVisibleDeviceIndices[i];
        pDeviceManager->ppVisibleDevices[i] = pDeviceManager->ppOpenedDevices[deviceMapIdx];
        pDeviceManager->ppVisibleDevices[i]->deviceId = i;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palDeviceManagerDeinitialize(palDeviceManager* pDeviceManager)
{
    palContext* pCurrContext = NULL;
    palContext* pNextContext = NULL;

    if (pDeviceManager == NULL)
    {
        return;
    }

    // Deinitialize the device manager
    palOsMutexLock(&pDeviceManager->activeContextsMutex);
    pCurrContext = pDeviceManager->pActiveContextsList;
    while (pCurrContext != NULL)
    {
        pNextContext = pCurrContext->pNext;
        if (pCurrContext->persistentState.isPrimary == PAL_FALSE)
        {
            // If the context is not primary, destroy it.
            palContextDestroy(pCurrContext);
        }
        pCurrContext = pNextContext;
    }
    palOsMutexUnlock(&pDeviceManager->activeContextsMutex);

    // Free the visible devices array.
    if (pDeviceManager->ppVisibleDevices != NULL)
    {
        memset(pDeviceManager->ppVisibleDevices, 0, pDeviceManager->visibleDeviceCount * sizeof(palDevice*));
        free(pDeviceManager->ppVisibleDevices);
    }

    // Free the visible device uuids array.
    if (pDeviceManager->ppVisibleDeviceUuids != NULL)
    {
        for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
        {
            memset(pDeviceManager->ppVisibleDeviceUuids[i], 0, PAL_THUNK_MAX_DEVICE_UUID_LENGTH * sizeof(palInt8));
            free(pDeviceManager->ppVisibleDeviceUuids[i]);
            pDeviceManager->ppVisibleDeviceUuids[i] = NULL;
        }
        free(pDeviceManager->ppVisibleDeviceUuids);
    }

    if (pDeviceManager->pVisibleDeviceIndices != NULL)
    {
        memset(pDeviceManager->pVisibleDeviceIndices, 0, pDeviceManager->openedDeviceCount * sizeof(palInt32));
        free(pDeviceManager->pVisibleDeviceIndices);
        pDeviceManager->pVisibleDeviceIndices = NULL;
    }

    if (pDeviceManager->ppOpenedDevices != NULL)
    {
        for (palInt32 i = 0; i < pDeviceManager->openedDeviceCount; ++i)
        {
            if (pDeviceManager->ppOpenedDevices[i] != NULL)
            {
                palDeviceDestroy(pDeviceManager->ppOpenedDevices[i]);
                pDeviceManager->ppOpenedDevices[i] = NULL;
            }
        }

        // Free the devices array.
        free(pDeviceManager->ppOpenedDevices);
    }

    palOsMutexDestroy(&pDeviceManager->activeContextsMutex);

    memset(pDeviceManager, 0, sizeof(palDeviceManager));
}

// =====================================================================================================================
void palDeviceManagerDestroy(palDeviceManager* pDeviceManager)
{
    if (pDeviceManager == NULL)
    {
        return;
    }

    palDeviceManagerDeinitialize(pDeviceManager);

    free(pDeviceManager);
}

// =====================================================================================================================
palResult palDeviceManagerGetDevice(palDeviceManager* pDeviceManager, palInt32 ordinal, palDevice** ppDevice)
{
    PAL_ASSERT(pDeviceManager != NULL);
    PAL_ASSERT(ppDevice != NULL);

    if ((ordinal < 0) || (ordinal >= pDeviceManager->visibleDeviceCount))
    {
        PAL_DBG_PRINTF_ERROR("Invalid device ordinal: %d", ordinal);
        return PAL_ERROR_INVALID_DEVICE; // Invalid ordinal
    }

    *ppDevice = pDeviceManager->ppVisibleDevices[ordinal];

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceManagerGetDeviceByPCIBusId(palDeviceManager* pDeviceManager,
                                              const palInt8* pciBusId,
                                              palDevice** ppDevice)
{
    palInt32   retCode    = 0;
    palInt32   domainId   = 0;
    palInt32   busId      = 0;
    palInt32   deviceId   = 0;
    palInt32   functionId = 0;
    palDevice* pDevice    = NULL;

    PAL_ASSERT(pDeviceManager != NULL);
    PAL_ASSERT(pciBusId != NULL);
    PAL_ASSERT(ppDevice != NULL);

    // Parse the PCI bus ID into domain, bus, device, and function IDs.
    // Try [domain]:[bus]:[device].[function]
    retCode = palOsSscanf(pciBusId, "%x:%x:%x.%x", &domainId, &busId, &deviceId, &functionId);
    if (retCode != 4)
    {
        // Try [domain]:[bus]:[device]
        retCode = palOsSscanf(pciBusId, "%x:%x:%x", &domainId, &busId, &deviceId);
        if (retCode == 3)
        {
            functionId = 0;
        }
        else
        {
            // Try [bus]:[device].[function]
            retCode = palOsSscanf(pciBusId, "%x:%x.%x", &busId, &deviceId, &functionId);
            if (retCode == 3)
            {
                domainId = 0;
            }
            else
            {
                PAL_DBG_PRINTF_ERROR("Invalid PCI bus ID format: %s", pciBusId);
                return PAL_ERROR_INVALID_VALUE;
            }
        }
    }

    if (functionId != 0)
    {
        PAL_DBG_PRINTF_ERROR("Invalid PCI function ID: %d. Only 0 are supported.", functionId);
        return PAL_ERROR_INVALID_VALUE; // Invalid PCI function ID
    }

    for (palInt32 i = 0; i< pDeviceManager->visibleDeviceCount; ++i)
    {
        pDevice = pDeviceManager->ppVisibleDevices[i];
        PAL_ASSERT(pDevice != NULL);

        if ((domainId != pDevice->pDeviceProperties->pciDomainID) ||
            (busId != pDevice->pDeviceProperties->pciBusID) ||
            (deviceId != pDevice->pDeviceProperties->pciDeviceID))
        {
            continue; // Skip to the next device if IDs do not match
        }
        else
        {
            // Found the device with matching PCI bus ID
            *ppDevice = pDevice;

            return PAL_SUCCESS;
        }
    }

    // No device found with the specified PCI bus ID
    return PAL_ERROR_INVALID_DEVICE;
}

// =====================================================================================================================
palInt32 palDeviceManagerGetDeviceCount(palDeviceManager* pDeviceManager)
{
    PAL_ASSERT(pDeviceManager != NULL);

    // Return the count of visible devices.
    return pDeviceManager->visibleDeviceCount;
}