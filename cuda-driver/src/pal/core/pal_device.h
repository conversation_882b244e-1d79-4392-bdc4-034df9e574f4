#ifndef PAL_DEVICE_H_
#define PAL_DEVICE_H_

#include "pal.h"
#include "pal_array.h"
#include "pal_deviceproperties.h"
#include "pal_os.h"
#include "pal_structures.h"
#include "pal_types.h"

typedef enum palDeviceAttribute_enum
{
    PAL_DEVICE_ATTRIBUTE_MAX_THREADS_PER_BLOCK = 1,                          /**< Maximum number of threads per block */
    PAL_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_X = 2,                                /**< Maximum block dimension X */
    PAL_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_Y = 3,                                /**< Maximum block dimension Y */
    PAL_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_Z = 4,                                /**< Maximum block dimension Z */
    PAL_DEVICE_ATTRIBUTE_MAX_GRID_DIM_X = 5,                                 /**< Maximum grid dimension X */
    PAL_DEVICE_ATTRIBUTE_MAX_GRID_DIM_Y = 6,                                 /**< Maximum grid dimension Y */
    PAL_DEVICE_ATTRIBUTE_MAX_GRID_DIM_Z = 7,                                 /**< Maximum grid dimension Z */
    PAL_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_BLOCK = 8,                    /**< Maximum shared memory available per block in bytes */
    PAL_DEVICE_ATTRIBUTE_TOTAL_CONSTANT_MEMORY = 9,                          /**< Memory available on device for __constant__ variables in a CUDA C kernel in bytes */
    PAL_DEVICE_ATTRIBUTE_WARP_SIZE = 10,                                     /**< Warp size in threads */
    PAL_DEVICE_ATTRIBUTE_MAX_PITCH = 11,                                     /**< Maximum pitch in bytes allowed by memory copies */
    PAL_DEVICE_ATTRIBUTE_MAX_REGISTERS_PER_BLOCK = 12,                       /**< Maximum number of 32-bit registers available per block */
    PAL_DEVICE_ATTRIBUTE_CLOCK_RATE = 13,                                    /**< Typical clock frequency in kilohertz */
    PAL_DEVICE_ATTRIBUTE_TEXTURE_ALIGNMENT = 14,                             /**< Alignment requirement for textures */
    PAL_DEVICE_ATTRIBUTE_GPU_OVERLAP = 15,                                   /**< Device can possibly copy memory and execute a kernel concurrently. Deprecated. Use instead CU_DEVICE_ATTRIBUTE_ASYNC_ENGINE_COUNT. */
    PAL_DEVICE_ATTRIBUTE_MULTIPROCESSOR_COUNT = 16,                          /**< Number of multiprocessors on device */
    PAL_DEVICE_ATTRIBUTE_KERNEL_EXEC_TIMEOUT = 17,                           /**< Specifies whether there is a run time limit on kernels */
    PAL_DEVICE_ATTRIBUTE_INTEGRATED = 18,                                    /**< Device is integrated with host memory */
    PAL_DEVICE_ATTRIBUTE_CAN_MAP_HOST_MEMORY = 19,                           /**< Device can map host memory into CUDA address space */
    PAL_DEVICE_ATTRIBUTE_COMPUTE_MODE = 20,                                  /**< Compute mode (See ::CUcomputemode for details) */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_WIDTH = 21,                       /**< Maximum 1D texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_WIDTH = 22,                       /**< Maximum 2D texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_HEIGHT = 23,                      /**< Maximum 2D texture height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_WIDTH = 24,                       /**< Maximum 3D texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_HEIGHT = 25,                      /**< Maximum 3D texture height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_DEPTH = 26,                       /**< Maximum 3D texture depth */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_WIDTH = 27,               /**< Maximum 2D layered texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_HEIGHT = 28,              /**< Maximum 2D layered texture height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_LAYERS = 29,              /**< Maximum layers in a 2D layered texture */
    PAL_DEVICE_ATTRIBUTE_SURFACE_ALIGNMENT = 30,                             /**< Alignment requirement for surfaces */
    PAL_DEVICE_ATTRIBUTE_CONCURRENT_KERNELS = 31,                            /**< Device can possibly execute multiple kernels concurrently */
    PAL_DEVICE_ATTRIBUTE_ECC_ENABLED = 32,                                   /**< Device has ECC support enabled */
    PAL_DEVICE_ATTRIBUTE_PCI_BUS_ID = 33,                                    /**< PCI bus ID of the device */
    PAL_DEVICE_ATTRIBUTE_PCI_DEVICE_ID = 34,                                 /**< PCI device ID of the device */
    PAL_DEVICE_ATTRIBUTE_TCC_DRIVER = 35,                                    /**< Device is using TCC driver model */
    PAL_DEVICE_ATTRIBUTE_MEMORY_CLOCK_RATE = 36,                             /**< Peak memory clock frequency in kilohertz */
    PAL_DEVICE_ATTRIBUTE_GLOBAL_MEMORY_BUS_WIDTH = 37,                       /**< Global memory bus width in bits */
    PAL_DEVICE_ATTRIBUTE_L2_CACHE_SIZE = 38,                                 /**< Size of L2 cache in bytes */
    PAL_DEVICE_ATTRIBUTE_MAX_THREADS_PER_MULTIPROCESSOR = 39,                /**< Maximum resident threads per multiprocessor */
    PAL_DEVICE_ATTRIBUTE_ASYNC_ENGINE_COUNT = 40,                            /**< Number of asynchronous engines */
    PAL_DEVICE_ATTRIBUTE_UNIFIED_ADDRESSING = 41,                            /**< Device shares a unified address space with the host */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LAYERED_WIDTH = 42,               /**< Maximum 1D layered texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LAYERED_LAYERS = 43,              /**< Maximum layers in a 1D layered texture */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_GATHER_WIDTH = 45,                /**< Maximum 2D texture width if CUDA_ARRAY3D_TEXTURE_GATHER is set */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_GATHER_HEIGHT = 46,               /**< Maximum 2D texture height if CUDA_ARRAY3D_TEXTURE_GATHER is set */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_WIDTH_ALTERNATE = 47,             /**< Alternate maximum 3D texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_HEIGHT_ALTERNATE = 48,            /**< Alternate maximum 3D texture height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_DEPTH_ALTERNATE = 49,             /**< Alternate maximum 3D texture depth */
    PAL_DEVICE_ATTRIBUTE_PCI_DOMAIN_ID = 50,                                 /**< PCI domain ID of the device */
    PAL_DEVICE_ATTRIBUTE_TEXTURE_PITCH_ALIGNMENT = 51,                       /**< Pitch alignment requirement for textures */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_WIDTH = 52,                  /**< Maximum cubemap texture width/height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_LAYERED_WIDTH = 53,          /**< Maximum cubemap layered texture width/height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_LAYERED_LAYERS = 54,         /**< Maximum layers in a cubemap layered texture */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_WIDTH = 55,                       /**< Maximum 1D surface width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_WIDTH = 56,                       /**< Maximum 2D surface width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_HEIGHT = 57,                      /**< Maximum 2D surface height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_WIDTH = 58,                       /**< Maximum 3D surface width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_HEIGHT = 59,                      /**< Maximum 3D surface height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_DEPTH = 60,                       /**< Maximum 3D surface depth */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_LAYERED_WIDTH = 61,               /**< Maximum 1D layered surface width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_LAYERED_LAYERS = 62,              /**< Maximum layers in a 1D layered surface */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_WIDTH = 63,               /**< Maximum 2D layered surface width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_HEIGHT = 64,              /**< Maximum 2D layered surface height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_LAYERS = 65,              /**< Maximum layers in a 2D layered surface */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_WIDTH = 66,                  /**< Maximum cubemap surface width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_LAYERED_WIDTH = 67,          /**< Maximum cubemap layered surface width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_LAYERED_LAYERS = 68,         /**< Maximum layers in a cubemap layered surface */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LINEAR_WIDTH = 69,                /**< Deprecated, do not use. Use cudaDeviceGetTexture1DLinearMaxWidth() or cuDeviceGetTexture1DLinearMaxWidth() instead. */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_WIDTH = 70,                /**< Maximum 2D linear texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_HEIGHT = 71,               /**< Maximum 2D linear texture height */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_PITCH = 72,                /**< Maximum 2D linear texture pitch in bytes */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_MIPMAPPED_WIDTH = 73,             /**< Maximum mipmapped 2D texture width */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_MIPMAPPED_HEIGHT = 74,            /**< Maximum mipmapped 2D texture height */
    PAL_DEVICE_ATTRIBUTE_COMPUTE_CAPABILITY_MAJOR = 75,                      /**< Major compute capability version number */
    PAL_DEVICE_ATTRIBUTE_COMPUTE_CAPABILITY_MINOR = 76,                      /**< Minor compute capability version number */
    PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_MIPMAPPED_WIDTH = 77,             /**< Maximum mipmapped 1D texture width */
    PAL_DEVICE_ATTRIBUTE_STREAM_PRIORITIES_SUPPORTED = 78,                   /**< Device supports stream priorities */
    PAL_DEVICE_ATTRIBUTE_GLOBAL_L1_CACHE_SUPPORTED = 79,                     /**< Device supports caching globals in L1 */
    PAL_DEVICE_ATTRIBUTE_LOCAL_L1_CACHE_SUPPORTED = 80,                      /**< Device supports caching locals in L1 */
    PAL_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_MULTIPROCESSOR = 81,          /**< Maximum shared memory available per multiprocessor in bytes */
    PAL_DEVICE_ATTRIBUTE_MAX_REGISTERS_PER_MULTIPROCESSOR = 82,              /**< Maximum number of 32-bit registers available per multiprocessor */
    PAL_DEVICE_ATTRIBUTE_MANAGED_MEMORY = 83,                                /**< Device can allocate managed memory on this system */
    PAL_DEVICE_ATTRIBUTE_MULTI_GPU_BOARD = 84,                               /**< Device is on a multi-GPU board */
    PAL_DEVICE_ATTRIBUTE_MULTI_GPU_BOARD_GROUP_ID = 85,                      /**< Unique id for a group of devices on the same multi-GPU board */
    PAL_DEVICE_ATTRIBUTE_HOST_NATIVE_ATOMIC_SUPPORTED = 86,                  /**< Link between the device and the host supports native atomic operations (this is a placeholder attribute, and is not supported on any current hardware)*/
    PAL_DEVICE_ATTRIBUTE_SINGLE_TO_DOUBLE_PRECISION_PERF_RATIO = 87,         /**< Ratio of single precision performance (in floating-point operations per second) to double precision performance */
    PAL_DEVICE_ATTRIBUTE_PAGEABLE_MEMORY_ACCESS = 88,                        /**< Device supports coherently accessing pageable memory without calling cudaHostRegister on it */
    PAL_DEVICE_ATTRIBUTE_CONCURRENT_MANAGED_ACCESS = 89,                     /**< Device can coherently access managed memory concurrently with the CPU */
    PAL_DEVICE_ATTRIBUTE_COMPUTE_PREEMPTION_SUPPORTED = 90,                  /**< Device supports compute preemption. */
    PAL_DEVICE_ATTRIBUTE_CAN_USE_HOST_POINTER_FOR_REGISTERED_MEM = 91,       /**< Device can access host registered memory at the same virtual address as the CPU */
    PAL_DEVICE_ATTRIBUTE_CAN_USE_STREAM_MEM_OPS_V1 = 92,                     /**< Deprecated, along with v1 MemOps API, ::cuStreamBatchMemOp and related APIs are supported. */
    PAL_DEVICE_ATTRIBUTE_CAN_USE_64_BIT_STREAM_MEM_OPS_V1 = 93,              /**< Deprecated, along with v1 MemOps API, 64-bit operations are supported in ::cuStreamBatchMemOp and related APIs. */
    PAL_DEVICE_ATTRIBUTE_CAN_USE_STREAM_WAIT_VALUE_NOR_V1 = 94,              /**< Deprecated, along with v1 MemOps API, ::CU_STREAM_WAIT_VALUE_NOR is supported. */
    PAL_DEVICE_ATTRIBUTE_COOPERATIVE_LAUNCH = 95,                            /**< Device supports launching cooperative kernels via ::cuLaunchCooperativeKernel */
    PAL_DEVICE_ATTRIBUTE_COOPERATIVE_MULTI_DEVICE_LAUNCH = 96,               /**< Deprecated, ::cuLaunchCooperativeKernelMultiDevice is deprecated. */
    PAL_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_BLOCK_OPTIN = 97,             /**< Maximum optin shared memory per block */
    PAL_DEVICE_ATTRIBUTE_CAN_FLUSH_REMOTE_WRITES = 98,                       /**< The ::CU_STREAM_WAIT_VALUE_FLUSH flag and the ::CU_STREAM_MEM_OP_FLUSH_REMOTE_WRITES MemOp are supported on the device. See \ref CUDA_MEMOP for additional details. */
    PAL_DEVICE_ATTRIBUTE_HOST_REGISTER_SUPPORTED = 99,                       /**< Device supports host memory registration via ::cudaHostRegister. */
    PAL_DEVICE_ATTRIBUTE_PAGEABLE_MEMORY_ACCESS_USES_HOST_PAGE_TABLES = 100, /**< Device accesses pageable memory via the host's page tables. */
    PAL_DEVICE_ATTRIBUTE_DIRECT_MANAGED_MEM_ACCESS_FROM_HOST = 101,          /**< The host can directly access managed memory on the device without migration. */
    PAL_DEVICE_ATTRIBUTE_VIRTUAL_MEMORY_MANAGEMENT_SUPPORTED = 102,          /**< Device supports virtual memory management APIs like ::cuMemAddressReserve, ::cuMemCreate, ::cuMemMap and related APIs */
    PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_POSIX_FILE_DESCRIPTOR_SUPPORTED = 103,  /**< Device supports exporting memory to a posix file descriptor with ::cuMemExportToShareableHandle, if requested via ::cuMemCreate */
    PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_WIN32_HANDLE_SUPPORTED = 104,           /**< Device supports exporting memory to a Win32 NT handle with ::cuMemExportToShareableHandle, if requested via ::cuMemCreate */
    PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_WIN32_KMT_HANDLE_SUPPORTED = 105,       /**< Device supports exporting memory to a Win32 KMT handle with ::cuMemExportToShareableHandle, if requested via ::cuMemCreate */
    PAL_DEVICE_ATTRIBUTE_MAX_BLOCKS_PER_MULTIPROCESSOR = 106,                /**< Maximum number of blocks per multiprocessor */
    PAL_DEVICE_ATTRIBUTE_GENERIC_COMPRESSION_SUPPORTED = 107,                /**< Device supports compression of memory */
    PAL_DEVICE_ATTRIBUTE_MAX_PERSISTING_L2_CACHE_SIZE = 108,                 /**< Maximum L2 persisting lines capacity setting in bytes. */
    PAL_DEVICE_ATTRIBUTE_MAX_ACCESS_POLICY_WINDOW_SIZE = 109,                /**< Maximum value of CUaccessPolicyWindow::num_bytes. */
    PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_WITH_CUDA_VMM_SUPPORTED = 110,      /**< Device supports specifying the GPUDirect RDMA flag with ::cuMemCreate */
    PAL_DEVICE_ATTRIBUTE_RESERVED_SHARED_MEMORY_PER_BLOCK = 111,             /**< Shared memory reserved by CUDA driver per block in bytes */
    PAL_DEVICE_ATTRIBUTE_SPARSE_CUDA_ARRAY_SUPPORTED = 112,                  /**< Device supports sparse CUDA arrays and sparse CUDA mipmapped arrays */
    PAL_DEVICE_ATTRIBUTE_READ_ONLY_HOST_REGISTER_SUPPORTED = 113,            /**< Device supports using the ::cuMemHostRegister flag ::CU_MEMHOSTERGISTER_READ_ONLY to register memory that must be mapped as read-only to the GPU */
    PAL_DEVICE_ATTRIBUTE_TIMELINE_SEMAPHORE_INTEROP_SUPPORTED = 114,         /**< External timeline semaphore interop is supported on the device */
    PAL_DEVICE_ATTRIBUTE_MEMORY_POOLS_SUPPORTED = 115,                       /**< Device supports using the ::cuMemAllocAsync and ::cuMemPool family of APIs */
    PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_SUPPORTED = 116,                    /**< Device supports GPUDirect RDMA APIs, like nvidia_p2p_get_pages (see https://docs.nvidia.com/cuda/gpudirect-rdma for more information) */
    PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_FLUSH_WRITES_OPTIONS = 117,         /**< The returned attribute shall be interpreted as a bitmask, where the individual bits are described by the ::CUflushGPUDirectRDMAWritesOptions enum */
    PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_WRITES_ORDERING = 118,              /**< GPUDirect RDMA writes to the device do not need to be flushed for consumers within the scope indicated by the returned attribute. See ::CUGPUDirectRDMAWritesOrdering for the numerical values returned here. */
    PAL_DEVICE_ATTRIBUTE_MEMPOOL_SUPPORTED_HANDLE_TYPES = 119,               /**< Handle types supported with mempool based IPC */
    PAL_DEVICE_ATTRIBUTE_CLUSTER_LAUNCH = 120,                               /**< Indicates device supports cluster launch */
    PAL_DEVICE_ATTRIBUTE_DEFERRED_MAPPING_CUDA_ARRAY_SUPPORTED = 121,        /**< Device supports deferred mapping CUDA arrays and CUDA mipmapped arrays */
    PAL_DEVICE_ATTRIBUTE_CAN_USE_64_BIT_STREAM_MEM_OPS = 122,                /**< 64-bit operations are supported in ::cuStreamBatchMemOp and related MemOp APIs. */
    PAL_DEVICE_ATTRIBUTE_CAN_USE_STREAM_WAIT_VALUE_NOR = 123,                /**< ::CU_STREAM_WAIT_VALUE_NOR is supported by MemOp APIs. */
    PAL_DEVICE_ATTRIBUTE_DMA_BUF_SUPPORTED = 124,                            /**< Device supports buffer sharing with dma_buf mechanism. */
    PAL_DEVICE_ATTRIBUTE_IPC_EVENT_SUPPORTED = 125,                          /**< Device supports IPC Events. */
    PAL_DEVICE_ATTRIBUTE_MEM_SYNC_DOMAIN_COUNT = 126,                        /**< Number of memory domains the device supports. */
    PAL_DEVICE_ATTRIBUTE_TENSOR_MAP_ACCESS_SUPPORTED = 127,                  /**< Device supports accessing memory using Tensor Map. */
    PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_FABRIC_SUPPORTED = 128,                 /**< Device supports exporting memory to a fabric handle with cuMemExportToShareableHandle() or requested with cuMemCreate() */
    PAL_DEVICE_ATTRIBUTE_UNIFIED_FUNCTION_POINTERS = 129,                    /**< Device supports unified function pointers. */
    PAL_DEVICE_ATTRIBUTE_NUMA_CONFIG = 130,
    PAL_DEVICE_ATTRIBUTE_NUMA_ID = 131,
    PAL_DEVICE_ATTRIBUTE_MULTICAST_SUPPORTED = 132,                          /**< Device supports switch multicast and reduction operations. */
    PAL_DEVICE_ATTRIBUTE_MPS_ENABLED = 133,                                  /**< Indicates if contexts created on this device will be shared via MPS */
    PAL_DEVICE_ATTRIBUTE_HOST_NUMA_ID = 134,                                 /**< NUMA ID of the host node closest to the device. Returns -1 when system does not support NUMA. */
    PAL_DEVICE_ATTRIBUTE_MAX
} palDeviceAttribute;

/**
 * Execution Affinity Types
 */
typedef enum palExecAffinityType_enum
{
    PAL_EXEC_AFFINITY_TYPE_SM_COUNT = 0,  /**< Create a context with limited SMs. */
    PAL_EXEC_AFFINITY_TYPE_MAX
} palExecAffinityType;

/**
 * Value for ::PAL_EXEC_AFFINITY_TYPE_SM_COUNT
 */
typedef struct palExecAffinitySmCount_st
{
    palUint32 val;    /**< The number of SMs the context is limited to use. */
} palExecAffinitySmCount;

/**
 * Execution Affinity Parameters
 */
typedef struct palExecAffinityParam_st
{
    palExecAffinityType type;
    union
    {
        palExecAffinitySmCount smCount;    /** Value for ::PAL_EXEC_AFFINITY_TYPE_SM_COUNT */
    } param;
} palExecAffinityParam;

/**
 * Legacy device properties
 */
typedef struct palLegacyDeviceProperties_st
{
    palInt32 maxThreadsPerBlock;     /**< Maximum number of threads per block */
    palInt32 maxThreadsDim[3];       /**< Maximum size of each dimension of a block */
    palInt32 maxGridSize[3];         /**< Maximum size of each dimension of a grid */
    palInt32 sharedMemPerBlock;      /**< Shared memory available per block in bytes */
    palInt32 totalConstantMemory;    /**< Constant memory available on device in bytes */
    palInt32 SIMDWidth;              /**< Warp size in threads */
    palInt32 memPitch;               /**< Maximum pitch in bytes allowed by memory copies */
    palInt32 regsPerBlock;           /**< 32-bit registers available per block */
    palInt32 clockRate;              /**< Clock frequency in kilohertz */
    palInt32 textureAlign;           /**< Alignment requirement for textures */
} palLegacyDeviceProperties;

/**
* Types of async notification that can be sent
*/
typedef enum palAsyncNotificationType_enum
{
    PAL_ASYNC_NOTIFICATION_TYPE_OVER_BUDGET = 0x1
} palAsyncNotificationType;

/**
* Information passed to the user via the async notification callback
*/
typedef struct palAsyncNotificationInfo_st
{
    palAsyncNotificationType type;
    union
    {
        struct
        {
            unsigned long long bytesOverBudget;
        } overBudget;
    } info;
} palAsyncNotificationInfo;

/**
 * PAL async notification callback
 * \param info Information describing what actions to take as a result of this trim notification.
 * \param userData Pointer to user defined data provided at registration.
 * \param callback The callback handle associated with this specific callback.
 */
typedef void (*palAsyncCallback)(palAsyncNotificationInfo* pInfo, void* pUserData, palAsyncCallbackEntry* pCallback);

/**
 * Handle for async notification callback
 */
struct palAsyncCallbackEntry_st
{
    // The callback function to call
    palAsyncCallback callback;

    // User data to pass to the callback function
    void* pUserData;

    // The notification info to pass to the callback function
    palAsyncNotificationInfo* pInfo;

    // Pointer to the previous callback entry in the list
    palAsyncCallbackEntry* pPrev;

    // Pointer to the next callback entry in the list
    palAsyncCallbackEntry* pNext;
};

struct palDevice_st
{
    // Pointer to the device manager
    palDeviceManager* pDeviceManager;

    // The GPU identifier
    palInt32 gpuId;

    // The device identifier
    palInt32 deviceId;

    // The device properties
    palDeviceProperties* pDeviceProperties;

    // Pointer to the kernel mode device object
    void* pKmdDevice;

    // The primary context of the device
    palContext* pPrimaryContext;

    // The mutex to protect the primary context
    palOsMutex primaryContextMutex;

    // The primary context creation info
    palContextCreateInfo* pPrimaryContextCreateInfo;

    // Indicates if the device is in MIG mode
    palBool isMigMode;

    // The MIG UUID of the device if in MIG mode
    palInt8 migUuid[16];

    // The memory manager for the device
    palMemoryManager* pMemoryManager;
};

/// @brief Create a device object
///
/// @param ppDevice The device object to create
/// @param pDeviceManager The device manager to use
/// @param gpuId The ID of the gpu to open
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceCreate(palDevice** ppDevice, palDeviceManager* pDeviceManager, palInt32 gpuId);

/// @brief Initialize a device object
///
/// @param pDevice The device object to initialize
/// @param pDeviceManager The device manager to use
/// @param gpuId The ID of the gpu to open
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceInitialize(palDevice* pDevice, palDeviceManager* pDeviceManager, palInt32 gpuId);

/// @brief Deinitialize a device object
///
/// @param pDevice The device object to deinitialize
void palDeviceDeinitialize(palDevice* pDevice);

/// @brief Destroy a device object
///
/// @param pDevice The device object to destroy
void palDeviceDestroy(palDevice* pDevice);

/// @brief Get the primary context of the device
///
/// @param pDevice The device object to get the properties from
/// @return Returns a pointer to the primary context of the device
palContext* palDeviceGetPrimaryContext(palDevice* pDevice);

/// @brief Initialize the primary context of the device
///
/// @param pDevice The device object to initialize the primary context for
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePrimaryContextInitialize(palDevice* pDevice);

/// @brief Retain the primary context of the device
///
/// @param pDevice The device object to retain the primary context for
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePrimaryContextRetain(palDevice* pDevice);

/// @brief Reset the primary context of the device
///
/// @param pDevice The device object to reset the primary context for
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePrimaryContextReset(palDevice* pDevice);

/// @brief Release the primary context of the device
///
/// @param pDevice The device object to release the primary context for
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePrimaryContextRelease(palDevice* pDevice);

/// @brief Get the flags of the primary context of the device
///
/// @param pDevice The device object to get the primary context flags from
/// @param pFlags Pointer to store the flags of the primary context
/// @param pState Pointer to store the state of the primary context
palResult palDevicePrimaryContextGetState(palDevice* pDevice, palUint32* pFlags, palInt32* pState);

/// @brief Check if the primary context of the device has valid flags set
///
/// @param flags The flags to check for the primary context
/// @return Returns PAL_SUCCESS if the flags are valid, or an error code if they are not
palResult palDevicePrimaryContextCheckFlags(palUint32 flags);

/// @brief Set the flags of the primary context of the device
///
/// @param pDevice The device object to set the primary context flags for
/// @param flags The flags to set for the primary context
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePrimaryContextSetFlags(palDevice* pDevice, palUint32 flags);

/// @brief Get the attribute of a device
///
/// @param pDevice The device object to get the attributes from
/// @param attribute The attribute to get
/// @param pValue Pointer to store the value of the attribute
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetAttribute(palDevice* pDevice, palDeviceAttribute attribute, palInt32* pValue);

/// @brief Get the device ID of a device
///
/// @param pDevice The device object to get the device ID from
/// @return Returns the device ID of the device.
palInt32 palDeviceGetId(palDevice* pDevice);

/// @brief Check if the device supports a specific execution affinity type
///
/// @param pDevice The device object to check
/// @param type The execution affinity type to check
/// @return Returns PAL_TRUE if the device supports the execution affinity type, otherwise returns PAL_FALSE
palBool palDeviceIsSupportExecAffinity(palDevice* pDevice, palExecAffinityType type);

/// @brief Get the LUID (Locally Unique Identifier) of a device
///
/// @param pDevice The device object to get the LUID for
/// @param pDeviceNodeMask Pointer to store the device node mask
/// @param pLuid Pointer to store the LUID of the device
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetLuid(palDevice* pDevice, palUint32* pDeviceNodeMask, palInt8* pLuid);

/// @brief Get the name of a device
///
/// @param pDevice The device object to get the name from
/// @param pName Pointer to store the name of the device
/// @param nameLength The length of the name buffer
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetName(palDevice* pDevice, palInt8* pName, palUint32 nameLength);

/// @brief Get the NVSCI sync attributes of a device
///
/// @param pDevice The device object to get the NVSCI sync attributes from
/// @param flags The flags to specify the type of attributes to retrieve
/// @param pSciSyncAttrList Pointer to store the NVSCI sync attributes list
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetNvSciSyncAttributes(palDevice* pDevice, palInt32 flags, void* pSciSyncAttrList);

/// @brief Get the maximum width for a 1D linear texture on a device
/// @param pDevice The device object to get the maximum width from
/// @param arrayElementSize The element size of the texture in bytes
/// @param pMaxWidth Pointer to store the maximum width of the 1D linear texture
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetTexture1DLinearMaxWidth(palDevice* pDevice, palUint64 arrayElementSize, size_t* pMaxWidth);

/// @brief Get the UUID of a device
///
/// @param pDevice The device object to get the UUID from
/// @param pUuid Pointer to store the UUID of the device
/// @param returnMigUuid If true, returns the MIG UUID if the device is in MIG mode; otherwise, returns the regular UUID
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetUuid(palDevice* pDevice, palInt8* pUuid, palBool returnMigUuid);

/// @brief Get the total memory of a device
///
/// @param pDevice The device object to get the total memory from
/// @param bytes Pointer to store the total memory in bytes
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetTotalMemory(palDevice* pDevice, size_t* bytes);

/// @brief Get the compute capability of a device
///
/// @param pDevice The device object to get the compute capability from
/// @param major Pointer to store the major version of the compute capability
/// @param minor Pointer to store the minor version of the compute capability
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetComputeCapability(palDevice* pDevice, palInt32* major, palInt32* minor);

/// @brief Get the legacy properties of a device in a legacy format
/// @param pDevice The device object to get the properties from
/// @param pLegacyProperties Pointer to store the legacy device properties
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetLegacyProperties(palDevice* pDevice, palLegacyDeviceProperties* pLegacyProperties);

/// @brief Get the PCI bus ID of a device
/// @param pDevice The device object to get the PCI bus ID from
/// @param length The length of the pPciBusId buffer in bytes
/// @param pPciBusId Pointer to store the PCI bus ID of the device
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetPCIBusId(palDevice* pDevice, palInt32 length, palInt8* pPciBusId);

/// @brief Register an asynchronous notification callback for a device
///
/// @param pDevice The device object to register the callback for
/// @param callbackFunc The callback function to call when the notification occurs
/// @param pUserData Pointer to user-defined data to pass to the callback function
/// @param pEntry Pointer to store the callback entry for later unregistration
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceRegisterAsyncNotification(palDevice* pDevice, palAsyncCallback callbackFunc,
                                             void* pUserData, palAsyncCallbackEntry* pEntry);

/// @brief Unregister an asynchronous notification callback for a device
///
/// @param pDevice The device object to unregister the callback from
/// @param pEntry Pointer to the callback entry to unregister
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceUnregisterAsyncNotification(palDevice* pDevice, palAsyncCallbackEntry* pEntry);

/// @brief Create a device context for the device
///
/// @param pDevice The device handle to create the context for
/// @param flags Flags to specify the context creation behavior
/// @param ppKmdContext Pointer to the kernel mode context to use for the device
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceCreateDeviceContext(palDevice* pDevice, palUint32 flags, void** ppKmdContext);

/// @brief Destroy a device context for the device
///
/// @param pDevice The device handle to destroy the context for
/// @param pKmdContext The kernel mode context to destroy for the device
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceDestroyDeviceContext(palDevice* pDevice, void* pKmdContext);

/// @brief Get memory information for the device
///
/// @param pDevice The device handle to get memory information from
/// @param pTotal Pointer to store the total memory in bytes
/// @param pFree Pointer to store the free memory in bytes
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceGetMemInfo(palDevice* pDevice, size_t* pTotal, size_t* pFree);

/// @brief Allocate physical memory on the device
///
/// @param pDevice The device handle to allocate memory on
/// @param pContext The context to use for the allocation
/// @param size The size of the memory to allocate in bytes
/// @param flags Flags to specify the allocation behavior
/// @param ppPhysicalMemoryObject Pointer to store the allocated physical memory object handle
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryCreate(palDevice* pDevice, palContext* pContext, size_t size, palUint64 flags,
                                        void** ppPhysicalMemoryObject);

/// @brief Release physical memory on the device
/// @param pDevice The device handle to release memory from
/// @param pContext The context to use for the release
/// @param pPhysicalMemoryObject The physical memory handle to release
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryDestroy(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryObject);

/// @brief Map physical memory to a virtual address space
///
/// @param pDevice The device handle to map the physical memory on
/// @param pContext The context to use for the mapping
/// @param virtualAddress The virtual address to map the physical memory to
/// @param pPhysicalMemoryObject The device (or host pinned/registered) physical memory object to map
/// @param size The size of the memory to map in bytes
/// @param flags Flags to specify the mapping behavior
/// @param ppPhysicalMemoryDeviceMapped Pointer to store the physical memory mapped address
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryDeviceMap(palDevice* pDevice, palContext* pContext, palUint64 virtualAddress,
                                           void* pPhysicalMemoryObject, size_t size, palUint64 flags,
                                           void** ppPhysicalMemoryDeviceMapped);

/// @brief Unmap physical memory from a virtual address space
/// @param pDevice The device handle to unmap the physical memory from
/// @param pContext The context to use for the unmapping
/// @param pPhysicalMemoryDeviceMapped The physical memory mapped address to unmap
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryDeviceUnmap(palDevice* pDevice, palContext* pContext,
                                             void* pPhysicalMemoryDeviceMapped);

/// @brief Map physical memory to the host address space
///
/// @param pDevice The device handle to map the physical memory on
/// @param pContext The context to use for the mapping
/// @param pPhysicalMemoryObject The physical memory mapped address on the device
/// @param size The size of the memory to map in bytes
/// @param flags Flags to specify the mapping behavior
/// @param ppHostMapped Pointer to store the host mapped address
/// @param ppHostVirtualAddress Pointer to store the host virtual address of the mapping
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryHostMap(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryObject,
                                         palUint64 size, palUint64 flags, void** ppHostMapped,
                                         void** ppHostVirtualAddress);

/// @brief Unmap physical memory from the host address space
///
/// @param pDevice The device handle to unmap the physical memory from
/// @param pContext The context to use for the unmapping
/// @param pPhysicalMemoryHostMapped The physical memory object host mapped handle
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryHostUnmap(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryHostMapped);

/// @brief Export physical memory to a dma_buf file descriptor
/// @param pDevice The device handle to export physical memory from
/// @param pContext The context to use for the export
/// @param pPhysicalMemoryObject The physical memory object to export
/// @param size The size of the physical memory to export in bytes
/// @param pageSize The page size of the physical memory to export in bytes
/// @param flags Flags to specify the export behavior
/// @param pDmaBufFd Pointer to store the file descriptor of the exported dma_buf
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryExportByDmaBuf(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryObject,
                                                palUint64 size, palUint64 pageSize, palUint64 flags, palInt32* pDmaBufFd);

/// @brief Import physical memory from a dma_buf file descriptor
///
/// @param pDevice The device handle to import physical memory to
/// @param pContext The context to use for the import
/// @param dmaBufFd The file descriptor of the dma_buf to import
/// @param ppRemoteContext Pointer to store the remote context handle associated with the imported physical memory
/// @param ppPhysicalMemoryObject Pointer to store the imported physical memory object handle
/// @param pSize Pointer to store the size of the imported physical memory in bytes
/// @param pPageSize Pointer to store the page size of the imported physical memory in bytes
/// @param pFlags Pointer to store the flags of the imported physical memory
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDevicePhysicalMemoryImportFromDmaBuf(palDevice* pDevice, palContext* pContext, palInt32 dmaBufFd,
                                                  void** ppRemoteContext, void** ppPhysicalMemoryObject,
                                                  palUint64* pSize, palUint64* pPageSize, palUint64* pFlags);

/// @brief Allocate pinned memory on the host for the corresponding device.
/// @param pDevice The device handle for pinned memory allocation.
/// @param pContext The context to use for the allocation
/// @param size The size of the memory to allocate in bytes
/// @param flags Flags to specify the allocation behavior
/// @param ppPinnedMemoryObject Pointer to store the allocated pinned memory object handle
/// @param ppHostVirtualAddress Pointer to store the host virtual address of the allocated pinned memory.
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceHostPinnedMemoryCreate(palDevice* pDevice, palContext* pContext, size_t size, palUint64 flags,
                                          void** ppPinnedMemoryObject, void** ppHostVirtualAddress);

/// @brief Destroy pinned memory on the host for the corresponding device.
///
/// @param pDevice The device handle to release memory from
/// @param pContext The context to use for the release
/// @param pPinnedMemoryObject The pinned memory handle to release
/// @param Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceHostPinnedMemoryDestroy(palDevice* pDevice, palContext* pContext, void* pPinnedMemoryObject);

/// @brief Register pageable memory for the device
/// @param pDevice The device handle to register host pageable memory.
/// @param pContext The context to use for the register.
/// @param pHostPtr The host pointer to the pageable memory
/// @param size The size of the pageable memory to register in bytes.
/// @param flags Flags to specify the registering behavior.
/// @param ppHostRegisteredMemoryObject Pointer to store the registered pageable memory object handle
/// @param pRegisteredOffset Pointer to store the offset of the start address from the registered pageable memory object.
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceHostPageableMemoryRegister(palDevice* pDevice, palContext* pContext, void* pHostPtr, palUint64 size,
                                              palUint64 flags, void** ppHostRegisteredMemoryObject,
                                              palUint64* pRegisteredOffset);

/// @brief Unregister pageable memory for the device
/// @param pDevice The device handle to unregister host pageable memory.
/// @param pContext The context to use for the unregister.
/// @param pHostPtr The host pointer to the pageable memory
/// @param pHostRegisteredMemoryObject The registered pageable memory object handle to unregister
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palDeviceHostPageableMemoryUnregister(palDevice* pDevice, palContext* pContext, void* pHostPtr,
                                                void* pHostRegisteredMemoryObject);
#endif // PAL_DEVICE_H_