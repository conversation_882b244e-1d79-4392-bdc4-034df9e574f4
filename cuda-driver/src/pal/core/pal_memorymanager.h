#ifndef PAL_MEMORYMANAGER_H_
#define PAL_MEMORYMANAGER_H_

#include "pal.h"
#include "pal_memoryheap.h"
#include "pal_os.h"
#include "pal_structures.h"
#include "pal_types.h"

struct palMemoryManager_st
{
    // The corresponding context handle.
    union
    {
        // The corresponding context handle.
        // This is used when the memory manager is created for the context.
        palContext* pContext;

        // The corresponding device handle.
        // This is used when the memory manager is created for the device.
        palDevice* pDevice;
    };

    // The flags to indicate if the memory manager is device memory manager
    palBool isDevice;

    // The mutex for protecting the memory manager.
    palOsMutex memoryManagerMutex;

    union
    {
        // The mutex for protecting the memory heap tree.
        palOsMutex memoryHeapMutex;
        // The mutex for protecting the memory pool tree.
        palOsMutex memoryPoolMutex;
    };

    union
    {
        // The memory heap pool map for the device.
        // The corresponding <key, value> pair is <heapType, palMemoryHeap*>
        palRbTree* pMemoryHeapMap;

        // The memory pool map for the device.
        // The corresponding <key, value> pair is <palMemoryPool*, NULL>
        palRbTree* pMemoryPoolMap;
    };

    // Default memory pool for the device.
    palMemoryPool* pDefaultMemoryPool;

    // Current memory pool for the device.
    palMemoryPool* pCurrentMemoryPool;

    // The mutex for protecting the memory object
    palOsMutex memoryObjectMutex;

    // The memory object map for the context.
    // The corresponding <key, value> pair is <virtAddress, palMemoryObject*>
    palRbTree* pMemoryObjectDeviceMap;

    // The memory object map for the host.
    // The corresponding <key, value> pair is <pHost, palMemoryObject*>
    palRbTree* pMemoryObjectHostMap;
};

/// @brief Create a memory manager for the context
///
/// @param ppMemoryManager Pointer to the memory manager to create
/// @param pParent The parent to create the memory manager for
/// @param isDevice Flag to indicate if the memory manager is primary, if true, it will be used for device
///                 memory management, otherwise it will be used for context memory management.
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerCreate(palMemoryManager** ppMemoryManager, void* pParent, palBool isDevice);

/// @brief Initialize the memory manager for the context
///
/// @param pMemoryManager The memory manager to initialize
/// @param pParent The context to initialize the memory manager for
/// @param isDevice Flag to indicate if the memory manager is primary, if true, it will be used for device
///                 memory management, otherwise it will be used for context memory management.
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerInitialize(palMemoryManager* pMemoryManager, void* pParent, palBool isDevice);

/// @brief Deinitialize the memory manager for the device
///
/// @param pMemoryManager The memory manager to deinitialize
void palMemoryManagerDeinitialize(palMemoryManager* pMemoryManager);

/// @brief Destroy the memory manager for the device
///
/// @param pMemoryManager The memory manager to destroy
void palMemoryManagerDestroy(palMemoryManager* pMemoryManager);

/// @brief Add a memory heap to the memory manager
///
/// @param pMemoryManager The memory manager to add the memory heap to
/// @param pMemoryHeap The memory heap to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerAddMemoryHeap(palMemoryManager* pMemoryManager, palMemoryHeap* pMemoryHeap);

/// @brief Remove a memory heap from the memory manager
///
/// @param pMemoryManager The memory manager to remove the memory heap from
/// @param pMemoryHeap The memory heap to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerRemoveMemoryHeap(palMemoryManager* pMemoryManager, palMemoryHeap* pMemoryHeap);

/// @brief Get a memory heap from the memory manager
///
/// @param pMemoryManager The memory manager to get the memory heap from
/// @param heapType The type of the memory heap to get
/// @param ppMemoryHeap Pointer to store the memory heap
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerGetMemoryHeap(palMemoryManager* pMemoryManager, palMemoryHeapType heapType,
                                        palMemoryHeap** ppMemoryHeap);

/// @brief Add a memory pool to the memory manager
///
/// @param pMemoryManager The memory manager to add the memory pool to
/// @param pMemoryPool The memory pool to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerAddMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool* pMemoryPool);

/// @brief Remove a memory pool from the memory manager
///
/// @param pMemoryManager The memory manager to remove the memory pool from
/// @param pMemoryPool The memory pool to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerRemoveMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool* pMemoryPool);

/// @brief Get the default memory pool of the memory manager
///
/// @param pMemoryManager The memory manager to get the default memory pool from
/// @param ppMemoryPool Pointer to the memory pool
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerGetDefaultMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool** ppMemoryPool);

/// @brief Get the current memory pool of the memory manager
///
/// @param pMemoryManager The memory manager to get the current memory pool from
/// @param ppMemoryPool Pointer to the memory pool
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerGetCurrentMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool** ppMemoryPool);

/// @brief Set the current memory pool of the memory manager
///
/// @param pMemoryManager The memory manager to set the current memory pool for
/// @param pMemoryPool The memory pool to set as the current memory pool
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerSetCurrentMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool* pMemoryPool);

/// @brief Add a memory object to the memory manager
///
/// @param pCtx The memory manager to add the memory object to
/// @param pMemoryObject The memory object to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerAddMemoryObject(palMemoryManager* pMemoryManager, palMemoryObject* pMemoryObject);

/// @brief Remove a memory object from the memory manager
///
/// @param pCtx The memory manager to remove the memory object from
/// @param pMemoryObject The memory object to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerRemoveMemoryObject(palMemoryManager* pMemoryManager, palMemoryObject* pMemoryObject);

/// @brief Get a memory object from the memory manager
///
/// @param pMemoryManager The memory manager to get the memory object from
/// @param devicePtr The device pointer of the memory object to get
/// @param ppMemoryObject Pointer to store the memory object
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerGetMemoryObject(palMemoryManager* pMemoryManager, void* ptr,
                                          palMemoryObject** ppMemoryObject);

/// @brief Allocate memory from the memory manager
///
/// @param pMemoryManager The memory manager to allocate memory from
/// @param pContext The context to allocate memory for
/// @param heapType The type of the memory heap to allocate from
/// @param bytesize The size of the memory to allocate
/// @param memFlags The memory flags for the allocation
/// @param hostFlags The host flags for the allocation
/// @param pHostPtr The host pointer for the allocation (optional)
/// @param ppMemoryObject Pointer to store the memory object of the allocated memory
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryManagerCreateMemoryObject(palMemoryManager* pMemoryManager, palContext* pContext,
                                             palMemoryHeapType heapType, palUint64 bytesize, palMemoryFlags memFlags,
                                             palUint32 hostFlags, void* pHostPtr, palMemoryObject** ppMemoryObject);

#endif // PAL_MEMORYMANAGER_H_