#include "pal_assert.h"
#include "pal_device.h"
#include "pal_memoryblock.h"
#include "pal_memorychunk.h"
#include "pal_memoryheap.h"
#include "pal_memorymanager.h"
#include "pal_memorypool.h"
#include "pal_rbtree.h"

// =====================================================================================================================
palResult palMemoryPoolCreate(palMemoryPool** ppMemoryPool, palMemoryManager* pMemoryManager,
                              palMemoryPoolType poolType, const palMemoryPoolProps* pProps)
{
    palResult      result      = PAL_SUCCESS;
    palMemoryPool* pMemoryPool = NULL;

    if ((ppMemoryPool == NULL) || (pMemoryManager == NULL) ||
        (poolType >= PAL_MEMORY_POOL_TYPE_MAX) || (pProps == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory pool creation.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pMemoryPool = (palMemoryPool*)malloc(sizeof(palMemoryPool));
    if (pMemoryPool == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for memory pool.");
        *ppMemoryPool = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palMemoryPoolInitialize(pMemoryPool, pMemoryManager, poolType, pProps);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory pool.");
        *ppMemoryPool = NULL;
        free(pMemoryPool);
        return result;
    }

    *ppMemoryPool = pMemoryPool;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolInitialize(palMemoryPool* pMemoryPool, palMemoryManager* pMemoryManager,
                                  palMemoryPoolType poolType, const palMemoryPoolProps* pProps)
{
    palResult result = PAL_SUCCESS;
    palInt32  error  = 0;

    if ((pMemoryPool == NULL) || (pMemoryManager == NULL) ||
        (poolType >= PAL_MEMORY_POOL_TYPE_MAX) || (pProps == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory pool initialization.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pMemoryPool, 0, sizeof(palMemoryPool));

    // Initialize the memory pool structure
    pMemoryPool->pMemoryManager = pMemoryManager;
    pMemoryPool->poolType       = poolType;

    // Initialize the mutex for protecting the memory block map
    error = palOsMutexInit(&pMemoryPool->memoryBlockMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory block mutex for memory pool.");
        memset(pMemoryPool, 0, sizeof(palMemoryPool));
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    // Create the used memory block map tree
    result = palRbTreeCreate(&pMemoryPool->pUsedMemoryBlockMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create used memory block map for memory pool.");
        palOsMutexDestroy(&pMemoryPool->memoryBlockMutex);
        memset(pMemoryPool, 0, sizeof(palMemoryPool));
        return result;
    }

    // Create the free memory block map tree
    result = palRbTreeCreate(&pMemoryPool->pFreeMemoryBlockMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create free memory block map for memory pool.");
        palRbTreeDestroy(pMemoryPool->pUsedMemoryBlockMap);
        palOsMutexDestroy(&pMemoryPool->memoryBlockMutex);
        memset(pMemoryPool, 0, sizeof(palMemoryPool));
        return result;
    }

    // Add the memory pool handle into the memory manager map container
    result = palMemoryManagerAddMemoryPool(pMemoryPool->pMemoryManager, pMemoryPool);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory pool to the memory manager.");
        palRbTreeDestroy(pMemoryPool->pUsedMemoryBlockMap);
        palRbTreeDestroy(pMemoryPool->pFreeMemoryBlockMap);
        palOsMutexDestroy(&pMemoryPool->memoryBlockMutex);
        memset(pMemoryPool, 0, sizeof(palMemoryPool));
        return result;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palMemoryPoolDeinitialize(palMemoryPool* pMemoryPool)
{
    palResult result = PAL_SUCCESS;

    if (pMemoryPool == NULL)
    {
        return;
    }

    // Remove the memory pool handle from the memory manager map container
    result = palMemoryManagerRemoveMemoryPool(pMemoryPool->pMemoryManager, pMemoryPool);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to remove memory pool from the memory manager.");
        return;
    }

    // Destroy the used memory block map tree
    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    if (pMemoryPool->pUsedMemoryBlockMap != NULL)
    {
        // Destroy the used memory pool map tree.
        while (palRbTreeGetSize(pMemoryPool->pUsedMemoryBlockMap) > 0)
        {
            // Remove the first element from the tree.
            palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryPool->pUsedMemoryBlockMap);
            PAL_ASSERT(pNode != NULL);
            palMemoryBlockRelease((palMemoryBlock*)pNode->value);
        }

        palRbTreeDestroy(pMemoryPool->pUsedMemoryBlockMap);
        pMemoryPool->pUsedMemoryBlockMap = NULL;
    }

    // Destroy the free memory block map tree
    if (pMemoryPool->pFreeMemoryBlockMap != NULL)
    {
        // Destroy the free memory pool map tree.
        while (palRbTreeGetSize(pMemoryPool->pFreeMemoryBlockMap) > 0)
        {
            // Remove the first element from the tree.
            palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryPool->pFreeMemoryBlockMap);
            PAL_ASSERT(pNode != NULL);
            palMemoryBlockRelease((palMemoryBlock*)pNode->value);
        }

        palRbTreeDestroy(pMemoryPool->pFreeMemoryBlockMap);
        pMemoryPool->pFreeMemoryBlockMap = NULL;
    }
    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    // Destroy the mutex
    palOsMutexDestroy(&pMemoryPool->memoryBlockMutex);
}

// =====================================================================================================================
void palMemoryPoolDestroy(palMemoryPool* pMemoryPool)
{
    if (pMemoryPool == NULL)
    {
        return;
    }

    // Deinitialize the memory pool structure
    palMemoryPoolDeinitialize(pMemoryPool);
    free(pMemoryPool);
}

// =====================================================================================================================
palResult palMemoryPoolAddFreeMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    // Insert the memory block into the free memory block map tree
    result = palRbTreeInsert(pMemoryPool->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryBlock);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory block to the free memory block map tree.");
        palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);
        return result;
    }
    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolRemoveFreeMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    pNode = palRbTreeSearch(pMemoryPool->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryPool->pFreeMemoryBlockMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory block not found in the free memory block map tree.");
        palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory block from the tree.
    palRbTreeDelete(pMemoryPool->pFreeMemoryBlockMap, pNode);
    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolGetFreeMemoryBlock(palMemoryPool* pMemoryPool, palUint64 size, palUint64 alignment,
                                          palMemoryBlock** ppMemoryBlock)
{
    palResult       result       = PAL_SUCCESS;
    palRbTreeNode*  pNode        = NULL;
    palMemoryBlock* pMemoryBlock = NULL;

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(ppMemoryBlock != NULL);

    if ((size == 0) || (alignment == 0) || ((alignment & (alignment - 1)) != 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid size or alignment for memory block allocation. Alignment must be a power of 2.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppMemoryBlock = NULL;

    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    pNode = palRbTreeGetFirst(pMemoryPool->pFreeMemoryBlockMap);
    while (pNode != palRbTreeGetEnd(pMemoryPool->pFreeMemoryBlockMap))
    {
        pMemoryBlock = (palMemoryBlock*)pNode->value;

        // Check if the memory block is large enough and aligned correctly
        if ((pMemoryBlock->size >= size) &&
            (pMemoryBlock->deviceVirtAddress % alignment == 0) &&
            (pMemoryBlock->alignment == alignment))
        {
            *ppMemoryBlock = pMemoryBlock;
            break;
        }

        // Move to the next node
        pNode = palRbTreeGetNext(pMemoryPool->pFreeMemoryBlockMap, pNode);
    }
    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    if (*ppMemoryBlock == NULL)
    {
        PAL_DBG_PRINTF_ERROR("No suitable free memory block found for size %llu and alignment %llu.",
                             size, alignment);
        return PAL_ERROR_NOT_FOUND;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolAddUsedMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    // Search for the memory block in the free memory block map tree.
    pNode = palRbTreeSearch(pMemoryPool->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryPool->pFreeMemoryBlockMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory block not found in the free memory block map tree.");
        palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory block from the free memory block map tree.
    palRbTreeDelete(pMemoryPool->pFreeMemoryBlockMap, pNode);

    // Insert the memory block into the used memory block map tree.
    result = palRbTreeInsert(pMemoryPool->pUsedMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryBlock);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory block to the used memory block map tree.");
        // If insertion fails, we need to reinsert the memory block back to the free memory block map.
        palRbTreeInsert(pMemoryPool->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                        (palRbTreeValueType)pMemoryBlock);
        // Unlock the mutex before returning.
        palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);
        return result;
    }

    // Mark the memory block as used.
    pMemoryBlock->isUsed = PAL_TRUE;

    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolRemoveUsedMemoryBlock(palMemoryPool* pMemoryPool, palMemoryBlock* pMemoryBlock)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    // Search for the memory block in the used memory block map tree.
    pNode = palRbTreeSearch(pMemoryPool->pUsedMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryPool->pUsedMemoryBlockMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory block not found in the used memory block map tree.");
        palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory block from the used memory block map tree.
    palRbTreeDelete(pMemoryPool->pUsedMemoryBlockMap, pNode);

    // Insert the memory block into the free memory block map tree.
    result = palRbTreeInsert(pMemoryPool->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryBlock);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory block to the free memory block map tree.");
        // If insertion fails, we need to reinsert the memory block back to the used memory block map.
        palRbTreeInsert(pMemoryPool->pUsedMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                        (palRbTreeValueType)pMemoryBlock);
        // Unlock the mutex before returning.
        palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);
        return result;
    }

    // Mark the memory block as not used.
    pMemoryBlock->isUsed = PAL_FALSE;

    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolGetFreeMemoryChunkFromUsedMemoryBlock(palMemoryPool* pMemoryPool,
                                                             palUint64 size,
                                                             palUint64 alignment,
                                                             palMemoryFlags memFlags,
                                                             palMemoryChunk** ppMemoryChunk)
{
    palResult       result              = PAL_SUCCESS;
    palMemoryBlock* pMemoryBlock        = NULL;
    palRbTreeNode*  pNode               = NULL;
    palMemoryChunk* pCurrentMemoryChunk = NULL;
    palMemoryChunk* pBestFitMemoryChunk = NULL;

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(ppMemoryChunk != NULL);

    *ppMemoryChunk = NULL;

    if ((size == 0) || (alignment == 0) || ((alignment & (alignment - 1)) != 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid size or alignment for memory chunk allocation. Alignment must be a power of 2.");
        return PAL_ERROR_INVALID_VALUE;
    }

    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    pNode = palRbTreeGetFirst(pMemoryPool->pUsedMemoryBlockMap);
    while (pNode != palRbTreeGetEnd(pMemoryPool->pUsedMemoryBlockMap))
    {
        pMemoryBlock = (palMemoryBlock*)pNode->value;

        if (palMemoryFlagsIsEqual(&pMemoryBlock->memFlags, &memFlags) == PAL_TRUE)
        {
            // Search for a best fit free memory chunk in the memory block
            result = palMemoryBlockGetFreeMemoryChunk(pMemoryBlock, size, alignment, &pCurrentMemoryChunk);
            if (result == PAL_SUCCESS)
            {
                if ((pBestFitMemoryChunk == NULL) || (pCurrentMemoryChunk->size < pBestFitMemoryChunk->size))
                {
                    pBestFitMemoryChunk = pCurrentMemoryChunk;
                }
            }
        }

        // Move to the next memory block
        pNode = palRbTreeGetNext(pMemoryPool->pUsedMemoryBlockMap, pNode);
    }

    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    if (pBestFitMemoryChunk == NULL)
    {
        PAL_DBG_PRINTF_ERROR("No suitable free memory chunk found for size %llu and alignment %llu.",
                             size, alignment);
        return PAL_ERROR_NOT_FOUND;
    }

    *ppMemoryChunk = pBestFitMemoryChunk;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolGetFreeMemoryChunkFromFreeMemoryBlock(palMemoryPool* pMemoryPool,
                                                             palUint64 size,
                                                             palUint64 alignment,
                                                             palMemoryFlags memFlags,
                                                             palMemoryChunk** ppMemoryChunk)
{
    palResult       result              = PAL_SUCCESS;
    palMemoryBlock* pMemoryBlock        = NULL;
    palRbTreeNode*  pNode               = NULL;
    palMemoryChunk* pCurrentMemoryChunk = NULL;
    palMemoryChunk* pBestFitMemoryChunk = NULL;

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(ppMemoryChunk != NULL);

    *ppMemoryChunk = NULL;

    if ((size == 0) || (alignment == 0) || ((alignment & (alignment - 1)) != 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid size or alignment for memory chunk allocation. Alignment must be a power of 2.");
        return PAL_ERROR_INVALID_VALUE;
    }

    palOsMutexLock(&pMemoryPool->memoryBlockMutex);
    pNode = palRbTreeGetFirst(pMemoryPool->pFreeMemoryBlockMap);
    while (pNode != palRbTreeGetEnd(pMemoryPool->pFreeMemoryBlockMap))
    {
        pMemoryBlock = (palMemoryBlock*)pNode->value;

        if (palMemoryFlagsIsEqual(&pMemoryBlock->memFlags, &memFlags) == PAL_TRUE)
        {
            // Search for a best fit free memory chunk in the memory block
            result = palMemoryBlockGetFreeMemoryChunk(pMemoryBlock, size, alignment, &pCurrentMemoryChunk);
            if (result == PAL_SUCCESS)
            {
                if ((pBestFitMemoryChunk == NULL) || (pCurrentMemoryChunk->size < pBestFitMemoryChunk->size))
                {
                    pBestFitMemoryChunk = pCurrentMemoryChunk;
                }
            }
        }

        // Move to the next memory block
        pNode = palRbTreeGetNext(pMemoryPool->pFreeMemoryBlockMap, pNode);
    }

    palOsMutexUnlock(&pMemoryPool->memoryBlockMutex);

    if (pBestFitMemoryChunk == NULL)
    {
        PAL_DBG_PRINTF_ERROR("No suitable free memory chunk found for size %llu and alignment %llu.",
                             size, alignment);
        return PAL_ERROR_NOT_FOUND;
    }

    *ppMemoryChunk = pBestFitMemoryChunk;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryPoolCreateMemoryObject(palMemoryPool* pMemoryPool, palContext* pContext, palUint64 size,
                                          palMemoryFlags memFlags, palUint64 hostFlags, void* pHostPtr,
                                          palMemoryObject** ppMemoryObject)
{
    palResult                 result                = PAL_SUCCESS;
    palMemoryBlockCreateInfo  blockCreateInfo       = {0};
    palMemoryBlock*           pMemoryBlock          = NULL;
    palMemoryChunk*           pMemoryChunk          = NULL;
    palMemoryChunk*           pSplitMemoryChunk     = NULL;
    palMemoryChunk*           pRemainderMemoryChunk = NULL;
    palMemoryObject*          pMemoryObject         = NULL;
    palMemoryObjectCreateInfo objectCreateInfo      = {0};

    PAL_ASSERT(pMemoryPool != NULL);
    PAL_ASSERT(pContext != NULL);
    PAL_ASSERT(ppMemoryObject != NULL);

    *ppMemoryObject = NULL;

    // Get a free memory chunk of used memory block from the memory heap
    result = palMemoryPoolGetFreeMemoryChunkFromUsedMemoryBlock(pMemoryPool, size, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE,
                                                                memFlags, &pMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        result = palMemoryPoolGetFreeMemoryChunkFromFreeMemoryBlock(pMemoryPool, size, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE,
                                                                    memFlags, &pMemoryChunk);
        if (result != PAL_SUCCESS)
        {
            // If no suitable memory chunk is found, create a new memory block
            blockCreateInfo.pContext     = pContext;
            blockCreateInfo.isMemoryHeap = PAL_FALSE;
            blockCreateInfo.size         = PAL_ALIGN(size, palOsGetPageSize());
            blockCreateInfo.alignment    = palOsGetPageSize();

            memcpy(&blockCreateInfo.memFlags, &memFlags, sizeof(palMemoryFlags));

            result = palMemoryBlockCreate(&pMemoryBlock, pMemoryPool, &blockCreateInfo);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to create memory block for memory object.");
                return result;
            }

            result = palMemoryBlockGetFreeMemoryChunk(pMemoryBlock, size, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE, &pMemoryChunk);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to get free memory chunk from memory block.");
                palMemoryBlockRelease(pMemoryBlock);
                return result;
            }
        }
    }

    // Split the memory chunk to fit the requested size if necessary
    result = palMemoryChunkSplit(pMemoryChunk, size, &pSplitMemoryChunk, &pRemainderMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to split memory chunk for memory object.");
        return result;
    }

    // Create the memory object
    objectCreateInfo.pMemoryChunk = pSplitMemoryChunk;
    objectCreateInfo.size         = size;
    objectCreateInfo.hostFlags    = hostFlags;
    objectCreateInfo.pHostPtr     = pHostPtr;

    // Set the memory flags for the memory object
    memcpy(&objectCreateInfo.memFlags, &memFlags, sizeof(palMemoryFlags));

    result = palMemoryObjectCreate(&pMemoryObject, pContext, &objectCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create memory object from memory chunk.");
        return result;
    }

    *ppMemoryObject = pMemoryObject;

    return PAL_SUCCESS;
}