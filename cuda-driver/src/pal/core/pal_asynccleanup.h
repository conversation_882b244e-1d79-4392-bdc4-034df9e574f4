#ifndef PAL_ASYNCCLEANUP_H_
#define PAL_ASYNCCLEANUP_H_

#include "pal.h"
#include "pal_types.h"
#include "pal_structures.h"

typedef enum palAsyncTaskType_enum
{
    PAL_ASYNC_TASK_TYPE_STREAM = 0,
    PAL_ASYNC_TASK_TYPE_MEMORY_OBJECT,
    PAL_ASYNC_TASK_TYPE_FENCE,
    PAL_ASYNC_TASK_TYPE_MAX,
} palAsyncTaskType;

typedef void (*palAsyncTaskDestroy)(void* pTask);
typedef palResult (*palAsyncTaskRelease)(void* pTask);

struct palAsyncTask_st
{
    // Type of the task
    palAsyncTaskType type;

    // Handle to the task
    void* pTask;

    // The task's dependency fence handle.
    palFence* pWaitFence;

    // The task's completion free function pointer.
    union
    {
        palAsyncTaskDestroy destroyFunc; // Function to destroy the task
        palAsyncTaskRelease releaseFunc; // Function to release the task
    };

    // The previous and next handle.
    palAsyncTask* pPrev;
    palAsyncTask* pNext;
};

struct palAsyncTaskManager_st
{
    // The map container of async task <waitfence, taskList>
    palRbTree* pTaskMap;

    // Number of tasks in the map
    palUint32 taskCount;
};

#endif // PAL_ASYNC_CLEANUP_H_