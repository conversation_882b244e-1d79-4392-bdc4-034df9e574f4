#include "pal_array.h"
#include "pal_assert.h"

// =====================================================================================================================
palResult palArrayCheckFormatAndNumChannels(palArrayFormat format, palUint32 numChannels)
{
    if ((numChannels != 1) && (numChannels != 2) && (numChannels != 3) && (numChannels != 4))
    {
        PAL_DBG_PRINTF_ERROR("Invalid number of channels: %d. It should be 1, 2, 3, or 4.", numChannels);
        return PAL_ERROR_INVALID_VALUE;
    }

    // Check if the format is valid for the specified number of channels.
    switch (format)
    {
        case PAL_AD_FORMAT_UNSIGNED_INT8:
        case PAL_AD_FORMAT_UNSIGNED_INT16:
        case PAL_AD_FORMAT_UNSIGNED_INT32:
        case PAL_AD_FORMAT_SIGNED_INT8:
        case PAL_AD_FORMAT_SIGNED_INT16:
        case PAL_AD_FORMAT_SIGNED_INT32:
        case PAL_AD_FORMAT_HALF:
        case PAL_AD_FORMAT_FLOAT:
            if ((numChannels != 1) && (numChannels != 2) && (numChannels != 4))
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 1, 2 or 4 for format: %d", format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_NV12:
            if (numChannels != 3)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 3 for format: %d", format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_UNORM_INT8X1:
        case PAL_AD_FORMAT_SNORM_INT8X1:
        case PAL_AD_FORMAT_UNORM_INT16X1:
        case PAL_AD_FORMAT_SNORM_INT16X1:
            if (numChannels != 1)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 1 for format: %d", format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_UNORM_INT8X2:
        case PAL_AD_FORMAT_SNORM_INT8X2:
        case PAL_AD_FORMAT_UNORM_INT16X2:
        case PAL_AD_FORMAT_SNORM_INT16X2:
            if (numChannels != 2)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 2 for format: %d", format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_UNORM_INT8X4:
        case PAL_AD_FORMAT_SNORM_INT8X4:
        case PAL_AD_FORMAT_UNORM_INT16X4:
        case PAL_AD_FORMAT_SNORM_INT16X4:
            if (numChannels != 4)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 4 for format: %d", format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_BC1_UNORM:
        case PAL_AD_FORMAT_BC1_UNORM_SRGB:
        case PAL_AD_FORMAT_BC2_UNORM:
        case PAL_AD_FORMAT_BC2_UNORM_SRGB:
        case PAL_AD_FORMAT_BC3_UNORM:
        case PAL_AD_FORMAT_BC3_UNORM_SRGB:
        case PAL_AD_FORMAT_BC7_UNORM:
        case PAL_AD_FORMAT_BC7_UNORM_SRGB:
            if (numChannels != 4)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 4 for format: %d", numChannels, format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_BC4_UNORM:
        case PAL_AD_FORMAT_BC4_SNORM:
            if (numChannels != 1)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 1 for format: %d", numChannels, format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_BC5_UNORM:
        case PAL_AD_FORMAT_BC5_SNORM:
            if (numChannels != 2)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 2 for format: %d", numChannels, format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        case PAL_AD_FORMAT_BC6H_UF16:
        case PAL_AD_FORMAT_BC6H_SF16:
            if (numChannels != 3)
            {
                PAL_DBG_PRINTF_ERROR("The number of channels should be 3 for format: %d", numChannels, format);
                return PAL_ERROR_INVALID_VALUE;
            }
            break;
        default:
            PAL_DBG_PRINTF_ERROR("Unsupported array format: %d", format);
            return PAL_ERROR_INVALID_VALUE;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palUint64 palArrayGetElementSize(palArrayFormat format, palUint32 numChannels)
{
    palUint64 size = 0;

    // Get the size in bytes based on the format and number of channels.
    switch (format)
    {
        case PAL_AD_FORMAT_UNSIGNED_INT8:
        case PAL_AD_FORMAT_SIGNED_INT8:
            size = 1 * numChannels;
            break;
        case PAL_AD_FORMAT_UNSIGNED_INT16:
        case PAL_AD_FORMAT_SIGNED_INT16:
        case PAL_AD_FORMAT_HALF:
            size = 2 * numChannels;
            break;
        case PAL_AD_FORMAT_UNSIGNED_INT32:
        case PAL_AD_FORMAT_SIGNED_INT32:
        case PAL_AD_FORMAT_FLOAT:
            size = 4 * numChannels;
            break;
        case PAL_AD_FORMAT_UNORM_INT8X1:
        case PAL_AD_FORMAT_SNORM_INT8X1:
            return 1;
        case PAL_AD_FORMAT_UNORM_INT8X2:
        case PAL_AD_FORMAT_SNORM_INT8X2:
            return 2;
        case PAL_AD_FORMAT_UNORM_INT8X4:
        case PAL_AD_FORMAT_SNORM_INT8X4:
            return 4;
        case PAL_AD_FORMAT_UNORM_INT16X1:
        case PAL_AD_FORMAT_SNORM_INT16X1:
            return 2;
        case PAL_AD_FORMAT_UNORM_INT16X2:
        case PAL_AD_FORMAT_SNORM_INT16X2:
            return 4;
        case PAL_AD_FORMAT_UNORM_INT16X4:
        case PAL_AD_FORMAT_SNORM_INT16X4:
            return 8;
        case PAL_AD_FORMAT_NV12:
            return 1; // YUV planar format with 4:2:0 sampling
        // BC1 and BC4 formats compress a 4 x 4 block down to 8 bytes
        case PAL_AD_FORMAT_BC1_UNORM:
        case PAL_AD_FORMAT_BC1_UNORM_SRGB:
        case PAL_AD_FORMAT_BC4_UNORM:
        case PAL_AD_FORMAT_BC4_SNORM:
            return 8;
        // BC2, BC3, BC5, BC6H and BC7 formats compress a 4 x 4 block down to 16 bytes
        case PAL_AD_FORMAT_BC2_UNORM:
        case PAL_AD_FORMAT_BC2_UNORM_SRGB:
        case PAL_AD_FORMAT_BC3_UNORM:
        case PAL_AD_FORMAT_BC3_UNORM_SRGB:
        case PAL_AD_FORMAT_BC5_UNORM:
        case PAL_AD_FORMAT_BC5_SNORM:
        case PAL_AD_FORMAT_BC6H_UF16:
        case PAL_AD_FORMAT_BC6H_SF16:
        case PAL_AD_FORMAT_BC7_UNORM:
        case PAL_AD_FORMAT_BC7_UNORM_SRGB:
            return 16;
        default:
            PAL_ASSERT_MSG(0, "Unsupported array format: %d", format);
            return 0; // Invalid format or unsupported format
    }

    return size;
}