#ifndef PAL_DEVICEMANAGER_H_
#define PAL_DEVICEMANAGER_H_

#include "pal.h"
#include "pal_structures.h"
#include "pal_types.h"

struct palDeviceManager_st
{
    // Pointer to the global manager
    palGlobalManager* pGlobalManager;

    // The number of opened devices
    palInt32 openedDeviceCount;

    // The array of opened devices
    palDevice** ppOpenedDevices;

    // The CUDA_VISIBLE_DEVICES environment variable value
    palInt8 cudaVisibleDevices[PAL_OS_ENV_VAR_LENGTH];

    // The count of devices specified in CUDA_VISIBLE_DEVICES.
    // This count is used to determine how many devices are visible to the application.
    palInt32 cudaVisibleDeviceCount;

    // The flag indicates whether the CUDA_VISIBLE_DEVICES environment variable
    // is set with gpu uuid or gpu index.
    palInt8 isUuid;

    // The indices of visible devices
    palInt32* pVisibleDeviceIndices;

    // The UUIDs of visible devices
    palInt8** ppVisibleDeviceUuids;

    // The number of visible devices, which are not hidden or filtered out.
    palInt32 visibleDeviceCount;

    // The array of devices
    palDevice** ppVisibleDevices;

    // The active contexts list.
    palContext* pActiveContextsList;

    // The mutex to protect the active contexts list.
    palOsMutex activeContextsMutex;
};

/// @brief Create a device manager
///
/// @param ppDeviceManager The device manager to create
/// @param pGlobalManager The global manager to use
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palDeviceManagerCreate(palDeviceManager** ppDeviceManager, palGlobalManager* pGlobalManager);

/// @brief Initialize a device manager
///
/// @param pDeviceManager The device manager to initialize
/// @param pGlobalManager The global manager to use
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palDeviceManagerInitialize(palDeviceManager* pDeviceManager, palGlobalManager* pGlobalManager);

/// @brief Deinitialize a device manager
///
/// @param pDeviceManager The device manager to deinitialize
void palDeviceManagerDeinitialize(palDeviceManager* pDeviceManager);

/// @brief Destroy a device manager
///
/// @param pDeviceManager The device manager to destroy
void palDeviceManagerDestroy(palDeviceManager* pDeviceManager);

/// @brief Get the device handle by the specified device ordinal.
///
/// @param pDeviceManager The device manager to use
/// @param ordinal The device ordinal (ID) to look for
/// @return Returns the device handle with the specified device ordinal, or NULL if not found
palResult palDeviceManagerGetDevice(palDeviceManager* pDeviceManager, palInt32 ordinal, palDevice** ppDevice);

/// @brief Get the device handle by the specified PCI bus ID.
/// @param pDeviceManager The device manager to use
/// @param pciBusId The PCI bus ID of the device to look for, in the format "domain:bus:device.function",
///                 "domain:bus:device" or "bus:device.function"
/// @param ppDevice Pointer to store the device handle if found
/// @return Returns PAL_SUCCESS if the device is found, or an error code if not found or on failure
palResult palDeviceManagerGetDeviceByPCIBusId(palDeviceManager* pDeviceManager,
                                              const palInt8* pciBusId,
                                              palDevice** ppDevice);

/// @brief Get the count of devices managed by the device manager.
///
/// @param pDeviceManager The device manager to use
/// @return Returns the count of devices managed by the device manager.
palInt32 palDeviceManagerGetDeviceCount(palDeviceManager* pDeviceManager);

#endif // PAL_DEVICEMANAGER_H_