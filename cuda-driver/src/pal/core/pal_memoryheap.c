#include "pal_assert.h"
#include "pal_context.h"
#include "pal_memorychunk.h"
#include "pal_memoryblock.h"
#include "pal_memoryheap.h"
#include "pal_memorymanager.h"
#include "pal_memoryobject.h"
#include "pal_rbtree.h"

// =====================================================================================================================
palResult palMemoryHeapCreate(palMemoryHeap** ppMemoryHeap, palMemoryManager* pMemoryManager,
                              palMemoryHeapType heapType)
{
    palResult      result      = PAL_SUCCESS;
    palMemoryHeap* pMemoryHeap = NULL;

    if ((ppMemoryHeap == NULL) || (pMemoryManager == NULL) || (heapType >= PAL_MEMORY_HEAP_TYPE_MAX))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory heap creation.\n");
        return PAL_ERROR_INVALID_VALUE;
    }

    pMemoryHeap = (palMemoryHeap*)malloc(sizeof(palMemoryHeap));
    if (pMemoryHeap == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for memory heap.\n");
        *ppMemoryHeap = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palMemoryHeapInitialize(pMemoryHeap, pMemoryManager, heapType);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory heap.\n");
        *ppMemoryHeap = NULL;
        free(pMemoryHeap);
        return result;
    }

    *ppMemoryHeap = pMemoryHeap;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryHeapInitialize(palMemoryHeap* pMemoryHeap, palMemoryManager* pMemoryManager,
                                  palMemoryHeapType heapType)
{
    palResult result = PAL_SUCCESS;
    palInt32  error  = 0;

    if ((pMemoryHeap == NULL) || (pMemoryManager == NULL) || (heapType >= PAL_MEMORY_HEAP_TYPE_MAX))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory heap creation.\n");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pMemoryHeap, 0, sizeof(palMemoryHeap));

    // Initialize the memory heap structure
    pMemoryHeap->pMemoryManager = pMemoryManager;
    pMemoryHeap->heapType       = heapType;

    error = palOsMutexInit(&pMemoryHeap->mutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize mutex for memory heap.\n");
        memset(pMemoryHeap, 0, sizeof(palMemoryHeap));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    error = palOsMutexInit(&pMemoryHeap->memoryBlockMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory block mutex for memory heap.\n");
        palOsMutexDestroy(&pMemoryHeap->mutex);
        memset(pMemoryHeap, 0, sizeof(palMemoryHeap));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palRbTreeCreate(&pMemoryHeap->pFreeMemoryBlockMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create free memory block map for memory heap.\n");
        palOsMutexDestroy(&pMemoryHeap->memoryBlockMutex);
        palOsMutexDestroy(&pMemoryHeap->mutex);
        memset(pMemoryHeap, 0, sizeof(palMemoryHeap));
        return result;
    }

    result = palRbTreeCreate(&pMemoryHeap->pUsedMemoryBlockMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create used memory block map for memory heap.\n");
        palRbTreeDestroy(pMemoryHeap->pFreeMemoryBlockMap);
        palOsMutexDestroy(&pMemoryHeap->memoryBlockMutex);
        palOsMutexDestroy(&pMemoryHeap->mutex);
        memset(pMemoryHeap, 0, sizeof(palMemoryHeap));
        return result;
    }

    // Add the memory heap handle into the memory manager map container.
    result = palMemoryManagerAddMemoryHeap(pMemoryManager, pMemoryHeap);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory heap to the memory manager.\n");
        palRbTreeDestroy(pMemoryHeap->pUsedMemoryBlockMap);
        palRbTreeDestroy(pMemoryHeap->pFreeMemoryBlockMap);
        palOsMutexDestroy(&pMemoryHeap->memoryBlockMutex);
        palOsMutexDestroy(&pMemoryHeap->mutex);
        memset(pMemoryHeap, 0, sizeof(palMemoryHeap));
        return result;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palMemoryHeapDeinitialize(palMemoryHeap* pMemoryHeap)
{
    palResult result = PAL_SUCCESS;

    if (pMemoryHeap == NULL)
    {
        return;
    }

    // Remove the memory heap handle from the memory manager map container.
    result = palMemoryManagerRemoveMemoryHeap(pMemoryHeap->pMemoryManager, pMemoryHeap);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to remove memory heap from the memory manager.\n");
        return;
    }

    palOsMutexLock(&pMemoryHeap->mutex);

    // Remove the memory objects handle from the map container of heap.
    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    if (pMemoryHeap->pUsedMemoryBlockMap != NULL)
    {
        // Destroy the used memory heap map tree.
        while (palRbTreeGetSize(pMemoryHeap->pUsedMemoryBlockMap) > 0)
        {
            // Remove the first element from the tree.
            palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryHeap->pUsedMemoryBlockMap);
            PAL_ASSERT(pNode != NULL);
            palMemoryBlockRelease((palMemoryBlock*)pNode->value);
        }

        palRbTreeDestroy(pMemoryHeap->pUsedMemoryBlockMap);
        pMemoryHeap->pUsedMemoryBlockMap = NULL;
    }

    if (pMemoryHeap->pFreeMemoryBlockMap != NULL)
    {
        // Destroy the free memory heap map tree.
        while (palRbTreeGetSize(pMemoryHeap->pFreeMemoryBlockMap) > 0)
        {
            // Remove the first element from the tree.
            palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryHeap->pFreeMemoryBlockMap);
            PAL_ASSERT(pNode != NULL);
            palMemoryBlockRelease((palMemoryBlock*)pNode->value);
        }

        palRbTreeDestroy(pMemoryHeap->pFreeMemoryBlockMap);
        pMemoryHeap->pFreeMemoryBlockMap = NULL;
    }
    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    palOsMutexUnlock(&pMemoryHeap->mutex);

    palOsMutexDestroy(&pMemoryHeap->memoryBlockMutex);
    palOsMutexDestroy(&pMemoryHeap->mutex);

    memset(pMemoryHeap, 0, sizeof(palMemoryHeap));
}

// =====================================================================================================================
void palMemoryHeapDestroy(palMemoryHeap* pMemoryHeap)
{
    if (pMemoryHeap == NULL)
    {
        return;
    }

    // Deinitialize the memory heap structure.
    palMemoryHeapDeinitialize(pMemoryHeap);

    // Free the memory heap handle.
    free(pMemoryHeap);
}

// =====================================================================================================================
palResult palMemoryHeapAddFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    result = palRbTreeInsert(pMemoryHeap->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryBlock);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory block to the free memory block map tree.");
        palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);
        return result;
    }
    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryHeapRemoveFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    pNode = palRbTreeSearch(pMemoryHeap->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryHeap->pFreeMemoryBlockMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory block not found in the free memory block map tree.");
        palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory block from the tree.
    palRbTreeDelete(pMemoryHeap->pFreeMemoryBlockMap, pNode);
    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryHeapGetFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palUint64 size, palUint64 alignment,
                                          palMemoryBlock** ppMemoryBlock)
{
    palResult       result       = PAL_SUCCESS;
    palRbTreeNode*  pNode        = NULL;
    palMemoryBlock* pMemoryBlock = NULL;

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(ppMemoryBlock != NULL);

    if ((size == 0) || (alignment == 0) || ((alignment & (alignment - 1)) != 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid size or alignment for memory block allocation. Alignment must be a power of 2.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppMemoryBlock = NULL;

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    pNode = palRbTreeGetFirst(pMemoryHeap->pFreeMemoryBlockMap);
    while (pNode != palRbTreeGetEnd(pMemoryHeap->pFreeMemoryBlockMap))
    {
        pMemoryBlock = (palMemoryBlock*)pNode->value;

        // Check if the memory block is large enough and aligned correctly
        if ((pMemoryBlock->size >= size) &&
            (pMemoryBlock->deviceVirtAddress % alignment == 0) &&
            (pMemoryBlock->alignment == alignment))
        {
            *ppMemoryBlock = pMemoryBlock;
            break;
        }

        // Move to the next node
        pNode = palRbTreeGetNext(pMemoryHeap->pFreeMemoryBlockMap, pNode);
    }
    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    if (*ppMemoryBlock == NULL)
    {
        PAL_DBG_PRINTF_ERROR("No suitable free memory block found for size %llu and alignment %llu.",
                             size, alignment);
        return PAL_ERROR_NOT_FOUND;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryHeapAddUsedMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    // Search for the memory block in the free memory block map tree.
    pNode = palRbTreeSearch(pMemoryHeap->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryHeap->pFreeMemoryBlockMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory block not found in the free memory block map tree.");
        palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory block from the free memory block map tree.
    palRbTreeDelete(pMemoryHeap->pFreeMemoryBlockMap, pNode);

    // Insert the memory block into the used memory block map tree.
    result = palRbTreeInsert(pMemoryHeap->pUsedMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryBlock);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory block to the used memory block map tree.");
        // If insertion fails, we need to reinsert the memory block back to the free memory block map.
        palRbTreeInsert(pMemoryHeap->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                        (palRbTreeValueType)pMemoryBlock);
        // Unlock the mutex before returning.
        palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);
        return result;
    }

    // Mark the memory block as used.
    pMemoryBlock->isUsed = PAL_TRUE;

    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryHeapRemoveUsedMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(pMemoryBlock != NULL);

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    // Search for the memory block in the used memory block map tree.
    pNode = palRbTreeSearch(pMemoryHeap->pUsedMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryHeap->pUsedMemoryBlockMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory block not found in the used memory block map tree.");
        palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory block from the used memory block map tree.
    palRbTreeDelete(pMemoryHeap->pUsedMemoryBlockMap, pNode);

    // Insert the memory block into the free memory block map tree.
    result = palRbTreeInsert(pMemoryHeap->pFreeMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryBlock);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory block to the free memory block map tree.");
        // If insertion fails, we need to reinsert the memory block back to the used memory block map.
        palRbTreeInsert(pMemoryHeap->pUsedMemoryBlockMap, (palRbTreeKeyType)pMemoryBlock->deviceVirtAddress,
                        (palRbTreeValueType)pMemoryBlock);
        // Unlock the mutex before returning.
        palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);
        return result;
    }

    // Mark the memory block as not used.
    pMemoryBlock->isUsed = PAL_FALSE;

    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryHeapGetFreeMemoryChunkFromUsedMemoryBlock(palMemoryHeap* pMemoryHeap, palUint64 size,
                                                             palUint64 alignment, palMemoryFlags flags,
                                                             palMemoryChunk** ppMemoryChunk)
{
    palResult       result              = PAL_SUCCESS;
    palMemoryBlock* pMemoryBlock        = NULL;
    palRbTreeNode*  pNode               = NULL;
    palMemoryChunk* pCurrentMemoryChunk = NULL;
    palMemoryChunk* pBestFitMemoryChunk = NULL;

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(ppMemoryChunk != NULL);

    *ppMemoryChunk = NULL;

    if ((size == 0) || (alignment == 0) || ((alignment & (alignment - 1)) != 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid size or alignment for memory chunk allocation. Alignment must be a power of 2.");
        return PAL_ERROR_INVALID_VALUE;
    }

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    pNode = palRbTreeGetFirst(pMemoryHeap->pUsedMemoryBlockMap);
    while (pNode != palRbTreeGetEnd(pMemoryHeap->pUsedMemoryBlockMap))
    {
        pMemoryBlock = (palMemoryBlock*)pNode->value;

        if (palMemoryFlagsIsEqual(&pMemoryBlock->memFlags, &flags) == PAL_TRUE)
        {
            // Search for a best fit free memory chunk in the memory block
            result = palMemoryBlockGetFreeMemoryChunk(pMemoryBlock, size, alignment, &pCurrentMemoryChunk);
            if (result == PAL_SUCCESS)
            {
                if ((pBestFitMemoryChunk == NULL) || (pCurrentMemoryChunk->size < pBestFitMemoryChunk->size))
                {
                    pBestFitMemoryChunk = pCurrentMemoryChunk;
                }
            }
        }

        // Move to the next memory block
        pNode = palRbTreeGetNext(pMemoryHeap->pUsedMemoryBlockMap, pNode);
    }

    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    if (pBestFitMemoryChunk == NULL)
    {
        PAL_DBG_PRINTF_INFO("No suitable free memory chunk found for size %llu and alignment %llu.",
                            size, alignment);
        return PAL_ERROR_NOT_FOUND;
    }

    *ppMemoryChunk = pBestFitMemoryChunk;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryHeapGetFreeMemoryChunkFromFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palUint64 size,
                                                             palUint64 alignment, palMemoryFlags flags,
                                                             palMemoryChunk** ppMemoryChunk)
{
    palResult       result              = PAL_SUCCESS;
    palMemoryBlock* pMemoryBlock        = NULL;
    palRbTreeNode*  pNode               = NULL;
    palMemoryChunk* pCurrentMemoryChunk = NULL;
    palMemoryChunk* pBestFitMemoryChunk = NULL;

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(ppMemoryChunk != NULL);

    *ppMemoryChunk = NULL;

    if ((size == 0) || (alignment == 0) || ((alignment & (alignment - 1)) != 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid size or alignment for memory chunk allocation. Alignment must be a power of 2.");
        return PAL_ERROR_INVALID_VALUE;
    }

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    pNode = palRbTreeGetFirst(pMemoryHeap->pFreeMemoryBlockMap);
    while (pNode != palRbTreeGetEnd(pMemoryHeap->pFreeMemoryBlockMap))
    {
        pMemoryBlock = (palMemoryBlock*)pNode->value;

        if (palMemoryFlagsIsEqual(&pMemoryBlock->memFlags, &flags) == PAL_TRUE)
        {
            // Search for a best fit free memory chunk in the memory block
            result = palMemoryBlockGetFreeMemoryChunk(pMemoryBlock, size, alignment, &pCurrentMemoryChunk);
            if (result == PAL_SUCCESS)
            {
                if ((pBestFitMemoryChunk == NULL) || (pCurrentMemoryChunk->size < pBestFitMemoryChunk->size))
                {
                    pBestFitMemoryChunk = pCurrentMemoryChunk;
                }
            }
        }

        // Move to the next memory block
        pNode = palRbTreeGetNext(pMemoryHeap->pFreeMemoryBlockMap, pNode);
    }

    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);

    if (pBestFitMemoryChunk == NULL)
    {
        PAL_DBG_PRINTF_INFO("No suitable free memory chunk found for size %llu and alignment %llu.",
                            size, alignment);
        return PAL_ERROR_NOT_FOUND;
    }

    *ppMemoryChunk = pBestFitMemoryChunk;

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palMemoryHeapDestroyAllFreeMemoryBlocks(palMemoryHeap* pMemoryHeap)
{
    palRbTreeNode* pNode = NULL;

    PAL_ASSERT(pMemoryHeap != NULL);

    palOsMutexLock(&pMemoryHeap->memoryBlockMutex);
    while (palRbTreeGetSize(pMemoryHeap->pFreeMemoryBlockMap) > 0)
    {
        // Remove the first element from the tree.
        pNode = palRbTreeGetFirst(pMemoryHeap->pFreeMemoryBlockMap);
        PAL_ASSERT(pNode != NULL);
        palMemoryBlockRelease((palMemoryBlock*)pNode->value);
    }
    palOsMutexUnlock(&pMemoryHeap->memoryBlockMutex);
}

// =====================================================================================================================
palResult palMemoryHeapCreateMemoryObject(palMemoryHeap* pMemoryHeap, palContext* pContext, palUint64 size,
                                          palMemoryFlags memFlags, palUint64 hostFlags, void* pHostPtr,
                                          palMemoryObject** ppMemoryObject)
{
    palResult                 result                = PAL_SUCCESS;
    palMemoryBlockCreateInfo  blockCreateInfo       = {0};
    palMemoryBlock*           pMemoryBlock          = NULL;
    palMemoryChunk*           pMemoryChunk          = NULL;
    palMemoryChunk*           pSplitMemoryChunk     = NULL;
    palMemoryChunk*           pRemainderMemoryChunk = NULL;
    palMemoryObject*          pMemoryObject         = NULL;
    palMemoryObjectCreateInfo objectCreateInfo      = {0};

    PAL_ASSERT(pMemoryHeap != NULL);
    PAL_ASSERT(pContext != NULL);
    PAL_ASSERT(ppMemoryObject != NULL);

    *ppMemoryObject = NULL;

    palOsMutexLock(&pMemoryHeap->mutex);

    if ((memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC) ||
        (memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC) ||
        (memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC_PORTABLE) ||
        (memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_MANAGED) ||
        (memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_PITCH))
    {
        // Handle device memory allocation cases (e.g., device or pinned memory) based on allocation API type,
        // regardless of the host pointer's state.

        // Get a free memory chunk of used memory block from the memory heap
        result = palMemoryHeapGetFreeMemoryChunkFromUsedMemoryBlock(pMemoryHeap, size, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE,
                                                                    memFlags, &pMemoryChunk);
        if (result != PAL_SUCCESS)
        {
            result = palMemoryHeapGetFreeMemoryChunkFromFreeMemoryBlock(pMemoryHeap, size, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE,
                                                                        memFlags, &pMemoryChunk);
            if (result != PAL_SUCCESS)
            {
                // If no suitable memory chunk is found, create a new memory block
                blockCreateInfo.pContext     = pContext;
                blockCreateInfo.isMemoryHeap = PAL_TRUE;
                blockCreateInfo.size         = PAL_ALIGN(size, palOsGetPageSize());
                blockCreateInfo.alignment    = palOsGetPageSize();

                // Set the memory flags for the memory block
                memcpy(&blockCreateInfo.memFlags, &memFlags, sizeof(palMemoryFlags));

                result = palMemoryBlockCreate(&pMemoryBlock, pMemoryHeap, &blockCreateInfo);
                if (result == PAL_ERROR_OUT_OF_MEMORY)
                {
                    // If memory block creation fails due to OOM, try to destroy all free memory blocks and retry once
                    PAL_DBG_PRINTF_WARN("Memory block creation failed due to OOM, destroying all free memory "
                                        "blocks and retrying.");
                    palMemoryHeapDestroyAllFreeMemoryBlocks(pMemoryHeap);

                    // Retry memory block creation
                    result = palMemoryBlockCreate(&pMemoryBlock, pMemoryHeap, &blockCreateInfo);
                    if (result != PAL_SUCCESS)
                    {
                        PAL_DBG_PRINTF_ERROR("Failed to create memory block for memory object after retry.");
                        palOsMutexUnlock(&pMemoryHeap->mutex);
                        return result;
                    }
                }
                else if (result != PAL_SUCCESS)
                {
                    PAL_DBG_PRINTF_ERROR("Failed to create memory block for memory object.");
                    palOsMutexUnlock(&pMemoryHeap->mutex);
                    return result;
                }

                // Get a free memory chunk from the newly created memory block
                result = palMemoryBlockGetFreeMemoryChunk(pMemoryBlock, size, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE,
                                                          &pMemoryChunk);
                if (result != PAL_SUCCESS)
                {
                    PAL_DBG_PRINTF_ERROR("Failed to get free memory chunk from memory block.");
                    palMemoryBlockRelease(pMemoryBlock);
                    palOsMutexUnlock(&pMemoryHeap->mutex);
                    return result;
                }
            }
        }

        if (memFlags.hasSubAllocator == PAL_TRUE)
        {
            // Split the memory chunk to fit the requested size if necessary
            result = palMemoryChunkSplit(pMemoryChunk, size, &pSplitMemoryChunk, &pRemainderMemoryChunk);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to split memory chunk for memory object.");
                palOsMutexUnlock(&pMemoryHeap->mutex);
                return result;
            }
        }
    }
    else
    {
        if (pHostPtr == NULL)
        {
            PAL_DBG_PRINTF_ERROR("pHostPtr must not be NULL for host register operation.");
            palOsMutexUnlock(&pMemoryHeap->mutex);
            return PAL_ERROR_INVALID_VALUE;
        }

        PAL_ASSERT((memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER) ||
                   (memFlags.allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER_PORTABLE));

        // Register the host pageable memory into a new memory block
        blockCreateInfo.pContext     = pContext;
        blockCreateInfo.isMemoryHeap = PAL_TRUE;
        blockCreateInfo.size         = PAL_ALIGN(size, palOsGetPageSize());
        blockCreateInfo.alignment    = palOsGetPageSize();
        blockCreateInfo.pHostPtr     = pHostPtr;

        // Set the memory flags for the memory block
        memcpy(&blockCreateInfo.memFlags, &memFlags, sizeof(palMemoryFlags));

        result = palMemoryBlockCreate(&pMemoryBlock, pMemoryHeap, &blockCreateInfo);
        if (result == PAL_ERROR_OUT_OF_MEMORY)
        {
            // If memory block creation fails due to OOM, try to destroy all free memory blocks and retry once
            PAL_DBG_PRINTF_WARN("Memory block creation failed due to OOM, destroying all free memory "
                                "blocks and retrying.");
            palMemoryHeapDestroyAllFreeMemoryBlocks(pMemoryHeap);

            // Retry memory block creation
            result = palMemoryBlockCreate(&pMemoryBlock, pMemoryHeap, &blockCreateInfo);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to create memory block for memory object after retry.");
                palOsMutexUnlock(&pMemoryHeap->mutex);
                return result;
            }
        }
        else if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to create memory block for memory object.");
            palOsMutexUnlock(&pMemoryHeap->mutex);
            return result;
        }

        // Get a free memory chunk from the newly created memory block
        result = palMemoryBlockGetFreeMemoryChunk(pMemoryBlock, size, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE, &pMemoryChunk);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get free memory chunk from memory block.");
            palMemoryBlockRelease(pMemoryBlock);
            palOsMutexUnlock(&pMemoryHeap->mutex);
            return result;
        }

        pSplitMemoryChunk = pMemoryChunk;
    }

    // Create the memory object
    objectCreateInfo.pMemoryChunk = pSplitMemoryChunk;
    objectCreateInfo.size         = size;
    objectCreateInfo.hostFlags    = hostFlags;
    objectCreateInfo.pHostPtr     = pHostPtr;

    // Set the memory flags for the memory object
    memcpy(&objectCreateInfo.memFlags, &memFlags, sizeof(palMemoryFlags));

    result = palMemoryObjectCreate(&pMemoryObject, pContext, &objectCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create memory object from memory chunk.");
        palOsMutexUnlock(&pMemoryHeap->mutex);
        return result;
    }

    palOsMutexUnlock(&pMemoryHeap->mutex);

    *ppMemoryObject = pMemoryObject;

    return PAL_SUCCESS;
}