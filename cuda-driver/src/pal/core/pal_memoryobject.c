#include "pal_assert.h"
#include "pal_context.h"
#include "pal_globalmanager.h"
#include "pal_memoryblock.h"
#include "pal_memorychunk.h"
#include "pal_memoryheap.h"
#include "pal_memorymanager.h"
#include "pal_memoryobject.h"
#include "pal_memorypool.h"
#include "pal_uvamanager.h"

/// @brief Initialize a memory object
/// @param pMemoryObject Pointer to the memory object to initialize
/// @param pContext The context to associate with the memory object
/// @param pCreateInfo The creation info for the memory object
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryObjectInitializeInternal(palMemoryObject* pMemoryObject, palContext* pContext,
                                            palMemoryObjectCreateInfo* pCreateInfo);

/// @brief Deinitialize a memory object
///
/// @param pMemoryObject Pointer to the memory object to deinitialize
void palMemoryObjectDeinitializeInternal(palMemoryObject* pMemoryObject);

// =====================================================================================================================
palResult palMemoryObjectCreate(palMemoryObject** ppMemoryObject, palContext* pContext,
                                palMemoryObjectCreateInfo* pCreateInfo)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryObject* pMemoryObject = NULL;

    if ((ppMemoryObject == NULL) || (pContext == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory object creation.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pMemoryObject = (palMemoryObject*)malloc(sizeof(palMemoryObject));
    if (pMemoryObject == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for memory object.");
        *ppMemoryObject = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palMemoryObjectInitializeInternal(pMemoryObject, pContext, pCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory object.");
        free(pMemoryObject);
        *ppMemoryObject = NULL;
        return result;
    }

    *ppMemoryObject = pMemoryObject;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryObjectInitializeInternal(palMemoryObject* pMemoryObject, palContext* pContext,
                                            palMemoryObjectCreateInfo* pCreateInfo)
{
    palResult       result       = PAL_SUCCESS;
    palMemoryBlock* pMemoryBlock = NULL;

    if ((pMemoryObject == NULL) || (pContext == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory object initialization.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pMemoryBlock = pCreateInfo->pMemoryChunk->pMemoryBlock;
    PAL_ASSERT(pMemoryBlock != NULL);

    // Initialize the memory object structure
    memset(pMemoryObject, 0, sizeof(palMemoryObject));

    memcpy(&pMemoryObject->memFlags, &pCreateInfo->memFlags, sizeof(palMemoryFlags));

    pMemoryObject->pContext     = pContext;
    pMemoryObject->size         = pCreateInfo->size;
    pMemoryObject->actualSize   = pCreateInfo->pMemoryChunk->size;
    pMemoryObject->hostFlags    = pCreateInfo->hostFlags;
    pMemoryObject->pMemoryChunk = pCreateInfo->pMemoryChunk;

    result = palMemoryBlockAddUsedMemoryChunk(pMemoryBlock, pMemoryObject->pMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add used memory chunk to the memory block.");
        memset(pMemoryObject, 0, sizeof(palMemoryObject));
        return result;
    }

    // Add memory object to the memory object's map in the memory manager.
    result = palMemoryManagerAddMemoryObject(pContext->pMemoryManager, pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory object to the context.");
        palMemoryBlockRemoveUsedMemoryChunk(pMemoryBlock, pMemoryObject->pMemoryChunk);
        memset(pMemoryObject, 0, sizeof(palMemoryObject));
        return result;
    }

    if (g_globalManager.pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Add the memory object to the UVA manager.
        result = palUvaManagerAddMemoryObject(g_globalManager.pUvaManager, pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to add memory object to the UVA manager.");
            palMemoryManagerRemoveMemoryObject(pContext->pMemoryManager, pMemoryObject);
            palMemoryBlockRemoveUsedMemoryChunk(pMemoryBlock, pMemoryObject->pMemoryChunk);
            memset(pMemoryObject, 0, sizeof(palMemoryObject));
            return result;
        }
    }

    // Initialize the reference count to 1.
    palOsAtomicStoreSeqCst32(&pMemoryObject->refCount, 1);

    // Retain the memory block to ensure it stays valid while the memory object exists.
    palMemoryBlockRetain(pMemoryBlock);

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palMemoryObjectDeinitializeInternal(palMemoryObject* pMemoryObject)
{
    palResult         result         = PAL_SUCCESS;
    palMemoryManager* pMemoryManager = NULL;
    palMemoryBlock*   pMemoryBlock   = NULL;

    if (pMemoryObject == NULL)
    {
        return;
    }

    pMemoryManager = palMemoryObjectGetMemoryManager(pMemoryObject);
    pMemoryBlock   = pMemoryObject->pMemoryChunk->pMemoryBlock;

    if (g_globalManager.pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Remove the memory object from the UVA manager.
        result = palUvaManagerRemoveMemoryObject(g_globalManager.pUvaManager, pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to remove memory object from the UVA manager.");
            return;
        }
    }

    // Remove the memory object from the memory manager's memory object map
    result = palMemoryManagerRemoveMemoryObject(pMemoryManager, pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to remove memory object from the context.");
        if (g_globalManager.pUvaManager->enableUnifiedAddressing == PAL_TRUE)
        {
            palUvaManagerAddMemoryObject(g_globalManager.pUvaManager, pMemoryObject);
        }
        return;
    }

    // Destroy the memory chunk associated with the memory object.
    result = palMemoryBlockRemoveUsedMemoryChunk(pMemoryBlock, pMemoryObject->pMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to remove used memory chunk from the memory block.");
        palMemoryManagerAddMemoryObject(pMemoryManager, pMemoryObject);
        if (g_globalManager.pUvaManager->enableUnifiedAddressing == PAL_TRUE)
        {
            palUvaManagerAddMemoryObject(g_globalManager.pUvaManager, pMemoryObject);
        }
        return;
    }

    // Release the memory block reference.
    palMemoryBlockRelease(pMemoryBlock);

    // Clear the memory object structure.
    memset(pMemoryObject, 0, sizeof(pMemoryObject));
}

// =====================================================================================================================
void palMemoryObjectRetain(palMemoryObject* pMemoryObject)
{
    PAL_ASSERT(pMemoryObject != NULL);

    // Increment the reference count atomically.
    palOsAtomicFetchAndIncrementAcqRel32(&pMemoryObject->refCount);
}

// =====================================================================================================================
void palMemoryObjectRelease(palMemoryObject* pMemoryObject)
{
    palUint32 oldRefCount;

    if (pMemoryObject == NULL)
    {
        return;
    }

    // Decrement the reference count atomically.
    oldRefCount = palOsAtomicFetchAndDecrementAcqRel32(&pMemoryObject->refCount);
    PAL_ASSERT(oldRefCount > 0);

    // Don't free memory object if it is not the last reference - that is, refCount > 0
    if (oldRefCount != 1)
    {
        return;
    }

    palMemoryObjectDeinitializeInternal(pMemoryObject);

    // Free the memory object handle.
    free(pMemoryObject);
}

// =====================================================================================================================
palUint64 palMemoryObjectGetOffsetOf(palMemoryObject* pMemoryObject, palUint64 devicePtr)
{
    PAL_ASSERT(pMemoryObject != NULL);
    PAL_ASSERT((devicePtr >= pMemoryObject->pMemoryChunk->deviceVirtAddress) &&
               (devicePtr < (pMemoryObject->pMemoryChunk->deviceVirtAddress + pMemoryObject->size)));
    return devicePtr - pMemoryObject->pMemoryChunk->deviceVirtAddress;
}

// =====================================================================================================================
palUint64 palMemoryObjectGetDevicePtr(palMemoryObject* pMemoryObject)
{
    PAL_ASSERT(pMemoryObject != NULL);
    return pMemoryObject->pMemoryChunk->deviceVirtAddress;
}

// =====================================================================================================================
void* palMemoryObjectGetHostPtr(palMemoryObject* pMemoryObject)
{
    PAL_ASSERT(pMemoryObject != NULL);
    return pMemoryObject->pMemoryChunk->pHostVirtAddress;
}

// =====================================================================================================================
palUint64 palMemoryObjectGetSize(palMemoryObject* pMemoryObject)
{
    PAL_ASSERT(pMemoryObject != NULL);
    return pMemoryObject->size;
}

// =====================================================================================================================
palMemoryFlags* palMemoryObjectGetMemoryFlags(palMemoryObject* pMemoryObject)
{
    PAL_ASSERT(pMemoryObject != NULL);
    return &pMemoryObject->memFlags;
}

// =====================================================================================================================
palUint32 palMemoryObjectGetHostFlags(palMemoryObject* pMemoryObject)
{
    PAL_ASSERT(pMemoryObject != NULL);
    return pMemoryObject->hostFlags;
}

// =====================================================================================================================
palMemoryManager* palMemoryObjectGetMemoryManager(palMemoryObject* pMemoryObject)
{
    PAL_ASSERT(pMemoryObject != NULL);

    palMemoryBlock* pMemoryBlock = pMemoryObject->pMemoryChunk->pMemoryBlock;
    if (pMemoryBlock->isMemoryHeap == PAL_TRUE)
    {
        return pMemoryBlock->pMemoryHeap->pMemoryManager;
    }
    else
    {
        return pMemoryBlock->pMemoryPool->pMemoryManager;
    }
}

// =====================================================================================================================
palResult palMemoryObjectGetHandleForAddressRange(palMemoryObject* pMemoryObject, palUint64 devicePtr, palUint64 size,
                                                  palMemRangeHandleType handleType, palUint64 flags, palUint64* pHandle)
{

    return PAL_ERROR_NOT_SUPPORTED;
}