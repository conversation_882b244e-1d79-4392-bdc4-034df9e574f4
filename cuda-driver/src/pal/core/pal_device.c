#include "pal_assert.h"
#include "pal_context.h"
#include "pal_device.h"
#include "pal_devicemanager.h"
#include "pal_memorymanager.h"
#include "pal_memorypool.h"
#include "pal_thunk.h"

// =====================================================================================================================
palResult palDeviceCreate(palDevice** ppDevice, palDeviceManager* pDeviceManager, palInt32 gpuId)
{
    palResult result   = PAL_SUCCESS;
    palDevice* pDevice = NULL;

    if ((ppDevice == NULL) || (pDeviceManager == NULL) || (gpuId < 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pDevice = (palDevice*)malloc(sizeof(palDevice));
    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for device.");
        *ppDevice = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palDeviceInitialize(pDevice, pDeviceManager, gpuId);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize device.");
        free(pDevice);
        *ppDevice = NULL;
        return result;
    }

    *ppDevice = pDevice;

    return result;
}

// =====================================================================================================================
palResult palDeviceInitialize(palDevice* pDevice, palDeviceManager* pDeviceManager, palInt32 gpuId)
{
    palResult result = PAL_SUCCESS;
    palInt32  error  = 0;

    if ((pDevice == NULL) || (pDeviceManager == NULL) || (gpuId < 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pDevice, 0, sizeof(palDevice));

    // Set the device manager and device ID.
    pDevice->pDeviceManager = pDeviceManager;
    pDevice->gpuId          = gpuId;

    result = palThunkDeviceOpen(gpuId, (void**)&pDevice->pKmdDevice);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to open gpu with ID %d.", gpuId);
        memset(pDevice, 0, sizeof(palDevice));
        return result;
    }

    pDevice->pDeviceProperties = (palDeviceProperties*)malloc(sizeof(palDeviceProperties));
    if (pDevice->pDeviceProperties == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for device properties.");
        palThunkDeviceClose(pDevice->pKmdDevice);
        memset(pDevice, 0, sizeof(palDevice));
        return PAL_ERROR_OUT_OF_MEMORY;
    }
    memset(pDevice->pDeviceProperties, 0, sizeof(palDeviceProperties));

    // Initialize the device properties.
    result = palThunkDeviceGetProperties(pDevice->pKmdDevice, pDevice->pDeviceProperties);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to get device properties for gpu ID %d.", gpuId);
        free(pDevice->pDeviceProperties);
        palThunkDeviceClose(pDevice->pKmdDevice);
        memset(pDevice, 0, sizeof(palDevice));
        return result;
    }

    // Initialize the primary context mutex.
    error = palOsMutexInit(&pDevice->primaryContextMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize primary context mutex.");
        free(pDevice->pDeviceProperties);
        palThunkDeviceClose(pDevice->pKmdDevice);
        memset(pDevice, 0, sizeof(palDevice));
        return result;
    }

    // Create the primary context for the device.
    pDevice->pPrimaryContext = (palContext*)malloc(sizeof(palContext));
    if (pDevice->pPrimaryContext == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for primary context.");
        palOsMutexDestroy(&pDevice->primaryContextMutex);
        free(pDevice->pDeviceProperties);
        palThunkDeviceClose(pDevice->pKmdDevice);
        memset(pDevice, 0, sizeof(palDevice));
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    pDevice->pPrimaryContextCreateInfo = (palContextCreateInfo*)malloc(sizeof(palContextCreateInfo));
    if (pDevice->pPrimaryContextCreateInfo == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for primary context create info.");
        free(pDevice->pPrimaryContext);
        palOsMutexDestroy(&pDevice->primaryContextMutex);
        free(pDevice->pDeviceProperties);
        palThunkDeviceClose(pDevice->pKmdDevice);
        memset(pDevice, 0, sizeof(palDevice));
        return PAL_ERROR_OUT_OF_MEMORY;
    }
    memset(pDevice->pPrimaryContextCreateInfo, 0, sizeof(palContextCreateInfo));
    pDevice->pPrimaryContextCreateInfo->isPrimary = PAL_TRUE;

    // Initialize the persistent state of the primary context.
    result = palContextPersistentStateInitialize(pDevice->pPrimaryContext, pDevice, PAL_TRUE);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize primary context persistent state.");
        free(pDevice->pPrimaryContextCreateInfo);
        free(pDevice->pPrimaryContext);
        palOsMutexDestroy(&pDevice->primaryContextMutex);
        free(pDevice->pDeviceProperties);
        palThunkDeviceClose(pDevice->pKmdDevice);
        memset(pDevice, 0, sizeof(palDevice));
        return result;
    }

    // Initialize the memory manager for the device.
    result = palMemoryManagerCreate(&pDevice->pMemoryManager, pDevice, PAL_TRUE);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create memory manager for device ID %d.", gpuId);
        palContextPersistentStateDeinitialize(pDevice->pPrimaryContext);
        free(pDevice->pPrimaryContextCreateInfo);
        free(pDevice->pPrimaryContext);
        palOsMutexDestroy(&pDevice->primaryContextMutex);
        free(pDevice->pDeviceProperties);
        palThunkDeviceClose(pDevice->pKmdDevice);
        memset(pDevice, 0, sizeof(palDevice));
        return result;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palDeviceDeinitialize(palDevice* pDevice)
{
    if (pDevice == NULL)
    {
        return;
    }

    if (pDevice->pMemoryManager != NULL)
    {
        // Destroy the memory manager for the device.
        palMemoryManagerDestroy(pDevice->pMemoryManager);
        pDevice->pMemoryManager = NULL;
    }

    // Perform any necessary cleanup for the device here.
    palOsMutexLock(&pDevice->primaryContextMutex);
    if (pDevice->pPrimaryContext != NULL)
    {
        // Destroy the primary context.
        palContextDestroy(pDevice->pPrimaryContext);

        // The primary context must be freed explicitly here since it is not managed by a context manager.
        memset(pDevice->pPrimaryContext, 0, sizeof(palContext));
        free(pDevice->pPrimaryContext);
        pDevice->pPrimaryContext = NULL;
    }
    palOsMutexUnlock(&pDevice->primaryContextMutex);
    palOsMutexDestroy(&pDevice->primaryContextMutex);

    if (pDevice->pPrimaryContextCreateInfo != NULL)
    {
        // Free the primary context create info.
        free(pDevice->pPrimaryContextCreateInfo);
        pDevice->pPrimaryContextCreateInfo = NULL;
    }

    if (pDevice->pDeviceProperties != NULL)
    {
        free(pDevice->pDeviceProperties);
        pDevice->pDeviceProperties = NULL;
    }

    if (pDevice->pKmdDevice != NULL)
    {
        palThunkDeviceClose(pDevice->pKmdDevice);
        pDevice->pKmdDevice = NULL;
    }

    // Reset the device structure.
    memset(pDevice, 0, sizeof(palDevice));
}

// =====================================================================================================================
void palDeviceDestroy(palDevice* pDevice)
{
    if (pDevice == NULL)
    {
        return;
    }

    palDeviceDeinitialize(pDevice);
    free(pDevice);
}

// =====================================================================================================================
palContext* palDeviceGetPrimaryContext(palDevice* pDevice)
{
    PAL_ASSERT(pDevice != NULL);

    // Return the primary context of the device.
    return pDevice->pPrimaryContext;
}

// =====================================================================================================================
palResult palDevicePrimaryContextInitialize(palDevice* pDevice)
{
    palResult result = PAL_SUCCESS;

    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid device handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    palOsMutexLock(&pDevice->primaryContextMutex);

    // Initialize the primary context for the device.
    if (pDevice->pPrimaryContext->persistentState.status != PAL_CONTEXT_STATUS_INITIALIZED)
    {
        result = palContextInitialize(pDevice->pPrimaryContext, pDevice, pDevice->pPrimaryContextCreateInfo);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to initialize primary context for device ID %d.", pDevice->deviceId);
            palOsMutexUnlock(&pDevice->primaryContextMutex);
            return result;
        }
    }
    palOsMutexUnlock(&pDevice->primaryContextMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDevicePrimaryContextRetain(palDevice* pDevice)
{
    palResult   result          = PAL_SUCCESS;
    palContext* pPrimaryContext = NULL;

    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid device handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pPrimaryContext = palDeviceGetPrimaryContext(pDevice);

    palOsMutexLock(&pDevice->primaryContextMutex);
    if (pPrimaryContext->persistentState.status != PAL_CONTEXT_STATUS_INITIALIZED)
    {
        palContextThreadRefCountIncrement(pPrimaryContext);

        result = palContextInitialize(pPrimaryContext, pDevice, pDevice->pPrimaryContextCreateInfo);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to retain primary context for device ID %d.", pDevice->deviceId);
            palContextThreadRefCountDecrement(pPrimaryContext);
            palOsMutexUnlock(&pDevice->primaryContextMutex);
            return result;
        }
    }
    PAL_ASSERT(pPrimaryContext->persistentState.status == PAL_CONTEXT_STATUS_INITIALIZED);
    palContextAttachRefCountIncrement(pPrimaryContext);
    palOsMutexUnlock(&pDevice->primaryContextMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDevicePrimaryContextReset(palDevice* pDevice)
{
    palResult   result          = PAL_SUCCESS;
    palContext* pPrimaryContext = NULL;

    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid device handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pPrimaryContext = palDeviceGetPrimaryContext(pDevice);

    palOsMutexLock(&pDevice->primaryContextMutex);
    if (pPrimaryContext->persistentState.status == PAL_CONTEXT_STATUS_INITIALIZED)
    {
        if (palOsAtomicLoadAcquire32(&pPrimaryContext->persistentState.attachRefCount) != 0)
        {
            PAL_DBG_PRINTF_WARN("Reset primary context while it is attached.");
        }

        palContextDeinitialize(pPrimaryContext);

        // Decrement the thread context stack reference count.
        // This will not free the primary context, as it is still attached.
        palContextThreadRefCountDecrement(pPrimaryContext);
    }
    PAL_ASSERT(pPrimaryContext->persistentState.status == PAL_CONTEXT_STATUS_NOT_INITIALIZED);

    result = palContextPersistentStateInitialize(pPrimaryContext, pDevice, PAL_TRUE);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize persistent state for primary context.");
        palOsMutexUnlock(&pDevice->primaryContextMutex);
        return result;
    }

    pPrimaryContext->apiVersion = PAL_CTX_API_VERSION_v3020;
    palOsMutexUnlock(&pDevice->primaryContextMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDevicePrimaryContextRelease(palDevice* pDevice)
{
    palResult   result          = PAL_SUCCESS;
    palContext* pPrimaryContext = NULL;

    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid device handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pPrimaryContext = palDeviceGetPrimaryContext(pDevice);

    palOsMutexLock(&pDevice->primaryContextMutex);

    if (pPrimaryContext->persistentState.attachRefCount == 0)
    {
        PAL_DBG_PRINTF_ERROR("Release primary context while it is not attached.");
        palOsMutexUnlock(&pDevice->primaryContextMutex);
        return PAL_ERROR_INVALID_CONTEXT;
    }

    // Decrement the attach reference count for the primary context.
    palOsAtomicFetchAndDecrementSeqCst32(&pPrimaryContext->persistentState.attachRefCount);
    if (palOsAtomicLoadAcquire32(&pPrimaryContext->persistentState.attachRefCount) == 0)
    {
        result = palDevicePrimaryContextReset(pDevice);
    }
    palOsMutexUnlock(&pDevice->primaryContextMutex);

    return result;
}

// =====================================================================================================================
palResult palDevicePrimaryContextGetState(palDevice* pDevice, palUint32* pFlags, palInt32* pState)
{
    palResult   result          = PAL_SUCCESS;
    palContext* pPrimaryContext = NULL;
    palUint32   flags           = 0;

    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid device handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pPrimaryContext = palDeviceGetPrimaryContext(pDevice);

    palOsMutexLock(&pDevice->primaryContextMutex);

    flags = pPrimaryContext->persistentState.flags;
    flags &= ~ PAL_CTX_MAP_HOST;
    *pFlags = flags;

    *pState = (pPrimaryContext->persistentState.status == PAL_CONTEXT_STATUS_INITIALIZED) ? 1 : 0;
    palOsMutexUnlock(&pDevice->primaryContextMutex);

    return result;
}

// =====================================================================================================================
palResult palDevicePrimaryContextCheckFlags(palUint32 flags)
{
    palUint32 schedFlags = 0;

    schedFlags = flags & PAL_CTX_SCHED_MASK;

    if (flags & ~PAL_CTX_FLAGS_MASK)
    {
        PAL_DBG_PRINTF_ERROR("Invalid flags specified: 0x%08X", flags & ~PAL_CTX_FLAGS_MASK);
        return PAL_ERROR_INVALID_VALUE;
    }

    if (schedFlags && ((schedFlags != PAL_CTX_SCHED_SPIN) &&
        (schedFlags != PAL_CTX_SCHED_YIELD) && (schedFlags != PAL_CTX_SCHED_BLOCKING_SYNC)))
    {
        PAL_DBG_PRINTF_ERROR("Invalid scheduling flags specified: 0x%08X", schedFlags);
        return PAL_ERROR_INVALID_VALUE;
    }

    if (flags & PAL_CTX_MAP_HOST)
    {
        PAL_DBG_PRINTF_WARN("PAL_CTX_MAP_HOST is redundant for primary context.");
        return PAL_ERROR_INVALID_VALUE;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDevicePrimaryContextSetFlags(palDevice* pDevice, palUint32 flags)
{
    palResult   result          = PAL_SUCCESS;
    palContext* pPrimaryContext = NULL;
    palUint32   currentFlags    = 0;
    palInt32    isActive        = 0;

    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid device handle.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pPrimaryContext = palDeviceGetPrimaryContext(pDevice);

    palOsMutexLock(&pDevice->primaryContextMutex);

    result = palDevicePrimaryContextGetState(pDevice, &currentFlags, &isActive);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to get primary context state for device ID %d.", pDevice->deviceId);
        palOsMutexUnlock(&pDevice->primaryContextMutex);
        return result;
    }

    if (isActive == 1)
    {
        PAL_DBG_PRINTF_ERROR("Cannot set flags for active primary context.");
        palOsMutexUnlock(&pDevice->primaryContextMutex);
        return PAL_ERROR_PRIMARY_CONTEXT_ACTIVE;
    }

    flags |= PAL_CTX_MAP_HOST;

    pDevice->pPrimaryContextCreateInfo->flags = flags;

    palOsMutexUnlock(&pDevice->primaryContextMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceGetAttribute(palDevice* pDevice, palDeviceAttribute attribute, palInt32* pValue)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pValue != NULL);

    switch (attribute)
    {
    case PAL_DEVICE_ATTRIBUTE_MAX_THREADS_PER_BLOCK:
        *pValue = pDevice->pDeviceProperties->maxThreadsPerBlock;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_X:
        *pValue = pDevice->pDeviceProperties->maxThreadsDim[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_Y:
        *pValue = pDevice->pDeviceProperties->maxThreadsDim[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_BLOCK_DIM_Z:
        *pValue = pDevice->pDeviceProperties->maxThreadsDim[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_GRID_DIM_X:
        *pValue = pDevice->pDeviceProperties->maxGridSize[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_GRID_DIM_Y:
        *pValue = pDevice->pDeviceProperties->maxGridSize[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_GRID_DIM_Z:
        *pValue = pDevice->pDeviceProperties->maxGridSize[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_BLOCK:
        *pValue = (palInt32)(pDevice->pDeviceProperties->sharedMemPerBlock);
        break;
    case PAL_DEVICE_ATTRIBUTE_TOTAL_CONSTANT_MEMORY:
        *pValue = (palInt32)(pDevice->pDeviceProperties->totalConstMem);
        break;
    case PAL_DEVICE_ATTRIBUTE_WARP_SIZE:
        *pValue = pDevice->pDeviceProperties->warpSize;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_PITCH:
        *pValue = (palInt32)(pDevice->pDeviceProperties->memPitch);
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_REGISTERS_PER_BLOCK:
        *pValue = pDevice->pDeviceProperties->regsPerBlock;
        break;
    case PAL_DEVICE_ATTRIBUTE_CLOCK_RATE:
        *pValue = pDevice->pDeviceProperties->clockRate;
        break;
    case PAL_DEVICE_ATTRIBUTE_TEXTURE_ALIGNMENT:
        *pValue = pDevice->pDeviceProperties->textureAlignment;
        break;
    case PAL_DEVICE_ATTRIBUTE_GPU_OVERLAP:
        *pValue = pDevice->pDeviceProperties->deviceOverlap;
        break;
    case PAL_DEVICE_ATTRIBUTE_MULTIPROCESSOR_COUNT:
        *pValue = pDevice->pDeviceProperties->multiProcessorCount;
        break;
    case PAL_DEVICE_ATTRIBUTE_KERNEL_EXEC_TIMEOUT:
        *pValue = pDevice->pDeviceProperties->kernelExecTimeoutEnabled;
        break;
    case PAL_DEVICE_ATTRIBUTE_INTEGRATED:
        *pValue = pDevice->pDeviceProperties->integrated;
        break;
    case PAL_DEVICE_ATTRIBUTE_CAN_MAP_HOST_MEMORY:
        *pValue = pDevice->pDeviceProperties->canMapHostMemory;
        break;
    case PAL_DEVICE_ATTRIBUTE_COMPUTE_MODE:
        *pValue = pDevice->pDeviceProperties->computeMode;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture1D;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture2D[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxTexture2D[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture3D[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxTexture3D[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_DEPTH:
        *pValue = pDevice->pDeviceProperties->maxTexture3D[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture2DLayered[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxTexture2DLayered[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LAYERED_LAYERS:
        *pValue = pDevice->pDeviceProperties->maxTexture2DLayered[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_SURFACE_ALIGNMENT:
        *pValue = pDevice->pDeviceProperties->surfaceAlignment;
        break;
    case PAL_DEVICE_ATTRIBUTE_CONCURRENT_KERNELS:
        *pValue = pDevice->pDeviceProperties->concurrentKernels;
        break;
    case PAL_DEVICE_ATTRIBUTE_ECC_ENABLED:
        *pValue = pDevice->pDeviceProperties->ECCEnabled;
        break;
    case PAL_DEVICE_ATTRIBUTE_PCI_BUS_ID:
        *pValue = pDevice->pDeviceProperties->pciBusID;
        break;
    case PAL_DEVICE_ATTRIBUTE_PCI_DEVICE_ID:
        *pValue = pDevice->pDeviceProperties->pciDeviceID;
        break;
    case PAL_DEVICE_ATTRIBUTE_TCC_DRIVER:
        *pValue = pDevice->pDeviceProperties->tccDriver;
        break;
    case PAL_DEVICE_ATTRIBUTE_MEMORY_CLOCK_RATE:
        *pValue = pDevice->pDeviceProperties->memoryClockRate;
        break;
    case PAL_DEVICE_ATTRIBUTE_GLOBAL_MEMORY_BUS_WIDTH:
        *pValue = pDevice->pDeviceProperties->memoryBusWidth;
        break;
    case PAL_DEVICE_ATTRIBUTE_L2_CACHE_SIZE:
        *pValue = pDevice->pDeviceProperties->l2CacheSize;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_THREADS_PER_MULTIPROCESSOR:
        *pValue = pDevice->pDeviceProperties->maxThreadsPerMultiProcessor;
        break;
    case PAL_DEVICE_ATTRIBUTE_ASYNC_ENGINE_COUNT:
        *pValue = pDevice->pDeviceProperties->asyncEngineCount;
        break;
    case PAL_DEVICE_ATTRIBUTE_UNIFIED_ADDRESSING:
        *pValue = pDevice->pDeviceProperties->unifiedAddressing;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LAYERED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture1DLayered[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LAYERED_LAYERS:
        *pValue = pDevice->pDeviceProperties->maxTexture1DLayered[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_GATHER_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture2DGather[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_GATHER_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxTexture2DGather[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_WIDTH_ALTERNATE:
        *pValue = pDevice->pDeviceProperties->maxTexture3DAlt[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_HEIGHT_ALTERNATE:
        *pValue = pDevice->pDeviceProperties->maxTexture3DAlt[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE3D_DEPTH_ALTERNATE:
        *pValue = pDevice->pDeviceProperties->maxTexture3DAlt[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_PCI_DOMAIN_ID:
        *pValue = pDevice->pDeviceProperties->pciDomainID;
        break;
    case PAL_DEVICE_ATTRIBUTE_TEXTURE_PITCH_ALIGNMENT:
        *pValue = pDevice->pDeviceProperties->texturePitchAlignment;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTextureCubemap;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_LAYERED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTextureCubemapLayered[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURECUBEMAP_LAYERED_LAYERS:
        *pValue = pDevice->pDeviceProperties->maxTextureCubemapLayered[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxSurface1D;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxSurface2D[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxSurface2D[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxSurface3D[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxSurface3D[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE3D_DEPTH:
        *pValue = pDevice->pDeviceProperties->maxSurface3D[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_LAYERED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxSurface1DLayered[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE1D_LAYERED_LAYERS:
        *pValue = pDevice->pDeviceProperties->maxSurface1DLayered[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxSurface2DLayered[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxSurface2DLayered[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACE2D_LAYERED_LAYERS:
        *pValue = pDevice->pDeviceProperties->maxSurface2DLayered[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxSurfaceCubemap;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_LAYERED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxSurfaceCubemapLayered[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_SURFACECUBEMAP_LAYERED_LAYERS:
        *pValue = pDevice->pDeviceProperties->maxSurfaceCubemapLayered[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_LINEAR_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture1DLinear;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture2DLinear[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxTexture2DLinear[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_LINEAR_PITCH:
        *pValue = pDevice->pDeviceProperties->maxTexture2DLinear[2];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_MIPMAPPED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture2DMipmap[0];
        break;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE2D_MIPMAPPED_HEIGHT:
        *pValue = pDevice->pDeviceProperties->maxTexture2DMipmap[1];
        break;
    case PAL_DEVICE_ATTRIBUTE_COMPUTE_CAPABILITY_MAJOR:
        *pValue = pDevice->pDeviceProperties->major;
        break;
    case PAL_DEVICE_ATTRIBUTE_COMPUTE_CAPABILITY_MINOR:
        *pValue = pDevice->pDeviceProperties->minor;
    case PAL_DEVICE_ATTRIBUTE_MAXIMUM_TEXTURE1D_MIPMAPPED_WIDTH:
        *pValue = pDevice->pDeviceProperties->maxTexture1DMipmap;
        break;
    case PAL_DEVICE_ATTRIBUTE_STREAM_PRIORITIES_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->streamPrioritiesSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_GLOBAL_L1_CACHE_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->globalL1CacheSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_LOCAL_L1_CACHE_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->localL1CacheSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_MULTIPROCESSOR:
        *pValue = pDevice->pDeviceProperties->sharedMemPerMultiprocessor;
        break;
    case PAL_DEVICE_ATTRIBUTE_MAX_REGISTERS_PER_MULTIPROCESSOR:
        *pValue = pDevice->pDeviceProperties->regsPerMultiprocessor;
        break;
    case PAL_DEVICE_ATTRIBUTE_MANAGED_MEMORY:
        *pValue = pDevice->pDeviceProperties->managedMemory;
        break;
    case PAL_DEVICE_ATTRIBUTE_MULTI_GPU_BOARD:
        *pValue = pDevice->pDeviceProperties->isMultiGpuBoard;
        break;
    case PAL_DEVICE_ATTRIBUTE_MULTI_GPU_BOARD_GROUP_ID:
        *pValue = pDevice->pDeviceProperties->multiGpuBoardGroupID;
        break;
    case PAL_DEVICE_ATTRIBUTE_HOST_NATIVE_ATOMIC_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->hostNativeAtomicSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_SINGLE_TO_DOUBLE_PRECISION_PERF_RATIO:
        *pValue = pDevice->pDeviceProperties->singleToDoublePrecisionPerfRatio;
        break;
    case PAL_DEVICE_ATTRIBUTE_PAGEABLE_MEMORY_ACCESS:
        *pValue = pDevice->pDeviceProperties->pageableMemoryAccess;
        break;
    case PAL_DEVICE_ATTRIBUTE_CONCURRENT_MANAGED_ACCESS:
        *pValue = pDevice->pDeviceProperties->concurrentManagedAccess;
        break;
    case PAL_DEVICE_ATTRIBUTE_COMPUTE_PREEMPTION_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->computePreemptionSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_CAN_USE_HOST_POINTER_FOR_REGISTERED_MEM:
        *pValue = pDevice->pDeviceProperties->canUseHostPointerForRegisteredMem;
        break;
    case PAL_DEVICE_ATTRIBUTE_CAN_USE_STREAM_MEM_OPS_V1:
    case PAL_DEVICE_ATTRIBUTE_CAN_USE_64_BIT_STREAM_MEM_OPS_V1:
    case PAL_DEVICE_ATTRIBUTE_CAN_USE_STREAM_WAIT_VALUE_NOR_V1:
        // These attributes are not supported in the current implementation.
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED;
    case PAL_DEVICE_ATTRIBUTE_COOPERATIVE_LAUNCH:
        *pValue = pDevice->pDeviceProperties->cooperativeLaunch;
        break;
    case PAL_DEVICE_ATTRIBUTE_COOPERATIVE_MULTI_DEVICE_LAUNCH:
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_BLOCK_OPTIN:
        *pValue = pDevice->pDeviceProperties->sharedMemPerBlockOptin;
        break;
    case PAL_DEVICE_ATTRIBUTE_CAN_FLUSH_REMOTE_WRITES:
        // This attribute is not supported in the current implementation.
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_HOST_REGISTER_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->hostRegisterSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_PAGEABLE_MEMORY_ACCESS_USES_HOST_PAGE_TABLES:
        *pValue = pDevice->pDeviceProperties->pageableMemoryAccessUsesHostPageTables;
        break;
    case PAL_DEVICE_ATTRIBUTE_DIRECT_MANAGED_MEM_ACCESS_FROM_HOST:
        *pValue = pDevice->pDeviceProperties->directManagedMemAccessFromHost;
        break;
    case PAL_DEVICE_ATTRIBUTE_VIRTUAL_MEMORY_MANAGEMENT_SUPPORTED:
        // TODO: Implement virtual memory management support.
        *pValue = 0; // Placeholder value, as this is not implemented yet.
        return PAL_ERROR_NOT_SUPPORTED;
    case PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_POSIX_FILE_DESCRIPTOR_SUPPORTED:
    case PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_WIN32_HANDLE_SUPPORTED:
    case PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_WIN32_KMT_HANDLE_SUPPORTED:
        // These attributes are not supported in the current implementation.
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        return PAL_ERROR_NOT_SUPPORTED;
    case PAL_DEVICE_ATTRIBUTE_MAX_BLOCKS_PER_MULTIPROCESSOR:
        *pValue = pDevice->pDeviceProperties->maxBlocksPerMultiProcessor;
        break;
    case PAL_DEVICE_ATTRIBUTE_GENERIC_COMPRESSION_SUPPORTED:
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_MAX_PERSISTING_L2_CACHE_SIZE:
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_MAX_ACCESS_POLICY_WINDOW_SIZE:
        *pValue = pDevice->pDeviceProperties->accessPolicyMaxWindowSize;
        break;
    case PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_WITH_CUDA_VMM_SUPPORTED:
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_RESERVED_SHARED_MEMORY_PER_BLOCK:
        *pValue = pDevice->pDeviceProperties->reservedSharedMemPerBlock;
        break;
    case PAL_DEVICE_ATTRIBUTE_SPARSE_CUDA_ARRAY_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->sparseCudaArraySupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_READ_ONLY_HOST_REGISTER_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->hostRegisterReadOnlySupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_TIMELINE_SEMAPHORE_INTEROP_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->timelineSemaphoreInteropSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_MEMORY_POOLS_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->memoryPoolsSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->gpuDirectRDMASupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_FLUSH_WRITES_OPTIONS:
        *pValue = pDevice->pDeviceProperties->gpuDirectRDMAFlushWritesOptions;
        break;
    case PAL_DEVICE_ATTRIBUTE_GPU_DIRECT_RDMA_WRITES_ORDERING:
        *pValue = pDevice->pDeviceProperties->gpuDirectRDMAWritesOrdering;
        break;
    case PAL_DEVICE_ATTRIBUTE_MEMPOOL_SUPPORTED_HANDLE_TYPES:
        *pValue = pDevice->pDeviceProperties->memoryPoolSupportedHandleTypes;
        break;
    case PAL_DEVICE_ATTRIBUTE_CLUSTER_LAUNCH:
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_DEFERRED_MAPPING_CUDA_ARRAY_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->deferredMappingCudaArraySupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_CAN_USE_64_BIT_STREAM_MEM_OPS:
    case PAL_DEVICE_ATTRIBUTE_CAN_USE_STREAM_WAIT_VALUE_NOR:
    case PAL_DEVICE_ATTRIBUTE_DMA_BUF_SUPPORTED:
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_IPC_EVENT_SUPPORTED:
        *pValue = pDevice->pDeviceProperties->ipcEventSupported;
        break;
    case PAL_DEVICE_ATTRIBUTE_MEM_SYNC_DOMAIN_COUNT:
    case PAL_DEVICE_ATTRIBUTE_TENSOR_MAP_ACCESS_SUPPORTED:
    case PAL_DEVICE_ATTRIBUTE_HANDLE_TYPE_FABRIC_SUPPORTED:
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED; // Placeholder, as this is not implemented yet.
    case PAL_DEVICE_ATTRIBUTE_UNIFIED_FUNCTION_POINTERS:
        *pValue = pDevice->pDeviceProperties->unifiedFunctionPointers;
        break;
    case PAL_DEVICE_ATTRIBUTE_NUMA_CONFIG:
    case PAL_DEVICE_ATTRIBUTE_NUMA_ID:
    case PAL_DEVICE_ATTRIBUTE_MULTICAST_SUPPORTED:
    case PAL_DEVICE_ATTRIBUTE_MPS_ENABLED:
    case PAL_DEVICE_ATTRIBUTE_HOST_NUMA_ID:
        // These attributes are not supported in the current implementation.
        PAL_DBG_PRINTF_WARN("Device attribute %d is not supported.", attribute);
        *pValue = 0;
        return PAL_ERROR_NOT_SUPPORTED;
    default:
        PAL_DBG_PRINTF_ERROR("Unknown device attribute: %d", attribute);
        return PAL_ERROR_INVALID_VALUE;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palInt32 palDeviceGetId(palDevice* pDevice)
{
    PAL_ASSERT(pDevice != NULL);

    // Return the device ID.
    return pDevice->deviceId;
}

// =====================================================================================================================
palBool palDeviceIsSupportExecAffinity(palDevice* pDevice, palExecAffinityType type)
{
    PAL_ASSERT(pDevice != NULL);

    // Check if the device supports execution affinity for the specified type.
    switch (type)
    {
    case PAL_EXEC_AFFINITY_TYPE_SM_COUNT:
        return PAL_TRUE; // The devices supports execution affinity.
    default:
        PAL_DBG_PRINTF_ERROR("Unsupported execution affinity type: %d", type);
        return PAL_FALSE;
    }
}

// =====================================================================================================================
palResult palDeviceGetLuid(palDevice* pDevice, palUint32* pDeviceNodeMask, palInt8* pLuid)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pDeviceNodeMask != NULL);
    PAL_ASSERT(pLuid != NULL);

    return palThunkDeviceGetLuid(pDevice->pKmdDevice, pDeviceNodeMask, pLuid);
}

// =====================================================================================================================
palResult palDeviceGetName(palDevice* pDevice, palInt8* pName, palUint32 nameLength)
{
    size_t srcLength = 0;

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pName != NULL);
    PAL_ASSERT(nameLength > 0);

    srcLength = strnlen(pDevice->pDeviceProperties->name, nameLength - 1);

    // Copy the device name to the provided buffer.
    if (srcLength >= nameLength)
    {
        PAL_DBG_PRINTF_ERROR("Provided buffer is too small for device name.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memcpy(pName, pDevice->pDeviceProperties->name, srcLength);
    pName[nameLength - 1] = '\0'; // Ensure null-termination

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceGetNvSciSyncAttributes(palDevice* pDevice, palInt32 flags, void* pSciSyncAttrList)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pSciSyncAttrList != NULL);

    // Placeholder, as this is not implemented yet.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palDeviceGetTexture1DLinearMaxWidth(palDevice* pDevice, palUint64 arrayElementSize, size_t* pMaxWidth)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pMaxWidth != NULL);

    // Placeholder, as this is not implemented yet.
    *pMaxWidth = 0; // Set to zero as a placeholder.

    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palDeviceGetUuid(palDevice* pDevice, palInt8* pUuid, palBool returnMigUuid)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pUuid != NULL);

    if ((returnMigUuid == PAL_TRUE) && (pDevice->isMigMode == PAL_TRUE))
    {
        // Copy the MIG UUID from the device structure to the provided buffer.
        memcpy(pUuid, pDevice->migUuid, sizeof(pDevice->migUuid));
    }
    else
    {
        // Copy the UUID from the device properties to the provided buffer.
        memcpy(pUuid, pDevice->pDeviceProperties->uuid, sizeof(pDevice->pDeviceProperties->uuid));
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceGetTotalMemory(palDevice* pDevice, size_t* pBytes)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pBytes != NULL);

    // Return the total memory available on the device.
    *pBytes = pDevice->pDeviceProperties->totalGlobalMem;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceGetComputeCapability(palDevice* pDevice, palInt32* major, palInt32* minor)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(major != NULL);
    PAL_ASSERT(minor != NULL);

    // Return the compute capability of the device.
    *major = pDevice->pDeviceProperties->major;
    *minor = pDevice->pDeviceProperties->minor;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceGetLegacyProperties(palDevice* pDevice, palLegacyDeviceProperties* pLegacyProperties)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pLegacyProperties != NULL);

    // Copy the device properties to the legacy properties structure.
    pLegacyProperties->maxThreadsPerBlock = pDevice->pDeviceProperties->maxThreadsPerBlock;
    pLegacyProperties->maxThreadsDim[0]   = pDevice->pDeviceProperties->maxThreadsDim[0];
    pLegacyProperties->maxThreadsDim[1]   = pDevice->pDeviceProperties->maxThreadsDim[1];
    pLegacyProperties->maxThreadsDim[2]   = pDevice->pDeviceProperties->maxThreadsDim[2];
    pLegacyProperties->maxGridSize[0]     = pDevice->pDeviceProperties->maxGridSize[0];
    pLegacyProperties->maxGridSize[1]     = pDevice->pDeviceProperties->maxGridSize[1];
    pLegacyProperties->maxGridSize[2]     = pDevice->pDeviceProperties->maxGridSize[2];
    pLegacyProperties->sharedMemPerBlock  = (palInt32)(pDevice->pDeviceProperties->sharedMemPerBlock);
    pLegacyProperties->SIMDWidth          = (palInt32)(pDevice->pDeviceProperties->warpSize);
    pLegacyProperties->memPitch           = (palInt32)(pDevice->pDeviceProperties->memPitch);
    pLegacyProperties->regsPerBlock       = pDevice->pDeviceProperties->regsPerBlock;
    pLegacyProperties->clockRate          = pDevice->pDeviceProperties->clockRate;
    pLegacyProperties->textureAlign       = pDevice->pDeviceProperties->textureAlignment;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceGetPCIBusId(palDevice* pDevice, palInt32 length, palInt8* pPciBusId)
{
    palInt32 retLength = 0;

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pPciBusId != NULL);
    PAL_ASSERT(length > 0);

    // Format the PCI bus ID as a string.
    // The format is "domain:bus:device.function"
    retLength= palOsSnprintf(pPciBusId, length, "%04x:%02x:%02x.0",
        pDevice->pDeviceProperties->pciDomainID,
        pDevice->pDeviceProperties->pciBusID,
        pDevice->pDeviceProperties->pciDeviceID);
    if (retLength < 0 || retLength >= length)
    {
        PAL_DBG_PRINTF_ERROR("Failed to format PCI bus ID, buffer too small.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Ensure the string is null-terminated.
    pPciBusId[retLength] = '\0';

    return PAL_SUCCESS;

}

// =====================================================================================================================
palResult palDeviceRegisterAsyncNotification(palDevice* pDevice, palAsyncCallback callbackFunc,
                                             void* pUserData, palAsyncCallbackEntry* pEntry)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(callbackFunc != NULL);
    PAL_ASSERT(pUserData != NULL);
    PAL_ASSERT(pEntry != NULL);

    // TODO: Implement the logic to register the async notification.

    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palDeviceUnregisterAsyncNotification(palDevice* pDevice, palAsyncCallbackEntry* pEntry)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pEntry != NULL);

    // TODO: Implement the logic to unregister the async notification.

    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palDeviceCreateDeviceContext(palDevice* pDevice, palUint32 flags, void** ppKmdContext)
{
    palResult result      = PAL_SUCCESS;
    void*     pKmdContext = NULL;

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(ppKmdContext != NULL);

    // Create a new device context using the provided KMD context.
    result = palThunkDeviceContextCreate(pDevice->pKmdDevice, flags, &pKmdContext);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create device context for device ID %d.", pDevice->deviceId);
        return result;
    }

    *ppKmdContext = pKmdContext;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDeviceDestroyDeviceContext(palDevice* pDevice, void* pKmdContext)
{
    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pKmdContext != NULL);

    // Destroy the device context using the provided KMD context.
    return palThunkDeviceContextDestroy(pDevice->pKmdDevice, pKmdContext);
}

// =====================================================================================================================
palResult palDeviceGetMemInfo(palDevice* pDevice, size_t* pTotal, size_t* pFree)
{
    palResult           result     = PAL_SUCCESS;
    palDeviceMemoryInfo memoryInfo = {0};

    PAL_ASSERT(pDevice != NULL);

    // Get the memory information for the device.
    result = palThunkDeviceGetMemoryInfo(pDevice->pKmdDevice, &memoryInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to get memory info for device ID %d.", pDevice->deviceId);
        return result;
    }

    if (pTotal != NULL)
    {
        *pTotal = memoryInfo.totalPhysicalMemorySize;
    }

    if (pFree != NULL)
    {
        *pFree = memoryInfo.freePhysicalMemorySize;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palDevicePhysicalMemoryCreate(palDevice* pDevice, palContext* pContext, size_t size, palUint64 flags,
                                        void** ppPhysicalMemory)
{
    palThunkDevicePhysicalMemoryCreateInfo createInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(ppPhysicalMemory != NULL);

    createInfo.pContext = pContext->pKmdContext;
    createInfo.size     = size;
    createInfo.pageSize = pDevice->pDeviceProperties->physicalMemoryAlignment;
    createInfo.flags    = flags;
    return palThunkDevicePhysicalMemoryObjectCreate(pDevice->pKmdDevice, &createInfo, ppPhysicalMemory);
}

// =====================================================================================================================
palResult palDevicePhysicalMemoryDestroy(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryObject)
{
    palThunkDevicePhysicalMemoryDestroyInfo destroyInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pPhysicalMemoryObject != NULL);

    destroyInfo.pContext              = pContext->pKmdContext;
    destroyInfo.pPhysicalMemoryObject = pPhysicalMemoryObject;
    return palThunkDevicePhysicalMemoryObjectDestroy(pDevice->pKmdDevice, &destroyInfo);
}

// =====================================================================================================================
palResult palDevicePhysicalMemoryDeviceMap(palDevice* pDevice, palContext* pContext, palUint64 virtualAddress,
                                           void* pPhysicalMemoryObject, size_t size, palUint64 flags,
                                           void** ppPhysicalMemoryDeviceMapped)
{
    palThunkDevicePhysicalMemoryDeviceMapInfo mapInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(virtualAddress != 0);
    PAL_ASSERT(pPhysicalMemoryObject != NULL);
    PAL_ASSERT(ppPhysicalMemoryDeviceMapped != NULL);
    PAL_ASSERT(size > 0);

    mapInfo.pContext              = pContext->pKmdContext;
    mapInfo.deviceVirtualAddress  = virtualAddress;
    mapInfo.pPhysicalMemoryObject = pPhysicalMemoryObject;
    mapInfo.flags                 = flags;
    mapInfo.size                  = size;
    return palThunkDevicePhysicalMemoryObjectDeviceMap(pDevice->pKmdDevice, &mapInfo, ppPhysicalMemoryDeviceMapped);
}

// =====================================================================================================================
palResult palDevicePhysicalMemoryDeviceUnmap(palDevice* pDevice, palContext* pContext,
                                             void* pPhysicalMemoryDeviceMapped)
{
    palThunkDevicePhysicalMemoryDeviceUnmapInfo unmapInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pPhysicalMemoryDeviceMapped != NULL);

    unmapInfo.pContext                    = pContext->pKmdContext;
    unmapInfo.pPhysicalMemoryDeviceMapped = pPhysicalMemoryDeviceMapped;
    return palThunkDevicePhysicalMemoryObjectDeviceUnmap(pDevice->pKmdDevice, &unmapInfo);
}

/// ====================================================================================================================
palResult palDevicePhysicalMemoryHostMap(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryObject,
                                         palUint64 size, palUint64 flags, void** ppHostMapped,
                                         void** ppHostVirtualAddress)
{
    palThunkDevicePhysicalMemoryHostMapInfo mapInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pPhysicalMemoryObject != NULL);
    PAL_ASSERT(ppHostMapped != NULL);
    PAL_ASSERT(ppHostVirtualAddress != NULL);

    mapInfo.pContext              = pContext->pKmdContext;
    mapInfo.pPhysicalMemoryObject = pPhysicalMemoryObject;
    mapInfo.flags                 = 0; // No flags currently used.
    mapInfo.size                  = size;
    return palThunkDevicePhysicalMemoryObjectHostMap(pDevice->pKmdDevice, &mapInfo, ppHostMapped, ppHostVirtualAddress);
}

// =====================================================================================================================
palResult palDevicePhysicalMemoryHostUnmap(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryHostMapped)
{
    palThunkDevicePhysicalMemoryHostUnmapInfo unmapInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pPhysicalMemoryHostMapped != NULL);

    unmapInfo.pContext                  = pContext->pKmdContext;
    unmapInfo.pPhysicalMemoryHostMapped = pPhysicalMemoryHostMapped;
    return palThunkDevicePhysicalMemoryObjectHostUnmap(pDevice->pKmdDevice, &unmapInfo);
}

// =====================================================================================================================
palResult palDevicePhysicalMemoryExportByDmaBuf(palDevice* pDevice, palContext* pContext, void* pPhysicalMemoryObject,
                                                palUint64 size, palUint64 pageSize, palUint64 flags, palInt32* pDmaBufFd)
{
    palThunkDevicePhysicalMemoryExportInfo exportInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pPhysicalMemoryObject != NULL);
    PAL_ASSERT(pDmaBufFd != NULL);

    exportInfo.pContext              = pContext->pKmdContext;
    exportInfo.pPhysicalMemoryObject = pPhysicalMemoryObject;
    exportInfo.size                 = size;
    exportInfo.pageSize             = pageSize;
    exportInfo.flags                = flags;
    return palThunkDevicePhysicalMemoryObjectExportByDmaBuf(pDevice->pKmdDevice, &exportInfo, pDmaBufFd);
}

// =====================================================================================================================
palResult palDevicePhysicalMemoryImportFromDmaBuf(palDevice* pDevice, palContext* pContext, palInt32 dmaBufFd,
                                                  void** ppRemoteContext, void** ppPhysicalMemoryObject,
                                                  palUint64* pSize, palUint64* pPageSize, palUint64* pFlags)
{
    palThunkDevicePhysicalMemoryImportInfo importInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(ppPhysicalMemoryObject != NULL);

    importInfo.pContext = pContext->pKmdContext;
    importInfo.dmaBufFd = dmaBufFd;
    return palThunkDevicePhysicalMemoryObjectImportFromDmaBuf(pDevice->pKmdDevice, &importInfo, ppRemoteContext,
                                                              ppPhysicalMemoryObject, pSize, pPageSize, pFlags);
}

// =====================================================================================================================
palResult palDeviceHostPinnedMemoryCreate(palDevice* pDevice, palContext* pContext, size_t size, palUint64 flags,
                                          void** ppPinnedMemoryObject, void** ppHostVirtualAddress)
{
    palThunkHostPinnedMemoryCreateInfo createInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(ppPinnedMemoryObject != NULL);
    PAL_ASSERT(ppHostVirtualAddress != NULL);

    createInfo.pContext = pContext->pKmdContext;
    createInfo.size     = size;
    createInfo.flags    = flags;
    return palThunkDeviceHostPinnedMemoryObjectCreate(pDevice->pKmdDevice, &createInfo, ppPinnedMemoryObject,
                                                      ppHostVirtualAddress);
}

// =====================================================================================================================
palResult palDeviceHostPinnedMemoryDestroy(palDevice* pDevice, palContext* pContext, void* pPinnedMemoryObject)
{
    palThunkHostPinnedMemoryDestroyInfo destroyInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pPinnedMemoryObject != NULL);

    destroyInfo.pContext                = pContext->pKmdContext;
    destroyInfo.pHostPinnedMemoryObject = pPinnedMemoryObject;
    return palThunkDeviceHostPinnedMemoryObjectDestroy(pDevice->pKmdDevice, &destroyInfo);
}

// =====================================================================================================================
palResult palDeviceHostPageableMemoryRegister(palDevice* pDevice, palContext* pContext, void* pHostPtr, palUint64 size,
                                              palUint64 flags, void** ppHostRegisteredMemoryObject,
                                              palUint64* pRegisteredOffset)
{
    palThunkHostPageableMemoryRegisterInfo registerInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pHostPtr != NULL);
    PAL_ASSERT(size > 0);
    PAL_ASSERT(ppHostRegisteredMemoryObject != NULL);

    registerInfo.pContext = pContext->pKmdContext;
    registerInfo.pHostPtr = pHostPtr;
    registerInfo.size     = size;
    registerInfo.flags    = flags;
    return palThunkDeviceHostPageableMemoryObjectRegister(pDevice->pKmdDevice, &registerInfo,
                                                          ppHostRegisteredMemoryObject, pRegisteredOffset);
}

// =====================================================================================================================
palResult palDeviceHostPageableMemoryUnregister(palDevice* pDevice, palContext* pContext, void* pHostPtr,
                                                void* pHostRegisteredMemoryObject)
{
    palThunkHostPageableMemoryUnregisterInfo unregisterInfo = {0};

    PAL_ASSERT(pDevice != NULL);
    PAL_ASSERT(pHostPtr != NULL);
    PAL_ASSERT(pHostRegisteredMemoryObject != NULL);

    unregisterInfo.pContext                    = pContext->pKmdContext;
    unregisterInfo.pHostPtr                    = pHostPtr;
    unregisterInfo.pHostRegisteredMemoryObject = pHostRegisteredMemoryObject;
    return palThunkDeviceHostPageableMemoryObjectUnregister(pDevice->pKmdDevice, &unregisterInfo);
}