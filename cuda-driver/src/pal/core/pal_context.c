#include "pal_assert.h"
#include "pal_context.h"
#include "pal_device.h"
#include "pal_devicemanager.h"
#include "pal_globalmanager.h"
#include "pal_list.h"
#include "pal_memorymanager.h"
#include "pal_rbtree.h"
#include "pal_thunk.h"
#include "pal_tlsthreaddata.h"

// =====================================================================================================================
palResult palContextCreate(palContext** ppCtx, palDevice* pDevice, palContextCreateInfo* pCreateInfo)
{
    palResult   result = PAL_SUCCESS;
    palContext* pCtx   = NULL;

    if ((ppCtx == NULL) || (pDevice == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pCtx = (palContext*)malloc(sizeof(palContext));
    if (pCtx == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for context.");
        *ppCtx = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palContextInitialize(pCtx, pDevice, pCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize context.");
        free(pCtx);
        *ppCtx = NULL;
        return result;
    }

    *ppCtx = pCtx;

    return result;
}

// =====================================================================================================================
palResult palContextPersistentStateInitialize(palContext* pCtx, palDevice* pDevice, palBool isPrimary)
{
    palInt32 error;

    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(pDevice != NULL);

    memset(pCtx, 0, sizeof(palContext));

    // Initialize the persistent state of the context here.
    pCtx->persistentState.status = PAL_CONTEXT_STATUS_NOT_INITIALIZED;
    pCtx->persistentState.pDevice = pDevice;
    pCtx->persistentState.isPrimary = isPrimary;
    pCtx->persistentState.threadCtxStackRefCount = 0;
    pCtx->persistentState.attachRefCount = 0;

    // Initialize the mutex for the context state operations.
    error = palOsMutexInit(&pCtx->persistentState.mutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create mutex for context state operations.");
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    pCtx->persistentState.contextId = palOsAtomicFetchAndIncrementSeqCst64(
        (_Atomic palUint64*)&pDevice->pDeviceManager->pGlobalManager->contextIdCounter);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palContextInitialize(palContext* pCtx, palDevice* pDevice, palContextCreateInfo* pCreateInfo)
{
    palResult   result  = PAL_SUCCESS;
    palInt32    error   = 0;
    palContext* pTmpCtx = NULL;

    if ((pCtx == NULL) || (pDevice == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pCreateInfo->isPrimary == PAL_FALSE)
    {
        // Non-primary context should initialize persistent state.
        // Primary context persistent state initialization is intentionally omitted here
        // because it is handled during device initialization.
        result = palContextPersistentStateInitialize(pCtx, pDevice, pCreateInfo->isPrimary);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to initialize persistent state.");
            memset(pCtx, 0, sizeof(palContext));
            return result;
        }
    }

    // Create the kmd context handle.
    result = palDeviceCreateDeviceContext(pDevice, pCtx->currentFlags, &pCtx->pKmdContext);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create kmd context handle for device ID %d.", pDevice->deviceId);
        if (pCreateInfo->isPrimary == PAL_FALSE)
        {
            // If this is a non-primary context, we need to deinitialize the persistent state.
            palContextPersistentStateDeinitialize(pCtx);
        }
        memset(pCtx, 0, sizeof(palContext));
        return result;
    }

    // Create the memory manager for the context.
    result = palMemoryManagerCreate(&pCtx->pMemoryManager, pCtx, PAL_FALSE);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create memory manager for context.");
        palDeviceDestroyDeviceContext(pDevice, pCtx->pKmdContext);
        if (pCreateInfo->isPrimary == PAL_FALSE)
        {
            // If this is a non-primary context, we need to deinitialize the persistent state.
            palContextPersistentStateDeinitialize(pCtx);
        }
        memset(pCtx, 0, sizeof(palContext));
        return result;
    }

    pCtx->lowPriority  = PAL_CTX_STREAM_PRIORITY_TYPE_LOW;
    pCtx->highPriority = PAL_CTX_STREAM_PRIORITY_TYPE_HIGH;
    pCtx->apiVersion   = PAL_CTX_API_VERSION_DEFAULT;
    pCtx->funcCache    = PAL_FUNC_CACHE_PREFER_NONE;
    pCtx->sharedConfig = PAL_SHARED_MEM_CONFIG_DEFAULT_BANK_SIZE;

    palOsAtomicFetchAndIncrementSeqCst32(&pCtx->persistentState.threadCtxStackRefCount);
    palOsAtomicFetchAndIncrementSeqCst32(&pCtx->persistentState.attachRefCount);

    // Initialize the context resources.
    palOsMutexLock(&pCtx->persistentState.mutex);
    pCtx->persistentState.status = PAL_CONTEXT_STATUS_INITIALIZING;

    // TODO: Perform any additional initialization here.
    palOsMutexLock(&pDevice->pDeviceManager->activeContextsMutex);
    PAL_LIST_INSERT_PREV_NEXT(pDevice->pDeviceManager->pActiveContextsList, pCtx, pTmpCtx, pPrev, pNext);
    palOsMutexUnlock(&pDevice->pDeviceManager->activeContextsMutex);

    pCtx->persistentState.status = PAL_CONTEXT_STATUS_INITIALIZED;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palContextPersistentStateDeinitialize(palContext* pCtx)
{
    // Deinitialize the persistent state of the context here.
    // For example, if there are any allocated buffers or handles, free them.
    // make sure that this context is no longer in TLS
    PAL_ASSERT(pCtx->persistentState.threadCtxStackRefCount == 0);

    // make sure that this context is the right state
    PAL_ASSERT(pCtx->persistentState.status == PAL_CONTEXT_STATUS_NOT_INITIALIZED);

    palOsMutexDestroy(&pCtx->persistentState.mutex);

    // zero out and free whole context structure
    memset(pCtx, 0, sizeof(palContext));
}

// =====================================================================================================================
void palContextDeinitialize(palContext* pCtx)
{
    palDevice* pDevice = NULL;

    PAL_ASSERT(pCtx != NULL);

    if (pCtx->persistentState.status != PAL_CONTEXT_STATUS_INITIALIZED)
    {
        return;
    }

    pDevice = pCtx->persistentState.pDevice;

    // Deinitialize any resources associated with the context here.
    palOsMutexLock(&pCtx->persistentState.mutex);
    pCtx->persistentState.status = PAL_CONTEXT_STATUS_DEINITIALIZING;

    palOsMutexLock(&pDevice->pDeviceManager->activeContextsMutex);
    PAL_LIST_REMOVE_PREV_NEXT(pDevice->pDeviceManager->pActiveContextsList, pCtx, pPrev, pNext);
    palOsMutexUnlock(&pDevice->pDeviceManager->activeContextsMutex);

    // Destroy the memory manager associated with the context.
    if (pCtx->pMemoryManager != NULL)
    {
        palMemoryManagerDestroy(pCtx->pMemoryManager);
        pCtx->pMemoryManager = NULL;
    }

    // Destroy the kmd context handle.
    if (pCtx->pKmdContext != NULL)
    {
        palDeviceDestroyDeviceContext(pDevice, pCtx->pKmdContext);
        pCtx->pKmdContext = NULL;
    }

    pCtx->persistentState.status = PAL_CONTEXT_STATUS_NOT_INITIALIZED;
    palOsMutexUnlock(&pCtx->persistentState.mutex);
}

// =====================================================================================================================
void palContextDestroy(palContext* pCtx)
{
    PAL_ASSERT(pCtx != NULL);

    // Deinitialize the context.
    palContextDeinitialize(pCtx);

    // Context state is deinitialized now. We can free the context
    // if the threadCtxStackRefCount reaches 0.
    palContextThreadRefCountDecrement(pCtx);
}

// =====================================================================================================================
void palContextThreadRefCountIncrement(palContext* pCtx)
{
    PAL_ASSERT(pCtx != NULL);

    palOsAtomicFetchAndIncrementSeqCst32(&pCtx->persistentState.threadCtxStackRefCount);
}

// =====================================================================================================================
void palContextThreadRefCountDecrement(palContext* pCtx)
{
    palUint32 oldRefCount = 0;

    PAL_ASSERT(pCtx != NULL);

    oldRefCount = palOsAtomicFetchAndDecrementSeqCst32(&pCtx->persistentState.threadCtxStackRefCount);
    // Don't free context if it is current to any thread or the context state is initialized,
    // that is, threadCtxStackRefCount > 0
    if (oldRefCount != 1)
    {
        return;
    }

    PAL_ASSERT(pCtx->persistentState.status == PAL_CONTEXT_STATUS_NOT_INITIALIZED);

    if (pCtx->persistentState.isPrimary == PAL_TRUE)
    {
        // Don't free primary contexts, just return early.
        return;
    }

    // Deinitialize the persistent state of the context.
    palContextPersistentStateDeinitialize(pCtx);

    // Free the context handle.
    free(pCtx);
}

// =====================================================================================================================
void palContextAttachRefCountIncrement(palContext* pCtx)
{
    PAL_ASSERT(pCtx != NULL);

    if (pCtx->persistentState.isPrimary == PAL_TRUE)
    {
        // Primary context attach reference count is not incremented.
        PAL_DBG_PRINTF_WARN("Attempting to increment attach reference count for primary context.");
        return;
    }

    palOsAtomicFetchAndIncrementSeqCst32(&pCtx->persistentState.attachRefCount);
}

// =====================================================================================================================
palResult palContextAttachRefCountDecrement(palContext* pCtx, palTlsThreadData* pTlsThreadData)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(pTlsThreadData != NULL);

    // Decrement the attach reference count for the context.
    if (pCtx->persistentState.isPrimary == PAL_TRUE)
    {
        // Primary context attach reference count is not incremented.
        PAL_DBG_PRINTF_WARN("Attempting to decrement attach reference count for primary context.");
        return PAL_SUCCESS;
    }

    if (palOsAtomicFetchAndDecrementSeqCst32(&pCtx->persistentState.attachRefCount) == 1)
    {
        // If we dropped the reference count to 0, destroy the context and pop it.
        palContextDestroy(pCtx);

        // Pop the current context from the thread data.
        result = palTlsThreadDataPopCurrentContext(pTlsThreadData);
        PAL_ASSERT(result == PAL_SUCCESS);
    }

    return result;
}

// =====================================================================================================================
palBool palContextIsActive(palContext* pCtx)
{
    palBool isActive = PAL_FALSE;

    PAL_ASSERT(pCtx != NULL);

    // Lock the mutex to safely access the persistent state of the context.
    palOsMutexLock(&pCtx->persistentState.mutex);
    isActive = (pCtx->persistentState.status == PAL_CONTEXT_STATUS_INITIALIZED) ? PAL_TRUE : PAL_FALSE;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    // Check if the context is active by verifying its persistent state status.
    return isActive;
}

// =====================================================================================================================
palDevice* palContextGetDevice(palContext* pCtx)
{
    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(pCtx->persistentState.pDevice != NULL);

    // Return the device associated with the context.
    return pCtx->persistentState.pDevice;
}

// =====================================================================================================================
palResult palContextGetExecAffinity(palContext* pCtx, palExecAffinityType execAffinityType,
                                    palExecAffinityParam* pExecAffinityParams)
{
    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(execAffinityType != PAL_EXEC_AFFINITY_TYPE_MAX);
    PAL_ASSERT(pExecAffinityParams != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    // TODO: We need to calculate the execution affinity parameters based on the context and device.
    *pExecAffinityParams = pCtx->execAffinityParam;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    // Return the execution affinity parameters and type from the persistent state of the context.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palContextSetFlags(palContext* pCtx, palUint32 flags)
{
    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    pCtx->currentFlags = flags;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palCtxApiVersion palContextGetApiVersion(palContext* pCtx)
{
    palCtxApiVersion apiVersion = PAL_CTX_API_VERSION_DEFAULT;

    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    apiVersion = pCtx->apiVersion;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return apiVersion;
}

// =====================================================================================================================
palResult palContextSetCacheConfig(palContext* pCtx, palFuncCache funcCache)
{
    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(funcCache <= PAL_FUNC_CACHE_PREFER_EQUAL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    pCtx->funcCache = funcCache;
    // TODO: Implement logic to apply the cache configuration to the device.
    // This might involve calling device-specific APIs to set the cache configuration.
    // For now, we just store the preference in the context.
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palFuncCache palContextGetCacheConfig(palContext* pCtx)
{
    palFuncCache funcCache = PAL_FUNC_CACHE_PREFER_NONE;

    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    funcCache = pCtx->funcCache;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return funcCache;
}

// =====================================================================================================================
palInt32 palContextGetDeviceId(palContext* pCtx)
{
    palInt32 deviceId = -1;

    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(pCtx->persistentState.pDevice != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    deviceId = pCtx->persistentState.pDevice->deviceId;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    // Return the device ID from the persistent state of the context.
    return deviceId;
}

// =====================================================================================================================
palUint32 palContextGetFlags(palContext* pCtx)
{
    palUint32 flags = 0;

    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    flags = pCtx->currentFlags;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return flags;
}

// =====================================================================================================================
palUint64 palContextGetContextId(palContext* pCtx)
{
    palUint64 contextId = 0;

    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    contextId = pCtx->persistentState.contextId;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return contextId;
}

palResult palContextSetLimit(palContext* pCtx, palCtxLimit limit, palUint64 value)
{
    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);

    // TODO:

    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS; // Placeholder, as the actual implementation is not provided.
}

// =====================================================================================================================
palResult palContextGetLimit(palContext* pCtx, palCtxLimit limit, palUint64* pValue)
{
    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(pValue != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);

    switch (limit)
    {
    case PAL_CTX_LIMIT_STACK_SIZE:
        *pValue = 0; // TODO: Implement stack size limit retrieval.
        break;
    case PAL_CTX_LIMIT_PRINTF_FIFO_SIZE:
        *pValue = 0; // TODO: Implement printf FIFO size limit retrieval.
        break;
    case PAL_CTX_LIMIT_MALLOC_HEAP_SIZE:
        *pValue = 0; // TODO: Implement malloc heap size limit retrieval.
        break;
    case PAL_CTX_LIMIT_DEV_RUNTIME_SYNC_DEPTH:
        *pValue = 0; // TODO: Implement device runtime sync depth limit retrieval.
        break;
    case PAL_CTX_LIMIT_DEV_RUNTIME_PENDING_LAUNCH_COUNT:
        *pValue = 0; // TODO: Implement device runtime pending launch count limit retrieval.
        break;
    case PAL_CTX_LIMIT_MAX_L2_FETCH_GRANULARITY:
        *pValue = 128; // Example value, should be replaced with actual implementation.
        break;
    case PAL_CTX_LIMIT_PERSISTING_L2_CACHE_SIZE:
        *pValue = 0; // TODO: Implement persisting L2 cache size limit retrieval.
        break;
    default:
        palOsMutexUnlock(&pCtx->persistentState.mutex);
        return PAL_ERROR_INVALID_VALUE;
    }

    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palContextGetStreamPriorityRange(palContext* pCtx,
                                           palCtxStreamPriorityType* pLowPriority,
                                           palCtxStreamPriorityType* pHighPriority)
{
    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(pLowPriority != NULL);
    PAL_ASSERT(pHighPriority != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    *pLowPriority  = pCtx->lowPriority;
    *pHighPriority = pCtx->highPriority;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palContextSynchronize(palContext* pCtx)
{
    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);

    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS; // Placeholder, as the actual implementation is not provided.
}

// =====================================================================================================================
palResult palContextResetPersistingL2Cache(palContext* pCtx)
{
    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);

    // TODO: Implement the logic to reset the persisting L2 cache.

    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palContextSetSharedMemConfig(palContext* pCtx, palSharedConfig sharedConfig)
{
    PAL_ASSERT(pCtx != NULL);
    PAL_ASSERT(sharedConfig <= PAL_SHARED_MEM_CONFIG_EIGHT_BYTE_BANK_SIZE);

    palOsMutexLock(&pCtx->persistentState.mutex);
    pCtx->sharedConfig = sharedConfig;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return PAL_SUCCESS; // Placeholder, as the actual implementation is not provided.
}

// =====================================================================================================================
palSharedConfig palContextGetSharedMemConfig(palContext* pCtx)
{
    palSharedConfig sharedConfig = PAL_SHARED_MEM_CONFIG_DEFAULT_BANK_SIZE;

    PAL_ASSERT(pCtx != NULL);

    palOsMutexLock(&pCtx->persistentState.mutex);
    sharedConfig = pCtx->sharedConfig;
    palOsMutexUnlock(&pCtx->persistentState.mutex);

    return sharedConfig;
}

// =====================================================================================================================
palResult palContextFlushGPUDirectRDMAWrites(palContext* pCtx, palFlushGPUDirectRDMAWritesTarget target,
                                             palFlushGPUDirectRDMAWritesScope scope)
{
    palDevice* pDevice = NULL;

    PAL_ASSERT(pCtx != NULL);

    pDevice = pCtx->persistentState.pDevice;
    PAL_ASSERT(pDevice != NULL);

    if (pDevice->pDeviceProperties->gpuDirectRDMAFlushWritesOptions == 0)
    {
        PAL_DBG_PRINTF_ERROR("GPU Direct RDMA writes flushing is not supported on this device.");
        return PAL_ERROR_NOT_SUPPORTED;
    }

    // TODO: Implement the logic to flush GPU Direct RDMA writes.
    // This might involve calling device-specific APIs to ensure that remote writes are visible
    // to the CUDA device context owning the data or all CUDA device contexts, depending on the scope.
    // For now, we just log the target and scope.
    PAL_DBG_PRINTF_INFO("Flushing GPU Direct RDMA writes with target %d and scope %d.", scope, target);

    return PAL_SUCCESS; // Placeholder, as the actual implementation is not provided.
}