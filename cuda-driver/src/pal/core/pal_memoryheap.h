#ifndef PAL_MEMORYHEAP_H_
#define PAL_MEMORYHEAP_H_

#include "pal.h"
#include "pal_memoryflags.h"
#include "pal_types.h"
#include "pal_structures.h"

typedef enum palMemoryHeapType_enum
{
    PAL_MEMORY_HEAP_TYPE_DEVICE = 0, /**< Device local memory heap type */
    PAL_MEMORY_HEAP_TYPE_PINNED,     /**< GTT memory heap type */
    PAL_MEMORY_HEAP_TYPE_UVA,        /**< UVA memory heap type */
    PAL_MEMORY_HEAP_TYPE_HIDDEN,     /**< Device kernel hidden memory heap type */
    PAL_MEMORY_HEAP_TYPE_MAX         /**< Maximum memory heap type */
} palMemoryHeapType;

struct palMemoryHeap_st
{
    // The memory manager associated with the memory heap.
    palMemoryManager* pMemoryManager;

    // The type of the memory heap.
    palMemoryHeapType heapType;

    // The mutex for protecting the memory allocation and deallocation of memory blocks.
    palOsMutex mutex;

    // The mutex for protecting the memory block map.
    palOsMutex memoryBlockMutex;

    // The used memory block map for the heap (key: VirtAddress, value: palMemoryBlock*).
    palRbTree* pUsedMemoryBlockMap;

    // The free memory block map for the heap (key: VirtAddress, value: palMemoryBlock*)
    palRbTree* pFreeMemoryBlockMap;
};

/// @brief Create a memory heap
///
/// @param ppMemoryHeap Pointer to the memory heap to create
/// @param pMemoryManager The memory manager to associate with the memory heap
/// @param heapType The type of the memory heap to create
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapCreate(palMemoryHeap** ppMemoryHeap, palMemoryManager* pMemoryManager, palMemoryHeapType heapType);

/// @brief Initialize a memory heap
///
/// @param pMemoryHeap The memory heap to initialize
/// @param pMemoryManager The memory manager to associate with the memory heap
/// @param heapType The type of the memory heap to initialize
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapInitialize(palMemoryHeap* pMemoryHeap, palMemoryManager* pMemoryManager, palMemoryHeapType heapType);

/// @brief Deinitialize a memory heap
/// @param pMemoryHeap The memory heap to deinitialize
void palMemoryHeapDeinitialize(palMemoryHeap* pMemoryHeap);

/// @brief Destroy a memory heap
///
/// @param pMemoryHeap The memory heap to destroy
void palMemoryHeapDestroy(palMemoryHeap* pMemoryHeap);

/// @brief Add a free memory block to the memory heap
///
/// @param pMemoryHeap The memory heap to add the free memory block to
/// @param pMemoryBlock The memory block to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapAddFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock);

/// @brief Remove a free memory block from the memory heap
///
/// @param pMemoryHeap The memory heap to remove the memory block from
/// @param pMemoryBlock The memory block to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapRemoveFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock);

/// @brief Get a free memory block from the memory heap
///
/// @param pMemoryHeap The memory heap to get the free memory block from
/// @param size The size of the memory block to get
/// @param alignment The alignment size of the memory block to get
/// @param ppMemoryBlock Pointer to store the free memory block
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapGetFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palUint64 size, palUint64 alignment,
                                          palMemoryBlock** ppMemoryBlock);

/// @brief Add a used memory block to the memory heap
/// Note: This function will move the memory block from the free memory block map to the used memory block map.
///
/// @param pMemoryHeap The memory heap to add the used memory block to
/// @param pMemoryBlock The memory block to add
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapAddUsedMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock);

/// @brief Remove a used memory block from the memory heap
/// Note: This function will move the memory block from the used memory block map to the free memory block map.
///
/// @param pMemoryHeap The memory heap to remove the memory block from
/// @param pMemoryBlock The memory block to remove
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapRemoveUsedMemoryBlock(palMemoryHeap* pMemoryHeap, palMemoryBlock* pMemoryBlock);

/// @brief Get a free memory chunk from a used memory block in the memory heap
/// @param pMemoryHeap The memory heap to get the free memory chunk from
/// @param size The size of the memory chunk to get
/// @param alignment The alignment size of the memory chunk to get
/// @param flags The memory flags for the memory chunk to get
/// @param ppMemoryChunk Pointer to store the free memory chunk
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapGetFreeMemoryChunkFromUsedMemoryBlock(palMemoryHeap* pMemoryHeap, palUint64 size, palUint64 alignment,
                                                             palMemoryFlags flags, palMemoryChunk** ppMemoryChunk);

/// @brief Get a free memory chunk from a free memory block in the memory heap
///
/// @param pMemoryHeap The memory heap to get the free memory chunk from
/// @param size The size of the memory chunk to get
/// @param alignment The alignment size of the memory chunk to get
/// @param flags The memory flags for the memory chunk to get
/// @param ppMemoryChunk Pointer to store the free memory chunk
/// @return Returns PAL_SUCCESS on success, or an error code on failure
palResult palMemoryHeapGetFreeMemoryChunkFromFreeMemoryBlock(palMemoryHeap* pMemoryHeap, palUint64 size, palUint64 alignment,
                                                             palMemoryFlags flags, palMemoryChunk** ppMemoryChunk);

/// @brief Destroy all free memory blocks in the memory heap
///
/// @param pMemoryHeap The memory heap to destroy all free memory blocks from
void palMemoryHeapDestroyAllFreeMemoryBlocks(palMemoryHeap* pMemoryHeap);

/// @brief Create a memory object in the memory heap
///
/// @param pMemoryHeap The memory heap to create the memory object in
/// @param pContext The context to create the memory object for
/// @param size The size of the memory object to create
/// @param memFlags The memory flags for the memory object
/// @param hostFlags The host flags for the memory object
/// @param pHostPtr The host pointer for the memory object (optional)
/// @param ppMemoryObject Pointer to store the created memory object
palResult palMemoryHeapCreateMemoryObject(palMemoryHeap* pMemoryHeap, palContext* pContext, palUint64 size,
                                          palMemoryFlags memFlags, palUint64 hostFlags, void* pHostPtr,
                                          palMemoryObject** ppMemoryObject);

#endif // PAL_MEMORYHEAP_H_