#ifndef PAL_GLOBAL_MANAGER_H_
#define PAL_GLOBAL_MANAGER_H_

#include "pal.h"
#include "pal_types.h"
#include "pal_structures.h"
#include "g_pal_settings.h"

typedef enum palGlobalManagerStatus_enum
{
    PAL_GLOBAL_MANAGER_STATUS_NOT_INITIALIZED = 0,
    PAL_GLOBAL_MANAGER_STATUS_INITIALIZED,
    PAL_GLOBAL_MANAGER_STATUS_DEINITIALIZED,
    PAL_GLOBAL_MANAGER_STATUS_MAX
} palGlobalManagerStatus;

struct palGlobalManager_st
{
    // The status of the global manager
    volatile _Atomic palUint32 status;

    // The initialized mutex.
    palOsMutex initMutex;

    // Pointer to the settings
    palSettings* pSettings;

    // Pointer to the settings file manager
    palSettingsFileMgr* pSettingsFileMgr;

    // Pointer to the device manager
    palDeviceManager* pDeviceManager;

    // Pointer to the thread manager
    palTlsThreadManager* pThreadManager;

    // Pointer to the UVA manager
    palUvaManager* pUvaManager;

    // The counter for the number of contexts
    palUint64 contextIdCounter;
};

extern palGlobalManager g_globalManager;

/// @brief Initialize the global manager
/// This function initializes the global manager, which is responsible for managing global resources
/// and settings for the application.
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palGlobalManagerInitialize(void);

/// @brief Deinitialize the global manager
/// This function deinitializes the global manager, releasing any resources it holds.
void palGlobalManagerDeinitialize(void);

/// @brief Get the status of the global manager
/// This function retrieves the current status of the global manager.
/// @return PAL_SUCCESS if the global manager is initialized, PAL_ERROR_NOT_INITIALIZED if it is not initialized,
palResult palGlobalManagerGetStatus(void);

#endif // PAL_GLOBAL_MANAGER_H_