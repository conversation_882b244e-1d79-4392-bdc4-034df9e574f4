#include "pal_assert.h"
#include "pal_device.h"
#include "pal_memorychunk.h"
#include "pal_memoryheap.h"
#include "pal_memorymanager.h"
#include "pal_memoryobject.h"
#include "pal_memorypool.h"
#include "pal_rbtree.h"

// =====================================================================================================================
palResult palMemoryManagerCreate(palMemoryManager** ppMemoryManager, void* pParent, palBool isDevice)
{
    palResult         result         = PAL_SUCCESS;
    palMemoryManager* pMemoryManager = NULL;

    if ((ppMemoryManager == NULL) || (pParent == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pMemoryManager = (palMemoryManager*)malloc(sizeof(*pMemoryManager));
    if (pMemoryManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for memory manager.");
        *ppMemoryManager = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palMemoryManagerInitialize(pMemoryManager, pParent, isDevice);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory manager.");
        free(pMemoryManager);
        *ppMemoryManager = NULL;
        return result;
    }

    *ppMemoryManager = pMemoryManager;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerInitialize(palMemoryManager* pMemoryManager, void* pParent, palBool isDevice)
{
    palResult result = PAL_SUCCESS;
    palInt32  error  = 0;

    if ((pMemoryManager == NULL) || (pParent == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pMemoryManager, 0, sizeof(palMemoryManager));

    pMemoryManager->isDevice = isDevice;

    error = palOsMutexInit(&pMemoryManager->memoryManagerMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory manager mutex.");
        free(pMemoryManager);
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    error = palOsMutexInit(&pMemoryManager->memoryObjectMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory object mutex.");
        palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
        memset(pMemoryManager, 0, sizeof(palMemoryManager));
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    result = palRbTreeCreate(&pMemoryManager->pMemoryObjectDeviceMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create memory object tree.");
        palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);
        palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
        memset(pMemoryManager, 0, sizeof(palMemoryManager));
        return result;
    }

    result = palRbTreeCreate(&pMemoryManager->pMemoryObjectHostMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create host memory object tree.");
        palRbTreeDestroy(pMemoryManager->pMemoryObjectDeviceMap);
        palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);
        palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
        memset(pMemoryManager, 0, sizeof(palMemoryManager));
        return result;
    }

    if (isDevice == PAL_TRUE)
    {
        pMemoryManager->pDevice = (palDevice*)pParent;

        error = palOsMutexInit(&pMemoryManager->memoryPoolMutex);
        if (error != 0)
        {
            PAL_DBG_PRINTF_ERROR("Failed to initialize memory pool mutex.");
            palRbTreeDestroy(pMemoryManager->pMemoryObjectHostMap);
            palRbTreeDestroy(pMemoryManager->pMemoryObjectDeviceMap);
            palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);
            palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
            memset(pMemoryManager, 0, sizeof(pMemoryManager));
            return PAL_ERROR_OPERATING_SYSTEM;
        }

        result = palRbTreeCreate(&pMemoryManager->pMemoryPoolMap, NULL);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to create memory pool tree.");
            palOsMutexDestroy(&pMemoryManager->memoryPoolMutex);
            palRbTreeDestroy(pMemoryManager->pMemoryObjectHostMap);
            palRbTreeDestroy(pMemoryManager->pMemoryObjectDeviceMap);
            palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);
            palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
            memset(pMemoryManager, 0, sizeof(palMemoryManager));
            return result;
        }
    }
    else
    {
        pMemoryManager->pContext = (palContext*)pParent;

        error = palOsMutexInit(&pMemoryManager->memoryHeapMutex);
        if (error != 0)
        {
            PAL_DBG_PRINTF_ERROR("Failed to initialize memory heap mutex.");
            palRbTreeDestroy(pMemoryManager->pMemoryObjectHostMap);
            palRbTreeDestroy(pMemoryManager->pMemoryObjectDeviceMap);
            palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);
            palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
            memset(pMemoryManager, 0, sizeof(palMemoryManager));
            return PAL_ERROR_OPERATING_SYSTEM;
        }

        result = palRbTreeCreate(&pMemoryManager->pMemoryHeapMap, NULL);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to create memory heap tree.");
            palOsMutexDestroy(&pMemoryManager->memoryHeapMutex);
            palRbTreeDestroy(pMemoryManager->pMemoryObjectHostMap);
            palRbTreeDestroy(pMemoryManager->pMemoryObjectDeviceMap);
            palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);
            palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
            memset(pMemoryManager, 0, sizeof(palMemoryManager));
            return result;
        }

        // Create the memory heaps for the memory manager.
        for (palInt32 heapType = PAL_MEMORY_HEAP_TYPE_DEVICE; heapType < PAL_MEMORY_HEAP_TYPE_MAX; ++heapType)
        {
            palMemoryHeap* pMemoryHeap = NULL;
            result = palMemoryHeapCreate(&pMemoryHeap, pMemoryManager, (palMemoryHeapType)heapType);
            if (result != PAL_SUCCESS)
            {
                PAL_DBG_PRINTF_ERROR("Failed to create memory heap of type %d.", heapType);
                for (palInt32 i = PAL_MEMORY_HEAP_TYPE_DEVICE; i < heapType; ++i)
                {
                    palMemoryHeap* pHeap = NULL;
                    palMemoryManagerGetMemoryHeap(pMemoryManager, (palMemoryHeapType)i, &pHeap);
                    if (pHeap != NULL)
                    {
                        palMemoryHeapDestroy(pHeap);
                    }
                }
                // Clean up the memory heap map and mutexes before returning.
                palRbTreeDestroy(pMemoryManager->pMemoryHeapMap);
                palOsMutexDestroy(&pMemoryManager->memoryHeapMutex);
                palRbTreeDestroy(pMemoryManager->pMemoryObjectHostMap);
                palRbTreeDestroy(pMemoryManager->pMemoryObjectDeviceMap);
                palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);
                palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);
                memset(pMemoryManager, 0, sizeof(palMemoryManager));
                return result;
            }
        }
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
void palMemoryManagerDeinitialize(palMemoryManager* pMemoryManager)
{
    if (pMemoryManager == NULL)
    {
        return;
    }

    // Remove all memory objects associated with the context.
    palOsMutexLock(&pMemoryManager->memoryObjectMutex);
    if (pMemoryManager->pMemoryObjectDeviceMap != NULL)
    {
        // Destroy the memory object map tree.
        while (palRbTreeGetSize(pMemoryManager->pMemoryObjectDeviceMap) > 0)
        {
            // Remove the first element from the tree.
            palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryManager->pMemoryObjectDeviceMap);
            PAL_ASSERT(pNode != NULL);
            palMemoryObjectRelease((palMemoryObject*)pNode->value);
        }

        palRbTreeDestroy(pMemoryManager->pMemoryObjectDeviceMap);
        pMemoryManager->pMemoryObjectDeviceMap = NULL;
    }

    if (pMemoryManager->pMemoryObjectHostMap != NULL)
    {
        // Destroy the host memory object map tree.
        while (palRbTreeGetSize(pMemoryManager->pMemoryObjectHostMap) > 0)
        {
            // Remove the first element from the tree.
            palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryManager->pMemoryObjectHostMap);
            PAL_ASSERT(pNode != NULL);
            palMemoryObjectRelease((palMemoryObject*)pNode->value);
        }

        palRbTreeDestroy(pMemoryManager->pMemoryObjectHostMap);
        pMemoryManager->pMemoryObjectHostMap = NULL;
    }
    palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);
    palOsMutexDestroy(&pMemoryManager->memoryObjectMutex);

    if (pMemoryManager->isDevice == PAL_TRUE)
    {
        // Remove all memory pools associated with the context.
        // This will also destroy the memory pool map tree.
        palOsMutexLock(&pMemoryManager->memoryPoolMutex);
        if (pMemoryManager->pMemoryPoolMap != NULL)
        {
            // Destroy the memory pool map tree.
            while (palRbTreeGetSize(pMemoryManager->pMemoryPoolMap) > 0)
            {
                // Remove the first element from the tree.
                palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryManager->pMemoryPoolMap);
                PAL_ASSERT(pNode != NULL);
                palMemoryPoolDestroy((palMemoryPool*)pNode->key);
            }

            palRbTreeDestroy(pMemoryManager->pMemoryPoolMap);
            pMemoryManager->pMemoryPoolMap = NULL;
        }
        palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);

        palOsMutexDestroy(&pMemoryManager->memoryPoolMutex);
    }
    else
    {
        // Remove all memory heaps associated with the device.
        // This will also destroy the memory heap map tree.
        palOsMutexLock(&pMemoryManager->memoryHeapMutex);
        if (pMemoryManager->pMemoryHeapMap != NULL)
        {
            // Destroy the memory heap map tree.
            while (palRbTreeGetSize(pMemoryManager->pMemoryHeapMap) > 0)
            {
                // Remove the first element from the tree.
                palRbTreeNode* pNode = palRbTreeGetFirst(pMemoryManager->pMemoryHeapMap);
                PAL_ASSERT(pNode != NULL);
                palMemoryHeapDestroy((palMemoryHeap*)pNode->value);
            }

            palRbTreeDestroy(pMemoryManager->pMemoryHeapMap);
            pMemoryManager->pMemoryHeapMap = NULL;
        }
        palOsMutexUnlock(&pMemoryManager->memoryHeapMutex);

        palOsMutexDestroy(&pMemoryManager->memoryHeapMutex);
    }

    // Destroy the memory manager mutex.
    palOsMutexDestroy(&pMemoryManager->memoryManagerMutex);

    memset(pMemoryManager, 0, sizeof(palMemoryManager));
}

// =====================================================================================================================
void palMemoryManagerDestroy(palMemoryManager* pMemoryManager)
{
    if (pMemoryManager == NULL)
    {
        return;
    }

    palMemoryManagerDeinitialize(pMemoryManager);

    free(pMemoryManager);
}

// =====================================================================================================================
palResult palMemoryManagerAddMemoryHeap(palMemoryManager* pMemoryManager, palMemoryHeap* pMemoryHeap)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(pMemoryHeap != NULL);

    palOsMutexLock(&pMemoryManager->memoryHeapMutex);
    result = palRbTreeInsert(pMemoryManager->pMemoryHeapMap, (palRbTreeKeyType)pMemoryHeap->heapType,
                             (palRbTreeValueType)pMemoryHeap);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory heap to the memory manager.");
        palOsMutexUnlock(&pMemoryManager->memoryHeapMutex);
        return result;
    }
    palOsMutexUnlock(&pMemoryManager->memoryHeapMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerRemoveMemoryHeap(palMemoryManager* pMemoryManager, palMemoryHeap* pMemoryHeap)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(pMemoryHeap != NULL);

    palOsMutexLock(&pMemoryManager->memoryHeapMutex);
    pNode = palRbTreeSearch(pMemoryManager->pMemoryHeapMap, (palRbTreeKeyType)pMemoryHeap->heapType);
    if (pNode == palRbTreeGetEnd(pMemoryManager->pMemoryHeapMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory heap not found in the memory manager.");
        palOsMutexUnlock(&pMemoryManager->memoryHeapMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory heap from the tree.
    palRbTreeDelete(pMemoryManager->pMemoryHeapMap, pNode);
    palOsMutexUnlock(&pMemoryManager->memoryHeapMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerGetMemoryHeap(palMemoryManager* pMemoryManager, palMemoryHeapType heapType,
                                        palMemoryHeap** ppMemoryHeap)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;
    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(ppMemoryHeap != NULL);

    palOsMutexLock(&pMemoryManager->memoryHeapMutex);
    pNode = palRbTreeSearch(pMemoryManager->pMemoryHeapMap, (palRbTreeKeyType)heapType);
    if (pNode == palRbTreeGetEnd(pMemoryManager->pMemoryHeapMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory heap not found in the memory manager.");
        palOsMutexUnlock(&pMemoryManager->memoryHeapMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    *ppMemoryHeap = (palMemoryHeap*)pNode->value;
    palOsMutexUnlock(&pMemoryManager->memoryHeapMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerAddMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool* pMemoryPool)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(pMemoryPool != NULL);

    palOsMutexLock(&pMemoryManager->memoryPoolMutex);
    result = palRbTreeInsert(pMemoryManager->pMemoryPoolMap, (palRbTreeKeyType)pMemoryPool,
                             (palRbTreeValueType)NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory pool to the memory manager.");
        palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);
        return result;
    }
    palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);

    return PAL_SUCCESS;
}

// ====================================================================================================================
palResult palMemoryManagerRemoveMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool* pMemoryPool)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(pMemoryPool != NULL);

    palOsMutexLock(&pMemoryManager->memoryPoolMutex);
    pNode = palRbTreeSearch(pMemoryManager->pMemoryPoolMap, (palRbTreeKeyType)pMemoryPool);
    if (pNode == palRbTreeGetEnd(pMemoryManager->pMemoryPoolMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory pool not found in the memory manager.");
        palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory pool from the tree.
    palRbTreeDelete(pMemoryManager->pMemoryPoolMap, pNode);
    palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerGetDefaultMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool** ppMemoryPool)
{
    palResult          result = PAL_SUCCESS;
    palMemoryPoolProps props  = {0};

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(ppMemoryPool != NULL);

    palOsMutexLock(&pMemoryManager->memoryPoolMutex);

    if (pMemoryManager->pDefaultMemoryPool == NULL)
    {
        props.allocType                = PAL_MEM_ALLOCATION_TYPE_INVALID;
        props.handleTypes              = PAL_MEM_HANDLE_TYPE_POSIX_FILE_DESCRIPTOR;
        props.location.type            = PAL_MEM_LOCATION_TYPE_DEVICE;
        props.location.id              = pMemoryManager->pDevice->deviceId;
        props.pWin32SecurityAttributes = NULL;
        props.maxSize                  = 0; // Default memory pool needs set to 0 to use a system dependent value.
        result = palMemoryPoolCreate(&pMemoryManager->pDefaultMemoryPool, pMemoryManager,
                                     PAL_MEMORY_POOL_TYPE_DEFAULT,  &props);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to create default memory pool.");
            *ppMemoryPool = NULL;
            palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);
            return result;
        }
    }

    *ppMemoryPool = pMemoryManager->pDefaultMemoryPool;

    palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerGetCurrentMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool** ppMemoryPool)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(ppMemoryPool != NULL);

    palOsMutexLock(&pMemoryManager->memoryPoolMutex);

    if (pMemoryManager->pCurrentMemoryPool == NULL)
    {
        // By default, if the current memory pool is not set, we return the default memory pool.
        result = palMemoryManagerGetDefaultMemoryPool(pMemoryManager, ppMemoryPool);
    }
    else
    {
        *ppMemoryPool = pMemoryManager->pCurrentMemoryPool;
    }

    palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerSetCurrentMemoryPool(palMemoryManager* pMemoryManager, palMemoryPool* pMemoryPool)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryManager != NULL);

    if (pMemoryPool->pMemoryManager != pMemoryManager)
    {
        PAL_DBG_PRINTF_ERROR("Memory pool does not belong to the memory manager.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Set the current memory pool to the specified memory pool.
    palOsMutexLock(&pMemoryManager->memoryPoolMutex);
    pMemoryManager->pCurrentMemoryPool = pMemoryPool;
    palOsMutexUnlock(&pMemoryManager->memoryPoolMutex);
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerAddMemoryObject(palMemoryManager* pMemoryManager, palMemoryObject* pMemoryObject)
{
    palResult result = PAL_SUCCESS;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(pMemoryObject != NULL);

    palOsMutexLock(&pMemoryManager->memoryObjectMutex);
    result = palRbTreeInsert(pMemoryManager->pMemoryObjectDeviceMap,
                             (palRbTreeKeyType)pMemoryObject->pMemoryChunk->deviceVirtAddress,
                             (palRbTreeValueType)pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory object to the memory manager.");
        palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);
        return result;
    }

    // If the memory object is for host, we also add it to the host memory object map.
    void* pHost = palMemoryObjectGetHostPtr(pMemoryObject);
    if (pHost != NULL)
    {
        result = palRbTreeInsert(pMemoryManager->pMemoryObjectHostMap, (palRbTreeKeyType)pHost,
                                 (palRbTreeValueType)pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to add host memory object to the memory manager.");
            palRbTreeErase(pMemoryManager->pMemoryObjectHostMap,
                           (palRbTreeKeyType)pMemoryObject->pMemoryChunk->deviceVirtAddress);
            palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);
            return result;
        }
    }

    palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerRemoveMemoryObject(palMemoryManager* pMemoryManager, palMemoryObject* pMemoryObject)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(pMemoryObject != NULL);

    palOsMutexLock(&pMemoryManager->memoryObjectMutex);
    pNode = palRbTreeSearch(pMemoryManager->pMemoryObjectDeviceMap,
                            (palRbTreeKeyType)pMemoryObject->pMemoryChunk->deviceVirtAddress);
    if (pNode == palRbTreeGetEnd(pMemoryManager->pMemoryObjectDeviceMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory object not found in the memory manager.");
        palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory object from the tree.
    palRbTreeDelete(pMemoryManager->pMemoryObjectDeviceMap, pNode);

    // If the memory object is for host, we also remove it from the host memory object map.
    void* pHost = palMemoryObjectGetHostPtr(pMemoryObject);
    if (pHost != NULL)
    {
        pNode = palRbTreeSearch(pMemoryManager->pMemoryObjectHostMap, (palRbTreeKeyType)pHost);
        if (pNode != palRbTreeGetEnd(pMemoryManager->pMemoryObjectHostMap))
        {
            // Remove the host memory object from the tree.
            palRbTreeDelete(pMemoryManager->pMemoryObjectHostMap, pNode);
        }
    }

    palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palMemoryManagerGetMemoryObject(palMemoryManager* pMemoryManager, void* ptr, palMemoryObject** ppMemoryObject)
{
    palResult        result        = PAL_SUCCESS;
    palRbTreeNode*   pCurrNode     = NULL;
    palRbTreeNode*   pNextNode     = NULL;
    palMemoryObject* pMemoryObject = NULL;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(ppMemoryObject != NULL);

    palOsMutexLock(&pMemoryManager->memoryObjectMutex);
    pNextNode = palRbTreeUpperBound(pMemoryManager->pMemoryObjectDeviceMap, (palRbTreeKeyType)ptr);
    pCurrNode = palRbTreeGetPrev(pMemoryManager->pMemoryObjectDeviceMap, pNextNode);
    if (pCurrNode != palRbTreeGetEnd(pMemoryManager->pMemoryObjectDeviceMap))
    {
        pMemoryObject = (palMemoryObject*)pCurrNode->value;
        palUint64 devicePtr = palMemoryObjectGetDevicePtr(pMemoryObject);
        if (((palUint64)ptr >= devicePtr) && ((palUint64)ptr < (devicePtr + palMemoryObjectGetSize(pMemoryObject))))
        {
            *ppMemoryObject = pMemoryObject;
            palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);
            return PAL_SUCCESS;
        }
    }

    // If we didn't find the memory object in the device map, we check the host map.
    pNextNode = palRbTreeUpperBound(pMemoryManager->pMemoryObjectHostMap, (palRbTreeKeyType)ptr);
    pCurrNode = palRbTreeGetPrev(pMemoryManager->pMemoryObjectHostMap, pNextNode);
    if (pCurrNode != palRbTreeGetEnd(pMemoryManager->pMemoryObjectHostMap))
    {
        pMemoryObject = (palMemoryObject*)pCurrNode->value;
        void* pHostPtr = palMemoryObjectGetHostPtr(pMemoryObject);
        if ((ptr >= pHostPtr) && (ptr < (void*)((palUint8*)pHostPtr + palMemoryObjectGetSize(pMemoryObject))))
        {
            *ppMemoryObject = pMemoryObject;
            palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);
            return PAL_SUCCESS;
        }
    }

    *ppMemoryObject = NULL;

    palOsMutexUnlock(&pMemoryManager->memoryObjectMutex);

    return PAL_ERROR_NOT_FOUND;
}

// =====================================================================================================================
palResult palMemoryManagerCreateMemoryObject(palMemoryManager* pMemoryManager, palContext* pContext,
                                             palMemoryHeapType heapType, palUint64 bytesize, palMemoryFlags memFlags,
                                             palUint32 hostFlags, void* pHostPtr, palMemoryObject** ppMemoryObject)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryHeap*   pMemoryHeap   = NULL;
    palMemoryObject* pMemoryObject = NULL;

    PAL_ASSERT(pMemoryManager != NULL);
    PAL_ASSERT(pContext != NULL);
    PAL_ASSERT(ppMemoryObject != NULL);

    palOsMutexLock(&pMemoryManager->memoryManagerMutex);

    // Get the memory heap from the memory manager.
    result = palMemoryManagerGetMemoryHeap(pMemoryManager, heapType, &pMemoryHeap);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to get memory heap of type %d from the memory manager.", heapType);
        palOsMutexUnlock(&pMemoryManager->memoryManagerMutex);
        return result;
    }

    // Allocate memory from the memory heap.
    result = palMemoryHeapCreateMemoryObject(pMemoryHeap, pContext, bytesize, memFlags, hostFlags, pHostPtr,
                                             ppMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory of size %llu from the memory heap of type %d.",
                             bytesize, heapType);
        palOsMutexUnlock(&pMemoryManager->memoryManagerMutex);
        return result;
    }

    palOsMutexUnlock(&pMemoryManager->memoryManagerMutex);

    return PAL_SUCCESS;
}