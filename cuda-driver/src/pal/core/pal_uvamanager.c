#include "pal_assert.h"
#include "pal_context.h"
#include "pal_device.h"
#include "pal_globalmanager.h"
#include "pal_memoryflags.h"
#include "pal_memoryheap.h"
#include "pal_memorymanager.h"
#include "pal_memoryobject.h"
#include "pal_memorypool.h"
#include "pal_rbtree.h"
#include "pal_settingsfilemgr.h"
#include "pal_uvamanager.h"

// =====================================================================================================================
palResult palUvaManagerCreate(palUvaManager** ppUvaManager, palGlobalManager* pGlobalManager)
{
    palResult      result      = PAL_SUCCESS;
    palUvaManager* pUvaManager = NULL;

    if ((ppUvaManager == NULL) || (pGlobalManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pUvaManager = (palUvaManager*)malloc(sizeof(palUvaManager));
    if (pUvaManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for UVA manager.");
        *ppUvaManager = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palUvaManagerInitialize(pUvaManager, pGlobalManager);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize UVA manager.");
        free(pUvaManager);
        *ppUvaManager = NULL;
        return result;
    }

    *ppUvaManager = pUvaManager;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerInitialize(palUvaManager* pUvaManager, palGlobalManager* pGlobalManager)
{
    palResult result = PAL_SUCCESS;
    palInt32  error  = 0;

    if ((pUvaManager == NULL) || (pGlobalManager == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pUvaManager, 0, sizeof(palUvaManager));

    pUvaManager->pGlobalManager = pGlobalManager;

    error = palOsMutexInit(&pUvaManager->uvaManagerMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize UVA manager mutex.");
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    result = palRbTreeCreate(&pUvaManager->pMemoryObjectDeviceMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create memory object tree.");
        palOsMutexDestroy(&pUvaManager->uvaManagerMutex);
        return result;
    }

    result = palRbTreeCreate(&pUvaManager->pMemoryObjectHostMap, NULL);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create host memory object tree.");
        palRbTreeDestroy(pUvaManager->pMemoryObjectDeviceMap);
        palOsMutexDestroy(&pUvaManager->uvaManagerMutex);
        return result;
    }

    error = palOsMutexInit(&pUvaManager->memoryObjectMutex);
    if (error != 0)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory object mutex.");
        palRbTreeDestroy(pUvaManager->pMemoryObjectHostMap);
        palRbTreeDestroy(pUvaManager->pMemoryObjectDeviceMap);
        palOsMutexDestroy(&pUvaManager->uvaManagerMutex);
        return PAL_ERROR_OPERATING_SYSTEM;
    }

    // Initialize the enableUnifiedAddressing flag
    pUvaManager->enableUnifiedAddressing = pGlobalManager->pSettings->enableUnifiedAddressing;

    return PAL_SUCCESS;
}

// ====================================================================================================================
void palUvaManagerDeinitialize(palUvaManager* pUvaManager)
{
    palRbTreeNode*    pNode          = NULL;
    palMemoryObject*  pMemoryObject  = NULL;
    palMemoryManager* pMemoryManager = NULL;

    if (pUvaManager == NULL)
    {
        return;
    }

    // Destroy the memory object map.
    palOsMutexLock(&pUvaManager->memoryObjectMutex);
    if (pUvaManager->pMemoryObjectDeviceMap != NULL)
    {
        // Destroy the memory object map tree.
        while (palRbTreeGetSize(pUvaManager->pMemoryObjectDeviceMap) > 0)
        {
            // Remove the first element from the tree.
            pNode = palRbTreeGetFirst(pUvaManager->pMemoryObjectDeviceMap);
            PAL_ASSERT(pNode != NULL);
            pMemoryObject = (palMemoryObject*)pNode->value;
            palMemoryObjectRelease(pMemoryObject);
        }

        palRbTreeDestroy(pUvaManager->pMemoryObjectDeviceMap);
        pUvaManager->pMemoryObjectDeviceMap = NULL;
    }

    if (pUvaManager->pMemoryObjectHostMap != NULL)
    {
        // Destroy the memory object map tree.
        while (palRbTreeGetSize(pUvaManager->pMemoryObjectHostMap) > 0)
        {
            // Remove the first element from the tree.
            pNode = palRbTreeGetFirst(pUvaManager->pMemoryObjectHostMap);
            PAL_ASSERT(pNode != NULL);
            pMemoryObject = (palMemoryObject*)pNode->value;
            palMemoryObjectRelease(pMemoryObject);

        }
        palRbTreeDestroy(pUvaManager->pMemoryObjectHostMap);
        pUvaManager->pMemoryObjectHostMap = NULL;
    }
    palOsMutexUnlock(&pUvaManager->memoryObjectMutex);

    // Destroy the mutex.
    palOsMutexDestroy(&pUvaManager->memoryObjectMutex);
    palOsMutexDestroy(&pUvaManager->uvaManagerMutex);
}

// ====================================================================================================================
void palUvaManagerDestroy(palUvaManager* pUvaManager)
{
    if (pUvaManager == NULL)
    {
        return;
    }

    palUvaManagerDeinitialize(pUvaManager);

    free(pUvaManager);
}

// =====================================================================================================================
palResult palUvaManagerAddMemoryObject(palUvaManager* pUvaManager, palMemoryObject* pMemoryObject)
{
    palResult result = PAL_SUCCESS;
    PAL_ASSERT(pUvaManager != NULL);
    PAL_ASSERT(pMemoryObject != NULL);

    palOsMutexLock(&pUvaManager->memoryObjectMutex);
    result = palRbTreeInsert(pUvaManager->pMemoryObjectDeviceMap,
                             (palRbTreeKeyType)palMemoryObjectGetDevicePtr(pMemoryObject),
                             (palRbTreeValueType)pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory object to the UVA manager.");
        palOsMutexUnlock(&pUvaManager->memoryObjectMutex);
        return result;
    }

    void* pHost = palMemoryObjectGetHostPtr(pMemoryObject);
    if (pHost != NULL)
    {
        result = palRbTreeInsert(pUvaManager->pMemoryObjectHostMap, (palRbTreeKeyType)pHost,
                                 (palRbTreeValueType)pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to add host memory object to the UVA manager.");
            palRbTreeErase(pUvaManager->pMemoryObjectHostMap,
                           (palRbTreeKeyType)palMemoryObjectGetDevicePtr(pMemoryObject));
            palOsMutexUnlock(&pUvaManager->memoryObjectMutex);
            return result;
        }
    }
    palOsMutexUnlock(&pUvaManager->memoryObjectMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerRemoveMemoryObject(palUvaManager* pUvaManager, palMemoryObject* pMemoryObject)
{
    palResult      result = PAL_SUCCESS;
    palRbTreeNode* pNode  = NULL;

    PAL_ASSERT(pUvaManager != NULL);
    PAL_ASSERT(pMemoryObject != NULL);

    palOsMutexLock(&pUvaManager->memoryObjectMutex);
    pNode = palRbTreeSearch(pUvaManager->pMemoryObjectDeviceMap,
                            (palRbTreeKeyType)palMemoryObjectGetDevicePtr(pMemoryObject));
    if (pNode == palRbTreeGetEnd(pUvaManager->pMemoryObjectDeviceMap))
    {
        PAL_DBG_PRINTF_ERROR("Memory object not found in the memory manager.");
        palOsMutexUnlock(&pUvaManager->memoryObjectMutex);
        return PAL_ERROR_NOT_FOUND;
    }

    // Remove the memory object from the tree.
    palRbTreeDelete(pUvaManager->pMemoryObjectDeviceMap, pNode);

    // If the memory object is for host, we also remove it from the host memory object map.
    void* pHost = palMemoryObjectGetHostPtr(pMemoryObject);
    if (pHost != NULL)
    {
        pNode = palRbTreeSearch(pUvaManager->pMemoryObjectHostMap, (palRbTreeKeyType)pHost);
        if (pNode != palRbTreeGetEnd(pUvaManager->pMemoryObjectHostMap))
        {
            // Remove the host memory object from the tree.
            palRbTreeDelete(pUvaManager->pMemoryObjectHostMap, pNode);
        }
    }

    palOsMutexUnlock(&pUvaManager->memoryObjectMutex);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerGetMemoryObject(palUvaManager* pUvaManager, const void* ptr, palMemoryObject** ppMemoryObject)
{
    palResult        result        = PAL_SUCCESS;
    palRbTreeNode*   pCurrNode     = NULL;
    palRbTreeNode*   pNextNode     = NULL;
    palMemoryObject* pMemoryObject = NULL;

    PAL_ASSERT(pUvaManager != NULL);
    PAL_ASSERT(ppMemoryObject != NULL);

    palOsMutexLock(&pUvaManager->memoryObjectMutex);
    pNextNode = palRbTreeUpperBound(pUvaManager->pMemoryObjectDeviceMap, (palRbTreeKeyType)ptr);
    pCurrNode = palRbTreeGetPrev(pUvaManager->pMemoryObjectDeviceMap, pNextNode);
    if (pCurrNode != palRbTreeGetEnd(pUvaManager->pMemoryObjectDeviceMap))
    {
        pMemoryObject = (palMemoryObject*)pCurrNode->value;
        palUint64 devicePtr = palMemoryObjectGetDevicePtr(pMemoryObject);
        if (((palUint64)ptr >= devicePtr) && ((palUint64)ptr < (devicePtr + palMemoryObjectGetSize(pMemoryObject))))
        {
            *ppMemoryObject = pMemoryObject;
            palOsMutexUnlock(&pUvaManager->memoryObjectMutex);
            return PAL_SUCCESS;
        }
    }

    // If we didn't find the memory object in the device map, we check the host map.
    pNextNode = palRbTreeUpperBound(pUvaManager->pMemoryObjectHostMap, (palRbTreeKeyType)ptr);
    pCurrNode = palRbTreeGetPrev(pUvaManager->pMemoryObjectHostMap, pNextNode);
    if (pCurrNode != palRbTreeGetEnd(pUvaManager->pMemoryObjectHostMap))
    {
        pMemoryObject = (palMemoryObject*)pCurrNode->value;
        void* pHostPtr = palMemoryObjectGetHostPtr(pMemoryObject);
        if ((ptr >= pHostPtr) && (ptr < (void*)((palUint8*)pHostPtr + palMemoryObjectGetSize(pMemoryObject))))
        {
            *ppMemoryObject = pMemoryObject;
            palOsMutexUnlock(&pUvaManager->memoryObjectMutex);
            return PAL_SUCCESS;
        }
    }

    *ppMemoryObject = NULL;

    palOsMutexUnlock(&pUvaManager->memoryObjectMutex);

    return PAL_ERROR_NOT_FOUND;
}

// =====================================================================================================================
palResult palUvaManagerMemAlloc(palUvaManager* pUvaManager, palContext* pContext, size_t bytesize,
                                palUint64* pDevicePtr)
{
    palResult         result         = PAL_SUCCESS;
    palMemoryManager* pMemoryManager = pContext->pMemoryManager;
    palMemoryObject*  pMemoryObject  = NULL;
    palMemoryFlags    memFlags       = {0};

    if ((pUvaManager == NULL) || (pContext == NULL) || (pDevicePtr == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Allocate memory in the UVA manager.
    palMemoryHeapType heapType = PAL_MEMORY_HEAP_TYPE_DEVICE;
    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        heapType = PAL_MEMORY_HEAP_TYPE_UVA;
    }

    memFlags.location        = PAL_MEMORY_LOCATION_TYPE_DEVICE;
    memFlags.hostCacheType   = PAL_MEMORY_HOST_CACHE_TYPE_INVALID;
    memFlags.deviceCacheType = PAL_MEMORY_DEVICE_CACHE_TYPE_CACHED;
    memFlags.type            = PAL_MEMORY_TYPE_GENERAL;
    memFlags.layout          = PAL_MEMORY_LAYOUT_TYPE_LINEAR;
    memFlags.isPortable      = PAL_FALSE;
    memFlags.isManaged       = PAL_FALSE;
    memFlags.isIomem         = PAL_FALSE;
    memFlags.hostMapType     = PAL_MEMORY_HOST_MAP_TYPE_NONE;
    memFlags.deviceMapType   = PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA;
    memFlags.ownerType       = PAL_MEMORY_OWNER_TYPE_USER;
    memFlags.pageSizeType    = PAL_MEMORY_PAGE_SIZE_TYPE_4K;
    memFlags.accessFlags     = PAL_MEMORY_ACCESS_FLAGS_READ_WRITE;
    memFlags.visibilityType  = PAL_MEMORY_VISIBILITY_TYPE_GLOBAL;
    memFlags.hasSubAllocator = PAL_TRUE;
    memFlags.allocApiType    = PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC;
    result = palMemoryManagerCreateMemoryObject(pMemoryManager, pContext, heapType, bytesize, memFlags,
                                                0, NULL, &pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory of size %zu in UVA manager.", bytesize);
        *pDevicePtr = 0;
        return result;
    }

    // Set the device pointer to the base virtual address of the memory chunk.
    *pDevicePtr = palMemoryObjectGetDevicePtr(pMemoryObject);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemAllocManaged(palUvaManager* pUvaManager, palContext* pContext, size_t bytesize,
                                       palUint32 flags, palUint64* pDevicePtr)
{
    palResult         result         = PAL_SUCCESS;
    palMemoryManager* pMemoryManager = pContext->pMemoryManager;
    palMemoryObject*  pMemoryObject  = NULL;
    palMemoryFlags    memFlags       = {0};

    if ((pUvaManager == NULL) || (pContext == NULL) || (pDevicePtr == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Allocate memory in the UVA manager.
    palMemoryHeapType heapType = PAL_MEMORY_HEAP_TYPE_UVA;
    if (pUvaManager->enableUnifiedAddressing == PAL_FALSE)
    {
        PAL_DBG_PRINTF_ERROR("Unified addressing is not enabled, cannot allocate managed memory.");
        *pDevicePtr = 0;
        return PAL_ERROR_NOT_SUPPORTED;
    }

    memFlags.location        = PAL_MEMORY_LOCATION_TYPE_DEVICE;
    memFlags.hostCacheType   = PAL_MEMORY_HOST_CACHE_TYPE_CACHED;
    memFlags.deviceCacheType = PAL_MEMORY_DEVICE_CACHE_TYPE_CACHED;
    memFlags.type            = PAL_MEMORY_TYPE_MANAGED;
    memFlags.layout          = PAL_MEMORY_LAYOUT_TYPE_LINEAR;
    memFlags.isPortable      = PAL_FALSE;
    memFlags.isManaged       = PAL_TRUE;
    memFlags.isIomem         = PAL_FALSE;
    memFlags.hostMapType     = PAL_MEMORY_HOST_MAP_TYPE_NONE;
    memFlags.deviceMapType   = PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA;
    memFlags.ownerType       = PAL_MEMORY_OWNER_TYPE_USER;
    memFlags.pageSizeType    = PAL_MEMORY_PAGE_SIZE_TYPE_4K;
    memFlags.accessFlags     = PAL_MEMORY_ACCESS_FLAGS_READ_WRITE;
    memFlags.visibilityType  = (flags == PAL_MEM_ATTACH_GLOBAL) ? PAL_MEMORY_VISIBILITY_TYPE_GLOBAL :
                               PAL_MEMORY_VISIBILITY_TYPE_HOST;
    memFlags.hasSubAllocator = PAL_TRUE;
    memFlags.allocApiType    = PAL_MEMORY_ALLOC_API_TYPE_MEM_ALLOC_MANAGED;
    result = palMemoryManagerCreateMemoryObject(pMemoryManager, pContext, heapType, bytesize, memFlags,
                                                0, NULL, &pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory of size %zu in UVA manager.", bytesize);
        *pDevicePtr = 0;
        return result;
    }

    // Set the device pointer to the base virtual address of the memory chunk.
    *pDevicePtr = palMemoryObjectGetDevicePtr(pMemoryObject);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemFree(palUvaManager* pUvaManager, palContext* pContext, palUint64 devicePtr)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryObject* pMemoryObject = NULL;

    if (pUvaManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid UVA manager.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Get the memory object from the UVA manager.
        result = palUvaManagerGetMemoryObject(pUvaManager, (const void*)devicePtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with device pointer %llu from UVA manager.", devicePtr);
            return result;
        }
    }
    else
    {
        if (pContext == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Invalid context for memory free operation.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the memory object from the memory manager.
        result = palMemoryManagerGetMemoryObject(pContext->pMemoryManager, (void*)devicePtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with device pointer %llu from memory manager.", devicePtr);
            return result;
        }
    }

    // Destroy the memory object.
    palMemoryObjectRelease(pMemoryObject);

    return PAL_SUCCESS;
}


// =====================================================================================================================
palResult palUvaManagerMemHostAlloc(palUvaManager* pUvaManager, palContext* pContext, size_t bytesize,
                                    palUint32 hostFlags, void** ppHostPtr)
{
    palResult         result         = PAL_SUCCESS;
    palMemoryManager* pMemoryManager = pContext->pMemoryManager;
    palMemoryObject*  pMemoryObject  = NULL;
    palMemoryHeapType heapType       = PAL_MEMORY_HEAP_TYPE_PINNED;
    palMemoryFlags    memFlags       = {0};

    if ((pUvaManager == NULL) || (pContext == NULL) || (ppHostPtr == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Initialize the memory flags.
    memFlags.location        = PAL_MEMORY_LOCATION_TYPE_HOST;
    memFlags.hostCacheType   = (hostFlags == PAL_MEMHOSTALLOC_WRITECOMBINED) ?
                               PAL_MEMORY_HOST_CACHE_TYPE_WRITECOMBINED :
                               PAL_MEMORY_HOST_CACHE_TYPE_CACHED;
    memFlags.deviceCacheType = PAL_MEMORY_DEVICE_CACHE_TYPE_CACHED;
    memFlags.type            = PAL_MEMORY_TYPE_GENERAL;
    memFlags.layout          = PAL_MEMORY_LAYOUT_TYPE_LINEAR;
    memFlags.isPortable      = (hostFlags == PAL_MEMHOSTALLOC_PORTABLE) ? PAL_TRUE : PAL_FALSE;
    memFlags.isManaged       = PAL_FALSE;
    memFlags.isIomem         = PAL_FALSE;
    memFlags.hostMapType     = PAL_MEMORY_HOST_MAP_TYPE_HOST_VA;
    memFlags.deviceMapType   = (hostFlags == PAL_MEMHOSTALLOC_DEVICEMAP) ?
                               PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA :
                               PAL_MEMORY_DEVICE_MAP_TYPE_NONE;
    memFlags.ownerType       = PAL_MEMORY_OWNER_TYPE_USER;
    memFlags.pageSizeType    = PAL_MEMORY_PAGE_SIZE_TYPE_4K;
    memFlags.accessFlags     = PAL_MEMORY_ACCESS_FLAGS_READ_WRITE;
    memFlags.visibilityType  = PAL_MEMORY_VISIBILITY_TYPE_GLOBAL;
    memFlags.hasSubAllocator = PAL_TRUE;
    memFlags.allocApiType    = PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_ALLOC;
    result = palMemoryManagerCreateMemoryObject(pMemoryManager, pContext, heapType, bytesize, memFlags,
                                                hostFlags, NULL, &pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory of size %zu in UVA manager.", bytesize);
        *ppHostPtr = NULL;
        return result;
    }

    // Set the host pointer to the base virtual address of the memory chunk.
    *ppHostPtr = palMemoryObjectGetHostPtr(pMemoryObject);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemFreeHost(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryObject* pMemoryObject = NULL;

    if (pUvaManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid UVA manager.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Get the memory object from the UVA manager.
        result = palUvaManagerGetMemoryObject(pUvaManager, pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %p from UVA manager.", pHostPtr);
            return result;
        }
    }
    else
    {
        if (pContext == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Invalid context for memory free operation.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the memory object from the memory manager.
        result = palMemoryManagerGetMemoryObject(pContext->pMemoryManager, pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %p from memory manager.", pHostPtr);
            return result;
        }
    }

    // Destroy the memory object.
    palMemoryObjectRelease(pMemoryObject);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemHostGetDevicePointer(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr,
                                               palUint32 hostFlags, palUint64* pDevicePtr)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryObject* pMemoryObject = NULL;

    if (pUvaManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid UVA manager.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Get the memory object from the UVA manager.
        result = palUvaManagerGetMemoryObject(pUvaManager, pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %p from UVA manager.", pHostPtr);
            return result;
        }
    }
    else
    {
        if (pContext == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Invalid context for getting device pointer operation.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the memory object from the memory manager.
        result = palMemoryManagerGetMemoryObject(pContext->pMemoryManager, pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %llu from memory manager.", pHostPtr);
            return result;
        }
    }

    if (hostFlags != palMemoryObjectGetHostFlags(pMemoryObject))
    {
        PAL_DBG_PRINTF_ERROR("Host flags do not match for memory object with host pointer %llu.", pHostPtr);
        return PAL_ERROR_INVALID_VALUE;
    }

    // Set the device pointer to the base virtual address of the memory chunk.
    *pDevicePtr = palMemoryObjectGetDevicePtr(pMemoryObject);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemGetHandleForAddressRange(palUvaManager* pUvaManager, palUint64 devicePtr, palUint64 size,
                                                   palMemRangeHandleType handleType, palUint64 flags, void* pHandle)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryObject* pMemoryObject = NULL;

    if (pUvaManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid UVA manager.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Get the memory object from the UVA manager.
    result = palUvaManagerGetMemoryObject(pUvaManager, (const void*)devicePtr, &pMemoryObject);
    if (result == PAL_SUCCESS)
    {
        palInt32 supported = 0;
        result = palDeviceGetAttribute(palContextGetDevice(pMemoryObject->pContext),
                                       PAL_DEVICE_ATTRIBUTE_DMA_BUF_SUPPORTED, &supported);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get device attribute for DMA_BUF support.");
            return result;
        }

        if (supported == 0)
        {
            PAL_DBG_PRINTF_ERROR("Device does not support DMA_BUF handle type.");
            return PAL_ERROR_NOT_SUPPORTED;
        }

        palUint64 offset = palMemoryObjectGetOffsetOf(pMemoryObject, devicePtr);
        if (offset + size > palMemoryObjectGetSize(pMemoryObject))
        {
            PAL_DBG_PRINTF_ERROR("Requested address range exceeds memory object size.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the handle for the address range from the memory object.
        result = palMemoryObjectGetHandleForAddressRange(pMemoryObject, devicePtr, size, handleType, flags, pHandle);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get handle for address range with device pointer %llu.", devicePtr);
            return result;
        }
    }
    else
    {
        // Get the memory allocation from the virtual memory manager.
        return PAL_ERROR_NOT_SUPPORTED;
    }

}

// =====================================================================================================================
palResult palUvaManagerMemHostGetFlags(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr,
                                       palUint32* pHostFlags)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryObject* pMemoryObject = NULL;

    if (pUvaManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid UVA manager.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Get the memory object from the UVA manager.
        result = palUvaManagerGetMemoryObject(pUvaManager, pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %llu from UVA manager.", pHostPtr);
            return result;
        }
    }
    else
    {
        if (pContext == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Invalid context for getting host flags operation.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the memory object from the memory manager.
        result = palMemoryManagerGetMemoryObject(pContext->pMemoryManager, pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %llu from memory manager.", pHostPtr);
            return result;
        }
    }

    // Get the host flags of the memory object.
    *pHostFlags = palMemoryObjectGetHostFlags(pMemoryObject);

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemGetAddressRange(palUvaManager* pUvaManager, palContext* pContext, palUint64 devicePtr,
                                          palUint64* pBase, size_t* pSize)
{
    palResult        result        = PAL_SUCCESS;
    palMemoryObject* pMemoryObject = NULL;

    if (pUvaManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid UVA manager.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Get the memory object from the UVA manager.
        result = palUvaManagerGetMemoryObject(pUvaManager, (const void*)devicePtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with device pointer %llu from UVA manager.", devicePtr);
            return result;
        }
    }
    else
    {
        if (pContext == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Invalid context for getting address range operation.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the memory object from the memory manager.
        result = palMemoryManagerGetMemoryObject(pContext->pMemoryManager, (void*)devicePtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with device pointer %llu from memory manager.",
                                 devicePtr);
            return result;
        }
    }

    if (pBase != NULL)
    {
        // Set the base address to the device pointer of the memory object.
        *pBase = palMemoryObjectGetDevicePtr(pMemoryObject);
    }

    if (pSize != NULL)
    {
        // Set the size to the size of the memory object.
        *pSize = palMemoryObjectGetSize(pMemoryObject);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemHostRegister(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr,
                                       size_t size, palUint32 flags)
{
    palResult         result         = PAL_SUCCESS;
    palMemoryManager* pMemoryManager = pContext->pMemoryManager;
    palMemoryObject*  pMemoryObject  = NULL;
    palMemoryHeapType heapType       = PAL_MEMORY_HEAP_TYPE_PINNED;
    palMemoryFlags    memFlags       = {0};

    if ((pUvaManager == NULL) || (pContext == NULL) || (pHostPtr == NULL) || (size == 0))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Get the memory object from the UVA manager.
        result = palUvaManagerGetMemoryObject(pUvaManager, (const void*)pHostPtr, &pMemoryObject);
    }
    else
    {
        if (pContext == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Invalid context for host register operation.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the memory object from the memory manager.
        result = palMemoryManagerGetMemoryObject(pContext->pMemoryManager, (void*)pHostPtr, &pMemoryObject);
    }

    if (result == PAL_SUCCESS)
    {
        palMemoryFlags* pMemFlags = palMemoryObjectGetMemoryFlags(pMemoryObject);
        if ((pMemFlags->allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER) ||
            (pMemFlags->allocApiType == PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER_PORTABLE))
        {
            // The memory object is already registered.
            PAL_DBG_PRINTF_WARN("Memory object with host pointer %p is already registered in UVA manager.", pHostPtr);
            return PAL_ERROR_HOST_MEMORY_ALREADY_REGISTERED;
        }

        return PAL_ERROR_INVALID_VALUE;
    }

    if (flags & PAL_MEMHOSTREGISTER_READ_ONLY)
    {
        palInt32 supported = 0;
        palDevice* pDevice = palContextGetDevice(pContext);
        result = palDeviceGetAttribute(pDevice, PAL_DEVICE_ATTRIBUTE_READ_ONLY_HOST_REGISTER_SUPPORTED, &supported);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get host register read-only support attribute.");
            return result;
        }

        if (supported == 0)
        {
            PAL_DBG_PRINTF_ERROR("Read-only host register is not supported by the device.");
            return PAL_ERROR_NOT_SUPPORTED;
        }
        else
        {
            // Set the read-only flag in the memory flags.
            memFlags.accessFlags = PAL_MEMORY_ACCESS_FLAGS_READ_ONLY;
        }
    }

    // Initialize the memory flags.
    memFlags.location        = PAL_MEMORY_LOCATION_TYPE_HOST;
    memFlags.hostCacheType   = PAL_MEMORY_HOST_CACHE_TYPE_CACHED;
    memFlags.deviceCacheType = PAL_MEMORY_DEVICE_CACHE_TYPE_CACHED;
    memFlags.type            = PAL_MEMORY_TYPE_GENERAL;
    memFlags.layout          = PAL_MEMORY_LAYOUT_TYPE_LINEAR;
    memFlags.isPortable      = (flags & PAL_MEMHOSTREGISTER_PORTABLE) ? PAL_TRUE : PAL_FALSE;
    memFlags.isManaged       = PAL_FALSE;
    memFlags.isIomem         = (flags & PAL_MEMHOSTREGISTER_IOMEMORY) ? PAL_TRUE : PAL_FALSE;
    memFlags.hostMapType     = PAL_MEMORY_HOST_MAP_TYPE_HOST_VA;
    memFlags.deviceMapType   = (flags & PAL_MEMHOSTREGISTER_DEVICEMAP) ?
                               PAL_MEMORY_DEVICE_MAP_TYPE_DEVICE_VA :
                               PAL_MEMORY_DEVICE_MAP_TYPE_NONE;
    memFlags.ownerType       = PAL_MEMORY_OWNER_TYPE_USER;
    memFlags.pageSizeType    = PAL_MEMORY_PAGE_SIZE_TYPE_4K;
    memFlags.visibilityType  = PAL_MEMORY_VISIBILITY_TYPE_GLOBAL;
    memFlags.hasSubAllocator = PAL_FALSE;
    memFlags.allocApiType    = PAL_MEMORY_ALLOC_API_TYPE_MEM_HOST_REGISTER;
    memFlags.sharedType      = PAL_MEMORY_SHARING_TYPE_HOST_ADDRESS;
    result = palMemoryManagerCreateMemoryObject(pMemoryManager, pContext, heapType, size, memFlags,
                                                flags, pHostPtr, &pMemoryObject);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to register host memory of size %zu in UVA manager.", size);
        return result;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palUvaManagerMemHostUnregister(palUvaManager* pUvaManager, palContext* pContext, void* pHostPtr)
{
    palResult         result         = PAL_SUCCESS;
    palMemoryManager* pMemoryManager = pContext->pMemoryManager;
    palMemoryObject*  pMemoryObject  = NULL;

    if ((pUvaManager == NULL) || (pContext == NULL) || (pHostPtr == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pUvaManager->enableUnifiedAddressing == PAL_TRUE)
    {
        // Get the memory object from the UVA manager.
        result = palUvaManagerGetMemoryObject(pUvaManager, (const void*)pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %p from UVA manager.", pHostPtr);
            return result;
        }
    }
    else
    {
        if (pContext == NULL)
        {
            PAL_DBG_PRINTF_ERROR("Invalid context for host unregister operation.");
            return PAL_ERROR_INVALID_VALUE;
        }

        // Get the memory object from the memory manager.
        result = palMemoryManagerGetMemoryObject(pContext->pMemoryManager, (void*)pHostPtr, &pMemoryObject);
        if (result != PAL_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to get memory object with host pointer %p from memory manager.", pHostPtr);
            return result;
        }
    }

    // Destroy the memory object.
    palMemoryObjectRelease(pMemoryObject);

    return PAL_SUCCESS;
}