#ifndef PAL_TLSTHREADDATA_H_
#define PAL_TLSTHREADDATA_H_

#include "pal.h"
#include "pal_structures.h"
#include "pal_types.h"

#define PAL_TLS_THREAD_DATA_DESTROYED ((palTlsThreadData*)-1)

// Per-thread data (The TLS entry for each thread is a pointer to one of these things).
struct palTlsThreadData_st
{
    // Thread manager handle
    palTlsThreadManager* pThreadManager;

    // The stack of contexts on the thread.
    palContext** ppCtxStack;

    // The ctx stack size.
    palUint32 ctxStackSize;

    // The ctx stack capacity size.
    palUint32 ctxStackArraySize;

    // The identifier for recording the corresponding thread
    palUint64 threadId;

    // The flag to indicate if the thread is allowed to make API calls.
    palBool allowedToMakeApiCalls;

    // The previous TLS thread data in the process (to peek in and rip out contexts).
    palTlsThreadData* pPrev;

    // The previous TLS thread data in the process (to peek in and rip out contexts).
    palTlsThreadData* pNext;
};

/// @brief Create a thread data object
/// @param ppThreadData The thread data object to create
/// @param pThreadManager The thread manager to use
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palTlsThreadDataCreate(palTlsThreadData** ppThreadData, palTlsThreadManager* pThreadManager);

/// @brief Initialize a thread data object
///
/// @param pThreadData The thread data object to initialize
/// @param pThreadManager The thread manager to use
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palTlsThreadDataInitialize(palTlsThreadData* pThreadData, palTlsThreadManager* pThreadManager);

/// @brief Deinitialize a thread data object
///
/// @param pThreadData The thread data object to deinitialize
void palTlsThreadDataDeinitialize(palTlsThreadData* pThreadData);

/// @brief Destroy a thread data object
///
/// @param pThreadData The thread data object to destroy
void palTlsThreadDataDestroy(palTlsThreadData* pThreadData);

/// @brief Get the current context of the stack from the thread data object(or NULL if there is no stack)
///
/// @param pThreadData The thread data object to get the context from
/// @return The current context of the stack, or NULL if there is no stack
palContext* palTlsThreadDataGetCurrentContext(palTlsThreadData* pThreadData);

/// @brief Push a context onto the stack of the thread data object.
///
/// @param pThreadData The thread data object to push the context onto
/// @param pContext The context to push onto the stack
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palTlsThreadDataPushCurrentContext(palTlsThreadData* pThreadData, palContext* pContext);

/// @brief Pop a context from the stack of the thread data object.
///
/// @param pThreadData The thread data object to pop the context from
/// @return PAL_SUCCESS on success, or an error code on failure
palResult palTlsThreadDataPopCurrentContext(palTlsThreadData* pThreadData);

/// @brief Set the current context of the stack from the thread data object.
/// @param pThreadData The thread data object to set the context for
/// @param pContext The context to set as the current context
/// @return PAL_SUCCESS on success, or an error code on failure.
palResult palTlsThreadDataSetCurrentContext(palTlsThreadData* pThreadData, palContext* pContext);

/// @brief Check if the thread data object is allowed to make API calls.
///
/// @param pThreadData The thread data object to check
/// @return PAL_TRUE if this API  call is allowed, PAL_FALSE otherwise
palBool palTlsThreadDataIsAllowedToMakeApiCalls(palTlsThreadData* pThreadData);

/// @brief Set the thread data object to be allowed to make API calls.
///
/// @param pThreadData The thread data object
/// @param allowedToMakeApiCalls The flag to set for indicating if the thread is allowed to make API calls
void palTlsThreadDataSetAllowedToMakeApiCalls(palTlsThreadData* pThreadData, palBool allowedToMakeApiCalls);

#endif // PAL_TLSTHREADDATA_H_