#include "pal_assert.h"
#include "pal_memoryblock.h"
#include "pal_memorychunk.h"

// =====================================================================================================================
palResult palMemoryChunkCreate(palMemoryChunk** ppMemoryChunk, palMemoryBlock* pMemoryBlock,
                               palMemoryChunkCreateInfo* pCreateInfo)
{
    palResult       result       = PAL_SUCCESS;
    palMemoryChunk* pMemoryChunk = NULL;

    if ((ppMemoryChunk == NULL) || (pMemoryBlock == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory chunk creation.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pMemoryChunk = (palMemoryChunk*)malloc(sizeof(palMemoryChunk));
    if (pMemoryChunk == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for memory chunk.");
        *ppMemoryChunk = NULL;
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    result = palMemoryChunkInitialize(pMemoryChunk, pMemoryBlock, pCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize memory chunk.");
        free(pMemoryChunk);
        *ppMemoryChunk = NULL;
        return result;
    }

    *ppMemoryChunk = pMemoryChunk;

    return result;
}

// =====================================================================================================================
palResult palMemoryChunkInitialize(palMemoryChunk* pMemoryChunk, palMemoryBlock* pMemoryBlock,
                                   palMemoryChunkCreateInfo* pCreateInfo)
{
    palResult result = PAL_SUCCESS;

    if ((pMemoryChunk == NULL) || (pMemoryBlock == NULL) || (pCreateInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters for memory chunk initialization.");
        return PAL_ERROR_INVALID_VALUE;
    }

    memset(pMemoryChunk, 0, sizeof(palMemoryChunk));

    pMemoryChunk->pMemoryBlock      = pMemoryBlock;
    pMemoryChunk->deviceVirtAddress = pCreateInfo->deviceVirtAddress;
    pMemoryChunk->pHostVirtAddress  = (pCreateInfo->pHostVirtAddress != NULL) ? pCreateInfo->pHostVirtAddress : NULL;
    pMemoryChunk->offset            = pCreateInfo->offset;
    pMemoryChunk->size              = pCreateInfo->size;
    pMemoryChunk->alignmentSize     = pCreateInfo->alignmentSize;

    // Add the memory chunk to the free memory chunk map of the memory block.
    result = palMemoryBlockAddFreeMemoryChunk(pMemoryBlock, pMemoryChunk);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to add memory chunk to the free memory chunk map.");
        memset(pMemoryChunk, 0, sizeof(palMemoryChunk));
        return result;
    }

    return result;
}

// =====================================================================================================================
void palMemoryChunkDeinitialize(palMemoryChunk* pMemoryChunk)
{
    if (pMemoryChunk == NULL)
    {
        return;
    }

    // Remove the memory chunk from the free memory chunk map of the memory block.
    palMemoryBlockRemoveFreeMemoryChunk(pMemoryChunk->pMemoryBlock, pMemoryChunk);

    memset(pMemoryChunk, 0, sizeof(palMemoryChunk));
}

// =====================================================================================================================
void palMemoryChunkDestroy(palMemoryChunk* pMemoryChunk)
{
    if (pMemoryChunk == NULL)
    {
        return;
    }

    palMemoryChunkDeinitialize(pMemoryChunk);
    free(pMemoryChunk);
}

// =====================================================================================================================
palResult palMemoryChunkSplit(palMemoryChunk* pMemoryChunk, palUint64 size, palMemoryChunk** ppSplitMemoryChunk,
                              palMemoryChunk** ppRemainderMemoryChunk)
{
    palResult                result          = PAL_SUCCESS;
    palMemoryChunk*          pNewMemoryChunk = NULL;
    palUint64                splitChunkSize  = 0;
    palMemoryChunkCreateInfo chunkCreateInfo = {0};

    PAL_ASSERT(pMemoryChunk != NULL);

    if (pMemoryChunk->isUsed == PAL_TRUE)
    {
        PAL_DBG_PRINTF_ERROR("Cannot split a used memory chunk.");
        return PAL_ERROR_INVALID_VALUE;
    }

    splitChunkSize = PAL_ALIGN(size, PAL_MAX(pMemoryChunk->alignmentSize, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE));

    if (pMemoryChunk->size <= splitChunkSize)
    {
        PAL_DBG_PRINTF_ERROR("Memory chunk is too small to split.");
        return PAL_SUCCESS;
    }

    chunkCreateInfo.deviceVirtAddress = pMemoryChunk->deviceVirtAddress + splitChunkSize;
    chunkCreateInfo.pHostVirtAddress  = (pMemoryChunk->pHostVirtAddress != NULL)
                                        ? (void*)((uintptr_t)pMemoryChunk->pHostVirtAddress + splitChunkSize)
                                        : NULL;
    chunkCreateInfo.offset            = pMemoryChunk->offset + splitChunkSize;
    chunkCreateInfo.size              = pMemoryChunk->size - splitChunkSize;
    chunkCreateInfo.alignmentSize     = PAL_MAX(pMemoryChunk->alignmentSize, PAL_MEMORY_CHUNK_ALIGNMENT_SIZE);
    result = palMemoryChunkCreate(&pNewMemoryChunk, pMemoryChunk->pMemoryBlock, &chunkCreateInfo);
    if (result != PAL_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create new memory chunk after splitting.");
        return result;
    }

    // Update the original memory chunk size.
    pMemoryChunk->size = splitChunkSize;

    *ppSplitMemoryChunk     = pMemoryChunk;
    *ppRemainderMemoryChunk = pNewMemoryChunk;

    return result;
}

// =====================================================================================================================
palResult palMemoryChunkMerge(palMemoryChunk* pMemoryChunk1, palMemoryChunk* pMemoryChunk2,
                              palMemoryChunk** ppMergedMemoryChunk)
{
    palResult       result             = PAL_SUCCESS;
    palMemoryChunk* pFirstMemoryChunk  = NULL;
    palMemoryChunk* pSecondMemoryChunk = NULL;

    PAL_ASSERT(pMemoryChunk1 != NULL);
    PAL_ASSERT(pMemoryChunk2 != NULL);
    PAL_ASSERT(ppMergedMemoryChunk != NULL);

    if ((pMemoryChunk1->isUsed == PAL_TRUE) || (pMemoryChunk2->isUsed == PAL_TRUE))
    {
        PAL_DBG_PRINTF_ERROR("Cannot merge used memory chunks.");
        return PAL_ERROR_INVALID_VALUE;
    }

    if (pMemoryChunk1->deviceVirtAddress < pMemoryChunk2->deviceVirtAddress)
    {
        pFirstMemoryChunk  = pMemoryChunk1;
        pSecondMemoryChunk = pMemoryChunk2;
    }
    else
    {
        pFirstMemoryChunk  = pMemoryChunk2;
        pSecondMemoryChunk = pMemoryChunk1;
    }

    // Ensure the memory chunks are adjacent
    if ((pFirstMemoryChunk->deviceVirtAddress + pFirstMemoryChunk->size) != pSecondMemoryChunk->deviceVirtAddress)
    {
        PAL_DBG_PRINTF_ERROR("Memory chunks are not adjacent, cannot merge.");
        return PAL_ERROR_INVALID_VALUE;
    }

    pFirstMemoryChunk->size += pSecondMemoryChunk->size;

    palMemoryChunkDestroy(pSecondMemoryChunk);

    *ppMergedMemoryChunk = pFirstMemoryChunk;

    return PAL_SUCCESS;
}