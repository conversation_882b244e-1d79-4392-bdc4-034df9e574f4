#include "pal_assert.h"
#include "pal_init.h"
#include "pal_globalmanager.h"
#include "pal_os.h"
#include "pal_types.h"

#include <stdio.h>

typedef struct ipalPidLocks_st
{
    // Process ID of the process that started the call to ipalPlatformMutexInitOnce()
    volatile palOsPid initOnceBeginPid;

    // Process ID of the process that completed the call to ipalPlatformMutexInitOnce()
    volatile palOsPid initOnceEndPid;
} ipalPidLocks;

// The global pid lock for palInit()
static ipalPidLocks g_pidLocks = {0};

/// @brief The internal mutex of the global platform init once
///        The global platform needs t be guaranteed to be initialized only once, We make use of the different
///        application has the different pid value to lock it among multi-threads. For forked child process,
///        we may need to reinitialize all platform resources for the child process.
/// @note The global platform init once is used to initialize the global platform resources.
static void ipalInitMutexInitOnce(void);

// =====================================================================================================================
static void ipalInitMutexInitOnce(void)
{
    palOsPid thisPid;
    palOsPid oldBeginPid;

    thisPid = palOsGetProcessId();

    // Make sure we can atomically compare process ID.
    PAL_ASSERT(sizeof(thisPid) == sizeof(palUint32));

    // Mark that init-once has begun. If we get something besides thisPid then we have "won" the
    // init-once lock which we will note as being unlocked by settings initOnceEndPid to this thisPid.
    oldBeginPid = (palOsPid)palOsAtomicExchangeAcqRel32((volatile _Atomic palUint32*)&g_pidLocks.initOnceBeginPid,
                                                        (palUint32)thisPid);
    // If oldBeginPid is thisPid, then someone in this process already started (any maybe even finished) init-once.
    if (oldBeginPid == thisPid)
    {
        // Spin-wait until init-once is comleted.
        while ((palOsPid)palOsAtomicLoadAcquire32((volatile _Atomic palUint32*)&g_pidLocks.initOnceEndPid) != thisPid)
        {
            palOsThreadYield();
        }
    }
    else // if oldBeginPid is not thisPid, then init-once has not completed in this process.
    {
        // If oldBeginPid is non-zero then we are a forked child and we have to clean up
        // after the previous cuInit() call
        if (oldBeginPid != 0)
        {
            // Fixme: Do we really need to deinitialize the resources? since we still need to
            // re-initialize it again.
            memset(&g_globalManager, 0, sizeof(g_globalManager));
        }

        // Initialize the mutex.
        palOsMutexInit(&g_globalManager.initMutex);

        // Mark init-once as complete. Ensure all prior ops have completed.
        palOsAtomicStoreRelease32((volatile _Atomic palUint32*)&g_pidLocks.initOnceEndPid, thisPid);
    }
}

// =====================================================================================================================
// Define this destructor to handle the release of all resources when a process exits.
// We do not use atexit() since it can only handle situations where the process exits normally,
// and unexpected situations cannot be handled.
static void ipalDriverUnloadPosix(void)
{
    palGlobalManagerDeinitialize();

    palOsMutexDestroy(&g_globalManager.initMutex);
}

// =====================================================================================================================
palResult palInit(uint32_t flags, palInitCallbackFunc callbackFunc)
{
    palResult result = PAL_SUCCESS;

    ipalInitMutexInitOnce();

    palOsMutexLock(&g_globalManager.initMutex);

    result = palGlobalManagerInitialize();
    if (result != PAL_SUCCESS)
    {
        palOsMutexUnlock(&g_globalManager.initMutex);
        return result;
    }

    if (callbackFunc != NULL)
    {
        callbackFunc();
    }

    // Register the driver unload function to be called at process exit.
    // This ensures that resources are cleaned up properly when the process exits.
    atexit(ipalDriverUnloadPosix);

    palOsMutexUnlock(&g_globalManager.initMutex);

    return PAL_SUCCESS;
}