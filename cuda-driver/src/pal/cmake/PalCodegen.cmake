##
 #######################################################################################################################
 #
 #  Copyright (c) 2022-2024 Advanced Micro Devices, Inc. All Rights Reserved.
 #
 #  Permission is hereby granted, free of charge, to any person obtaining a copy
 #  of this software and associated documentation files (the "Software"), to deal
 #  in the Software without restriction, including without limitation the rights
 #  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 #  copies of the Software, and to permit persons to whom the Software is
 #  furnished to do so, subject to the following conditions:
 #
 #  The above copyright notice and this permission notice shall be included in all
 #  copies or substantial portions of the Software.
 #
 #  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 #  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 #  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 #  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 #  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 #  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 #  SOFTWARE.
 #
 #######################################################################################################################

include_guard()

find_package(Python3 3.6 QUIET REQUIRED
    COMPONENTS Interpreter
)

function(convert_pal_settings_name SETTINGS_FILE OUT_BASENAME_VAR FOR_FILE)
    # Convert input name convention to output.
    # eg, settings_core.json -> g_coreSettings

    # 1. get basename
    get_filename_component(SETTINGS_FILE_BASENAME ${SETTINGS_FILE} NAME_WE)
    # 2. split on '_' into list
    string(REPLACE "_" ";" OUT_PARTS ${SETTINGS_FILE_BASENAME})
    # 3. reverse
    list(REVERSE OUT_PARTS)
    # 4. first part goes in unmodified
    if(FOR_FILE)
        list(POP_FRONT OUT_PARTS OUT_BASENAME)
        string(PREPEND OUT_BASENAME "g_")
    else()
        set(OUT_BASENAME "")
    endif()
    # 5. remaining parts get capitalized
    foreach(OUT_PART ${OUT_PARTS})
        string(SUBSTRING ${OUT_PART} 0 1 FIRST_LETTER)
        string(SUBSTRING ${OUT_PART} 1 -1 REM_LETTERS)
        string(TOUPPER ${FIRST_LETTER} FIRST_LETTER)
        string(APPEND OUT_BASENAME "${FIRST_LETTER}${REM_LETTERS}")
    endforeach()

    set(${OUT_BASENAME_VAR} ${OUT_BASENAME} PARENT_SCOPE)
endfunction()

function(pal_gen_settings)
    # INPUT_JSON:
    #     Path to a JSON file describing all settings of a component.
    # GENERATED_FILENAME:
    #     The name of the generated C files. The final name will be prefixed with 'g_' and
    #     suffixed with '.h'/'.c'.
    # OUT_DIR:
    #     Path to output directory.
    # CLASS_NAME:
    #     The class name for this settings component.
    # INCLUDE_HEADERS:
    #     Header files the generated settings file needs to '#include'. For example, a header file
    #     that contains an existing enum definition.
    set(options EXPERIMENTS)
    set(oneValueArgs INPUT_JSON GENERATED_FILENAME OUT_DIR CLASS_NAME)
    set(multiValArgs INCLUDE_HEADERS)
    cmake_parse_arguments(PARSE_ARGV 0 SETTINGS "${options}" "${oneValueArgs}" "${multiValArgs}")

    if (NOT SETTINGS_INPUT_JSON)
        message(FATAL_ERROR "No settings input json file provided.")
    endif()

    set(GENERATED_HEADER_FILENAME "g_${SETTINGS_GENERATED_FILENAME}.h")
    set(GENERATED_SOURCE_FILENAME "g_${SETTINGS_GENERATED_FILENAME}.c")

    file(MAKE_DIRECTORY ${SETTINGS_OUT_DIR})
    target_include_directories(pal PRIVATE ${SETTINGS_OUT_DIR})

    target_sources(pal PRIVATE
        ${SETTINGS_OUT_DIR}/${GENERATED_HEADER_FILENAME}
        ${SETTINGS_OUT_DIR}/${GENERATED_SOURCE_FILENAME}
    )

    set_source_files_properties(
        ${SETTINGS_OUT_DIR}/${GENERATED_HEADER_FILENAME}
        ${SETTINGS_OUT_DIR}/${GENERATED_SOURCE_FILENAME}
        TARGET_DIRECTORY pal
        PROPERTIES GENERATED ON
    )

    if (SETTINGS_CLASS_NAME)
        list(APPEND CODEGEN_OPTIONAL_ARGS "--classname" "${SETTINGS_CLASS_NAME}")
    endif()

    if ((NOT EXISTS ${SETTINGS_OUT_DIR}/${GENERATED_HEADER_FILENAME}) OR
        (NOT EXISTS ${SETTINGS_OUT_DIR}/${GENERATED_SOURCE_FILENAME}))
        # Generate these during configuration so that they are guaranteed to exist.
        execute_process(
            COMMAND ${Python3_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/settings/codegen/settings_codegen.py
                    --input ${PAL_SOURCE_DIR}/${SETTINGS_INPUT_JSON}
                    --generated-filename ${SETTINGS_GENERATED_FILENAME}
                    --outdir ${SETTINGS_OUT_DIR}
                    --settings-struct-name ${SETTINGS_CLASS_NAME}
                    --include-headers ${SETTINGS_INCLUDE_HEADERS}
            COMMAND_ECHO STDOUT
        )
    endif()

    convert_pal_settings_name(${SETTINGS_INPUT_JSON} SETTING_TGT FALSE)
    add_custom_command(
        OUTPUT  ${SETTINGS_OUT_DIR}/${GENERATED_HEADER_FILENAME}
                ${SETTINGS_OUT_DIR}/${GENERATED_SOURCE_FILENAME}
        COMMAND ${Python3_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/settings/codegen/settings_codegen.py
                --input ${PAL_SOURCE_DIR}/${SETTINGS_INPUT_JSON}
                --generated-filename ${SETTINGS_GENERATED_FILENAME}
                --outdir ${SETTINGS_OUT_DIR}
                --settings-struct-name ${SETTINGS_CLASS_NAME}
                --include-headers ${SETTINGS_INCLUDE_HEADERS}
        COMMENT "Generating settings from ${PAL_SOURCE_DIR}/${SETTINGS_INPUT_JSON}..."
        DEPENDS ${PAL_SOURCE_DIR}/${SETTINGS_INPUT_JSON}
                ${PAL_SOURCE_DIR}/util/settings/codegen/settings_codegen.py
                ${PAL_SOURCE_DIR}/util/settings/codegen/settings_tmpl.c.jinja2
                ${PAL_SOURCE_DIR}/util/settings/codegen/settings_tmpl.h.jinja2
    )

    add_custom_target(${SETTING_TGT}
        DEPENDS ${SETTINGS_OUT_DIR}/${GENERATED_HEADER_FILENAME}
                ${SETTINGS_OUT_DIR}/${GENERATED_SOURCE_FILENAME}
        SOURCES ${SETTINGS_OUT_DIR}/${GENERATED_HEADER_FILENAME}
                ${SETTINGS_OUT_DIR}/${GENERATED_SOURCE_FILENAME}
    )
    add_dependencies(pal ${SETTING_TGT})
endfunction()

function(pal_setup_generated_code)
    pal_gen_settings(INPUT_JSON         util/settings/codegen/settings.json
                     GENERATED_FILENAME pal_settings
                     OUT_DIR            ${PAL_SETTINGS_GENERATED_DIR}
                     CLASS_NAME         palSettings
                     INCLUDE_HEADERS    pal.h pal_os.h pal_structures.h pal_types.h)
endfunction()