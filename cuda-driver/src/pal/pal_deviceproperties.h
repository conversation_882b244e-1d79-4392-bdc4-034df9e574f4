#ifndef PAL_DEVICEPROPERTIES_H_
#define PAL_DEVICEPROPERTIES_H_

#include <stdio.h>

typedef struct palDeviceProperties_st
{
    // The name is an ASCII string identifying the device.
    char name[256];
    // The uuid is a 16-byte unique identifier.
    char uuid[16];
    // The totalGlobalMem is the total amount of global memory available on the device in bytes.
    size_t totalGlobalMem;
    // The sharedMemPerBlock is the maximum amount of shared memory available to a thread block in bytes.
    size_t sharedMemPerBlock;
    // The regsPerBlock is the maximum number of 32-bit registers available to a thread block.
    int regsPerBlock;
    // The warpSize is the warp size in threads.
    int warpSize;
    // The memPitch is the maximum pitch in bytes allowed by the memory copy functions that involve memory
    // regions allocated through cudaMallocPitch().
    size_t memPitch;
    // The maxThreadsPerBlock is the maximum number of threads per block.
    int maxThreadsPerBlock;
    // The maxThreadsDim[3] contains the maximum size of each dimension of a block.
    int maxThreadsDim[3];
    // The maxGridSize[3] contains the maximum size of each dimension of a grid.
    int maxGridSize[3];
    // The clockRate is the clock frequency in kilohertz.
    int clockRate;
    // The totalConstMem is the total amount of constant memory available on the device in bytes.
    size_t totalConstMem;
    // The major, minor are the major and minor revision numbers defining the device's compute capability.
    int major;
    int minor;
    // The textureAlignment is the alignment requirement; texture base addresses that are aligned to
    // textureAlignment bytes do not need an offset applied to texture fetches.
    size_t textureAlignment;
    // texturePitchAlignment is the pitch alignment requirement for 2D texture references that
    // are bound to pitched memory.
    size_t texturePitchAlignment;
    // The deviceOverlap is 1 if the device can concurrently copy memory between host and device
    // while executing a kernel, or 0 if not. Deprecated, use instead asyncEngineCount.
    int deviceOverlap;
    // The multiProcessorCount is the number of multiprocessors on the device.
    int multiProcessorCount;
    // The kernelExecTimeoutEnabled is 1 if there is a run time limit for kernels executed on the
    // device, or 0 if not.
    int kernelExecTimeoutEnabled;
    // The integrated is 1 if the device is an integrated (motherboard) GPU and 0 if it is a discrete
    // (card) component.
    int integrated;
    // canMapHostMemory is 1 if the device can map host memory into the CUDA address space
    // for use with cudaHostAlloc()/cudaHostGetDevicePointer(), or 0 if not.
    int canMapHostMemory;
    // computeMode is the compute mode that the device is currently in. Available modes are as follows:
    // - cudaComputeModeDefault: Default mode - Device is not restricted and multiple
    //   threads can use cudaSetDevice() with this device.
    // - cudaComputeModeProhibited: Compute-prohibited mode - No threads can use cudaSetDevice() with this device.
    // - cudaComputeModeExclusiveProcess: Compute-exclusive-process mode - Many threads in one process will be able
    //   to use cudaSetDevice() with this device.
    int computeMode;
    // The maxTexture1D is the maximum 1D texture size.
    int maxTexture1D;
    // The maxTexture1DMipmap is the maximum 1D mipmapped texture texture size.
    int maxTexture1DMipmap;
    // The maxTexture1DLinear is the maximum 1D texture size for textures bound to linear memory.
    int maxTexture1DLinear;
    // The maxTexture2D[2] contains the maximum 2D texture dimensions.
    int maxTexture2D[2];
    // The maxTexture2DMipmap[2] contains the maximum 2D mipmapped texture dimensions.
    int maxTexture2DMipmap[2];
    // The maxTexture2DLinear[3] contains the maximum 2D texture dimensions for 2D textures bound to pitch linear memory.
    int maxTexture2DLinear[3];
    // The maxTexture2DGather[2] contains the maximum 2D texture dimensions if texture gather operations have to be performed.
    int maxTexture2DGather[2];
    // The maxTexture3D[3] contains the maximum 3D texture dimensions.
    int maxTexture3D[3];
    // The maxTexture3DAlt[3] contains the maximum alternate 3D texture dimensions.
    int maxTexture3DAlt[3];
    // The maxTextureCubemap is the maximum cubemap texture width or height.
    int maxTextureCubemap;
    // The maxTexture1DLayered[2] contains the maximum 1D layered texture dimensions.
    int maxTexture1DLayered[2];
    // The maxTexture2DLayered[3] contains the maximum 2D layered texture dimensions.
    int maxTexture2DLayered[3];
    // The maxTextureCubemapLayered[2] contains the maximum cubemap layered texture dimensions.
    int maxTextureCubemapLayered[2];
    // The maxSurface1D is the maximum 1D surface size.
    int maxSurface1D;
    // The maxSurface2D[2] contains the maximum 2D surface dimensions.
    int maxSurface2D[2];
    // The maxSurface3D[3] contains the maximum 3D surface dimensions.
    int maxSurface3D[3];
    // The maxSurface1DLayered[2] contains the maximum 1D layered surface dimensions.
    int maxSurface1DLayered[2];
    // The maxSurface2DLayered[3] contains the maximum 2D layered surface dimensions.
    int maxSurface2DLayered[3];
    // The maxSurfaceCubemap is the maximum cubemap surface width or height.
    int maxSurfaceCubemap;
    // The maxSurfaceCubemapLayered[2] contains the maximum cubemap layered surface dimensions.
    int maxSurfaceCubemapLayered[2];
    // The surfaceAlignment specifies the alignment requirements for surfaces.
    size_t surfaceAlignment;
    // The concurrentKernels is 1 if the device supports executing multiple kernels within the
    // same context simultaneously, or 0 if not. It is not guaranteed that multiple kernels will
    // be resident on the device concurrently so this feature should not be relied upon for correctness.
    int concurrentKernels;
    // The ECCEnabled is 1 if the device has ECC support turned on, or 0 if not.
    int ECCEnabled;
    // The pciBusID is the PCI bus identifier of the device.
    int pciBusID;
    // The pciDeviceID is the PCI device (sometimes called slot) identifier of the device.
    int pciDeviceID;
    // The pciDomainID is the PCI domain identifier of the device.
    int pciDomainID;
    // The tccDriver is 1 if the device is using a TCC driver or 0 if not.
    int tccDriver;
    // The asyncEngineCount is 1 when the device can concurrently copy memory between host and
    // device while executing a kernel. It is 2 when the device can concurrently copy memory
    // between host and device in both directions and execute a kernel at the same time. It is 0 if
    // neither of these is supported.
    int asyncEngineCount;
    // The unifiedAddressing is 1 if the device shares a unified address space with the host and 0 otherwise.
    int unifiedAddressing;
    // The memoryClockRate is the peak memory clock frequency in kilohertz.
    int memoryClockRate;
    // The memoryBusWidth is the memory bus width in bits.
    int memoryBusWidth;
    // The l2CacheSize is L2 cache size in bytes.
    int l2CacheSize;
    // The persistingL2CacheMaxSize is L2 cache's maximum persisting lines size in bytes.
    int persistingL2CacheMaxSize;
    // The maxThreadsPerMultiProcessor is the number of maximum resident threads per multiprocessor.
    int maxThreadsPerMultiProcessor;
    // The streamPrioritiesSupported is 1 if the device supports stream priorities, or 0 if it is not supported.
    int streamPrioritiesSupported;
    // The globalL1CacheSupported is 1 if the device supports caching of globals in L1 cache, or 0 if it is not supported.
    int globalL1CacheSupported;
    // The localL1CacheSupported is 1 if the device supports caching of locals in L1 cache, or 0 if it is not supported.
    int localL1CacheSupported;
    // The sharedMemPerMultiprocessor is the maximum amount of shared memory available to a multiprocessor in bytes;
    // this amount is shared by all thread blocks simultaneously resident on a multiprocessor.
    size_t sharedMemPerMultiprocessor;
    // The regsPerMultiprocessor is the maximum number of 32-bit registers available to a multiprocessor;
    // this number is shared by all thread blocks simultaneously resident on a multiprocessor.
    int regsPerMultiprocessor;
    // The managedMemory is 1 if the device supports allocating managed memory on this system, or 0 if it is not supported.
    int managedMemory;
    // The isMultiGpuBoard is 1 if the device is on a multi-GPU board (e.g. Gemini cards), and 0 if not;
    int isMultiGpuBoard;
    // The multiGpuBoardGroupID is a unique identifier for a group of devices associated with the same board.
    // Devices on the same multi-GPU board will share the same identifier.
    int multiGpuBoardGroupID;
    // The hostNativeAtomicSupported is 1 if the link between the device and the host supports native
    // atomic operations, or 0 if it is not supported.
    int hostNativeAtomicSupported;
    // The singleToDoublePrecisionPerfRatio is the ratio of single precision performance (in floatingpoint
    // operations per second) to double precision performance.
    int singleToDoublePrecisionPerfRatio;
    // The pageableMemoryAccess is 1 if the device supports coherently accessing pageable memory
    // without calling cudaHostRegister on it, and 0 otherwise.
    int pageableMemoryAccess;
    // The concurrentManagedAccess is 1 if the device can coherently access managed memory
    // concurrently with the CPU, and 0 otherwise.
    int concurrentManagedAccess;
    // The computePreemptionSupported is 1 if the device supports Compute Preemption, and 0 otherwise.
    int computePreemptionSupported;
    // The canUseHostPointerForRegisteredMem is 1 if the device can access host registered
    // memory at the same virtual address as the CPU, and 0 otherwise.
    int canUseHostPointerForRegisteredMem;
    // The cooperativeLaunch is 1 if the device supports launching cooperative kernels via
    // cudaLaunchCooperativeKernel, and 0 otherwise.
    int cooperativeLaunch;
    // The cooperativeMultiDeviceLaunch is 1 if the device supports launching cooperative kernels via
    // cudaLaunchCooperativeKernelMultiDevice, and 0 otherwise.
    int cooperativeMultiDeviceLaunch;
    // The sharedMemPerBlockOptin is the per device maximum shared memory per block usable by special opt in
    int sharedMemPerBlockOptin;
    // The pageableMemoryAccessUsesHostPageTables is 1 if the device accesses pageable memory
    // via the host's page tables, and 0 otherwise.
    int pageableMemoryAccessUsesHostPageTables;
    // The directManagedMemAccessFromHost is 1 if the host can directly access managed memory
    // on the device without migration, and 0 otherwise.
    int directManagedMemAccessFromHost;
    // The maxBlocksPerMultiProcessor is the maximum number of thread blocks that can reside on a multiprocessor.
    int maxBlocksPerMultiProcessor;
    // The accessPolicyMaxWindowSize is the maximum value of cudaAccessPolicyWindow::num_bytes.
    int accessPolicyMaxWindowSize;
    // The reservedSharedMemPerBlock is the shared memory reserved by CUDA driver per block in bytes.
    size_t reservedSharedMemPerBlock;
    // The hostRegisterSupported is 1 if the device supports host memory registration via cudaHostRegister,
    // and 0 otherwise.
    int hostRegisterSupported;
    // The sparseCudaArraySupported is 1 if the device supports sparse CUDA arrays and sparse
    // CUDA mipmapped arrays, 0 otherwise.
    int sparseCudaArraySupported;
    // The hostRegisterReadOnlySupported is 1 if the device supports using the cudaHostRegister
    // flag cudaHostRegisterReadOnly to register memory that must be mapped as read-only to the GPU.
    int hostRegisterReadOnlySupported;
    // The timelineSemaphoreInteropSupported is 1 if external timeline semaphore interop is supported on the device,
    // 0 otherwise.
    int timelineSemaphoreInteropSupported;
    // The memoryPoolsSupported is 1 if the device supports using the cudaMallocAsync and cudaMemPool family of APIs,
    // 0 otherwise.
    int memoryPoolsSupported;
    // The gpuDirectRDMASupported is 1 if the device supports GPUDirect RDMA APIs, 0 otherwise.
    int gpuDirectRDMASupported;
    // The gpuDirectRDMAFlushWritesOptions is a bitmask to be interpreted according to the
    // cudaFlushGPUDirectRDMAWritesOptions enum.
    unsigned int gpuDirectRDMAFlushWritesOptions;
    // The gpuDirectRDMAWritesOrdering See the cudaGPUDirectRDMAWritesOrdering enum for numerical values.
    int gpuDirectRDMAWritesOrdering;
    // The memoryPoolSupportedHandleTypes is a bitmask of handle types supported with mempoolbased IPC.
    unsigned int memoryPoolSupportedHandleTypes;
    // The deferredMappingCudaArraySupported is 1 if the device supports deferred mapping CUDA arrays
    int deferredMappingCudaArraySupported;
    // The ipcEventSupported is 1 if the device supports IPC Events, and 0 otherwise.
    int ipcEventSupported;
    // The unifiedFunctionPointers is 1 if the device support unified pointers, and 0 otherwise.
    int unifiedFunctionPointers;
    // The physicalMemoryAlignment is the alignment requirement for physical memory allocations in bytes.
    int physicalMemoryAlignment;
} palDeviceProperties;

// Structure to hold device memory information
typedef struct palDeviceMemoryInfo_st
{
    palUint64 totalVirtualMemorySize; // Total virtual memory in bytes
    palUint64 freeVirtualMemorySize;  // Free virtual memory in bytes
    palUint64 totalPhysicalMemorySize; // Total physical memory in bytes
    palUint64 freePhysicalMemorySize;  // Free physical memory in bytes
} palDeviceMemoryInfo;

#endif // PAL_DEVICEPROPERTIES_H_