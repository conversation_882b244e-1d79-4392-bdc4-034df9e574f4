#ifndef PAL_STRUCTURES_H_
#define PAL_STRUCTURES_H_

typedef struct palHashMap_st palHashMap;
typedef struct palListNode_st palListNode;
typedef struct palRbTreeNode_st palRbTreeNode;
typedef struct palRbTree_st palRbTree;
typedef struct palSettingValueInfo_st palSettingValueInfo;
typedef struct palSettingsFileMgr_st palSettingsFileMgr;
typedef struct palVector_st palVector;

typedef struct palGlobalManager_st palGlobalManager;
typedef struct palTlsThreadManager_st palTlsThreadManager;
typedef struct palTlsThreadData_st palTlsThreadData;
typedef struct palDeviceManager_st palDeviceManager;
typedef struct palDevice_st palDevice;
typedef struct palContext_st palContext;
typedef struct palContextCreateInfo_st palContextCreateInfo;
typedef struct palContextPersistentState_st palContextPersistentState;
typedef struct palStream_st palStream;
typedef struct palEvent_st palEvent;
typedef struct palFence_st palFence;
typedef struct palGraph_st palGraph;
typedef struct palAsyncCallbackEntry_st palAsyncCallbackEntry;
typedef struct palAsyncTask_st palAsyncTask;
typedef struct palAsyncTaskManager_st palAsyncTaskManager;

typedef struct palMemoryManager_st palMemoryManager;
typedef struct palMemoryHeap_st palMemoryHeap;
typedef struct palMemoryPool_st palMemoryPool;
typedef struct palMemoryBlock_st palMemoryBlock;
typedef struct palMemoryChunk_st palMemoryChunk;
typedef struct palMemoryObject_st palMemoryObject;
typedef struct palUvaManager_st palUvaManager;

#endif // PAL_STRUCTURES_H_