cmake_minimum_required(VERSION 3.12)

set(THUNK_SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/${ENABLE_THUNK_LAYER}/pal_thunk.c)

# Generate static library libthunk.a
add_library(thunk STATIC ${THUNK_SOURCES})

# Add the including file path.
target_include_directories(thunk PUBLIC
    ${CMAKE_SOURCE_DIR}/src/pal
    ${CMAKE_SOURCE_DIR}/src/pal/util
    ${CMAKE_SOURCE_DIR}/src/pal/util/os
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/${ENABLE_THUNK_LAYER}
)

if(WIN32)
    target_include_directories(thunk PUBLIC ${CMAKE_SOURCE_DIR}/src/pal/util/os/windows)
elseif(UNIX)
    target_include_directories(thunk PUBLIC ${CMAKE_SOURCE_DIR}/src/pal/util/os/posix)
else()
    message(FATAL_ERROR "Unknown platform!")
endif()
