#include "pal_thunk.h"

// =====================================================================================================================
palResult palThunkInitialize(void)
{
    // For now, we just return PAL_SUCCESS as the initialization is a no-op.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetCount(palUint32* pDeviceCount)
{
    if (pDeviceCount == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    // Set the device count to the output parameter.
    // For now, we just return a dummy device count of 8.
    *pDeviceCount = 8;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceOpen(palUint32 deviceId, void** ppDevice)
{
    palInt8 deviceFileName[256] = {0};
    void*   pDevice             = NULL;

    if (ppDevice == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppDevice = NULL;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceClose(void* pDevice)
{
    if (pDevice == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetProperties(void* pDevice, palDeviceProperties* pDeviceProp)
{
    (void)pDevice;
    (void)pDeviceProp;

    // For now, we just return PAL_SUCCESS for getting dummy device's properties.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetLuid(void* pDevice, palUint32* pDeviceNodeMask, palInt8* pLuid)
{
    (void)pDevice;
    (void)pDeviceNodeMask;
    (void)pLuid;

    // For now, we just return PAL_SUCCESS for getting dummy device's LUID.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetMemoryInfo(void* pDevice, palDeviceMemoryInfo* pMemoryInfo)
{
    (void)pDevice;
    (void)pMemoryInfo;

    // For now, we just return PAL_SUCCESS for getting dummy device's memory info.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceContextCreate(void* pDevice, palUint32 flags, void** ppContext)
{
    (void)pDevice;
    (void)flags;
    (void)ppContext;

    // For now, we just return an error as device context creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceContextDestroy(void* pDevice, void* pContext)
{
    (void)pDevice;
    (void)pContext;

    // For now, we just return an error as device context destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectCreate(void* pDevice,
                                                   palThunkDevicePhysicalMemoryCreateInfo* pCreateInfo,
                                                   void** ppPhysicalMemoryObject)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppPhysicalMemoryObject;

    // For now, we just return an error as physical memory creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDestroy(void* pDevice,
                                                    palThunkDevicePhysicalMemoryDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as physical memory destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDeviceMap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryDeviceMapInfo* pMapInfo,
                                                      void** ppPhysicalMemoryDeviceMapped)
{
    (void)pDevice;
    (void)pMapInfo;
    (void)ppPhysicalMemoryDeviceMapped;

    // For now, we just return an error as mapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDeviceUnmap(void* pDevice,
                                                        palThunkDevicePhysicalMemoryDeviceUnmapInfo* pUnmapInfo)
{
    (void)pDevice;
    (void)pUnmapInfo;

    // For now, we just return an error as unmapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectHostMap(void* pDevice,
                                                    palThunkDevicePhysicalMemoryHostMapInfo* pMapInfo,
                                                    void** ppPhysicalMemoryHostMapped,
                                                    void** ppHostVirtualAddress)
{
    (void)pDevice;
    (void)pMapInfo;
    (void)ppPhysicalMemoryHostMapped;
    (void)ppHostVirtualAddress;

    // For now, we just return an error as host mapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectHostUnmap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryHostUnmapInfo* pUnmapInfo)
{
    (void)pDevice;
    (void)pUnmapInfo;

    // For now, we just return an error as host unmapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// ====================================================================================================================
palResult palThunkDeviceHostPinnedMemoryObjectCreate(void* pDevice,
                                                     palThunkHostPinnedMemoryCreateInfo* pCreateInfo,
                                                     void** ppHostPinnedMemoryObject,
                                                     void** ppHostVirtualAddress)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppHostPinnedMemoryObject;
    (void)ppHostVirtualAddress;

    // For now, we just return an error as host pinned memory creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceHostPinnedMemoryObjectDestroy(void* pDevice,
                                                      palThunkHostPinnedMemoryDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as host pinned memory destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceHostPageableMemoryObjectRegister(void* pDevice,
                                                         palThunkHostPageableMemoryRegisterInfo* pRegisterInfo,
                                                         void** ppHostRegisteredMemoryObject,
                                                         palUint64* pRegisteredOffset)
{
    (void)pDevice;
    (void)pRegisterInfo;
    (void)ppHostRegisteredMemoryObject;
    (void)pRegisteredOffset;

    // For now, we just return an error as register physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceHostPageableMemoryObjectUnregister(void* pDevice,
                                                           palThunkHostPageableMemoryUnregisterInfo* pUnregisterInfo)
{
    (void)pDevice;
    (void)pUnregisterInfo;

    // For now, we just return an error as unregister physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectExportByDmaBuf(void* pDevice,
                                                           palThunkDevicePhysicalMemoryExportInfo* pExportInfo,
                                                           palInt32* pDmaBufFd)
{
    (void)pDevice;
    (void)pExportInfo;
    (void)pDmaBufFd;

    // For now, we just return an error as dma_buf exporter are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectImportFromDmaBuf(void* pDevice,
                                                             palThunkDevicePhysicalMemoryImportInfo* pImportInfo,
                                                             void** ppRemoteContext,
                                                             void** ppPhysicalMemoryObject,
                                                             palUint64* pSize,
                                                             palUint64* pPageSize,
                                                             palUint64* pFlags)
{
    (void)pDevice;
    (void)pImportInfo;
    (void)ppRemoteContext;
    (void)ppPhysicalMemoryObject;
    (void)pSize;
    (void)pPageSize;
    (void)pFlags;

    // For now, we just return an error as dma_buf importer are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueCreate(void* pDevice,
                                            palThunkUserModeQueueCreateInfo* pCreateInfo,
                                            void** ppQueue)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppQueue;

    // For now, we just return an error as user mode queue creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueDestroy(void* pDevice, palThunkUserModeQueueDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as user mode queue destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueSubmit(void* pDevice, palThunkUserModeQueueSubmitInfo* pSubmitInfo)
{
    (void)pDevice;
    (void)pSubmitInfo;

    // For now, we just return an error as user mode queue submission are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceCreate(void* pDevice, palThunkFenceCreateInfo* pCreateInfo, void** ppFence)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppFence;

    // For now, we just return an error as fence creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceDestroy(void* pDevice, palThunkFenceDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as fence destorying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceWait(void* pDevice, palThunkFenceWaitInfo* pWaitInfo)
{
    (void)pDevice;
    (void)pWaitInfo;

    // For now, we just return an error as waiting for fence are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceSignal(void* pDevice, palThunkFenceSignalInfo* pSignalInfo)
{
    (void)pDevice;
    (void)pSignalInfo;

    // For now, we just return an error as signaling fence are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceQuery(void* pDevice, palThunkFenceQueryInfo* pQueryInfo)
{
    (void)pDevice;
    (void)pQueryInfo;

    // For now, we just return an error as querying fence are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
void palThunkDeinitialize(void)
{
}