#include "pal_thunk.h"

#include <dirent.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>

#define PAL_THUNK_DEVICE_PATH_DIR "/dev"
#define PAL_THUNK_DEVICE_PATH_PREFIX "noone"

// =====================================================================================================================
palResult palThunkInitialize(void)
{
    // For now, we just return PAL_SUCCESS as the initialization is a no-op.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetCount(palUint32* pDeviceCount)
{
    DIR*           pDir        = NULL;
    palUint32      deviceCount = 0;
    struct dirent* pEntry      = NULL;
    size_t         prefixLen   = strlen(PAL_THUNK_DEVICE_PATH_PREFIX);
    palInt8        devFileName[256];

    if (pDeviceCount == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    pDir = opendir(PAL_THUNK_DEVICE_PATH_DIR);
    if (pDir == NULL)
    {
        // If we cannot open the device directory, we assume no devices are available.
        *pDeviceCount = 0;
        return PAL_SUCCESS;
    }

    while ((pEntry = readdir(pDir)) != NULL)
    {
        // Check if the entry is a file and starts with the device path prefix.
        if (strncmp(pEntry->d_name, PAL_THUNK_DEVICE_PATH_PREFIX, prefixLen) == 0)
        {
            // Construct the full device file name.
            snprintf(devFileName, sizeof(devFileName), "%s/%s", PAL_THUNK_DEVICE_PATH_DIR, pEntry->d_name);

            struct stat st;
            if (stat(devFileName, &st) == 0 && S_ISREG(st.st_mode))
            {
                // For now, we just count the number of such files.
                deviceCount++;
            }
        }
    }

    // Set the device count to the output parameter.
    // *pDeviceCount = deviceCount;
    // WORKAROUND: For now, we just return a dummy device count of 8.
    *pDeviceCount = deviceCount;

    closedir(pDir);

    // If we found no devices, return PAL_ERROR_NO_DEVICE, otherwise return PAL_SUCCESS.
    return (deviceCount == 0) ? PAL_ERROR_NO_DEVICE : PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceOpen(palUint32 deviceId, void** ppDevice)
{
    palInt8  deviceFileName[256] = {0};
    palInt32 deviceFd            = -1;

    if (ppDevice == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    // Construct the full device file path: "/dev/nooneX"
    palOsSnprintf(deviceFileName, sizeof(deviceFileName), "%s/%s%u", PAL_THUNK_DEVICE_PATH_DIR,
                  PAL_THUNK_DEVICE_PATH_PREFIX, deviceId);

    deviceFd = open(deviceFileName, O_RDWR | O_APPEND | O_NONBLOCK);
    if (deviceFd < 0)
    {
        // If we cannot open the device file, return an error.
        PAL_DBG_PRINTF_ERROR("Failed to open device file: %s", deviceFileName);
        return PAL_ERROR_INVALID_DEVICE;
    }

    *ppDevice = (void*)deviceFd;

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceClose(void* pDevice)
{
    if (pDevice == NULL)
    {
        return PAL_ERROR_INVALID_VALUE;
    }

    // Close the device handle.
    close((palInt32)pDevice);

    // For now, we just return PAL_SUCCESS for closing dummy device handle.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetProperties(void* pDevice, palDeviceProperties* pDeviceProp)
{
    (void)pDevice;
    (void)pDeviceProp;

    // For now, we just return PAL_SUCCESS for getting dummy device's properties.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetLuid(void* pDevice, palUint32* pDeviceNodeMask, palInt8* pLuid)
{
    (void)pDevice;
    (void)pDeviceNodeMask;
    (void)pLuid;

    // For now, we just return PAL_SUCCESS for getting dummy device's LUID.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetMemoryInfo(void* pDevice, palDeviceMemoryInfo* pMemoryInfo)
{
    (void)pDevice;
    (void)pMemoryInfo;

    // For now, we just return PAL_SUCCESS for getting dummy device's memory info.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceContextCreate(void* pDevice, palUint32 flags, void** ppContext)
{
    (void)pDevice;
    (void)flags;
    (void)ppContext;

    // For now, we just return an error as device context creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceContextDestroy(void* pDevice, void* pContext)
{
    (void)pDevice;
    (void)pContext;

    // For now, we just return an error as device context destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectCreate(void* pDevice,
                                                   palThunkDevicePhysicalMemoryCreateInfo* pCreateInfo,
                                                   void** ppPhysicalMemoryObject)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppPhysicalMemoryObject;

    // For now, we just return an error as physical memory creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDestroy(void* pDevice,
                                                    palThunkDevicePhysicalMemoryDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as physical memory destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDeviceMap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryDeviceMapInfo* pMapInfo,
                                                      void** ppPhysicalMemoryDeviceMapped)
{
    (void)pDevice;
    (void)pMapInfo;
    (void)ppPhysicalMemoryDeviceMapped;

    // For now, we just return an error as mapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDeviceUnmap(void* pDevice,
                                                        palThunkDevicePhysicalMemoryDeviceUnmapInfo* pUnmapInfo)
{
    (void)pDevice;
    (void)pUnmapInfo;

    // For now, we just return an error as unmapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectHostMap(void* pDevice,
                                                    palThunkDevicePhysicalMemoryHostMapInfo* pMapInfo,
                                                    void** ppPhysicalMemoryHostMapped,
                                                    void** ppHostVirtualAddress)
{
    (void)pDevice;
    (void)pMapInfo;
    (void)ppPhysicalMemoryHostMapped;
    (void)ppHostVirtualAddress;

    // For now, we just return an error as host mapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectHostUnmap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryHostUnmapInfo* pUnmapInfo)
{
    (void)pDevice;
    (void)pUnmapInfo;

    // For now, we just return an error as host unmapping physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectExportByDmaBuf(void* pDevice,
                                                           palThunkDevicePhysicalMemoryExportInfo* pExportInfo,
                                                           palInt32* pDmaBufFd)
{
    (void)pDevice;
    (void)pExportInfo;
    (void)pDmaBufFd;

    // For now, we just return an error as dma_buf exporter are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectImportFromDmaBuf(void* pDevice,
                                                             palThunkDevicePhysicalMemoryImportInfo* pImportInfo,
                                                             void** ppRemoteContext,
                                                             void** ppPhysicalMemoryObject,
                                                             palUint64* pSize,
                                                             palUint64* pPageSize,
                                                             palUint64* pFlags)
{
    (void)pDevice;
    (void)pImportInfo;
    (void)ppPhysicalMemoryObject;
    (void)pSize;
    (void)pPageSize;
    (void)pFlags;

    // For now, we just return an error as dma_buf importer are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// ====================================================================================================================
palResult palThunkDeviceHostPinnedMemoryObjectCreate(void* pDevice,
                                                     palThunkHostPinnedMemoryCreateInfo* pCreateInfo,
                                                     void** ppHostPinnedMemoryObject,
                                                     void** ppHostVirtualAddress)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppHostPinnedMemoryObject;
    (void)ppHostVirtualAddress;

    // For now, we just return an error as host pinned memory creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceHostPinnedMemoryObjectDestroy(void* pDevice,
                                                      palThunkHostPinnedMemoryDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as host pinned memory destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceHostPageableMemoryObjectRegister(void* pDevice,
                                                         palThunkHostPageableMemoryRegisterInfo* pRegisterInfo,
                                                         void** ppHostRegisteredMemoryObject,
                                                         palUint64* pRegisteredOffset)
{
    (void)pDevice;
    (void)pRegisterInfo;
    (void)ppHostRegisteredMemoryObject;
    (void)pRegisteredOffset;

    // For now, we just return an error as register physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceHostPageableMemoryObjectUnregister(void* pDevice,
                                                           palThunkHostPageableMemoryUnregisterInfo* pUnregisterInfo)
{
    (void)pDevice;
    (void)pUnregisterInfo;

    // For now, we just return an error as unregister physical memory are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueCreate(void* pDevice,
                                            palThunkUserModeQueueCreateInfo* pCreateInfo,
                                            void** ppQueue)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppQueue;

    // For now, we just return an error as user mode queue creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueDestroy(void* pDevice, palThunkUserModeQueueDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as user mode queue destroying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueSubmit(void* pDevice, palThunkUserModeQueueSubmitInfo* pSubmitInfo)
{
    (void)pDevice;
    (void)pSubmitInfo;

    // For now, we just return an error as user mode queue submission are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceCreate(void* pDevice, palThunkFenceCreateInfo* pCreateInfo, void** ppFence)
{
    (void)pDevice;
    (void)pCreateInfo;
    (void)ppFence;

    // For now, we just return an error as fence creation are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceDestroy(void* pDevice, palThunkFenceDestroyInfo* pDestroyInfo)
{
    (void)pDevice;
    (void)pDestroyInfo;

    // For now, we just return an error as fence destorying are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceWait(void* pDevice, palThunkFenceWaitInfo* pWaitInfo)
{
    (void)pDevice;
    (void)pWaitInfo;

    // For now, we just return an error as waiting for fence are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceSignal(void* pDevice, palThunkFenceSignalInfo* pSignalInfo)
{
    (void)pDevice;
    (void)pSignalInfo;

    // For now, we just return an error as signaling fence are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceQuery(void* pDevice, palThunkFenceQueryInfo* pQueryInfo)
{
    (void)pDevice;
    (void)pQueryInfo;

    // For now, we just return an error as querying fence are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeinitialize(void)
{
    // For now, we just return PAL_SUCCESS as the deinitialization is a no-op.
    return PAL_SUCCESS;
}