#ifndef PAL_THUNK_DEVICE_H_
#define PAL_THUNK_DEVICE_H_

#include "pal.h"
#include "pal_deviceproperties.h"
#include "pal_types.h"

// The maximum number of devices that can be managed by the PAL Thunk layer.
#define PAL_THUNK_MAX_DEVICE_COUNT 32

// UUIDs are 40 characters long, plus 1 for the null terminator.
#define PAL_THUNK_MAX_DEVICE_UUID_LENGTH 41

typedef struct palThunkDevicePhysicalMemoryCreateInfo_st
{
    // Pointer to the context for which the physical memory is created
    void* pContext;

    // Size of the physical memory to create in bytes
    palUint64 size;

    // Size of the pages in bytes
    palUint64 pageSize;

    // Flags for memory creation, e.g., read/write permissions
    palUint64 flags;
} palThunkDevicePhysicalMemoryCreateInfo;

typedef struct palThunkDevicePhysicalMemoryDestroyInfo_st
{
    // Pointer to the context for which the physical memory is destroyed
    void* pContext;

    // Pointer to the physical memory object to destroy
    void* pPhysicalMemoryObject;
} palThunkDevicePhysicalMemoryDestroyInfo;

typedef struct palThunkDevicePhysicalMemoryDeviceMapInfo_st
{
    // Pointer to the context for which the physical memory is mapped
    void* pContext;

    // Device virtual address to map the physical memory to (0 for automatic allocation)
    // Note: This virtual address must be aligned to the device's physical memory alignment requirement.
    // That means the address must be aligned to 4KB.
    palUint64 deviceVirtualAddress;

    // Pointer to the device (host pinned or pageable) physical memory object to be mapped to the device.
    void* pPhysicalMemoryObject;

    // flags for memory mapping, e.g., read/write permissions
    palUint64 flags;

    // Size of the mapping in bytes
    palUint64 size;
} palThunkDevicePhysicalMemoryDeviceMapInfo;

typedef struct palThunkDevicePhysicalMemoryDeviceUnmapInfo_st
{
    // Pointer to the context for which the physical memory is unmapped
    void* pContext;

    // Pointer to the mapped physical memory handle to unmap
    void* pPhysicalMemoryDeviceMapped;
} palThunkDevicePhysicalMemoryDeviceUnmapInfo;

typedef struct palThunkDevicePhysicalMemoryHostMapInfo_st
{
    // Pointer to the context for which the physical memory is mapped
    void* pContext;

    // Pointer to the physical memory object to map
    void* pPhysicalMemoryObject;

    // flags for memory mapping, e.g., read/write permissions
    palUint64 flags;

    // Size of the mapping in bytes
    palUint64 size;
} palThunkDevicePhysicalMemoryHostMapInfo;

typedef struct palThunkDevicePhysicalMemoryHostUnmapInfo_st
{
    // Pointer to the context for which the physical memory is unmapped
    void* pContext;

    // Pointer to the host mapped handle to unmap
    void* pPhysicalMemoryHostMapped;
} palThunkDevicePhysicalMemoryHostUnmapInfo;

typedef struct palThunkDevicePhysicalMemoryExportInfo_st
{
    // Pointer to the context for which the physical memory is exported
    void* pContext;

    // Pointer to the physical memory object to export
    void* pPhysicalMemoryObject;

    // Size of the physical memory to export in bytes
    palUint64 size;

    // The page size of the physical memory to export in bytes
    palUint64 pageSize;

    // Flags for memory export, e.g., read/write permissions
    palUint64 flags;
} palThunkDevicePhysicalMemoryExportInfo;

typedef struct palThunkDevicePhysicalMemoryImportInfo_st
{
    // Pointer to the context for which the physical memory is imported
    void* pContext;

    // File descriptor of the DMA buffer to import
    palInt32 dmaBufFd;
} palThunkDevicePhysicalMemoryImportInfo;

typedef struct palThunkHostPinnedMemoryCreateInfo_st
{
    // Pointer to the context for which the host pinned memory is created
    void* pContext;

    // Size of the host pinned memory to create in bytes
    palUint64 size;

    // Flags for host pinned memory creation, e.g., read/write permissions
    palUint64 flags;
} palThunkHostPinnedMemoryCreateInfo;

typedef struct palThunkHostPinnedMemoryDestroyInfo_st
{
    // Pointer to the context for which the host pinned memory is destroyed
    void* pContext;

    // Pointer to the host pinned memory object to destroy
    void* pHostPinnedMemoryObject;
} palThunkHostPinnedMemoryDestroyInfo;

typedef struct palThunkHostPageableMemoryRegisterInfo_st
{
    // Pointer to the context for which the host pageable memory is registered
    void* pContext;

    // Pointer to host pageable memory to register
    void* pHostPtr;

    // Size of the host memory to register in bytes
    size_t size;

    // Flags for memory registration, e.g., read/write permissions
    palUint64 flags;
} palThunkHostPageableMemoryRegisterInfo;

typedef struct palThunkHostPageableMemoryUnregisterInfo_st
{
    // Pointer to the context for which the host pageable memory is unregistered
    void* pContext;

    // Pointer to pageable host memory to unregister
    void* pHostPtr;

    // Pointer to the registered pageable host memory object
    void* pHostRegisteredMemoryObject;
} palThunkHostPageableMemoryUnregisterInfo;

typedef struct palThunkUserModeQueueCreateInfo_st
{
    // Pointer to the context for which the user mode queue is created
    void* pContext;

    // Flags for creating the user mode queue, e.g., priority
    palUint32 flags;
} palThunkUserModeQueueCreateInfo;

typedef struct palThunkUserModeQueueDestroyInfo_st
{
    // Pointer to the context for which the user mode queue is destroyed
    void* pContext;

    // Pointer to the user mode queue to destroy
    void* pQueue;
} palThunkUserModeQueueDestroyInfo;

typedef struct palThunkUserModeQueueSubmitInfo_st
{
    // Pointer to the context for which the user mode queue is submitted
    void* pContext;

    // Pointer to the user mode queue to submit
    void* pQueue;

    // Pointer to the write pointer for the submission
    void* writePtr;

    // Size of the submission in bytes
    size_t size;
} palThunkUserModeQueueSubmitInfo;

typedef struct palThunkFenceCreateInfo_st
{
    // Pointer to the context for which the fence is created
    void* pContext;

    // Flags for creating the fence, e.g., signaled state
    palUint32 flags;
} palThunkFenceCreateInfo;

typedef struct palThunkFenceDestroyInfo_st
{
    // Pointer to the context for which the fence is destroyed
    void* pContext;

    // Pointer to the fence to destroy
    void* pFence;
} palThunkFenceDestroyInfo;

typedef struct palThunkFenceWaitInfo_st
{
    // Pointer to the context for which the fence wait is performed
    void* pContext;

    // Pointer to the user mode queue associated with the fence wait
    void* pQueue;

    // Pointer to the fence to wait on
    void* pFence;
} palThunkFenceWaitInfo;

typedef struct palThunkFenceSignalInfo_st
{
    // Pointer to the context for which the fence is signaled
    void* pContext;

    // Pointer to the user mode queue to signal the fence on
    void* pQueue;

    // Pointer to the fence to signal
    void* pFence;
} palThunkFenceSignalInfo;

typedef struct palThunkFenceQueryInfo_st
{
    // Pointer to the context for which the fence is queried
    void* pContext;

    // Pointer to the fence to query
    void* pFence;
} palThunkFenceQueryInfo;

/// @brief Initialize the PAL Thunk layer.
///
/// This function should be called before any other PAL Thunk functions are used.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkInitialize(void);

/// @brief Get the number of devices available.
///
/// @param pDeviceCount Pointer to store the number of devices found.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceGetCount(palUint32* pDeviceCount);

/// @brief Open a device handle.
///
/// @param deviceId The ID of the device to open.
/// @param ppDevice Pointer to store the device handle.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceOpen(palUint32 deviceId, void** ppDevice);

/// @brief Close a device handle.
///
/// @param pDevice The device handle to close.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceClose(void* pDevice);

/// @brief Get the properties of a device.
///
/// @param pDevice The device handle.
/// @param pDeviceProp Pointer to store the device properties.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceGetProperties(void* pDevice, palDeviceProperties* pDeviceProp);

/// @brief Get the LUID (Locally Unique Identifier) of a device.
///
/// @param pDevice The device handle to get the LUID for.
/// @param pDeviceNodeMask Pointer to store the device node mask.
/// @param pLuid Pointer to store the LUID of the device.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceGetLuid(void* pDevice, palUint32* pDeviceNodeMask, palInt8* pLuid);

/// @brief Get memory information for the device.
///
/// @param pDevice The device handle to get memory information from.
/// @param pMemoryInfo Pointer to store the device memory information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceGetMemoryInfo(void* pDevice, palDeviceMemoryInfo* pMemoryInfo);

/// @brief Create a device context.
///
/// @param pDevice The device handle to create the context for.
/// @param flags Flags for creating the context, e.g., priority, scheduler.
/// @param ppContext Pointer to store the created context handle.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceContextCreate(void* pDevice, palUint32 flags, void** ppContext);

/// @brief Destroy a device context.
///
/// @param pDevice The device handle for which the context will be destroyed.
/// @param pContext The device context handle to destroy.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceContextDestroy(void* pDevice, void* pContext);

/// @brief Create device physical memory object.
///
/// @param pDevice The device handle to create the physical memory for.
/// @param pCreateInfo Pointer to the physical memory creation information.
/// @param ppPhysicalMemoryObject Pointer to store the created physical memory object handle.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectCreate(void* pDevice,
                                                   palThunkDevicePhysicalMemoryCreateInfo* pCreateInfo,
                                                   void** ppPhysicalMemoryObject);

/// @brief Destroy device physical memory object.
///
/// @param pDevice The device handle to destroy the physical memory for.
/// @param pDestroyInfo Pointer to the physical memory destruction information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectDestroy(void* pDevice,
                                                    palThunkDevicePhysicalMemoryDestroyInfo* pDestroyInfo);

/// @brief Map physical memory object to a virtual address space.
///
/// @param pDevice The device handle to map the physical memory for.
/// @param pMapInfo Pointer to the physical memory mapping information.
/// @param ppPhysicalMemoryDeviceMapped Pointer to store the device mapped memory pointer.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectDeviceMap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryDeviceMapInfo* pMapInfo,
                                                      void** ppPhysicalMemoryDeviceMapped);

/// @brief Unmap physical memory from a virtual address space.
///
/// @param pDevice The device handle to unmap the physical memory from.
/// @param pUnmapInfo Pointer to the physical memory unmapping information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectDeviceUnmap(void* pDevice,
                                                        palThunkDevicePhysicalMemoryDeviceUnmapInfo* pUnmapInfo);

/// @brief Host map physical memory to a host address space.
/// @param pDevice The device handle to host map the physical memory for.
/// @param pMapInfo Pointer to the physical memory host mapping information.
/// @param ppHostMapped Pointer to store the host mapped handle.
/// @param ppHostVirtualAddress Pointer to store the host virtual address of the mapped memory.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectHostMap(void* pDevice,
                                                    palThunkDevicePhysicalMemoryHostMapInfo* pMapInfo,
                                                    void** ppPhysicalMemoryHostMapped,
                                                    void** ppHostVirtualAddress);

/// @brief Host unmap physical memory from a host address space.
/// @param pDevice The device handle to host unmap the device physical memory from.
/// @param pUnmapInfo Pointer to the device physical memory host unmapping information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectHostUnmap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryHostUnmapInfo* pUnmapInfo);

/// @brief Export device physical memory to a DMA buffer.
///
/// @param pDevice The device handle to export the physical memory from.
/// @param pExportInfo The exported physical memory information.
/// @param pDmaBufFd Pointer to store the file descriptor of the exported DMA buffer.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectExportByDmaBuf(void* pDevice,
                                                           palThunkDevicePhysicalMemoryExportInfo* pExportInfo,
                                                           palInt32* pDmaBufFd);

/// @brief Import device physical memory from a DMA buffer.
///
/// @param pDevice The device handle to import the physical memory to.
/// @param pImportInfo The imported physical memory information.
/// @param ppPhysicalMemory Pointer to store the imported physical memory handle.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDevicePhysicalMemoryObjectImportFromDmaBuf(void* pDevice,
                                                             palThunkDevicePhysicalMemoryImportInfo* pImportInfo,
                                                             void** ppRemoteContext,
                                                             void** ppPhysicalMemoryObject,
                                                             palUint64* pSize,
                                                             palUint64* pPageSize,
                                                             palUint64* pFlags);

/// @brief Create host pinned memory object.
/// @param pDevice The device handle to create the host pinned memory for.
/// @param pCreateInfo Pointer to the host pinned memory creation information.
/// @param ppHostPinnedMemoryObject Pointer to store the created host pinned memory object handle.
/// @param ppHostVirtualAddress Pointer to store the host virtual address of the created host pinned memory object.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceHostPinnedMemoryObjectCreate(void* pDevice,
                                                     palThunkHostPinnedMemoryCreateInfo* pCreateInfo,
                                                     void** ppHostPinnedMemoryObject,
                                                     void** ppHostVirtualAddress);

/// @brief Destroy host pinned memory object.
/// @param pDevice The device handle to destroy the host pinned memory for.
/// @param pDestroyInfo Pointer to the host pinned memory destruction information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceHostPinnedMemoryObjectDestroy(void* pDevice,
                                                      palThunkHostPinnedMemoryDestroyInfo* pDestroyInfo);

/// @brief Register physical memory for use with the device.
/// @param pDevice The device handle to register the physical memory for.
/// @param pRegisterInfo Pointer to the physical memory registration information.
/// @param ppHostRegisteredMemoryObject Pointer to store the registered host pageable memory object.
/// @param pRegisteredOffset Pointer to store the offset of the start address from the registered memory object.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceHostPageableMemoryObjectRegister(void* pDevice,
                                                         palThunkHostPageableMemoryRegisterInfo* pRegisterInfo,
                                                         void** ppHostRegisteredMemoryObject,
                                                         palUint64* pRegisteredOffset);

/// @brief Unregister physical memory that was previously registered.
///
/// @param pDevice The device handle to unregister the physical memory from.
/// @param pUnregisterInfo Pointer to the physical memory unregistration information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceHostPageableMemoryObjectUnregister(void* pDevice,
                                                           palThunkHostPageableMemoryUnregisterInfo* pUnregisterInfo);

/// @brief Create a user mode queue for the device.
///
/// @param pDevice The device handle to create the user mode queue for.
/// @param pCreateInfo Pointer to the user mode queue creation information.
/// @param ppQueue Pointer to store the created user mode queue handle.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceUserModeQueueCreate(void* pDevice,
                                            palThunkUserModeQueueCreateInfo* pCreateInfo,
                                            void** ppQueue);

/// @brief Destroy a user mode queue.
///
/// @param pDevice The device handle for which the user mode queue was created.
/// @param pDestroyInfo Pointer to the user mode queue destruction information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceUserModeQueueDestroy(void* pDevice, palThunkUserModeQueueDestroyInfo* pDestroyInfo);

/// @brief Submit a user mode queue for execution.
///
/// @param pQueue The user mode queue handle to submit.
/// @param writePtr Pointer to the write pointer for the queue submission.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceUserModeQueueSubmit(void* pDevice, palThunkUserModeQueueSubmitInfo* pSubmitInfo);

/// @brief Create a fence for synchronization.
///
/// @param pContext The context handle to create the fence for.
/// @param pCreateInfo Pointer to the fence creation information.
/// @param ppFence Pointer to store the created fence handle.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceFenceCreate(void* pDevice, palThunkFenceCreateInfo* pCreateInfo, void** ppFence);

/// @brief Destroy a fence.
///
/// @param pDevice The device handle for which the fence was created.
/// @param pDestroyInfo Pointer to the fence destruction information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceFenceDestroy(void* pDevice, palThunkFenceDestroyInfo* pDestroyInfo);

/// @brief Wait for a fence to be signaled.
///
/// @param pDevice The device handle to wait for the fence on.
/// @param pWaitInfo Pointer to the fence wait information.
/// @return Returns PAL_SUCCESS if the fence was signaled, PAL_ERROR_TIMEOUT if the timeout expired,
///         or an error code on failure.
palResult palThunkDeviceFenceWait(void* pDevice, palThunkFenceWaitInfo* pWaitInfo);

/// @brief Signal a fence to indicate completion of an operation.
///
/// @param pDevice The device handle used to signal the fence.
/// @param pSignalInfo Pointer to the fence signal information.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
palResult palThunkDeviceFenceSignal(void* pDevice, palThunkFenceSignalInfo* pSignalInfo);

/// @brief Query the status of a fence to check if it has been signaled.
///
/// @param pFence The fence handle to query.
/// @return Returns PAL_SUCCESS if the fence is signaled, PAL_ERROR_NOT_READY if it is not signaled,
///         or an error code on failure.
palResult palThunkDeviceFenceQuery(void* pDevice, palThunkFenceQueryInfo* pQueryInfo);

/// @brief Deinitialize the PAL Thunk layer.
///
/// This function should be called when the PAL Thunk layer is no longer needed.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
void palThunkDeinitialize(void);

#endif // PAL_THUNK_DEVICE_H_