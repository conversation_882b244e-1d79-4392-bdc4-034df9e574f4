#ifndef KFE_TYPEDEFS_H_
#define KFE_TYPEDEFS_H_

#include <kfe.h>

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

/*
 * Macros for the latest version for each KFE function in kfe.h
 */
#define PFN_kfeInitialize PFN_kfeInitialize_v1000
#define PFN_kfeDeviceGetCount PFN_kfeDeviceGetCount_v1000
#define PFN_kfeDeviceOpen PFN_kfeDeviceOpen_v1000
#define PFN_kfeDeviceClose PFN_kfeDeviceClose_v1000
#define PFN_kfeDeviceGetProperties PFN_kfeDeviceGetProperties_v1000
#define PFN_kfeContextCreate PFN_kfeContextCreate_v1000
#define PFN_kfeContextDestroy PFN_kfeContextDestroy_v1000
#define PFN_kfePhysicalMemoryCreate PFN_kfePhysicalMemoryCreate_v1000
#define PFN_kfePhysicalMemoryDestroy PFN_kfePhysicalMemoryDestroy_v1000
#define PFN_kfePhysicalMemoryDeviceMap PFN_kfePhysicalMemoryDeviceMap_v1000
#define PFN_kfePhysicalMemoryDeviceUnmap PFN_kfePhysicalMemoryDeviceUnmap_v1000
#define PFN_kfePhysicalMemoryHostMap PFN_kfePhysicalMemoryHostMap_v1000
#define PFN_kfePhysicalMemoryHostUnmap PFN_kfePhysicalMemoryHostUnmap_v1000
#define PFN_kfePhysicalMemoryExportByDmaBuf PFN_kfePhysicalMemoryExportByDmaBuf_v1000
#define PFN_kfePhysicalMemoryImportFromDmaBuf PFN_kfePhysicalMemoryImportFromDmaBuf_v1000
#define PFN_kfePinnedMemoryCreate PFN_kfePinnedMemoryCreate_v1000
#define PFN_kfePinnedMemoryDestroy PFN_kfePinnedMemoryDestroy_v1000
#define PFN_kfePageableMemoryHostRegister PFN_kfePageableMemoryHostRegister_v1000
#define PFN_kfePageableMemoryHostUnregister PFN_kfePageableMemoryHostUnregister_v1000
#define PFN_kfeUserModeQueueCreate PFN_kfeUserModeQueueCreate_v1000
#define PFN_kfeUserModeQueueDestroy PFN_kfeUserModeQueueDestroy_v1000
#define PFN_kfeUserModeQueueSubmit PFN_kfeUserModeQueueSubmit_v1000
#define PFN_kfeFenceCreate PFN_kfeFenceCreate_v1000
#define PFN_kfeFenceDestroy PFN_kfeFenceDestroy_v1000
#define PFN_kfeFenceWait PFN_kfeFenceWait_v1000
#define PFN_kfeFenceSignal PFN_kfeFenceSignal_v1000
#define PFN_kfeFenceQuery PFN_kfeFenceQuery_v1000
#define PFN_kfeFinalize PFN_kfeFinalize_v1000

/*
 * Function pointer typedefs for the KFE function in kfe.h
 */
typedef kfeResult (KFEAPI *PFN_kfeInitialize_v1000)(void);
typedef kfeResult (KFEAPI *PFN_kfeDeviceGetCount_v1000)(int* pCount);
typedef kfeResult (KFEAPI *PFN_kfeDeviceOpen_v1000)(kfeDevice* pDevice, int gpuId);
typedef kfeResult (KFEAPI *PFN_kfeDeviceClose_v1000)(kfeDevice device);
typedef kfeResult (KFEAPI *PFN_kfeDeviceGetProperties_v1000)(kfeDevice device, kfeDeviceProperties* pProperties);
typedef kfeResult (KFEAPI *PFN_kfeContextCreate_v1000)(kfeContext* pContext, unsigned int flags, kfeDevice device);
typedef kfeResult (KFEAPI *PFN_kfeContextDestroy_v1000)(kfeContext context);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryCreate_v1000)(kfeContext context, uint64_t size, uint64_t pageSize, uint64_t flags, kfePhysicalMemoryObject* pMemoryObject);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryDestroy_v1000)(kfeContext context, kfePhysicalMemoryObject memoryObject);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryDeviceMap_v1000)(kfeContext context, void* pDeviceVirtualAddress, kfePhysicalMemoryObject memoryObject,
                                                                 uint64_t size, uint64_t flags, kfePhysicalMemoryObjectDeviceMapped* pPhysicalMemoryDeviceMapped);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryDeviceUnmap_v1000)(kfeContext context, kfePhysicalMemoryObjectDeviceMapped physicalMemoryDeviceMapped);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryHostMap_v1000)(kfeContext context, kfePhysicalMemoryObject memoryObject, uint64_t size,
                                                               uint64_t flags, kfePhysicalMemoryObjectHostMapped* pPhysicalMemoryHostMapped,
                                                               void** ppHostVirtualAddress);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryHostUnmap_v1000)(kfeContext context, kfePhysicalMemoryObjectHostMapped physicalMemoryHostMapped);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryExportByDmaBuf_v1000)(kfeContext context, kfePhysicalMemoryObject memoryObject, uint64_t size,
                                                                      uint64_t pageSize, uint64_t flags, int* pDmaBufFd);
typedef kfeResult (KFEAPI *PFN_kfePhysicalMemoryImportFromDmaBuf_v1000)(kfeContext context, int dmaBufFd, kfeContext* pRemoteContext,
                                                                        kfePhysicalMemoryObject* pMemoryObject, uint64_t* pSize,
                                                                        uint64_t* pPageSize, uint64_t* pFlags);
typedef kfeResult (KFEAPI *PFN_kfePinnedMemoryCreate_v1000)(kfeContext context, uint64_t size, uint64_t flags, kfePinnedMemoryObject* pPinnedMemory,
                                                            void** ppHostVirtualAddress);
typedef kfeResult (KFEAPI *PFN_kfePinnedMemoryDestroy_v1000)(kfeContext context, kfePinnedMemoryObject pinnedMemory);
typedef kfeResult (KFEAPI *PFN_kfePageableMemoryHostRegister_v1000)(kfeContext context, void* pHostVirtualAddress, uint64_t size, uint64_t flags,
                                                                    kfePageableMemoryHostRegistered* pHostPageableMemoryRegistered,
                                                                    uint64_t* pRegisteredOffset);
typedef kfeResult (KFEAPI *PFN_kfePageableMemoryHostUnregister_v1000)(kfeContext context, kfePageableMemoryHostRegistered hostPageableMemoryRegistered);
typedef kfeResult (KFEAPI *PFN_kfeUserModeQueueCreate_v1000)(kfeUserModeQueue* pUserModeQueue, unsigned int flags, kfeContext context);
typedef kfeResult (KFEAPI *PFN_kfeUserModeQueueDestroy_v1000)(kfeUserModeQueue userModeQueue);
typedef kfeResult (KFEAPI *PFN_kfeUserModeQueueSubmit_v1000)(kfeUserModeQueue userModeQueue, kfeCommandPacket* pCommandPackets,
                                                             unsigned int numCmdPackets);
typedef kfeResult (KFEAPI *PFN_kfeFenceCreate_v1000)(kfeFence* pFence, kfeContext context);
typedef kfeResult (KFEAPI *PFN_kfeFenceDestroy_v1000)(kfeFence fence);
typedef kfeResult (KFEAPI *PFN_kfeFenceWait_v1000)(kfeUserModeQueue userModeQueue, kfeFence fence);
typedef kfeResult (KFEAPI *PFN_kfeFenceSignal_v1000)(kfeUserModeQueue userModeQueue, kfeFence fence);
typedef kfeResult (KFEAPI *PFN_kfeFenceQuery_v1000)(kfeFence fence);
typedef kfeResult (KFEAPI *PFN_kfeFinalize_v1000)(void);

#ifdef __cplusplus
}
#endif // __cplusplus

#endif // KFE_TYPEDEFS_H_