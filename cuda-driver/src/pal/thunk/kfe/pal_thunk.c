#include "kfe_typedefs.h"
#include "pal_assert.h"
#include "pal_thunk.h"
#include "pal_os.h"

typedef struct palKfeManager_st
{
    // Placeholder for any future members needed for the kfe manager.
    void* pKfeLib;

    // The function pointers for the kfe API.
    PFN_kfeInitialize pfnInitialize;
    PFN_kfeDeviceGetCount pfnDeviceGetCount;
    PFN_kfeDeviceOpen pfnDeviceOpen;
    PFN_kfeDeviceClose pfnDeviceClose;
    PFN_kfeDeviceGetProperties pfnDeviceGetProperties;
    PFN_kfeContextCreate pfnContextCreate;
    PFN_kfeContextDestroy pfnContextDestroy;
    PFN_kfePhysicalMemoryCreate pfnPhysicalMemoryCreate;
    PFN_kfePhysicalMemoryDestroy pfnPhysicalMemoryDestroy;
    PFN_kfePhysicalMemoryDeviceMap pfnPhysicalMemoryDeviceMap;
    PFN_kfePhysicalMemoryDeviceUnmap pfnPhysicalMemoryDeviceUnmap;
    PFN_kfePhysicalMemoryHostMap pfnPhysicalMemoryHostMap;
    PFN_kfePhysicalMemoryHostUnmap pfnPhysicalMemoryHostUnmap;
    PFN_kfePhysicalMemoryExportByDmaBuf pfnPhysicalMemoryExportByDmaBuf;
    PFN_kfePhysicalMemoryImportFromDmaBuf pfnPhysicalMemoryImportFromDmaBuf;
    PFN_kfePinnedMemoryCreate pfnPinnedMemoryCreate;
    PFN_kfePinnedMemoryDestroy pfnPinnedMemoryDestroy;
    PFN_kfePageableMemoryHostRegister pfnPageableMemoryHostRegister;
    PFN_kfePageableMemoryHostUnregister pfnPageableMemoryHostUnregister;
    PFN_kfeUserModeQueueCreate pfnUserModeQueueCreate;
    PFN_kfeUserModeQueueDestroy pfnUserModeQueueDestroy;
    PFN_kfeUserModeQueueSubmit pfnUserModeQueueSubmit;
    PFN_kfeFenceCreate pfnFenceCreate;
    PFN_kfeFenceDestroy pfnFenceDestroy;
    PFN_kfeFenceWait pfnFenceWait;
    PFN_kfeFenceSignal pfnFenceSignal;
    PFN_kfeFenceQuery pfnFenceQuery;
    PFN_kfeFinalize pfnFinalize;
} palKfeManager;

// Global instance of the kfe manager.
// This should be initialized in palThunkInitialize() and cleaned up in palThunkDeinitialize().
static palKfeManager* g_pKfeManager = NULL;

#define PAL_THUNK_LOAD_KFE_FUNCTION(manager, funcName)                         \
    do {                                                                       \
        (manager)->pfn##funcName = (PFN_kfe##funcName)palOsGetProcAddress(     \
            (manager)->pKfeLib, "kfe" #funcName);                              \
        if ((manager)->pfn##funcName == NULL) {                                \
            PAL_DBG_PRINTF_ERROR("Failed to load kfe" #funcName " function."); \
            palOsUnloadLibrary((manager)->pKfeLib);                            \
            free(manager);                                                     \
            return PAL_ERROR_NOT_INITIALIZED;                                  \
        }                                                                      \
    } while(0)

/// @brief Converts a kfeResult to a palResult.
/// This function maps the Kfe error codes to PAL error codes.
/// It is used to convert the results of KFE API calls to the corresponding PAL results.
/// @param result The result code from a KFE API call.
/// @return The corresponding palResult code.
static palResult ipalThunkCastKfeResult(kfeResult result)
{
    switch (result)
    {
        case KFE_SUCCESS:
            return PAL_SUCCESS;
        case KFE_ERROR_INVALID_VALUE:
            return PAL_ERROR_INVALID_VALUE;
        case KFE_ERROR_OUT_OF_MEMORY:
            return PAL_ERROR_OUT_OF_MEMORY;
        case KFE_ERROR_NOT_INITIALIZED:
            return PAL_ERROR_NOT_INITIALIZED;
        case KFE_ERROR_DEINITIALIZED:
            return PAL_ERROR_DEINITIALIZED;
        case KFE_ERROR_NOT_SUPPORTED:
            return PAL_ERROR_NOT_SUPPORTED;
        case KFE_ERROR_NO_DEVICE:
            return PAL_ERROR_NO_DEVICE;
        case KFE_ERROR_OPERATING_SYSTEM:
            return PAL_ERROR_OPERATING_SYSTEM;
        case KFE_ERROR_NOT_READY:
            return PAL_ERROR_NOT_READY;
        case KFE_ERROR_UNKNOWN:
            return PAL_ERROR_UNKNOWN;
        default:
            return PAL_ERROR_UNKNOWN;
    }
}

// =====================================================================================================================
palResult palThunkInitialize(void)
{
    palResult      result      = PAL_SUCCESS;
    palKfeManager* pKfeManager = NULL;

    pKfeManager = (palKfeManager*)malloc(sizeof(palKfeManager));
    if (pKfeManager == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to allocate memory for KFE manager.");
        return PAL_ERROR_OUT_OF_MEMORY;
    }

    memset(pKfeManager, 0, sizeof(palKfeManager));

    pKfeManager->pKfeLib = palOsLoadLibrary("libkfe.so");
    if (pKfeManager->pKfeLib == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Failed to load KFE library.");
        free(pKfeManager);
        return PAL_ERROR_NOT_INITIALIZED;
    }

    // Load the function pointers for the KFE API.
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, Initialize);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, DeviceGetCount);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, DeviceOpen);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, DeviceClose);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, DeviceGetProperties);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, ContextCreate);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, ContextDestroy);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryCreate);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryDestroy);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryDeviceMap);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryDeviceUnmap);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryHostMap);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryHostUnmap);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryExportByDmaBuf);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PhysicalMemoryImportFromDmaBuf);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PinnedMemoryCreate);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PinnedMemoryDestroy);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PageableMemoryHostRegister);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, PageableMemoryHostUnregister);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, UserModeQueueCreate);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, UserModeQueueDestroy);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, UserModeQueueSubmit);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, FenceCreate);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, FenceDestroy);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, FenceWait);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, FenceSignal);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, FenceQuery);
    PAL_THUNK_LOAD_KFE_FUNCTION(pKfeManager, Finalize);

    result = pKfeManager->pfnInitialize();
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to initialize KFE: %d", result);
        palOsUnloadLibrary(pKfeManager->pKfeLib);
        free(pKfeManager);
        return ipalThunkCastKfeResult(result);
    }

    g_pKfeManager = pKfeManager;

    // For now, we just return PAL_SUCCESS as the initialization is a no-op.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetCount(palUint32* pDeviceCount)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnDeviceGetCount == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if (pDeviceCount == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter passed to palThunkDeviceGetCount.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to get the device count.
    result = g_pKfeManager->pfnDeviceGetCount(pDeviceCount);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to get device count from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceOpen(palUint32 deviceId, void** ppDevice)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnDeviceOpen == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if (ppDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter passed to palThunkDeviceOpen.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppDevice = NULL;

    // Call the KFE API to open the device.
    result = g_pKfeManager->pfnDeviceOpen((kfeDevice*)ppDevice, deviceId);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to open device %u from KFE: %d", deviceId, result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceClose(void* pDevice)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnDeviceClose == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if (pDevice == NULL)
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameter passed to palThunkDeviceClose.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to close the device.
    result = g_pKfeManager->pfnDeviceClose((kfeDevice)pDevice);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to close device from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetProperties(void* pDevice, palDeviceProperties* pDeviceProp)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnDeviceGetProperties == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pDeviceProp == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceGetProperties.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to get the device properties.
    result = g_pKfeManager->pfnDeviceGetProperties((kfeDevice)pDevice, (kfeDeviceProperties*)pDeviceProp);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to get device properties from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetLuid(void* pDevice, palUint32* pDeviceNodeMask, palInt8* pLuid)
{
    (void)pDevice;
    (void)pDeviceNodeMask;
    (void)pLuid;

    // For now, we just return PAL_SUCCESS for getting dummy device's LUID.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceGetMemoryInfo(void* pDevice, palDeviceMemoryInfo* pMemoryInfo)
{
    (void)pDevice;
    (void)pMemoryInfo;

    // For now, we just return PAL_SUCCESS for getting dummy device's memory info.
    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceContextCreate(void* pDevice, palUint32 flags, void** ppContext)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnContextCreate == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (ppContext == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceContextCreate.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to create a device context.
    result = g_pKfeManager->pfnContextCreate((kfeContext*)ppContext, flags, (kfeDevice)pDevice);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create device context from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceContextDestroy(void* pDevice, void* pContext)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnContextDestroy == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pContext == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceContextDestroy.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to destroy the device context.
    result = g_pKfeManager->pfnContextDestroy((kfeContext)pContext);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to destroy device context from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectCreate(void* pDevice,
                                                   palThunkDevicePhysicalMemoryCreateInfo* pCreateInfo,
                                                   void** ppPhysicalMemoryObject)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryCreate == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pCreateInfo == NULL) || (ppPhysicalMemoryObject == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDevicePhysicalMemoryObjectCreate.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppPhysicalMemoryObject = NULL;

    result = g_pKfeManager->pfnPhysicalMemoryCreate((kfeContext)pCreateInfo->pContext,
                                                    pCreateInfo->size,
                                                    pCreateInfo->pageSize,
                                                    pCreateInfo->flags,
                                                    (kfePhysicalMemoryObject*)ppPhysicalMemoryObject);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDestroy(void* pDevice,
                                                    palThunkDevicePhysicalMemoryDestroyInfo* pDestroyInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryDestroy == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pDestroyInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDevicePhysicalMemoryObjectDestroy.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to destroy the physical memory.
    result = g_pKfeManager->pfnPhysicalMemoryDestroy((kfeContext)pDestroyInfo->pContext,
                                                     (kfePhysicalMemoryObject)pDestroyInfo->pPhysicalMemoryObject);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to destroy physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDeviceMap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryDeviceMapInfo* pMapInfo,
                                                      void** ppPhysicalMemoryDeviceMapped)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryDeviceMap == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pMapInfo == NULL) || (ppPhysicalMemoryDeviceMapped == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDevicePhysicalMemoryObjectDeviceMap.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppPhysicalMemoryDeviceMapped = NULL;

    result = g_pKfeManager->pfnPhysicalMemoryDeviceMap((kfeContext)pMapInfo->pContext,
                                                       (void*)pMapInfo->deviceVirtualAddress,
                                                       (kfePhysicalMemoryObject)pMapInfo->pPhysicalMemoryObject,
                                                       pMapInfo->size,
                                                       pMapInfo->flags,
                                                       (kfePhysicalMemoryObject*)ppPhysicalMemoryDeviceMapped);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to map physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectDeviceUnmap(void* pDevice,
                                                        palThunkDevicePhysicalMemoryDeviceUnmapInfo* pUnmapInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryDeviceUnmap == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pUnmapInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDevicePhysicalMemoryObjectDeviceUnmap.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to unmap the physical memory.
    result = g_pKfeManager->pfnPhysicalMemoryDeviceUnmap(
        (kfeContext)pUnmapInfo->pContext,
        (kfePhysicalMemoryObject)pUnmapInfo->pPhysicalMemoryDeviceMapped);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to unmap physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectHostMap(void* pDevice,
                                                    palThunkDevicePhysicalMemoryHostMapInfo* pMapInfo,
                                                    void** ppPhysicalMemoryHostMapped,
                                                    void** ppHostVirtualAddress)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryHostMap == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pMapInfo == NULL) ||
        (ppPhysicalMemoryHostMapped == NULL) || (ppHostVirtualAddress == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDevicePhysicalMemoryObjectHostMap.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppPhysicalMemoryHostMapped = NULL;

    result = g_pKfeManager->pfnPhysicalMemoryHostMap((kfeContext)pMapInfo->pContext,
                                                     pMapInfo->pPhysicalMemoryObject,
                                                     pMapInfo->size,
                                                     pMapInfo->flags,
                                                     (kfePhysicalMemoryObject*)ppPhysicalMemoryHostMapped,
                                                     ppHostVirtualAddress);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to map physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectHostUnmap(void* pDevice,
                                                      palThunkDevicePhysicalMemoryHostUnmapInfo* pUnmapInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryHostUnmap == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pUnmapInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkHostUnmapPhysicalMemory.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to unmap the physical memory.
    result = g_pKfeManager->pfnPhysicalMemoryHostUnmap((kfeContext)pUnmapInfo->pContext,
                                                       (kfePhysicalMemoryObject)pUnmapInfo->pPhysicalMemoryHostMapped);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to unmap physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}


// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectExportByDmaBuf(void* pDevice,
                                                           palThunkDevicePhysicalMemoryExportInfo* pExportInfo,
                                                           palInt32* pDmaBufFd)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryExportByDmaBuf == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pExportInfo == NULL) || (pDmaBufFd == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDevicePhysicalMemoryObjectExportByDmaBuf.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *pDmaBufFd = -1; // Initialize to an invalid file descriptor.

    // Call the KFE API to export physical memory by dma_buf.
    result = g_pKfeManager->pfnPhysicalMemoryExportByDmaBuf(pExportInfo->pContext,
                                                            pExportInfo->pPhysicalMemoryObject,
                                                            pExportInfo->size,
                                                            pExportInfo->pageSize,
                                                            pExportInfo->flags,
                                                            pDmaBufFd);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to export physical memory by dma_buf from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDevicePhysicalMemoryObjectImportFromDmaBuf(void* pDevice,
                                                             palThunkDevicePhysicalMemoryImportInfo* pImportInfo,
                                                             void** ppRemoteContext,
                                                             void** ppPhysicalMemoryObject,
                                                             palUint64* pSize,
                                                             palUint64* pPageSize,
                                                             palUint64* pFlags)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryImportFromDmaBuf == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pImportInfo == NULL) || (ppRemoteContext == NULL) ||
        (ppPhysicalMemoryObject == NULL) || (pSize == NULL) || (pPageSize == NULL) || (pFlags == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDevicePhysicalMemoryObjectImportFromDmaBuf.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppPhysicalMemoryObject = NULL;

    // Call the KFE API to import physical memory from dma_buf.
    result = g_pKfeManager->pfnPhysicalMemoryImportFromDmaBuf(pImportInfo->pContext,
                                                              pImportInfo->dmaBufFd,
                                                              ppRemoteContext,
                                                              ppPhysicalMemoryObject,
                                                              (uint64_t*)pSize,
                                                              (uint64_t*)pPageSize,
                                                              (uint64_t*)pFlags);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to import physical memory from dma_buf from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceHostPinnedMemoryObjectCreate(void* pDevice,
                                                     palThunkHostPinnedMemoryCreateInfo* pCreateInfo,
                                                     void** ppHostPinnedMemoryObject,
                                                     void** ppHostVirtualAddress)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryCreate == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pCreateInfo == NULL) || (ppHostPinnedMemoryObject == NULL) ||
        (ppHostVirtualAddress == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceHostPinnedMemoryObjectCreate.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppHostPinnedMemoryObject = NULL;

    result = g_pKfeManager->pfnPinnedMemoryCreate((kfeContext)pCreateInfo->pContext,
                                                  pCreateInfo->size,
                                                  pCreateInfo->flags,
                                                  (kfePhysicalMemoryObject*)ppHostPinnedMemoryObject,
                                                  ppHostVirtualAddress);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create host pinned memory object from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceHostPinnedMemoryObjectDestroy(void* pDevice,
                                                palThunkHostPinnedMemoryDestroyInfo* pDestroyInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPhysicalMemoryDestroy == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pDestroyInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceHostPinnedMemoryObjectDestroy.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to destroy the host pinned memory object.
    result = g_pKfeManager->pfnPinnedMemoryDestroy((kfeContext)pDestroyInfo->pContext,
                                                   (kfePhysicalMemoryObject)pDestroyInfo->pHostPinnedMemoryObject);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to destroy host pinned memory object from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceHostPageableMemoryObjectRegister(void* pDevice,
                                                         palThunkHostPageableMemoryRegisterInfo* pRegisterInfo,
                                                         void** ppHostRegisteredMemoryObject,
                                                         palUint64* pRegisteredOffset)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPageableMemoryHostRegister == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pRegisterInfo == NULL) || (ppHostRegisteredMemoryObject == NULL) ||
        (pRegisteredOffset == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceHostPageableMemoryObjectRegister.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppHostRegisteredMemoryObject = NULL;
    result = g_pKfeManager->pfnPageableMemoryHostRegister((kfeContext)pRegisterInfo->pContext,
                                                          pRegisterInfo->pHostPtr,
                                                          pRegisterInfo->size,
                                                          pRegisterInfo->flags,
                                                          ppHostRegisteredMemoryObject,
                                                          (uint64_t*)pRegisteredOffset);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to register physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceHostPageableMemoryObjectUnregister(void* pDevice,
                                                           palThunkHostPageableMemoryUnregisterInfo* pUnregisterInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnPageableMemoryHostUnregister == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pUnregisterInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceHostPageableMemoryObjectUnregister.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to unregister the physical memory.
    result = g_pKfeManager->pfnPageableMemoryHostUnregister((kfeContext)pUnregisterInfo->pContext,
                                                            pUnregisterInfo->pHostRegisteredMemoryObject);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to unregister physical memory from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueCreate(void* pDevice,
                                            palThunkUserModeQueueCreateInfo* pCreateInfo,
                                            void** ppQueue)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnUserModeQueueCreate == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pCreateInfo == NULL) || (ppQueue == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceUserModeQueueCreate.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppQueue = NULL;

    // Call the KFE API to create a user mode queue.
    result = g_pKfeManager->pfnUserModeQueueCreate(ppQueue,
                                                   pCreateInfo->flags,
                                                   pCreateInfo->pContext);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create user mode queue from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueDestroy(void* pDevice, palThunkUserModeQueueDestroyInfo* pDestroyInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnUserModeQueueDestroy == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pDestroyInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceUserModeQueueDestroy.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to destroy the user mode queue.
    result = g_pKfeManager->pfnUserModeQueueDestroy((kfeUserModeQueue)pDestroyInfo->pQueue);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to destroy user mode queue from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceUserModeQueueSubmit(void* pDevice, palThunkUserModeQueueSubmitInfo* pSubmitInfo)
{
    (void)pDevice;
    (void)pSubmitInfo;

    // For now, we just return an error as user mode queue submission are not supported.
    return PAL_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
palResult palThunkDeviceFenceCreate(void* pDevice, palThunkFenceCreateInfo* pCreateInfo, void** ppFence)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnFenceCreate == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pCreateInfo == NULL) || (ppFence == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceFenceCreate.");
        return PAL_ERROR_INVALID_VALUE;
    }

    *ppFence = NULL;

    // Call the KFE API to create a fence.
    result = g_pKfeManager->pfnFenceCreate((kfeFence*)ppFence, (kfeContext)pCreateInfo->pContext);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to create fence from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceFenceDestroy(void* pDevice, palThunkFenceDestroyInfo* pDestroyInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnFenceDestroy == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pDestroyInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceFenceDestroy.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to destroy the fence.
    result = g_pKfeManager->pfnFenceDestroy((kfeFence)pDestroyInfo->pFence);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to destroy fence from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceFenceWait(void* pDevice, palThunkFenceWaitInfo* pWaitInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnFenceWait == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pWaitInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceFenceWait.");
        return PAL_ERROR_INVALID_VALUE;
    }

    // Call the KFE API to wait for the fence.
    result = g_pKfeManager->pfnFenceWait((kfeUserModeQueue)pWaitInfo->pQueue, (kfeFence)pWaitInfo->pFence);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to wait for fence from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceFenceSignal(void* pDevice, palThunkFenceSignalInfo* pSignalInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnFenceSignal == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pSignalInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceFenceSignal.");
        return PAL_ERROR_INVALID_VALUE;
    }

    result = g_pKfeManager->pfnFenceSignal((kfeUserModeQueue)pSignalInfo->pQueue, (kfeFence)pSignalInfo->pFence);
    if (result != KFE_SUCCESS)
    {
        PAL_DBG_PRINTF_ERROR("Failed to signal fence from KFE: %d", result);
        return ipalThunkCastKfeResult(result);
    }

    return PAL_SUCCESS;
}

// =====================================================================================================================
palResult palThunkDeviceFenceQuery(void* pDevice, palThunkFenceQueryInfo* pQueryInfo)
{
    kfeResult result;

    if ((g_pKfeManager == NULL) || (g_pKfeManager->pfnFenceQuery == NULL))
    {
        PAL_DBG_PRINTF_ERROR("KFE manager or function pointer is not initialized.");
        return PAL_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == NULL) || (pQueryInfo == NULL))
    {
        PAL_DBG_PRINTF_ERROR("Invalid parameters passed to palThunkDeviceFenceQuery.");
        return PAL_ERROR_INVALID_VALUE;
    }

    return ipalThunkCastKfeResult(g_pKfeManager->pfnFenceQuery((kfeFence)pQueryInfo->pFence));
}

// =====================================================================================================================
void palThunkDeinitialize(void)
{
    kfeResult result;

    if (g_pKfeManager != NULL)
    {
        result = g_pKfeManager->pfnFinalize();
        if (result != KFE_SUCCESS)
        {
            PAL_DBG_PRINTF_ERROR("Failed to finalize KFE: %d", result);
        }

        // Unload the KFE library.
        if (g_pKfeManager->pKfeLib != NULL)
        {
            palOsUnloadLibrary(g_pKfeManager->pKfeLib);
        }

        // Free the KFE manager structure.
        free(g_pKfeManager);
        g_pKfeManager = NULL;
    }
}