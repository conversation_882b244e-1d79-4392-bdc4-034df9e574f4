#ifndef PAL_TYPES_H_
#define PAL_TYPES_H_

#include <inttypes.h>
#include <stdbool.h>

typedef _Bool              palBool;

#define PAL_TRUE           ((palBool)(0 == 0))
#define PAL_FALSE          ((palBool)(0 != 0))

typedef char               palInt8;
typedef unsigned char      palUint8;

typedef signed short       palInt16;
typedef unsigned short     palUint16;

typedef signed int         palInt32;
typedef unsigned int       palUint32;

typedef signed long long   palInt64;
typedef unsigned long long palUint64;

typedef float              palFloat32;
typedef double             palFloat64;

#endif // PAL_TYPES_H_