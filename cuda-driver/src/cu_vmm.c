#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
* \brief Free an address range reservation.
*
* Frees a virtual address range reserved by cuMemAddressReserve.  The size
* must match what was given to memAddress<PERSON><PERSON><PERSON> and the ptr given must
* match what was returned from memAddressReserve.
*
* \param[in] ptr  - Starting address of the virtual address range to free
* \param[in] size - Size of the virtual address region to free
* \return
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemAddressReserve
*/
CUresult cuMemAddressFree(CUdeviceptr ptr, size_t size)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Allocate an address range reservation.
*
* Reserves a virtual address range based on the given parameters, giving
* the starting address of the range in \p ptr.  This API requires a system that
* supports UVA.  The size and address parameters must be a multiple of the
* host page size and the alignment must be a power of two or zero for default
* alignment.
*
* \param[out] ptr       - Resulting pointer to start of virtual address range allocated
* \param[in]  size      - Size of the reserved virtual address range requested
* \param[in]  alignment - Alignment of the reserved virtual address range requested
* \param[in]  addr      - Fixed starting address range requested
* \param[in]  flags     - Currently unused, must be zero
* \return
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_OUT_OF_MEMORY,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemAddressFree
*/
CUresult cuMemAddressReserve(CUdeviceptr *ptr, size_t size, size_t alignment, CUdeviceptr addr, unsigned long long flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Create a CUDA memory handle representing a memory allocation of a given size described by the given properties
*
* This creates a memory allocation on the target device specified through the
* \p prop structure. The created allocation will not have any device or host
* mappings. The generic memory \p handle for the allocation can be
* mapped to the address space of calling process via ::cuMemMap. This handle
* cannot be transmitted directly to other processes (see
* ::cuMemExportToShareableHandle).  On Windows, the caller must also pass
* an LPSECURITYATTRIBUTE in \p prop to be associated with this handle which
* limits or allows access to this handle for a recipient process (see
* ::CUmemAllocationProp::win32HandleMetaData for more).  The \p size of this
* allocation must be a multiple of the the value given via
* ::cuMemGetAllocationGranularity with the ::CU_MEM_ALLOC_GRANULARITY_MINIMUM
* flag.
* To create a CPU allocation targeting a specific host NUMA node, applications must
* set ::CUmemAllocationProp::CUmemLocation::type to ::CU_MEM_LOCATION_TYPE_HOST_NUMA and
* ::CUmemAllocationProp::CUmemLocation::id must specify the NUMA ID of the CPU.
* On systems where NUMA is not available ::CUmemAllocationProp::CUmemLocation::id must be set to 0.
*
* Applications can set ::CUmemAllocationProp::requestedHandleTypes to
* ::CU_MEM_HANDLE_TYPE_FABRIC in order to create allocations suitable for sharing
* within an IMEX domain. An IMEX domain is either an OS instance or a group of securely
* connected OS instances using the NVIDIA IMEX daemon. An IMEX channel is a global resource
* within the IMEX domain that represents a logical entity that aims to provide fine grained
* accessibility control for the participating processes. When exporter and importer CUDA processes
* have been granted access to the same IMEX channel, they can securely share memory.
* If the allocating process does not have access setup for an IMEX channel, attempting to create
* a ::CUmemGenericAllocationHandle with ::CU_MEM_HANDLE_TYPE_FABRIC will result in ::CUDA_ERROR_NOT_PERMITTED.
* The nvidia-modprobe CLI provides more information regarding setting up of IMEX channels.
*
* If ::CUmemAllocationProp::allocFlags::usage contains ::CU_MEM_CREATE_USAGE_TILE_POOL flag then
* the memory allocation is intended only to be used as backing tile pool for sparse CUDA arrays
* and sparse CUDA mipmapped arrays.
* (see ::cuMemMapArrayAsync).
*
* \param[out] handle - Value of handle returned. All operations on this allocation are to be performed using this handle.
* \param[in]  size   - Size of the allocation requested
* \param[in]  prop   - Properties of the allocation to create.
* \param[in]  flags  - flags for future use, must be zero now.
* \return
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_OUT_OF_MEMORY,
* ::CUDA_ERROR_INVALID_DEVICE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
* \notefnerr
*
* \sa ::cuMemRelease, ::cuMemExportToShareableHandle, ::cuMemImportFromShareableHandle
*/
CUresult cuMemCreate(CUmemGenericAllocationHandle *handle, size_t size, const CUmemAllocationProp *prop, unsigned long long flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Exports an allocation to a requested shareable handle type
*
* Given a CUDA memory handle, create a shareable memory
* allocation handle that can be used to share the memory with other
* processes. The recipient process can convert the shareable handle back into a
* CUDA memory handle using ::cuMemImportFromShareableHandle and map
* it with ::cuMemMap. The implementation of what this handle is and how it
* can be transferred is defined by the requested handle type in \p handleType
*
* Once all shareable handles are closed and the allocation is released, the allocated
* memory referenced will be released back to the OS and uses of the CUDA handle afterward
* will lead to undefined behavior.
*
* This API can also be used in conjunction with other APIs (e.g. Vulkan, OpenGL)
* that support importing memory from the shareable type
*
* \param[out] shareableHandle - Pointer to the location in which to store the requested handle type
* \param[in] handle           - CUDA handle for the memory allocation
* \param[in] handleType       - Type of shareable handle requested (defines type and size of the \p shareableHandle output parameter)
* \param[in] flags            - Reserved, must be zero
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemImportFromShareableHandle
*/
CUresult cuMemExportToShareableHandle(void *shareableHandle, CUmemGenericAllocationHandle handle, CUmemAllocationHandleType handleType, unsigned long long flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Get the access \p flags set for the given \p location and \p ptr
*
* \param[out] flags   - Flags set for this location
* \param[in] location - Location in which to check the flags for
* \param[in] ptr      - Address in which to check the access flags for
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_INVALID_DEVICE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemSetAccess
*/
CUresult cuMemGetAccess(unsigned long long *flags, const CUmemLocation *location, CUdeviceptr ptr)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Calculates either the minimal or recommended granularity
*
* Calculates either the minimal or recommended granularity
* for a given allocation specification and returns it in granularity.  This
* granularity can be used as a multiple for alignment, size, or address mapping.
*
* \param[out] granularity Returned granularity.
* \param[in]  prop Property for which to determine the granularity for
* \param[in]  option Determines which granularity to return
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemCreate, ::cuMemMap
*/
CUresult cuMemGetAllocationGranularity(size_t *granularity, const CUmemAllocationProp *prop, CUmemAllocationGranularity_flags option)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Retrieve the contents of the property structure defining properties for this handle
*
* \param[out] prop  - Pointer to a properties structure which will hold the information about this handle
* \param[in] handle - Handle which to perform the query on
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemCreate, ::cuMemImportFromShareableHandle
*/
CUresult cuMemGetAllocationPropertiesFromHandle(CUmemAllocationProp *prop, CUmemGenericAllocationHandle handle)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Imports an allocation from a requested shareable handle type.
*
* If the current process cannot support the memory described by this shareable
* handle, this API will error as ::CUDA_ERROR_NOT_SUPPORTED.
*
* If \p shHandleType is ::CU_MEM_HANDLE_TYPE_FABRIC and the importer process has not been
* granted access to the same IMEX channel as the exporter process, this API will error
* as ::CUDA_ERROR_NOT_PERMITTED.
*
* \note Importing shareable handles exported from some graphics APIs(VUlkan, OpenGL, etc)
* created on devices under an SLI group may not be supported, and thus this API will
* return CUDA_ERROR_NOT_SUPPORTED.
* There is no guarantee that the contents of \p handle will be the same CUDA memory handle
* for the same given OS shareable handle, or the same underlying allocation.
*
* \param[out] handle       - CUDA Memory handle for the memory allocation.
* \param[in]  osHandle     - Shareable Handle representing the memory allocation that is to be imported.
* \param[in]  shHandleType - handle type of the exported handle ::CUmemAllocationHandleType.
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemExportToShareableHandle, ::cuMemMap, ::cuMemRelease
*/
CUresult cuMemImportFromShareableHandle(CUmemGenericAllocationHandle *handle, void *osHandle, CUmemAllocationHandleType shHandleType)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Maps an allocation handle to a reserved virtual address range.
*
* Maps bytes of memory represented by \p handle starting from byte \p offset to
* \p size to address range [\p addr, \p addr + \p size]. This range must be an
* address reservation previously reserved with ::cuMemAddressReserve, and
* \p offset + \p size must be less than the size of the memory allocation.
* Both \p ptr, \p size, and \p offset must be a multiple of the value given via
* ::cuMemGetAllocationGranularity with the ::CU_MEM_ALLOC_GRANULARITY_MINIMUM flag.
* If \p handle represents a multicast object, \p ptr, \p size and \p offset must
* be aligned to the value returned by ::cuMulticastGetGranularity with the flag
* ::CU_MULTICAST_MINIMUM_GRANULARITY. For best performance however, it is
* recommended that \p ptr, \p size and \p offset be aligned to the value
* returned by ::cuMulticastGetGranularity with the flag
* ::CU_MULTICAST_RECOMMENDED_GRANULARITY.
*
* Please note calling ::cuMemMap does not make the address accessible,
* the caller needs to update accessibility of a contiguous mapped VA
* range by calling ::cuMemSetAccess.
*
* Once a recipient process obtains a shareable memory handle
* from ::cuMemImportFromShareableHandle, the process must
* use ::cuMemMap to map the memory into its address ranges before
* setting accessibility with ::cuMemSetAccess.
*
* ::cuMemMap can only create mappings on VA range reservations
* that are not currently mapped.
*
* \param[in] ptr    - Address where memory will be mapped.
* \param[in] size   - Size of the memory mapping.
* \param[in] offset - Offset into the memory represented by
*                   - \p handle from which to start mapping
*                   - Note: currently must be zero.
* \param[in] handle - Handle to a shareable memory
* \param[in] flags  - flags for future use, must be zero now.
* \return
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_INVALID_DEVICE,
* ::CUDA_ERROR_OUT_OF_MEMORY,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
* \notefnerr
*
* \sa ::cuMemUnmap, ::cuMemSetAccess, ::cuMemCreate, ::cuMemAddressReserve, ::cuMemImportFromShareableHandle
*/
CUresult cuMemMap(CUdeviceptr ptr, size_t size, size_t offset, CUmemGenericAllocationHandle handle, unsigned long long flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Maps or unmaps subregions of sparse CUDA arrays and sparse CUDA mipmapped arrays
 *
 * Performs map or unmap operations on subregions of sparse CUDA arrays and sparse CUDA mipmapped arrays.
 * Each operation is specified by a ::CUarrayMapInfo entry in the \p mapInfoList array of size \p count.
 * The structure ::CUarrayMapInfo is defined as follow:
 \code
     typedef struct CUarrayMapInfo_st {
        CUresourcetype resourceType;
        union {
            CUmipmappedArray mipmap;
            CUarray array;
        } resource;

        CUarraySparseSubresourceType subresourceType;
        union {
            struct {
                unsigned int level;
                unsigned int layer;
                unsigned int offsetX;
                unsigned int offsetY;
                unsigned int offsetZ;
                unsigned int extentWidth;
                unsigned int extentHeight;
                unsigned int extentDepth;
            } sparseLevel;
            struct {
                unsigned int layer;
                unsigned long long offset;
                unsigned long long size;
            } miptail;
        } subresource;

        CUmemOperationType memOperationType;

        CUmemHandleType memHandleType;
        union {
            CUmemGenericAllocationHandle memHandle;
        } memHandle;

        unsigned long long offset;
        unsigned int deviceBitMask;
        unsigned int flags;
        unsigned int reserved[2];
    } CUarrayMapInfo;
 \endcode
 *
 * where ::CUarrayMapInfo::resourceType specifies the type of resource to be operated on.
 * If ::CUarrayMapInfo::resourceType is set to ::CUresourcetype::CU_RESOURCE_TYPE_ARRAY then
 * ::CUarrayMapInfo::resource::array must be set to a valid sparse CUDA array handle.
 * The CUDA array must be either a 2D, 2D layered or 3D CUDA array and must have been allocated using
 * ::cuArrayCreate or ::cuArray3DCreate with the flag ::CUDA_ARRAY3D_SPARSE
 * or ::CUDA_ARRAY3D_DEFERRED_MAPPING.
 * For CUDA arrays obtained using ::cuMipmappedArrayGetLevel, ::CUDA_ERROR_INVALID_VALUE will be returned.
 * If ::CUarrayMapInfo::resourceType is set to ::CUresourcetype::CU_RESOURCE_TYPE_MIPMAPPED_ARRAY
 * then ::CUarrayMapInfo::resource::mipmap must be set to a valid sparse CUDA mipmapped array handle.
 * The CUDA mipmapped array must be either a 2D, 2D layered or 3D CUDA mipmapped array and must have been
 * allocated using ::cuMipmappedArrayCreate with the flag ::CUDA_ARRAY3D_SPARSE
 * or ::CUDA_ARRAY3D_DEFERRED_MAPPING.
 *
 * ::CUarrayMapInfo::subresourceType specifies the type of subresource within the resource.
 * ::CUarraySparseSubresourceType_enum is defined as:
 \code
    typedef enum CUarraySparseSubresourceType_enum {
        CU_ARRAY_SPARSE_SUBRESOURCE_TYPE_SPARSE_LEVEL = 0,
        CU_ARRAY_SPARSE_SUBRESOURCE_TYPE_MIPTAIL = 1
    } CUarraySparseSubresourceType;
 \endcode
 *
 * where ::CUarraySparseSubresourceType::CU_ARRAY_SPARSE_SUBRESOURCE_TYPE_SPARSE_LEVEL indicates a
 * sparse-miplevel which spans at least one tile in every dimension. The remaining miplevels which
 * are too small to span at least one tile in any dimension constitute the mip tail region as indicated by
 * ::CUarraySparseSubresourceType::CU_ARRAY_SPARSE_SUBRESOURCE_TYPE_MIPTAIL subresource type.
 *
 * If ::CUarrayMapInfo::subresourceType is set to ::CUarraySparseSubresourceType::CU_ARRAY_SPARSE_SUBRESOURCE_TYPE_SPARSE_LEVEL
 * then ::CUarrayMapInfo::subresource::sparseLevel struct must contain valid array subregion offsets and extents.
 * The ::CUarrayMapInfo::subresource::sparseLevel::offsetX, ::CUarrayMapInfo::subresource::sparseLevel::offsetY
 * and ::CUarrayMapInfo::subresource::sparseLevel::offsetZ must specify valid X, Y and Z offsets respectively.
 * The ::CUarrayMapInfo::subresource::sparseLevel::extentWidth, ::CUarrayMapInfo::subresource::sparseLevel::extentHeight
 * and ::CUarrayMapInfo::subresource::sparseLevel::extentDepth must specify valid width, height and depth extents respectively.
 * These offsets and extents must be aligned to the corresponding tile dimension.
 * For CUDA mipmapped arrays ::CUarrayMapInfo::subresource::sparseLevel::level must specify a valid mip level index. Otherwise,
 * must be zero.
 * For layered CUDA arrays and layered CUDA mipmapped arrays ::CUarrayMapInfo::subresource::sparseLevel::layer must specify a valid layer index. Otherwise,
 * must be zero.
 * ::CUarrayMapInfo::subresource::sparseLevel::offsetZ must be zero and ::CUarrayMapInfo::subresource::sparseLevel::extentDepth
 * must be set to 1 for 2D and 2D layered CUDA arrays and CUDA mipmapped arrays.
 * Tile extents can be obtained by calling ::cuArrayGetSparseProperties and ::cuMipmappedArrayGetSparseProperties
 *
 * If ::CUarrayMapInfo::subresourceType is set to ::CUarraySparseSubresourceType::CU_ARRAY_SPARSE_SUBRESOURCE_TYPE_MIPTAIL
 * then ::CUarrayMapInfo::subresource::miptail struct must contain valid mip tail offset in
 * ::CUarrayMapInfo::subresource::miptail::offset and size in ::CUarrayMapInfo::subresource::miptail::size.
 * Both, mip tail offset and mip tail size must be aligned to the tile size.
 * For layered CUDA mipmapped arrays which don't have the flag ::CU_ARRAY_SPARSE_PROPERTIES_SINGLE_MIPTAIL set in ::CUDA_ARRAY_SPARSE_PROPERTIES::flags
 * as returned by ::cuMipmappedArrayGetSparseProperties, ::CUarrayMapInfo::subresource::miptail::layer must specify a valid layer index.
 * Otherwise, must be zero.
 *
 * If ::CUarrayMapInfo::resource::array or ::CUarrayMapInfo::resource::mipmap was created with ::CUDA_ARRAY3D_DEFERRED_MAPPING
 * flag set the ::CUarrayMapInfo::subresourceType and the contents of ::CUarrayMapInfo::subresource will be ignored.
 *
 * ::CUarrayMapInfo::memOperationType specifies the type of operation. ::CUmemOperationType is defined as:
 \code
    typedef enum CUmemOperationType_enum {
        CU_MEM_OPERATION_TYPE_MAP = 1,
        CU_MEM_OPERATION_TYPE_UNMAP = 2
    } CUmemOperationType;
 \endcode
 * If ::CUarrayMapInfo::memOperationType is set to ::CUmemOperationType::CU_MEM_OPERATION_TYPE_MAP then the subresource
 * will be mapped onto the tile pool memory specified by ::CUarrayMapInfo::memHandle at offset ::CUarrayMapInfo::offset.
 * The tile pool allocation has to be created by specifying the ::CU_MEM_CREATE_USAGE_TILE_POOL flag when calling ::cuMemCreate. Also,
 * ::CUarrayMapInfo::memHandleType must be set to ::CUmemHandleType::CU_MEM_HANDLE_TYPE_GENERIC.
 *
 * If ::CUarrayMapInfo::memOperationType is set to ::CUmemOperationType::CU_MEM_OPERATION_TYPE_UNMAP then an unmapping operation
 * is performed. ::CUarrayMapInfo::memHandle must be NULL.
 *
 * ::CUarrayMapInfo::deviceBitMask specifies the list of devices that must map or unmap physical memory.
 * Currently, this mask must have exactly one bit set, and the corresponding device must match the device associated with the stream.
 * If ::CUarrayMapInfo::memOperationType is set to ::CUmemOperationType::CU_MEM_OPERATION_TYPE_MAP, the device must also match
 * the device associated with the tile pool memory allocation as specified by ::CUarrayMapInfo::memHandle.
 *
 * ::CUarrayMapInfo::flags and ::CUarrayMapInfo::reserved[] are unused and must be set to zero.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE
 *
 * \param[in] mapInfoList - List of ::CUarrayMapInfo
 * \param[in] count       - Count of ::CUarrayMapInfo  in \p mapInfoList
 * \param[in] hStream     - Stream identifier for the stream to use for map or unmap operations
 *
 * \sa ::cuMipmappedArrayCreate, ::cuArrayCreate, ::cuArray3DCreate, ::cuMemCreate, ::cuArrayGetSparseProperties, ::cuMipmappedArrayGetSparseProperties
 */
CUresult cuMemMapArrayAsync(CUarrayMapInfo  *mapInfoList, unsigned int count, CUstream hStream)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Release a memory handle representing a memory allocation which was previously allocated through cuMemCreate.
*
* Frees the memory that was allocated on a device through cuMemCreate.
*
* The memory allocation will be freed when all outstanding mappings to the memory
* are unmapped and when all outstanding references to the handle (including it's
* shareable counterparts) are also released. The generic memory handle can be
* freed when there are still outstanding mappings made with this handle. Each
* time a recipient process imports a shareable handle, it needs to pair it with
* ::cuMemRelease for the handle to be freed.  If \p handle is not a valid handle
* the behavior is undefined.
*
* \param[in] handle Value of handle which was returned previously by cuMemCreate.
* \return
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
* \notefnerr
*
* \sa ::cuMemCreate
*/
CUresult cuMemRelease(CUmemGenericAllocationHandle handle)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Given an address \p addr, returns the allocation handle of the backing memory allocation.
*
* The handle is guaranteed to be the same handle value used to map the memory. If the address
* requested is not mapped, the function will fail. The returned handle must be released with
* corresponding number of calls to ::cuMemRelease.
*
* \note The address \p addr, can be any address in a range previously mapped
* by ::cuMemMap, and not necessarily the start address.
*
* \param[out] handle CUDA Memory handle for the backing memory allocation.
* \param[in] addr Memory address to query, that has been mapped previously.
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
*
* \sa ::cuMemCreate, ::cuMemRelease, ::cuMemMap
*/
CUresult cuMemRetainAllocationHandle(CUmemGenericAllocationHandle *handle, void *addr)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Set the access flags for each location specified in \p desc for the given virtual address range
*
* Given the virtual address range via \p ptr and \p size, and the locations
* in the array given by \p desc and \p count, set the access flags for the
* target locations.  The range must be a fully mapped address range
* containing all allocations created by ::cuMemMap / ::cuMemCreate.
* Users cannot specify ::CU_MEM_LOCATION_TYPE_HOST_NUMA accessibility for allocations created on with other location types.
* Note: When ::CUmemAccessDesc::CUmemLocation::type is ::CU_MEM_LOCATION_TYPE_HOST_NUMA, ::CUmemAccessDesc::CUmemLocation::id
* is ignored.
* When setting the access flags for a virtual address range mapping a multicast
* object, \p ptr and \p size must be aligned to the value returned by
* ::cuMulticastGetGranularity with the flag ::CU_MULTICAST_MINIMUM_GRANULARITY.
* For best performance however, it is recommended that \p ptr and \p size be
* aligned to the value returned by ::cuMulticastGetGranularity with the flag
* ::CU_MULTICAST_RECOMMENDED_GRANULARITY.
*
* \param[in] ptr   - Starting address for the virtual address range
* \param[in] size  - Length of the virtual address range
* \param[in] desc  - Array of ::CUmemAccessDesc that describe how to change the
*                  - mapping for each location specified
* \param[in] count - Number of ::CUmemAccessDesc in \p desc
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_INVALID_DEVICE,
* ::CUDA_ERROR_NOT_SUPPORTED
* \notefnerr
* \note_sync
*
* \sa ::cuMemSetAccess, ::cuMemCreate, :cuMemMap
*/
CUresult cuMemSetAccess(CUdeviceptr ptr, size_t size, const CUmemAccessDesc *desc, size_t count)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
* \brief Unmap the backing memory of a given address range.
*
* The range must be the entire contiguous address range that was mapped to.  In
* other words, ::cuMemUnmap cannot unmap a sub-range of an address range mapped
* by ::cuMemCreate / ::cuMemMap.  Any backing memory allocations will be freed
* if there are no existing mappings and there are no unreleased memory handles.
*
* When ::cuMemUnmap returns successfully the address range is converted to an
* address reservation and can be used for a future calls to ::cuMemMap.  Any new
* mapping to this virtual address will need to have access granted through
* ::cuMemSetAccess, as all mappings start with no accessibility setup.
*
* \param[in] ptr  - Starting address for the virtual address range to unmap
* \param[in] size - Size of the virtual address range to unmap
* \returns
* ::CUDA_SUCCESS,
* ::CUDA_ERROR_INVALID_VALUE,
* ::CUDA_ERROR_NOT_INITIALIZED,
* ::CUDA_ERROR_DEINITIALIZED,
* ::CUDA_ERROR_NOT_PERMITTED,
* ::CUDA_ERROR_NOT_SUPPORTED
* \notefnerr
* \note_sync
*
* \sa ::cuMemCreate, ::cuMemAddressReserve
*/
CUresult cuMemUnmap(CUdeviceptr ptr, size_t size)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}