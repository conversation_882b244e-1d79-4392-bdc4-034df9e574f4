#include "cu_init.h"

#include "core/pal_init.h"

CUdriverExportTable g_driverExportTable = {0};

static void initDriverFunctionExportTable(void)
{
    g_driverExportTable.pfnGetErrorName = cuGetErrorName;
    g_driverExportTable.pfnGetErrorString = cuGetErrorString;
    g_driverExportTable.pfnInit = cuInit;
    g_driverExportTable.pfnDriverGetVersion = cuDriverGetVersion;
    g_driverExportTable.pfnDeviceGet = cuDeviceGet;
    g_driverExportTable.pfnDeviceGetAttribute = cuDeviceGetAttribute;
    g_driverExportTable.pfnDeviceGetCount = cuDeviceGetCount;
    g_driverExportTable.pfnDeviceGetDefaultMemPool = cuDeviceGetDefaultMemPool;
    g_driverExportTable.pfnDeviceGetExecAffinitySupport = cuDeviceGetExecAffinitySupport;
    g_driverExportTable.pfnDeviceGetLuid = cuDeviceGetLuid;
    g_driverExportTable.pfnDeviceGetMemPool = cuDeviceGetMemPool;
    g_driverExportTable.pfnDeviceGetName = cuDeviceGetName;
    g_driverExportTable.pfnDeviceGetUuid = cuDeviceGetUuid;
    g_driverExportTable.pfnDeviceGetUuid_v2 = cuDeviceGetUuid_v2;
    g_driverExportTable.pfnDeviceSetMemPool = cuDeviceSetMemPool;
    g_driverExportTable.pfnDeviceTotalMem = cuDeviceTotalMem;
    g_driverExportTable.pfnFlushGPUDirectRDMAWrites = cuFlushGPUDirectRDMAWrites;
    g_driverExportTable.pfnDeviceComputeCapability = cuDeviceComputeCapability;
    g_driverExportTable.pfnDeviceGetProperties = cuDeviceGetProperties;
    g_driverExportTable.pfnDevicePrimaryCtxGet = cuDevicePrimaryCtxGet;
    g_driverExportTable.pfnDevicePrimaryCtxGetState = cuDevicePrimaryCtxGetState;
    g_driverExportTable.pfnDevicePrimaryCtxInit = cuDevicePrimaryCtxInit;
    g_driverExportTable.pfnDevicePrimaryCtxRelease = cuDevicePrimaryCtxRelease;
    g_driverExportTable.pfnDevicePrimaryCtxReset = cuDevicePrimaryCtxReset;
    g_driverExportTable.pfnDevicePrimaryCtxRetain = cuDevicePrimaryCtxRetain;
    g_driverExportTable.pfnDevicePrimaryCtxSetFlags = cuDevicePrimaryCtxSetFlags;
    g_driverExportTable.pfnCtxCreate = cuCtxCreate;
    g_driverExportTable.pfnCtxCreate_v3 = cuCtxCreate_v3;
    g_driverExportTable.pfnCtxDestroy = cuCtxDestroy;
    g_driverExportTable.pfnCtxGetApiVersion = cuCtxGetApiVersion;
    g_driverExportTable.pfnCtxGetCacheConfig = cuCtxGetCacheConfig;
    g_driverExportTable.pfnCtxGetCurrent = cuCtxGetCurrent;
    g_driverExportTable.pfnCtxGetDevice = cuCtxGetDevice;
    g_driverExportTable.pfnCtxGetExecAffinity = cuCtxGetExecAffinity;
    g_driverExportTable.pfnCtxGetFlags = cuCtxGetFlags;
    g_driverExportTable.pfnCtxGetId = cuCtxGetId;
    g_driverExportTable.pfnCtxGetLimit = cuCtxGetLimit;
    g_driverExportTable.pfnCtxGetStreamPriorityRange = cuCtxGetStreamPriorityRange;
    g_driverExportTable.pfnCtxPopCurrent = cuCtxPopCurrent;
    g_driverExportTable.pfnCtxResetPersistingL2Cache = cuCtxResetPersistingL2Cache;
    g_driverExportTable.pfnCtxSetCacheConfig = cuCtxSetCacheConfig;
    g_driverExportTable.pfnCtxSetCurrent = cuCtxSetCurrent;
    g_driverExportTable.pfnCtxSetFlags = cuCtxSetFlags;
    g_driverExportTable.pfnCtxSetLimit = cuCtxSetLimit;
    g_driverExportTable.pfnCtxSynchronize = cuCtxSynchronize;
    g_driverExportTable.pfnCtxAttach = cuCtxAttach;
    g_driverExportTable.pfnCtxDetach = cuCtxDetach;
    g_driverExportTable.pfnCtxGetSharedMemConfig = cuCtxGetSharedMemConfig;
    g_driverExportTable.pfnCtxSetSharedMemConfig = cuCtxSetSharedMemConfig;
    g_driverExportTable.pfnLinkAddData = cuLinkAddData;
    g_driverExportTable.pfnLinkAddFile = cuLinkAddFile;
    g_driverExportTable.pfnLinkComplete = cuLinkComplete;
    g_driverExportTable.pfnLinkCreate = cuLinkCreate;
    g_driverExportTable.pfnLinkDestroy = cuLinkDestroy;
    g_driverExportTable.pfnModuleEnumerateFunctions = cuModuleEnumerateFunctions;
    g_driverExportTable.pfnModuleGetFunction = cuModuleGetFunction;
    g_driverExportTable.pfnModuleGetFunctionCount = cuModuleGetFunctionCount;
    g_driverExportTable.pfnModuleGetGlobal = cuModuleGetGlobal;
    g_driverExportTable.pfnModuleGetLoadingMode = cuModuleGetLoadingMode;
    g_driverExportTable.pfnModuleLoad = cuModuleLoad;
    g_driverExportTable.pfnModuleLoadData = cuModuleLoadData;
    g_driverExportTable.pfnModuleLoadDataEx = cuModuleLoadDataEx;
    g_driverExportTable.pfnModuleLoadFatBinary = cuModuleLoadFatBinary;
    g_driverExportTable.pfnModuleUnload = cuModuleUnload;
    g_driverExportTable.pfnKernelGetAttribute = cuKernelGetAttribute;
    g_driverExportTable.pfnKernelGetFunction = cuKernelGetFunction;
    g_driverExportTable.pfnKernelGetName = cuKernelGetName;
    g_driverExportTable.pfnKernelGetParamInfo = cuKernelGetParamInfo;
    g_driverExportTable.pfnKernelSetAttribute = cuKernelSetAttribute;
    g_driverExportTable.pfnKernelSetCacheConfig = cuKernelSetCacheConfig;
    g_driverExportTable.pfnLibraryEnumerateKernels = cuLibraryEnumerateKernels;
    g_driverExportTable.pfnLibraryGetGlobal = cuLibraryGetGlobal;
    g_driverExportTable.pfnLibraryGetKernel = cuLibraryGetKernel;
    g_driverExportTable.pfnLibraryGetKernelCount = cuLibraryGetKernelCount;
    g_driverExportTable.pfnLibraryGetManaged = cuLibraryGetManaged;
    g_driverExportTable.pfnLibraryGetModule = cuLibraryGetModule;
    g_driverExportTable.pfnLibraryGetUnifiedFunction = cuLibraryGetUnifiedFunction;
    g_driverExportTable.pfnLibraryLoadData = cuLibraryLoadData;
    g_driverExportTable.pfnLibraryLoadFromFile = cuLibraryLoadFromFile;
    g_driverExportTable.pfnLibraryUnload = cuLibraryUnload;
    g_driverExportTable.pfnArray3DCreate = cuArray3DCreate;
    g_driverExportTable.pfnArray3DGetDescriptor = cuArray3DGetDescriptor;
    g_driverExportTable.pfnArrayCreate = cuArrayCreate;
    g_driverExportTable.pfnArrayDestroy = cuArrayDestroy;
    g_driverExportTable.pfnArrayGetDescriptor = cuArrayGetDescriptor;
    g_driverExportTable.pfnArrayGetMemoryRequirements = cuArrayGetMemoryRequirements;
    g_driverExportTable.pfnArrayGetPlane = cuArrayGetPlane;
    g_driverExportTable.pfnDeviceGetByPCIBusId = cuDeviceGetByPCIBusId;
    g_driverExportTable.pfnDeviceGetPCIBusId = cuDeviceGetPCIBusId;
    g_driverExportTable.pfnDeviceRegisterAsyncNotification = cuDeviceRegisterAsyncNotification;
    g_driverExportTable.pfnDeviceUnregisterAsyncNotification = cuDeviceUnregisterAsyncNotification;
    g_driverExportTable.pfnIpcCloseMemHandle = cuIpcCloseMemHandle;
    g_driverExportTable.pfnIpcGetEventHandle = cuIpcGetEventHandle;
    g_driverExportTable.pfnIpcGetMemHandle = cuIpcGetMemHandle;
    g_driverExportTable.pfnIpcOpenEventHandle = cuIpcOpenEventHandle;
    g_driverExportTable.pfnIpcOpenMemHandle = cuIpcOpenMemHandle;
    g_driverExportTable.pfnMemAlloc = cuMemAlloc;
    g_driverExportTable.pfnMemAllocHost = cuMemAllocHost;
    g_driverExportTable.pfnMemAllocManaged = cuMemAllocManaged;
    g_driverExportTable.pfnMemAllocPitch = cuMemAllocPitch;
    g_driverExportTable.pfnMemcpy = cuMemcpy;
    g_driverExportTable.pfnMemcpy2D = cuMemcpy2D;
    g_driverExportTable.pfnMemcpy2DAsync = cuMemcpy2DAsync;
    g_driverExportTable.pfnMemcpy2DUnaligned = cuMemcpy2DUnaligned;
    g_driverExportTable.pfnMemcpy3D = cuMemcpy3D;
    g_driverExportTable.pfnMemcpy3DAsync = cuMemcpy3DAsync;
    g_driverExportTable.pfnMemcpy3DPeer = cuMemcpy3DPeer;
    g_driverExportTable.pfnMemcpy3DPeerAsync = cuMemcpy3DPeerAsync;
    g_driverExportTable.pfnMemcpyAsync = cuMemcpyAsync;
    g_driverExportTable.pfnMemcpyAtoA = cuMemcpyAtoA;
    g_driverExportTable.pfnMemcpyAtoD = cuMemcpyAtoD;
    g_driverExportTable.pfnMemcpyAtoH = cuMemcpyAtoH;
    g_driverExportTable.pfnMemcpyAtoHAsync = cuMemcpyAtoHAsync;
    g_driverExportTable.pfnMemcpyDtoA = cuMemcpyDtoA;
    g_driverExportTable.pfnMemcpyDtoD = cuMemcpyDtoD;
    g_driverExportTable.pfnMemcpyDtoDAsync = cuMemcpyDtoDAsync;
    g_driverExportTable.pfnMemcpyDtoH = cuMemcpyDtoH;
    g_driverExportTable.pfnMemcpyDtoHAsync = cuMemcpyDtoHAsync;
    g_driverExportTable.pfnMemcpyHtoA = cuMemcpyHtoA;
    g_driverExportTable.pfnMemcpyHtoAAsync = cuMemcpyHtoAAsync;
    g_driverExportTable.pfnMemcpyHtoD = cuMemcpyHtoD;
    g_driverExportTable.pfnMemcpyHtoDAsync = cuMemcpyHtoDAsync;
    g_driverExportTable.pfnMemcpyPeer = cuMemcpyPeer;
    g_driverExportTable.pfnMemcpyPeerAsync = cuMemcpyPeerAsync;
    g_driverExportTable.pfnMemFree = cuMemFree;
    g_driverExportTable.pfnMemFreeHost = cuMemFreeHost;
    g_driverExportTable.pfnMemGetAddressRange = cuMemGetAddressRange;
    g_driverExportTable.pfnMemGetHandleForAddressRange = cuMemGetHandleForAddressRange;
    g_driverExportTable.pfnMemGetInfo = cuMemGetInfo;
    g_driverExportTable.pfnMemHostAlloc = cuMemHostAlloc;
    g_driverExportTable.pfnMemHostGetDevicePointer = cuMemHostGetDevicePointer;
    g_driverExportTable.pfnMemHostGetFlags = cuMemHostGetFlags;
    g_driverExportTable.pfnMemHostRegister = cuMemHostRegister;
    g_driverExportTable.pfnMemHostUnregister = cuMemHostUnregister;
    g_driverExportTable.pfnMemsetD16 = cuMemsetD16;
    g_driverExportTable.pfnMemsetD16Async = cuMemsetD16Async;
    g_driverExportTable.pfnMemsetD2D16 = cuMemsetD2D16;
    g_driverExportTable.pfnMemsetD2D16Async = cuMemsetD2D16Async;
    g_driverExportTable.pfnMemsetD2D32 = cuMemsetD2D32;
    g_driverExportTable.pfnMemsetD2D32Async = cuMemsetD2D32Async;
    g_driverExportTable.pfnMemsetD2D8 = cuMemsetD2D8;
    g_driverExportTable.pfnMemsetD2D8Async = cuMemsetD2D8Async;
    g_driverExportTable.pfnMemsetD32 = cuMemsetD32;
    g_driverExportTable.pfnMemsetD32Async = cuMemsetD32Async;
    g_driverExportTable.pfnMemsetD8 = cuMemsetD8;
    g_driverExportTable.pfnMemsetD8Async = cuMemsetD8Async;
    g_driverExportTable.pfnMemAddressFree = cuMemAddressFree;
    g_driverExportTable.pfnMemAddressReserve = cuMemAddressReserve;
    g_driverExportTable.pfnMemCreate = cuMemCreate;
    g_driverExportTable.pfnMemExportToShareableHandle = cuMemExportToShareableHandle;
    g_driverExportTable.pfnMemGetAccess = cuMemGetAccess;
    g_driverExportTable.pfnMemGetAllocationGranularity = cuMemGetAllocationGranularity;
    g_driverExportTable.pfnMemGetAllocationPropertiesFromHandle = cuMemGetAllocationPropertiesFromHandle;
    g_driverExportTable.pfnMemImportFromShareableHandle = cuMemImportFromShareableHandle;
    g_driverExportTable.pfnMemMap = cuMemMap;
    g_driverExportTable.pfnMemMapArrayAsync = cuMemMapArrayAsync;
    g_driverExportTable.pfnMemRelease = cuMemRelease;
    g_driverExportTable.pfnMemRetainAllocationHandle = cuMemRetainAllocationHandle;
    g_driverExportTable.pfnMemSetAccess = cuMemSetAccess;
    g_driverExportTable.pfnMemUnmap = cuMemUnmap;
    g_driverExportTable.pfnMemAllocAsync = cuMemAllocAsync;
    g_driverExportTable.pfnMemAllocFromPoolAsync = cuMemAllocFromPoolAsync;
    g_driverExportTable.pfnMemFreeAsync = cuMemFreeAsync;
    g_driverExportTable.pfnMemPoolCreate = cuMemPoolCreate;
    g_driverExportTable.pfnMemPoolDestroy = cuMemPoolDestroy;
    g_driverExportTable.pfnMemPoolExportPointer = cuMemPoolExportPointer;
    g_driverExportTable.pfnMemPoolExportToShareableHandle = cuMemPoolExportToShareableHandle;
    g_driverExportTable.pfnMemPoolGetAccess = cuMemPoolGetAccess;
    g_driverExportTable.pfnMemPoolGetAttribute = cuMemPoolGetAttribute;
    g_driverExportTable.pfnMemPoolImportFromShareableHandle = cuMemPoolImportFromShareableHandle;
    g_driverExportTable.pfnMemPoolImportPointer = cuMemPoolImportPointer;
    g_driverExportTable.pfnMemPoolSetAccess = cuMemPoolSetAccess;
    g_driverExportTable.pfnMemPoolSetAttribute = cuMemPoolSetAttribute;
    g_driverExportTable.pfnMemPoolTrimTo = cuMemPoolTrimTo;
    g_driverExportTable.pfnMulticastAddDevice = cuMulticastAddDevice;
    g_driverExportTable.pfnMulticastBindAddr = cuMulticastBindAddr;
    g_driverExportTable.pfnMulticastBindMem = cuMulticastBindMem;
    g_driverExportTable.pfnMulticastCreate = cuMulticastCreate;
    g_driverExportTable.pfnMulticastGetGranularity = cuMulticastGetGranularity;
    g_driverExportTable.pfnMulticastUnbind = cuMulticastUnbind;
    g_driverExportTable.pfnMemAdvise = cuMemAdvise;
    g_driverExportTable.pfnMemAdvise_v2 = cuMemAdvise_v2;
    g_driverExportTable.pfnMemPrefetchAsync = cuMemPrefetchAsync;
    g_driverExportTable.pfnMemPrefetchAsync_v2 = cuMemPrefetchAsync_v2;
    g_driverExportTable.pfnMemRangeGetAttribute = cuMemRangeGetAttribute;
    g_driverExportTable.pfnMemRangeGetAttributes = cuMemRangeGetAttributes;
    g_driverExportTable.pfnPointerGetAttribute = cuPointerGetAttribute;
    g_driverExportTable.pfnPointerGetAttributes = cuPointerGetAttributes;
    g_driverExportTable.pfnPointerSetAttribute = cuPointerSetAttribute;
    g_driverExportTable.pfnStreamAddCallback = cuStreamAddCallback;
    g_driverExportTable.pfnStreamAttachMemAsync = cuStreamAttachMemAsync;
    g_driverExportTable.pfnStreamBeginCapture = cuStreamBeginCapture;
    g_driverExportTable.pfnStreamBeginCaptureToGraph = cuStreamBeginCaptureToGraph;
    g_driverExportTable.pfnStreamCopyAttributes = cuStreamCopyAttributes;
    g_driverExportTable.pfnStreamCreate = cuStreamCreate;
    g_driverExportTable.pfnStreamCreateWithPriority = cuStreamCreateWithPriority;
    g_driverExportTable.pfnStreamDestroy = cuStreamDestroy;
    g_driverExportTable.pfnStreamEndCapture = cuStreamEndCapture;
    g_driverExportTable.pfnStreamGetAttribute = cuStreamGetAttribute;
    g_driverExportTable.pfnStreamGetCaptureInfo_v2 = cuStreamGetCaptureInfo_v2;
    g_driverExportTable.pfnStreamGetCaptureInfo_v3 = cuStreamGetCaptureInfo_v3;
    g_driverExportTable.pfnStreamGetCtx = cuStreamGetCtx;
    g_driverExportTable.pfnStreamGetFlags = cuStreamGetFlags;
    g_driverExportTable.pfnStreamGetId = cuStreamGetId;
    g_driverExportTable.pfnStreamGetPriority = cuStreamGetPriority;
    g_driverExportTable.pfnStreamIsCapturing = cuStreamIsCapturing;
    g_driverExportTable.pfnStreamQuery = cuStreamQuery;
    g_driverExportTable.pfnStreamSetAttribute = cuStreamSetAttribute;
    g_driverExportTable.pfnStreamSynchronize = cuStreamSynchronize;
    g_driverExportTable.pfnStreamUpdateCaptureDependencies = cuStreamUpdateCaptureDependencies;
    g_driverExportTable.pfnStreamUpdateCaptureDependencies_v2 = cuStreamUpdateCaptureDependencies_v2;
    g_driverExportTable.pfnStreamWaitEvent = cuStreamWaitEvent;
    g_driverExportTable.pfnThreadExchangeStreamCaptureMode = cuThreadExchangeStreamCaptureMode;
    g_driverExportTable.pfnEventCreate = cuEventCreate;
    g_driverExportTable.pfnEventDestroy = cuEventDestroy;
    g_driverExportTable.pfnEventElapsedTime = cuEventElapsedTime;
    g_driverExportTable.pfnEventQuery = cuEventQuery;
    g_driverExportTable.pfnEventRecord = cuEventRecord;
    g_driverExportTable.pfnEventRecordWithFlags = cuEventRecordWithFlags;
    g_driverExportTable.pfnEventSynchronize = cuEventSynchronize;
    g_driverExportTable.pfnDestroyExternalMemory = cuDestroyExternalMemory;
    g_driverExportTable.pfnDestroyExternalSemaphore = cuDestroyExternalSemaphore;
    g_driverExportTable.pfnExternalMemoryGetMappedBuffer = cuExternalMemoryGetMappedBuffer;
    g_driverExportTable.pfnImportExternalMemory = cuImportExternalMemory;
    g_driverExportTable.pfnImportExternalSemaphore = cuImportExternalSemaphore;
    g_driverExportTable.pfnSignalExternalSemaphoresAsync = cuSignalExternalSemaphoresAsync;
    g_driverExportTable.pfnWaitExternalSemaphoresAsync = cuWaitExternalSemaphoresAsync;
    g_driverExportTable.pfnStreamBatchMemOp = cuStreamBatchMemOp;
    g_driverExportTable.pfnStreamWaitValue32 = cuStreamWaitValue32;
    g_driverExportTable.pfnStreamWaitValue64 = cuStreamWaitValue64;
    g_driverExportTable.pfnStreamWriteValue32 = cuStreamWriteValue32;
    g_driverExportTable.pfnStreamWriteValue64 = cuStreamWriteValue64;
    g_driverExportTable.pfnFuncGetAttribute = cuFuncGetAttribute;
    g_driverExportTable.pfnFuncGetModule = cuFuncGetModule;
    g_driverExportTable.pfnFuncGetName = cuFuncGetName;
    g_driverExportTable.pfnFuncGetParamInfo = cuFuncGetParamInfo;
    g_driverExportTable.pfnFuncIsLoaded = cuFuncIsLoaded;
    g_driverExportTable.pfnFuncLoad = cuFuncLoad;
    g_driverExportTable.pfnFuncSetAttribute = cuFuncSetAttribute;
    g_driverExportTable.pfnFuncSetCacheConfig = cuFuncSetCacheConfig;
    g_driverExportTable.pfnLaunchCooperativeKernel = cuLaunchCooperativeKernel;
    g_driverExportTable.pfnLaunchCooperativeKernelMultiDevice = cuLaunchCooperativeKernelMultiDevice;
    g_driverExportTable.pfnLaunchHostFunc = cuLaunchHostFunc;
    g_driverExportTable.pfnLaunchKernel = cuLaunchKernel;
    g_driverExportTable.pfnLaunchKernelEx = cuLaunchKernelEx;
    g_driverExportTable.pfnFuncSetBlockShape = cuFuncSetBlockShape;
    g_driverExportTable.pfnFuncSetSharedMemConfig = cuFuncSetSharedMemConfig;
    g_driverExportTable.pfnFuncSetSharedSize = cuFuncSetSharedSize;
    g_driverExportTable.pfnLaunch = cuLaunch;
    g_driverExportTable.pfnLaunchGrid = cuLaunchGrid;
    g_driverExportTable.pfnLaunchGridAsync = cuLaunchGridAsync;
    g_driverExportTable.pfnParamSetf = cuParamSetf;
    g_driverExportTable.pfnParamSeti = cuParamSeti;
    g_driverExportTable.pfnParamSetSize = cuParamSetSize;
    g_driverExportTable.pfnParamSetv = cuParamSetv;
    g_driverExportTable.pfnDeviceGetGraphMemAttribute = cuDeviceGetGraphMemAttribute;
    g_driverExportTable.pfnDeviceGraphMemTrim = cuDeviceGraphMemTrim;
    g_driverExportTable.pfnDeviceSetGraphMemAttribute = cuDeviceSetGraphMemAttribute;
    g_driverExportTable.pfnGraphAddBatchMemOpNode = cuGraphAddBatchMemOpNode;
    g_driverExportTable.pfnGraphAddChildGraphNode = cuGraphAddChildGraphNode;
    g_driverExportTable.pfnGraphAddDependencies = cuGraphAddDependencies;
    g_driverExportTable.pfnGraphAddDependencies_v2 = cuGraphAddDependencies_v2;
    g_driverExportTable.pfnGraphAddEmptyNode = cuGraphAddEmptyNode;
    g_driverExportTable.pfnGraphAddEventRecordNode = cuGraphAddEventRecordNode;
    g_driverExportTable.pfnGraphAddEventWaitNode = cuGraphAddEventWaitNode;
    g_driverExportTable.pfnGraphAddExternalSemaphoresSignalNode = cuGraphAddExternalSemaphoresSignalNode;
    g_driverExportTable.pfnGraphAddExternalSemaphoresWaitNode = cuGraphAddExternalSemaphoresWaitNode;
    g_driverExportTable.pfnGraphAddHostNode = cuGraphAddHostNode;
    g_driverExportTable.pfnGraphAddKernelNode = cuGraphAddKernelNode;
    g_driverExportTable.pfnGraphAddMemAllocNode = cuGraphAddMemAllocNode;
    g_driverExportTable.pfnGraphAddMemcpyNode = cuGraphAddMemcpyNode;
    g_driverExportTable.pfnGraphAddMemFreeNode = cuGraphAddMemFreeNode;
    g_driverExportTable.pfnGraphAddMemsetNode = cuGraphAddMemsetNode;
    g_driverExportTable.pfnGraphAddNode = cuGraphAddNode;
    g_driverExportTable.pfnGraphAddNode_v2 = cuGraphAddNode_v2;
    g_driverExportTable.pfnGraphBatchMemOpNodeGetParams = cuGraphBatchMemOpNodeGetParams;
    g_driverExportTable.pfnGraphBatchMemOpNodeSetParams = cuGraphBatchMemOpNodeSetParams;
    g_driverExportTable.pfnGraphChildGraphNodeGetGraph = cuGraphChildGraphNodeGetGraph;
    g_driverExportTable.pfnGraphClone = cuGraphClone;
    g_driverExportTable.pfnGraphConditionalHandleCreate = cuGraphConditionalHandleCreate;
    g_driverExportTable.pfnGraphCreate = cuGraphCreate;
    g_driverExportTable.pfnGraphDebugDotPrint = cuGraphDebugDotPrint;
    g_driverExportTable.pfnGraphDestroy = cuGraphDestroy;
    g_driverExportTable.pfnGraphDestroyNode = cuGraphDestroyNode;
    g_driverExportTable.pfnGraphEventRecordNodeGetEvent = cuGraphEventRecordNodeGetEvent;
    g_driverExportTable.pfnGraphEventRecordNodeSetEvent = cuGraphEventRecordNodeSetEvent;
    g_driverExportTable.pfnGraphEventWaitNodeGetEvent = cuGraphEventWaitNodeGetEvent;
    g_driverExportTable.pfnGraphEventWaitNodeSetEvent = cuGraphEventWaitNodeSetEvent;
    g_driverExportTable.pfnGraphExecBatchMemOpNodeSetParams = cuGraphExecBatchMemOpNodeSetParams;
    g_driverExportTable.pfnGraphExecChildGraphNodeSetParams = cuGraphExecChildGraphNodeSetParams;
    g_driverExportTable.pfnGraphExecDestroy = cuGraphExecDestroy;
    g_driverExportTable.pfnGraphExecEventRecordNodeSetEvent = cuGraphExecEventRecordNodeSetEvent;
    g_driverExportTable.pfnGraphExecEventWaitNodeSetEvent = cuGraphExecEventWaitNodeSetEvent;
    g_driverExportTable.pfnGraphExecExternalSemaphoresSignalNodeSetParams = cuGraphExecExternalSemaphoresSignalNodeSetParams;
    g_driverExportTable.pfnGraphExecExternalSemaphoresWaitNodeSetParams = cuGraphExecExternalSemaphoresWaitNodeSetParams;
    g_driverExportTable.pfnGraphExecGetFlags = cuGraphExecGetFlags;
    g_driverExportTable.pfnGraphExecHostNodeSetParams = cuGraphExecHostNodeSetParams;
    g_driverExportTable.pfnGraphExecKernelNodeSetParams = cuGraphExecKernelNodeSetParams;
    g_driverExportTable.pfnGraphExecMemcpyNodeSetParams = cuGraphExecMemcpyNodeSetParams;
    g_driverExportTable.pfnGraphExecMemsetNodeSetParams = cuGraphExecMemsetNodeSetParams;
    g_driverExportTable.pfnGraphExecNodeSetParams = cuGraphExecNodeSetParams;
    g_driverExportTable.pfnGraphExecUpdate = cuGraphExecUpdate;
    g_driverExportTable.pfnGraphExternalSemaphoresSignalNodeGetParams = cuGraphExternalSemaphoresSignalNodeGetParams;
    g_driverExportTable.pfnGraphExternalSemaphoresSignalNodeSetParams = cuGraphExternalSemaphoresSignalNodeSetParams;
    g_driverExportTable.pfnGraphExternalSemaphoresWaitNodeGetParams = cuGraphExternalSemaphoresWaitNodeGetParams;
    g_driverExportTable.pfnGraphExternalSemaphoresWaitNodeSetParams = cuGraphExternalSemaphoresWaitNodeSetParams;
    g_driverExportTable.pfnGraphGetEdges = cuGraphGetEdges;
    g_driverExportTable.pfnGraphGetEdges_v2 = cuGraphGetEdges_v2;
    g_driverExportTable.pfnGraphGetNodes = cuGraphGetNodes;
    g_driverExportTable.pfnGraphGetRootNodes = cuGraphGetRootNodes;
    g_driverExportTable.pfnGraphHostNodeGetParams = cuGraphHostNodeGetParams;
    g_driverExportTable.pfnGraphHostNodeSetParams = cuGraphHostNodeSetParams;
    g_driverExportTable.pfnGraphInstantiate = cuGraphInstantiate;
    g_driverExportTable.pfnGraphInstantiateWithParams = cuGraphInstantiateWithParams;
    g_driverExportTable.pfnGraphKernelNodeCopyAttributes = cuGraphKernelNodeCopyAttributes;
    g_driverExportTable.pfnGraphKernelNodeGetAttribute = cuGraphKernelNodeGetAttribute;
    g_driverExportTable.pfnGraphKernelNodeGetParams = cuGraphKernelNodeGetParams;
    g_driverExportTable.pfnGraphKernelNodeSetAttribute = cuGraphKernelNodeSetAttribute;
    g_driverExportTable.pfnGraphKernelNodeSetParams = cuGraphKernelNodeSetParams;
    g_driverExportTable.pfnGraphLaunch = cuGraphLaunch;
    g_driverExportTable.pfnGraphMemAllocNodeGetParams = cuGraphMemAllocNodeGetParams;
    g_driverExportTable.pfnGraphMemcpyNodeGetParams = cuGraphMemcpyNodeGetParams;
    g_driverExportTable.pfnGraphMemcpyNodeSetParams = cuGraphMemcpyNodeSetParams;
    g_driverExportTable.pfnGraphMemFreeNodeGetParams = cuGraphMemFreeNodeGetParams;
    g_driverExportTable.pfnGraphMemsetNodeGetParams = cuGraphMemsetNodeGetParams;
    g_driverExportTable.pfnGraphMemsetNodeSetParams = cuGraphMemsetNodeSetParams;
    g_driverExportTable.pfnGraphNodeFindInClone = cuGraphNodeFindInClone;
    g_driverExportTable.pfnGraphNodeGetDependencies = cuGraphNodeGetDependencies;
    g_driverExportTable.pfnGraphNodeGetDependencies_v2 = cuGraphNodeGetDependencies_v2;
    g_driverExportTable.pfnGraphNodeGetDependentNodes = cuGraphNodeGetDependentNodes;
    g_driverExportTable.pfnGraphNodeGetDependentNodes_v2 = cuGraphNodeGetDependentNodes_v2;
    g_driverExportTable.pfnGraphNodeGetEnabled = cuGraphNodeGetEnabled;
    g_driverExportTable.pfnGraphNodeGetType = cuGraphNodeGetType;
    g_driverExportTable.pfnGraphNodeSetEnabled = cuGraphNodeSetEnabled;
    g_driverExportTable.pfnGraphNodeSetParams = cuGraphNodeSetParams;
    g_driverExportTable.pfnGraphReleaseUserObject = cuGraphReleaseUserObject;
    g_driverExportTable.pfnGraphRemoveDependencies = cuGraphRemoveDependencies;
    g_driverExportTable.pfnGraphRemoveDependencies_v2 = cuGraphRemoveDependencies_v2;
    g_driverExportTable.pfnGraphRetainUserObject = cuGraphRetainUserObject;
    g_driverExportTable.pfnGraphUpload = cuGraphUpload;
    g_driverExportTable.pfnUserObjectCreate = cuUserObjectCreate;
    g_driverExportTable.pfnUserObjectRelease = cuUserObjectRelease;
    g_driverExportTable.pfnUserObjectRetain = cuUserObjectRetain;
    g_driverExportTable.pfnOccupancyAvailableDynamicSMemPerBlock = cuOccupancyAvailableDynamicSMemPerBlock;
    g_driverExportTable.pfnOccupancyMaxActiveBlocksPerMultiprocessor = cuOccupancyMaxActiveBlocksPerMultiprocessor;
    g_driverExportTable.pfnOccupancyMaxActiveBlocksPerMultiprocessorWithFlags = cuOccupancyMaxActiveBlocksPerMultiprocessorWithFlags;
    g_driverExportTable.pfnOccupancyMaxActiveClusters = cuOccupancyMaxActiveClusters;
    g_driverExportTable.pfnOccupancyMaxPotentialBlockSize = cuOccupancyMaxPotentialBlockSize;
    g_driverExportTable.pfnOccupancyMaxPotentialBlockSizeWithFlags = cuOccupancyMaxPotentialBlockSizeWithFlags;
    g_driverExportTable.pfnOccupancyMaxPotentialClusterSize = cuOccupancyMaxPotentialClusterSize;
    g_driverExportTable.pfnTensorMapEncodeIm2col = cuTensorMapEncodeIm2col;
    g_driverExportTable.pfnTensorMapEncodeTiled = cuTensorMapEncodeTiled;
    g_driverExportTable.pfnTensorMapReplaceAddress = cuTensorMapReplaceAddress;
    g_driverExportTable.pfnCtxDisablePeerAccess = cuCtxDisablePeerAccess;
    g_driverExportTable.pfnCtxEnablePeerAccess = cuCtxEnablePeerAccess;
    g_driverExportTable.pfnDeviceCanAccessPeer = cuDeviceCanAccessPeer;
    g_driverExportTable.pfnDeviceGetP2PAttribute = cuDeviceGetP2PAttribute;
    g_driverExportTable.pfnGetProcAddress = cuGetProcAddress;
    g_driverExportTable.pfnGetExportTable = cuGetExportTable;
    g_driverExportTable.pfnCoredumpGetAttribute = cuCoredumpGetAttribute;
    g_driverExportTable.pfnCoredumpGetAttributeGlobal = cuCoredumpGetAttributeGlobal;
    g_driverExportTable.pfnCoredumpSetAttribute = cuCoredumpSetAttribute;
    g_driverExportTable.pfnCoredumpSetAttributeGlobal = cuCoredumpSetAttributeGlobal;
    g_driverExportTable.pfnCtxFromGreenCtx = cuCtxFromGreenCtx;
    g_driverExportTable.pfnCtxGetDevResource = cuCtxGetDevResource;
    g_driverExportTable.pfnDeviceGetDevResource = cuDeviceGetDevResource;
    g_driverExportTable.pfnDevResourceGenerateDesc = cuDevResourceGenerateDesc;
    g_driverExportTable.pfnDevSmResourceSplitByCount = cuDevSmResourceSplitByCount;
    g_driverExportTable.pfnGreenCtxCreate = cuGreenCtxCreate;
    g_driverExportTable.pfnGreenCtxDestroy = cuGreenCtxDestroy;
    g_driverExportTable.pfnGreenCtxGetDevResource = cuGreenCtxGetDevResource;
    g_driverExportTable.pfnGreenCtxRecordEvent = cuGreenCtxRecordEvent;
    g_driverExportTable.pfnGreenCtxWaitEvent = cuGreenCtxWaitEvent;
    g_driverExportTable.pfnStreamGetGreenCtx = cuStreamGetGreenCtx;
    g_driverExportTable.pfnProfilerInitialize = cuProfilerInitialize;
    g_driverExportTable.pfnProfilerStart = cuProfilerStart;
    g_driverExportTable.pfnProfilerStop = cuProfilerStop;
}

/**
 * \brief Initialize the CUDA driver API
 * Initializes the driver API and must be called before any other function from
 * the driver API in the current process. Currently, the \p Flags parameter must be 0. If ::cuInit()
 * has not been called, any function from the driver API will return
 * ::CUDA_ERROR_NOT_INITIALIZED.
 *
 * \param Flags - Initialization flag for CUDA.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_SYSTEM_DRIVER_MISMATCH,
 * ::CUDA_ERROR_COMPAT_NOT_SUPPORTED_ON_DEVICE
 * \notefnerr
 */
CUresult cuInit(unsigned int Flags)
{
    if (Flags != 0)
    {
        return CUDA_ERROR_INVALID_VALUE;
    }

    return (CUresult)palInit(Flags, initDriverFunctionExportTable);
}