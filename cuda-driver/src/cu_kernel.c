#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Returns information about a kernel
 *
 * Returns in \p *pi the integer value of the attribute \p attrib for the kernel
 * \p kernel for the requested device \p dev. The supported attributes are:
 * - ::CU_FUNC_ATTRIBUTE_MAX_THREADS_PER_BLOCK: The maximum number of threads
 *   per block, beyond which a launch of the kernel would fail. This number
 *   depends on both the kernel and the requested device.
 * - ::CU_FUNC_ATTRIBUTE_SHARED_SIZE_BYTES: The size in bytes of
 *   statically-allocated shared memory per block required by this kernel.
 *   This does not include dynamically-allocated shared memory requested by
 *   the user at runtime.
 * - ::CU_FUNC_ATTRIBUTE_CONST_SIZE_BYTES: The size in bytes of user-allocated
 *   constant memory required by this kernel.
 * - ::CU_FUNC_ATTRIBUTE_LOCAL_SIZE_BYTES: The size in bytes of local memory
 *   used by each thread of this kernel.
 * - ::CU_FUNC_ATTRIBUTE_NUM_REGS: The number of registers used by each thread
 *   of this kernel.
 * - ::CU_FUNC_ATTRIBUTE_PTX_VERSION: The PTX virtual architecture version for
 *   which the kernel was compiled. This value is the major PTX version * 10
 *   + the minor PTX version, so a PTX version 1.3 function would return the
 *   value 13. Note that this may return the undefined value of 0 for cubins
 *   compiled prior to CUDA 3.0.
 * - ::CU_FUNC_ATTRIBUTE_BINARY_VERSION: The binary architecture version for
 *   which the kernel was compiled. This value is the major binary
 *   version * 10 + the minor binary version, so a binary version 1.3 function
 *   would return the value 13. Note that this will return a value of 10 for
 *   legacy cubins that do not have a properly-encoded binary architecture
 *   version.
 * - ::CU_FUNC_CACHE_MODE_CA: The attribute to indicate whether the kernel has
 *   been compiled with user specified option "-Xptxas --dlcm=ca" set.
 * - ::CU_FUNC_ATTRIBUTE_MAX_DYNAMIC_SHARED_SIZE_BYTES: The maximum size in bytes of
 *   dynamically-allocated shared memory.
 * - ::CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT: Preferred shared memory-L1
 *   cache split ratio in percent of total shared memory.
 * - ::CU_FUNC_ATTRIBUTE_CLUSTER_SIZE_MUST_BE_SET: If this attribute is set, the
 *   kernel must launch with a valid cluster size specified.
 * - ::CU_FUNC_ATTRIBUTE_REQUIRED_CLUSTER_WIDTH: The required cluster width in
 *   blocks.
 * - ::CU_FUNC_ATTRIBUTE_REQUIRED_CLUSTER_HEIGHT: The required cluster height in
 *   blocks.
 * - ::CU_FUNC_ATTRIBUTE_REQUIRED_CLUSTER_DEPTH: The required cluster depth in
 *   blocks.
 * - ::CU_FUNC_ATTRIBUTE_NON_PORTABLE_CLUSTER_SIZE_ALLOWED: Indicates whether
 *   the function can be launched with non-portable cluster size. 1 is allowed,
 *   0 is disallowed. A non-portable cluster size may only function on the
 *   specific SKUs the program is tested on. The launch might fail if the
 *   program is run on a different hardware platform. CUDA API provides
 *   cudaOccupancyMaxActiveClusters to assist with checking whether the desired
 *   size can be launched on the current device. A portable cluster size is
 *   guaranteed to be functional on all compute capabilities higher than the
 *   target compute capability. The portable cluster size for sm_90 is 8 blocks
 *   per cluster. This value may increase for future compute capabilities. The
 *   specific hardware unit may support higher cluster sizes that’s not
 *   guaranteed to be portable.
 * - ::CU_FUNC_ATTRIBUTE_CLUSTER_SCHEDULING_POLICY_PREFERENCE: The block
 *   scheduling policy of a function. The value type is CUclusterSchedulingPolicy.
 *
 * \note If another thread is trying to set the same attribute on the same device using
 * ::cuKernelSetAttribute() simultaneously, the attribute query will give the old or new
 * value depending on the interleavings chosen by the OS scheduler and memory consistency.
 *
 * \param pi     - Returned attribute value
 * \param attrib - Attribute requested
 * \param kernel  - Kernel to query attribute of
 * \param dev - Device to query attribute of
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuKernelSetAttribute,
 * ::cuLibraryGetKernel,
 * ::cuLaunchKernel,
 * ::cuKernelGetFunction,
 * ::cuLibraryGetModule,
 * ::cuModuleGetFunction,
 * ::cuFuncGetAttribute
 */
CUresult cuKernelGetAttribute(int *pi, CUfunction_attribute attrib, CUkernel kernel, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns a function handle
 *
 * Returns in \p pFunc the handle of the function for the requested kernel \p kernel and
 * the current context. If function handle is not found, the call returns ::CUDA_ERROR_NOT_FOUND.
 *
 * \param pFunc - Returned function handle
 * \param kernel - Kernel to retrieve function for the requested context
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_NOT_FOUND,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_CONTEXT_IS_DESTROYED
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuLibraryGetKernel,
 * ::cuLibraryGetModule,
 * ::cuModuleGetFunction
 */
CUresult cuKernelGetFunction(CUfunction *pFunc, CUkernel kernel)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns the function name for a ::CUkernel handle
 *
 * Returns in \p **name the function name associated with the kernel handle \p hfunc .
 * The function name is returned as a null-terminated string. The returned name is only 
 * valid when the kernel handle is valid. If the library is unloaded or reloaded, one 
 * must call the API again to get the updated name. This API may return a mangled name if
 * the function is not declared as having C linkage. If either \p **name or \p hfunc 
 * is NULL, ::CUDA_ERROR_INVALID_VALUE is returned.
 *
 * \param name - The returned name of the function
 * \param hfunc - The function handle to retrieve the name for 
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE
 * \notefnerr
 *
 */
CUresult cuKernelGetName(const char **name, CUkernel hfunc)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns the offset and size of a kernel parameter in the device-side parameter layout
 *
 * Queries the kernel parameter at \p paramIndex into \p kernel's list of parameters, and returns
 * in \p paramOffset and \p paramSize the offset and size, respectively, where the parameter
 * will reside in the device-side parameter layout. This information can be used to update kernel
 * node parameters from the device via ::cudaGraphKernelNodeSetParam() and
 * ::cudaGraphKernelNodeUpdatesApply(). \p paramIndex must be less than the number of parameters
 * that \p kernel takes. \p paramSize can be set to NULL if only the parameter offset is desired.
 *
 * \param kernel      - The kernel to query
 * \param paramIndex  - The parameter index to query
 * \param paramOffset - Returns the offset into the device-side parameter layout at which the parameter resides
 * \param paramSize   - Optionally returns the size of the parameter in the device-side parameter layout
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE,
 * \notefnerr
 *
* \sa ::cuFuncGetParamInfo
 */
CUresult cuKernelGetParamInfo(CUkernel kernel, size_t paramIndex, size_t *paramOffset, size_t *paramSize)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Sets information about a kernel
 *
 * This call sets the value of a specified attribute \p attrib on the kernel \p kernel
 * for the requested device \p dev to an integer value specified by \p val.
 * This function returns CUDA_SUCCESS if the new value of the attribute could be
 * successfully set. If the set fails, this call will return an error.
 * Not all attributes can have values set. Attempting to set a value on a read-only
 * attribute will result in an error (CUDA_ERROR_INVALID_VALUE)
 *
 * Note that attributes set using ::cuFuncSetAttribute() will override the attribute
 * set by this API irrespective of whether the call to ::cuFuncSetAttribute() is made
 * before or after this API call. However, ::cuKernelGetAttribute() will always
 * return the attribute value set by this API.
 *
 * Supported attributes are:
 * - ::CU_FUNC_ATTRIBUTE_MAX_DYNAMIC_SHARED_SIZE_BYTES: This is the maximum size in bytes of
 *   dynamically-allocated shared memory. The value should contain the requested
 *   maximum size of dynamically-allocated shared memory. The sum of this value and
 *   the function attribute ::CU_FUNC_ATTRIBUTE_SHARED_SIZE_BYTES cannot exceed the
 *   device attribute ::CU_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_BLOCK_OPTIN.
 *   The maximal size of requestable dynamic shared memory may differ by GPU
 *   architecture.
 * - ::CU_FUNC_ATTRIBUTE_PREFERRED_SHARED_MEMORY_CARVEOUT: On devices where the L1
 *   cache and shared memory use the same hardware resources, this sets the shared memory
 *   carveout preference, in percent of the total shared memory.
 *   See ::CU_DEVICE_ATTRIBUTE_MAX_SHARED_MEMORY_PER_MULTIPROCESSOR
 *   This is only a hint, and the driver can choose a different ratio if required to execute the function.
 * - ::CU_FUNC_ATTRIBUTE_REQUIRED_CLUSTER_WIDTH: The required cluster width in
 *   blocks. The width, height, and depth values must either all be 0 or all be
 *   positive. The validity of the cluster dimensions is checked at launch time.
 *   If the value is set during compile time, it cannot be set at runtime.
 *   Setting it at runtime will return CUDA_ERROR_NOT_PERMITTED.
 * - ::CU_FUNC_ATTRIBUTE_REQUIRED_CLUSTER_HEIGHT: The required cluster height in
 *   blocks. The width, height, and depth values must either all be 0 or all be
 *   positive. The validity of the cluster dimensions is checked at launch time.
 *   If the value is set during compile time, it cannot be set at runtime.
 *   Setting it at runtime will return CUDA_ERROR_NOT_PERMITTED.
 * - ::CU_FUNC_ATTRIBUTE_REQUIRED_CLUSTER_DEPTH: The required cluster depth in
 *   blocks. The width, height, and depth values must either all be 0 or all be
 *   positive. The validity of the cluster dimensions is checked at launch time.
 *   If the value is set during compile time, it cannot be set at runtime.
 *   Setting it at runtime will return CUDA_ERROR_NOT_PERMITTED.
 * - ::CU_FUNC_ATTRIBUTE_CLUSTER_SCHEDULING_POLICY_PREFERENCE: The block
 *   scheduling policy of a function. The value type is CUclusterSchedulingPolicy.
 *
 * \note The API has stricter locking requirements in comparison to its legacy counterpart
 * ::cuFuncSetAttribute() due to device-wide semantics. If multiple threads are trying to
 * set the same attribute on the same device simultaneously, the attribute setting will depend
 * on the interleavings chosen by the OS scheduler and memory consistency.
 *
 * \param attrib - Attribute requested
 * \param val - Value to set
 * \param kernel  - Kernel to set attribute of
 * \param dev - Device to set attribute of
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_OUT_OF_MEMORY
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuKernelGetAttribute,
 * ::cuLibraryGetKernel,
 * ::cuLaunchKernel,
 * ::cuKernelGetFunction,
 * ::cuLibraryGetModule,
 * ::cuModuleGetFunction,
 * ::cuFuncSetAttribute
 */
CUresult cuKernelSetAttribute(CUfunction_attribute attrib, int val, CUkernel kernel, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Sets the preferred cache configuration for a device kernel.
 *
 * On devices where the L1 cache and shared memory use the same hardware
 * resources, this sets through \p config the preferred cache configuration for
 * the device kernel \p kernel on the requested device \p dev. This is only a preference.
 * The driver will use the requested configuration if possible, but it is free to choose a different
 * configuration if required to execute \p kernel.  Any context-wide preference
 * set via ::cuCtxSetCacheConfig() will be overridden by this per-kernel
 * setting.
 *
 * Note that attributes set using ::cuFuncSetCacheConfig() will override the attribute
 * set by this API irrespective of whether the call to ::cuFuncSetCacheConfig() is made
 * before or after this API call.
 *
 * This setting does nothing on devices where the size of the L1 cache and
 * shared memory are fixed.
 *
 * Launching a kernel with a different preference than the most recent
 * preference setting may insert a device-side synchronization point.
 *
 *
 * The supported cache configurations are:
 * - ::CU_FUNC_CACHE_PREFER_NONE: no preference for shared memory or L1 (default)
 * - ::CU_FUNC_CACHE_PREFER_SHARED: prefer larger shared memory and smaller L1 cache
 * - ::CU_FUNC_CACHE_PREFER_L1: prefer larger L1 cache and smaller shared memory
 * - ::CU_FUNC_CACHE_PREFER_EQUAL: prefer equal sized L1 cache and shared memory
 *
 * \note The API has stricter locking requirements in comparison to its legacy counterpart
 * ::cuFuncSetCacheConfig() due to device-wide semantics. If multiple threads are trying to
 * set a config on the same device simultaneously, the cache config setting will depend
 * on the interleavings chosen by the OS scheduler and memory consistency.
 *
 * \param kernel  - Kernel to configure cache for
 * \param config - Requested cache configuration
 * \param dev - Device to set attribute of
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_OUT_OF_MEMORY
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuLibraryGetKernel,
 * ::cuKernelGetFunction,
 * ::cuLibraryGetModule,
 * ::cuModuleGetFunction,
 * ::cuFuncSetCacheConfig,
 * ::cuCtxSetCacheConfig,
 * ::cuLaunchKernel
 */
CUresult cuKernelSetCacheConfig(CUkernel kernel, CUfunc_cache config, CUdevice dev)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Retrieve the kernel handles within a library.
 *
 * Returns in \p kernels a maximum number of \p numKernels kernel handles within \p lib.
 * The returned kernel handle becomes invalid when the library is unloaded.
 *
 * \param kernels - Buffer where the kernel handles are returned to
 * \param numKernels - Maximum number of kernel handles may be returned to the buffer
 * \param lib - Library to query from
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa ::cuLibraryGetKernelCount
 */
CUresult cuLibraryEnumerateKernels(CUkernel *kernels, unsigned int numKernels, CUlibrary lib)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns a global device pointer
 *
 * Returns in \p *dptr and \p *bytes the base pointer and size of the global with
 * name \p name for the requested library \p library and the current context.
 * If no global for the requested name \p name exists, the call returns ::CUDA_ERROR_NOT_FOUND.
 * One of the parameters \p dptr or \p bytes (not both) can be NULL in which
 * case it is ignored.
 *
 * \param dptr - Returned global device pointer for the requested context
 * \param bytes - Returned global size in bytes
 * \param library - Library to retrieve global from
 * \param name - Name of global to retrieve
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_NOT_FOUND,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_CONTEXT_IS_DESTROYED
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuLibraryGetModule,
 * cuModuleGetGlobal
 */
CUresult cuLibraryGetGlobal(CUdeviceptr *dptr, size_t *bytes, CUlibrary library, const char *name)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns a kernel handle
 *
 * Returns in \p pKernel the handle of the kernel with name \p name located in library \p library.
 * If kernel handle is not found, the call returns ::CUDA_ERROR_NOT_FOUND.
 *
 * \param pKernel - Returned kernel handle
 * \param library - Library to retrieve kernel from
 * \param name - Name of kernel to retrieve
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_NOT_FOUND
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuKernelGetFunction,
 * ::cuLibraryGetModule,
 * ::cuModuleGetFunction
 */
CUresult cuLibraryGetKernel(CUkernel *pKernel, CUlibrary library, const char *name)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns the number of kernels within a library
 *
 * Returns in \p count the number of kernels in \p lib.
 *
 * \param count - Number of kernels found within the library
 * \param lib - Library to query
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_INVALID_VALUE
 */
CUresult cuLibraryGetKernelCount(unsigned int *count, CUlibrary lib)
{
    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns a pointer to managed memory
 *
 * Returns in \p *dptr and \p *bytes the base pointer and size of the managed memory with
 * name \p name for the requested library \p library. If no managed memory with the
 * requested name \p name exists, the call returns ::CUDA_ERROR_NOT_FOUND. One of the parameters
 * \p dptr or \p bytes (not both) can be NULL in which case it is ignored.
 * Note that managed memory for library \p library is shared across devices and is registered
 * when the library is loaded into atleast one context.
 *
 * \note The API requires a CUDA context to be present and initialized on at least one device.
 * If no context is present, the call returns ::CUDA_ERROR_NOT_FOUND.
 *
 * \param dptr - Returned pointer to the managed memory
 * \param bytes - Returned memory size in bytes
 * \param library - Library to retrieve managed memory from
 * \param name - Name of managed memory to retrieve
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_NOT_FOUND
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload
 */
CUresult cuLibraryGetManaged(CUdeviceptr *dptr, size_t *bytes, CUlibrary library, const char *name)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns a module handle
 *
 * Returns in \p pMod the module handle associated with the current context located in
 * library \p library. If module handle is not found, the call returns ::CUDA_ERROR_NOT_FOUND.
 *
 * \param pMod - Returned module handle
 * \param library - Library to retrieve module from
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_NOT_FOUND,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_CONTEXT_IS_DESTROYED
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuModuleGetFunction
 */
CUresult cuLibraryGetModule(CUmodule *pMod, CUlibrary library)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Returns a pointer to a unified function
 *
 * Returns in \p *fptr the function pointer to a unified function denoted by \p symbol.
 * If no unified function with name \p symbol exists, the call returns ::CUDA_ERROR_NOT_FOUND.
 * If there is no device with attribute ::CU_DEVICE_ATTRIBUTE_UNIFIED_FUNCTION_POINTERS present in the system,
 * the call may return ::CUDA_ERROR_NOT_FOUND.
 *
 * \param fptr - Returned pointer to a unified function
 * \param library - Library to retrieve function pointer memory from
 * \param symbol - Name of function pointer to retrieve
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * ::CUDA_ERROR_NOT_FOUND
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload
 */
CUresult cuLibraryGetUnifiedFunction(void **fptr, CUlibrary library, const char *symbol)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Load a library with specified code and options
 *
 * Takes a pointer \p code and loads the corresponding library \p library based on
 * the application defined library loading mode:
 * - If module loading is set to EAGER, via the environment variables described in "Module loading",
 *   \p library is loaded eagerly into all contexts at the time of the call and future contexts
 *   at the time of creation until the library is unloaded with ::cuLibraryUnload().
 * - If the environment variables are set to LAZY, \p library
 *   is not immediately loaded onto all existent contexts and will only be
 *   loaded when a function is needed for that context, such as a kernel launch.
 *
 * These environment variables are described in the CUDA programming guide under the 
 * "CUDA environment variables" section.
 *
 * The \p code may be a \e cubin or \e fatbin as output by \b nvcc,
 * or a NULL-terminated \e PTX, either as output by \b nvcc or hand-written.
 *
 * Options are passed as an array via \p jitOptions and any corresponding parameters are passed in
 * \p jitOptionsValues. The number of total JIT options is supplied via \p numJitOptions.
 * Any outputs will be returned via \p jitOptionsValues.
 *
 * Library load options are passed as an array via \p libraryOptions and any corresponding parameters are passed in
 * \p libraryOptionValues. The number of total library load options is supplied via \p numLibraryOptions.
 *
 * \param library             - Returned library
 * \param code                - Code to load
 * \param jitOptions          - Options for JIT
 * \param jitOptionsValues    - Option values for JIT
 * \param numJitOptions       - Number of options
 * \param libraryOptions      - Options for loading
 * \param libraryOptionValues - Option values for loading
 * \param numLibraryOptions   - Number of options for loading
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_PTX,
 * ::CUDA_ERROR_UNSUPPORTED_PTX_VERSION,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_NO_BINARY_FOR_GPU,
 * ::CUDA_ERROR_SHARED_OBJECT_SYMBOL_NOT_FOUND,
 * ::CUDA_ERROR_SHARED_OBJECT_INIT_FAILED,
 * ::CUDA_ERROR_JIT_COMPILER_NOT_FOUND
 *
 * \sa ::cuLibraryLoadFromFile,
 * ::cuLibraryUnload,
 * ::cuModuleLoad,
 * ::cuModuleLoadData,
 * ::cuModuleLoadDataEx
 */
CUresult cuLibraryLoadData(CUlibrary *library, const void *code,
    CUjit_option *jitOptions, void **jitOptionsValues, unsigned int numJitOptions,
    CUlibraryOption *libraryOptions, void** libraryOptionValues, unsigned int numLibraryOptions)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Load a library with specified file and options
 *
 * Takes a pointer \p code and loads the corresponding library \p library based on
 * the application defined library loading mode:
 * - If module loading is set to EAGER, via the environment variables described in "Module loading",
 *   \p library is loaded eagerly into all contexts at the time of the call and future contexts
 *   at the time of creation until the library is unloaded with ::cuLibraryUnload().
 * - If the environment variables are set to LAZY, \p library
 *   is not immediately loaded onto all existent contexts and will only be
 *   loaded when a function is needed for that context, such as a kernel launch.
 *
 * These environment variables are described in the CUDA programming guide under the 
 * "CUDA environment variables" section.
 *
 * The file should be a \e cubin file as output by \b nvcc, or a \e PTX file either
 * as output by \b nvcc or handwritten, or a \e fatbin file as output by \b nvcc.
 *
 * Options are passed as an array via \p jitOptions and any corresponding parameters are
 * passed in \p jitOptionsValues. The number of total options is supplied via \p numJitOptions.
 * Any outputs will be returned via \p jitOptionsValues.
 *
 * Library load options are passed as an array via \p libraryOptions and any corresponding parameters are passed in
 * \p libraryOptionValues. The number of total library load options is supplied via \p numLibraryOptions.
 *
 * \param library             - Returned library
 * \param fileName            - File to load from
 * \param jitOptions          - Options for JIT
 * \param jitOptionsValues    - Option values for JIT
 * \param numJitOptions       - Number of options
 * \param libraryOptions      - Options for loading
 * \param libraryOptionValues - Option values for loading
 * \param numLibraryOptions   - Number of options for loading
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_PTX,
 * ::CUDA_ERROR_UNSUPPORTED_PTX_VERSION,
 * ::CUDA_ERROR_OUT_OF_MEMORY,
 * ::CUDA_ERROR_NO_BINARY_FOR_GPU,
 * ::CUDA_ERROR_SHARED_OBJECT_SYMBOL_NOT_FOUND,
 * ::CUDA_ERROR_SHARED_OBJECT_INIT_FAILED,
 * ::CUDA_ERROR_JIT_COMPILER_NOT_FOUND
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryUnload,
 * ::cuModuleLoad,
 * ::cuModuleLoadData,
 * ::cuModuleLoadDataEx
 */
CUresult cuLibraryLoadFromFile(CUlibrary *library, const char *fileName,
    CUjit_option *jitOptions, void **jitOptionsValues, unsigned int numJitOptions,
    CUlibraryOption *libraryOptions, void **libraryOptionValues, unsigned int numLibraryOptions)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Unloads a library
 *
 * Unloads the library specified with \p library
 *
 * \param library - Library to unload
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa ::cuLibraryLoadData,
 * ::cuLibraryLoadFromFile,
 * ::cuModuleUnload
 */
CUresult cuLibraryUnload(CUlibrary library)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}