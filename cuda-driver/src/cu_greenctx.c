#include "cu_init.h"
#include "pal_tlsthreaddata.h"
#include "pal_tlsthreadmanager.h"

/**
 * \brief Converts a green context into the primary context
 *
 * The API converts a green context into the primary context returned in \p pContext. It is important
 * to note that the converted context \p pContext is a normal primary context but with
 * the resources of the specified green context \p hCtx. Once converted, it can then
 * be used to set the context current with ::cuCtxSetCurrent or with any of the CUDA APIs
 * that accept a CUcontext parameter.
 *
 * Users are expected to call this API before calling any CUDA APIs that accept a
 * CUcontext. Failing to do so will result in the APIs returning ::CUDA_ERROR_INVALID_CONTEXT.
 *
 * \param pContext Returned primary context with green context resources
 * \param hCtx Green context to convert
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::cuGreenCtxCreate
 */
CUresult cuCtxFromGreenCtx(CUcontext *pContext, CUgreenCtx hCtx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Get context resources
 *
 * Get the \p type resources available to the context represented by \p hCtx
 * \param hCtx - Context to get resource for
 *
 * Note: The API is not supported on 32-bit platforms.
 *
 * \param resource - Output pointer to a CUdevResource structure
 * \param type - Type of resource to retrieve
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_RESOURCE_TYPE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_CONTEXT
 *
 * \sa
 * ::cuDevResourceGenerateDesc
 */
CUresult cuCtxGetDevResource(CUcontext hCtx, CUdevResource* resource, CUdevResourceType type)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Get device resources
 *
 * Get the \p type resources available to the \p device.
 * This may often be the starting point for further partitioning or configuring of resources.
 *
 * Note: The API is not supported on 32-bit platforms.
 *
 * \param device - Device to get resource for
 * \param resource - Output pointer to a CUdevResource structure
 * \param type - Type of resource to retrieve
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_RESOURCE_TYPE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_DEVICE
 *
 * \sa
 * ::cuDevResourceGenerateDesc
 */
CUresult cuDeviceGetDevResource(CUdevice device, CUdevResource* resource, CUdevResourceType type)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Generate a resource descriptor
 *
 * Generates a resource descriptor with the set of resources specified in \p resources.
 * The generated resource descriptor is necessary for the creation of green contexts via the ::cuGreenCtxCreate API.
 * The API expects \p nbResources == 1, as there is only one type of resource and merging the same
 * types of resource is currently not supported.
 *
 * Note: The API is not supported on 32-bit platforms.
 *
 * \param phDesc - Output descriptor
 * \param resources - Array of resources to be included in the descriptor
 * \param nbResources - Number of resources passed in \p resources
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_RESOURCE_TYPE,
 * ::CUDA_ERROR_INVALID_RESOURCE_CONFIGURATION
 *
 * \sa
 * ::cuDevSmResourceSplitByCount
 */
CUresult cuDevResourceGenerateDesc(CUdevResourceDesc *phDesc, CUdevResource *resources, unsigned int nbResources)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Splits \p CU_DEV_RESOURCE_TYPE_SM resources.
 *
 * Splits \p CU_DEV_RESOURCE_TYPE_SM resources into \p nbGroups, adhering to the minimum SM count specified in \p minCount
 * and the usage flags in \p useFlags. If \p result is NULL, the API simulates a split and provides the amount of groups that
 * would be created in \p nbGroups. Otherwise, \p nbGroups must point to the amount of elements in \p result and on return,
 * the API will overwrite \p nbGroups with the amount actually created. The groups are written to the array in \p result.
 * \p nbGroups can be less than the total amount if a smaller number of groups is needed.
 *
 * This API is used to spatially partition the input resource. The input resource needs to come from one of
 * ::cuDeviceGetDevResource, ::cuCtxGetDevResource, or ::cuGreenCtxGetDevResource.
 * A limitation of the API is that the output results cannot be split again without
 * first creating a descriptor and a green context with that descriptor.
 *
 * When creating the groups, the API will take into account the performance and functional characteristics of the
 * input resource, and guarantee a split that will create a disjoint set of symmetrical partitions. This may lead to less groups created
 * than purely dividing the total SM count by the \p minCount due to cluster requirements or
 * alignment and granularity requirements for the minCount.
 *
 * The \p remainder set, might not have the same functional or performance guarantees as the groups in \p result.
 * Its use should be carefully planned and future partitions of the \p remainder set are discouraged.
 *
 * A successful API call must either have:
 * - A valid array of \p result pointers of size passed in \p nbGroups, with \p Input of type \p CU_DEV_RESOURCE_TYPE_SM.
 * Value of \p minCount must be between 0 and the SM count specified in \p input. \p remaining and \p useFlags are optional.
 * - NULL passed in for \p result, with a valid integer pointer in \p nbGroups and \p Input of type \p CU_DEV_RESOURCE_TYPE_SM.
 * Value of \p minCount must be between 0 and the SM count specified in \p input.
 * This queries the number of groups that would be created by the API.
 *
 * Note: The API is not supported on 32-bit platforms.
 *
 * \param result - Output array of \p CUdevResource resources. Can be NULL to query the number of groups.
 * \param nbGroups - This is a pointer, specifying the number of groups that would be or should be created as described below.
 * \param input - Input SM resource to be split. Must be a valid \p CU_DEV_RESOURCE_TYPE_SM resource.
 * \param remaining - If the input resource cannot be cleanly split among \p nbGroups, the remaining is placed in here.
 * Can be ommitted (NULL) if the user does not need the remaining set.
 * \param useFlags - Flags specifying how these partitions are used or which constraints to abide by when splitting the input.
 * \param minCount - Minimum number of SMs required
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_INVALID_RESOURCE_TYPE,
 * ::CUDA_ERROR_INVALID_RESOURCE_CONFIGURATION
 *
 * \sa
 * ::cuGreenCtxGetDevResource,
 * ::cuCtxGetDevResource,
 * ::cuDeviceGetDevResource
 */
CUresult cuDevSmResourceSplitByCount(CUdevResource* pResult, unsigned int* nbGroups, const CUdevResource* input, CUdevResource* remaining, unsigned int useFlags, unsigned int minCount)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Creates a green context with a specified set of resources.
 *
 * This API creates a green context with the resources specified in the descriptor \p desc and
 * returns it in the handle represented by \p phCtx. This API will retain the primary context on device \p dev,
 * which will is released when the green context is destroyed. It is advised to have the primary context active
 * before calling this API to avoid the heavy cost of triggering primary context initialization and
 * deinitialization multiple times.
 *
 * The API does not set the green context current. In order to set it current, you need to explicitly set it current
 * by first converting the green context to a CUcontext using ::cuCtxFromGreenCtx and subsequently calling
 * ::cuCtxSetCurrent / ::cuCtxPushCurrent. It should be noted that a green context can be current to only one
 * thread at a time. There is no internal synchronization to make API calls accessing the same green context
 * from multiple threads work.
 *
 * Note: The API is not supported on 32-bit platforms.
 *
 * \param phCtx - Pointer for the output handle to the green context
 * \param desc - Descriptor generated via ::cuDevResourceGenerateDesc which contains the set of resources to be used
 * \param dev - Device on which to create the green context.
 * \param flags - One of the supported green context creation flags. \p CU_GREEN_CTX_DEFAULT_STREAM is required.
 *
 * The supported flags are:
 * - \p CU_GREEN_CTX_DEFAULT_STREAM : Creates a default stream to use inside the green context. Required.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_DEVICE,
 * ::CUDA_ERROR_INVALID_VALUE,
 * ::CUDA_ERROR_NOT_SUPPORTED,
 * ::CUDA_ERROR_OUT_OF_MEMORY
 *
 * \sa
 * ::cuGreenCtxDestroy,
 * ::cuCtxFromGreenCtx,
 * ::cuCtxSetCurrent,
 * ::cuCtxPushCurrent,
 * ::cuDevResourceGenerateDesc,
 * ::cuDevicePrimaryCtxRetain,
 * ::cuCtxCreate,
 * ::cuCtxCreate_v3
 */
CUresult cuGreenCtxCreate(CUgreenCtx* phCtx, CUdevResourceDesc desc, CUdevice dev, unsigned int flags)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Destroys a green context
 *
 * Destroys the green context, releasing the primary context of the device that this green context was created for.
 * Any resources provisioned for this green context (that were initially available via the resource descriptor)
 * are released as well.
 * \param hCtx - Green context to be destroyed
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_CONTEXT_IS_DESTROYED
 *
 * \sa
 * ::cuGreenCtxCreate,
 * ::cuCtxDestroy
 */
CUresult cuGreenCtxDestroy(CUgreenCtx hCtx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Get green context resources
 *
 * Get the \p type resources available to the green context represented by \p hCtx
 * \param hCtx - Green context to get resource for
 * \param resource - Output pointer to a CUdevResource structure
 * \param type - Type of resource to retrieve
 *
 * \return
 * ::CUDA_SUCCESS
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_RESOURCE_TYPE,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::cuDevResourceGenerateDesc
 */
CUresult cuGreenCtxGetDevResource(CUgreenCtx hCtx, CUdevResource* resource, CUdevResourceType type)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Records an event.
 *
 * Captures in \phEvent all the activities of the green context of \phCtx
 * at the time of this call. \phEvent and \phCtx must be from the same
 * CUDA context. Calls such as ::cuEventQuery() or ::cuGreenCtxWaitEvent() will
 * then examine or wait for completion of the work that was captured. Uses of
 * \p hCtx after this call do not modify \p hEvent.
 *
 * \note The API will return an error if the specified green context \p hCtx
 * has a stream in the capture mode. In such a case, the call will invalidate
 * all the conflicting captures.
 *
 * \param hCtx - Green context to record event for
 * \param hEvent  - Event to record
 *
 * \return
 * ::CUDA_SUCCESS
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_HANDLE
 *
 * \sa
 * ::cuGreenCtxWaitEvent,
 * ::cuEventRecord
 */
CUresult cuGreenCtxRecordEvent(CUgreenCtx hCtx, CUevent hEvent)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Make a green context wait on an event
 *
 * Makes all future work submitted to green context \phCtx wait for all work
 * captured in \phEvent. The synchronization will be performed on the device
 * and will not block the calling CPU thread. See ::cuGreenCtxRecordEvent()
 * for details on what is captured by an event.
 *
 * \note The API will return an error and invalidate the capture if the specified
 * event \p hEvent is part of an ongoing capture sequence.
 *
 * \param hCtx    - Green context to wait
 * \param hEvent  - Event to wait on (may not be NULL)
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_HANDLE
 *
 * \sa
 * ::cuGreenCtxRecordEvent,
 * ::cuStreamWaitEvent
 */
CUresult cuGreenCtxWaitEvent(CUgreenCtx hCtx, CUevent hEvent)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}

/**
 * \brief Query the green context associated with a stream
 *
 * Returns the CUDA green context that the stream is associated with, or NULL if the stream
 * is not associated with any green context.
 *
 * The stream handle \p hStream can refer to any of the following:
 * <ul>
 *   <li>
 *   a stream created via any of the CUDA driver APIs such as ::cuStreamCreate.
 *   If during stream creation the context that was active in the calling thread was obtained
 *   with cuCtxFromGreenCtx, that green context is returned in \p phCtx.
 *   Otherwise, \p *phCtx is set to NULL instead.
 *   </li>
 *   <li>
 *   special stream such as the NULL stream or ::CU_STREAM_LEGACY.
 *   In that case if context that is active in the calling thread was obtained
 *   with cuCtxFromGreenCtx, that green context is returned.
 *   Otherwise, \p *phCtx is set to NULL instead.
 *   </li>
 * </ul>
 * Passing an invalid handle will result in undefined behavior.
 *
 * \param hStream - Handle to the stream to be queried
 * \param phCtx   - Returned green context associated with the stream
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_DEINITIALIZED,
 * ::CUDA_ERROR_NOT_INITIALIZED,
 * ::CUDA_ERROR_INVALID_CONTEXT,
 * ::CUDA_ERROR_INVALID_HANDLE,
 * \notefnerr
 *
 * \sa ::cuStreamDestroy,
 * ::cuStreamCreateWithPriority,
 * ::cuStreamGetPriority,
 * ::cuStreamGetFlags,
 * ::cuStreamWaitEvent,
 * ::cuStreamQuery,
 * ::cuStreamSynchronize,
 * ::cuStreamAddCallback,
 * ::cudaStreamCreate,
 * ::cudaStreamCreateWithFlags
 */
CUresult cuStreamGetGreenCtx(CUstream hStream, CUgreenCtx *phCtx)
{
    palResult         result         = PAL_SUCCESS;
    palTlsThreadData* pTlsThreadData = NULL;

    result = palGlobalManagerGetStatus();
    if (result != PAL_SUCCESS)
    {
        return (CUresult)result;
    }

    result = palTlsThreadManagerGetCurrentThreadData(g_globalManager.pThreadManager, &pTlsThreadData);
    if (result != PAL_SUCCESS)
    {
        CU_DBG_PRINTF_ERROR("Failed to get current thread data.");
        return (CUresult)result;
    }

    if (palTlsThreadDataIsAllowedToMakeApiCalls(pTlsThreadData) == PAL_FALSE)
    {
        CU_DBG_PRINTF_ERROR("Thread is not allowed to make API calls.");
        return CUDA_ERROR_NOT_PERMITTED;
    }

    return CUDA_ERROR_NOT_SUPPORTED;
}