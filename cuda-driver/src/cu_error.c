#include "cuda.h"

/**
 * \brief Gets the string representation of an error code enum name
 *
 * Sets \p *pStr to the address of a NULL-terminated string representation
 * of the name of the enum error code \p error.
 * If the error code is not recognized, ::CUDA_ERROR_INVALID_VALUE
 * will be returned and \p *pStr will be set to the NULL address.
 *
 * \param error - Error code to convert to string
 * \param pStr - Address of the string pointer.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::CUresult,
 * ::cudaGetErrorName
 */
CUresult cuGetErrorName(CUresult error, const char **pStr)
{
    if (pStr == NULL)
    {
        return CUDA_ERROR_INVALID_VALUE;
    }

    switch (error)
    {
    case CUDA_SUCCESS:
        *pStr = "CUDA_SUCCESS";
        break;
    case CUDA_ERROR_INVALID_VALUE:
        *pStr = "CUDA_ERROR_INVALID_VALUE";
        break;
    case CUDA_ERROR_OUT_OF_MEMORY:
        *pStr = "CUDA_ERROR_OUT_OF_MEMORY";
        break;
    case CUDA_ERROR_NOT_INITIALIZED:
        *pStr = "CUDA_ERROR_NOT_INITIALIZED";
        break;
    case CUDA_ERROR_DEINITIALIZED:
        *pStr = "CUDA_ERROR_DEINITIALIZED";
        break;
    case CUDA_ERROR_PROFILER_DISABLED:
        *pStr = "CUDA_ERROR_PROFILER_DISABLED";
        break;
    case CUDA_ERROR_PROFILER_NOT_INITIALIZED:
        *pStr = "CUDA_ERROR_PROFILER_NOT_INITIALIZED";
        break;
    case CUDA_ERROR_PROFILER_ALREADY_STARTED:
        *pStr = "CUDA_ERROR_PROFILER_ALREADY_STARTED";
        break;
    case CUDA_ERROR_PROFILER_ALREADY_STOPPED:
        *pStr = "CUDA_ERROR_PROFILER_ALREADY_STOPPED";
        break;
    case CUDA_ERROR_STUB_LIBRARY:
        *pStr = "CUDA_ERROR_STUB_LIBRARY";
        break;
    case CUDA_ERROR_DEVICE_UNAVAILABLE:
        *pStr = "CUDA_ERROR_DEVICE_UNAVAILABLE";
        break;
    case CUDA_ERROR_NO_DEVICE:
        *pStr = "CUDA_ERROR_NO_DEVICE";
        break;
    case CUDA_ERROR_INVALID_DEVICE:
        *pStr = "CUDA_ERROR_INVALID_DEVICE";
        break;
    case CUDA_ERROR_DEVICE_NOT_LICENSED:
        *pStr = "CUDA_ERROR_DEVICE_NOT_LICENSED";
        break;
    case CUDA_ERROR_INVALID_IMAGE:
        *pStr = "CUDA_ERROR_INVALID_IMAGE";
        break;
    case CUDA_ERROR_INVALID_CONTEXT:
        *pStr = "CUDA_ERROR_INVALID_CONTEXT";
        break;
    case CUDA_ERROR_CONTEXT_ALREADY_CURRENT:
        *pStr = "CUDA_ERROR_CONTEXT_ALREADY_CURRENT";
        break;
    case CUDA_ERROR_MAP_FAILED:
        *pStr = "CUDA_ERROR_MAP_FAILED";
        break;
    case CUDA_ERROR_UNMAP_FAILED:
        *pStr = "CUDA_ERROR_UNMAP_FAILED";
        break;
    case CUDA_ERROR_ARRAY_IS_MAPPED:
        *pStr = "CUDA_ERROR_ARRAY_IS_MAPPED";
        break;
    case CUDA_ERROR_ALREADY_MAPPED:
        *pStr = "CUDA_ERROR_ALREADY_MAPPED";
        break;
    case CUDA_ERROR_NO_BINARY_FOR_GPU:
        *pStr = "CUDA_ERROR_NO_BINARY_FOR_GPU";
        break;
    case CUDA_ERROR_ALREADY_ACQUIRED:
        *pStr = "CUDA_ERROR_ALREADY_ACQUIRED";
        break;
    case CUDA_ERROR_NOT_MAPPED:
        *pStr = "CUDA_ERROR_NOT_MAPPED";
        break;
    case CUDA_ERROR_NOT_MAPPED_AS_ARRAY:
        *pStr = "CUDA_ERROR_NOT_MAPPED_AS_ARRAY";
        break;
    case CUDA_ERROR_NOT_MAPPED_AS_POINTER:
        *pStr = "CUDA_ERROR_NOT_MAPPED_AS_POINTER";
        break;
    case CUDA_ERROR_ECC_UNCORRECTABLE:
        *pStr = "CUDA_ERROR_ECC_UNCORRECTABLE";
        break;
    case CUDA_ERROR_UNSUPPORTED_LIMIT:
        *pStr = "CUDA_ERROR_UNSUPPORTED_LIMIT";
        break;
    case CUDA_ERROR_CONTEXT_ALREADY_IN_USE:
        *pStr = "CUDA_ERROR_CONTEXT_ALREADY_IN_USE";
        break;
    case CUDA_ERROR_PEER_ACCESS_UNSUPPORTED:
        *pStr = "CUDA_ERROR_PEER_ACCESS_UNSUPPORTED";
        break;
    case CUDA_ERROR_INVALID_PTX:
        *pStr = "CUDA_ERROR_INVALID_PTX";
        break;
    case CUDA_ERROR_INVALID_GRAPHICS_CONTEXT:
        *pStr = "CUDA_ERROR_INVALID_GRAPHICS_CONTEXT";
        break;
    case CUDA_ERROR_NVLINK_UNCORRECTABLE:
        *pStr = "CUDA_ERROR_NVLINK_UNCORRECTABLE";
        break;
    case CUDA_ERROR_JIT_COMPILER_NOT_FOUND:
        *pStr = "CUDA_ERROR_JIT_COMPILER_NOT_FOUND";
        break;
    case CUDA_ERROR_UNSUPPORTED_PTX_VERSION:
        *pStr = "CUDA_ERROR_UNSUPPORTED_PTX_VERSION";
        break;
    case CUDA_ERROR_JIT_COMPILATION_DISABLED:
        *pStr = "CUDA_ERROR_JIT_COMPILATION_DISABLED";
        break;
    case CUDA_ERROR_UNSUPPORTED_EXEC_AFFINITY:
        *pStr = "CUDA_ERROR_UNSUPPORTED_EXEC_AFFINITY";
        break;
    case CUDA_ERROR_UNSUPPORTED_DEVSIDE_SYNC:
        *pStr = "CUDA_ERROR_UNSUPPORTED_DEVSIDE_SYNC";
        break;
    case CUDA_ERROR_INVALID_SOURCE:
        *pStr = "CUDA_ERROR_INVALID_SOURCE";
        break;
    case CUDA_ERROR_FILE_NOT_FOUND:
        *pStr = "CUDA_ERROR_FILE_NOT_FOUND";
        break;
    case CUDA_ERROR_SHARED_OBJECT_SYMBOL_NOT_FOUND:
        *pStr = "CUDA_ERROR_SHARED_OBJECT_SYMBOL_NOT_FOUND";
        break;
    case CUDA_ERROR_SHARED_OBJECT_INIT_FAILED:
        *pStr = "CUDA_ERROR_SHARED_OBJECT_INIT_FAILED";
        break;
    case CUDA_ERROR_OPERATING_SYSTEM:
        *pStr = "CUDA_ERROR_OPERATING_SYSTEM";
        break;
    case CUDA_ERROR_INVALID_HANDLE:
        *pStr = "CUDA_ERROR_INVALID_HANDLE";
        break;
    case CUDA_ERROR_ILLEGAL_STATE:
        *pStr = "CUDA_ERROR_ILLEGAL_STATE";
        break;
    case CUDA_ERROR_LOSSY_QUERY:
        *pStr = "CUDA_ERROR_LOSSY_QUERY";
        break;
    case CUDA_ERROR_NOT_FOUND:
        *pStr = "CUDA_ERROR_NOT_FOUND";
        break;
    case CUDA_ERROR_NOT_READY:
        *pStr = "CUDA_ERROR_NOT_READY";
        break;
    case CUDA_ERROR_ILLEGAL_ADDRESS:
        *pStr = "CUDA_ERROR_ILLEGAL_ADDRESS";
        break;
    case CUDA_ERROR_LAUNCH_OUT_OF_RESOURCES:
        *pStr = "CUDA_ERROR_LAUNCH_OUT_OF_RESOURCES";
        break;
    case CUDA_ERROR_LAUNCH_TIMEOUT:
        *pStr = "CUDA_ERROR_LAUNCH_TIMEOUT";
        break;
    case CUDA_ERROR_LAUNCH_INCOMPATIBLE_TEXTURING:
        *pStr = "CUDA_ERROR_LAUNCH_INCOMPATIBLE_TEXTURING";
        break;
    case CUDA_ERROR_PEER_ACCESS_ALREADY_ENABLED:
        *pStr = "CUDA_ERROR_PEER_ACCESS_ALREADY_ENABLED";
        break;
    case CUDA_ERROR_PEER_ACCESS_NOT_ENABLED:
        *pStr = "CUDA_ERROR_PEER_ACCESS_NOT_ENABLED";
        break;
    case CUDA_ERROR_PRIMARY_CONTEXT_ACTIVE:
        *pStr = "CUDA_ERROR_PRIMARY_CONTEXT_ACTIVE";
        break;
    case CUDA_ERROR_CONTEXT_IS_DESTROYED:
        *pStr = "CUDA_ERROR_CONTEXT_IS_DESTROYED";
        break;
    case CUDA_ERROR_ASSERT:
        *pStr = "CUDA_ERROR_ASSERT";
        break;
    case CUDA_ERROR_TOO_MANY_PEERS:
        *pStr = "CUDA_ERROR_TOO_MANY_PEERS";
        break;
    case CUDA_ERROR_HOST_MEMORY_ALREADY_REGISTERED:
        *pStr = "CUDA_ERROR_HOST_MEMORY_ALREADY_REGISTERED";
        break;
    case CUDA_ERROR_HOST_MEMORY_NOT_REGISTERED:
        *pStr = "CUDA_ERROR_HOST_MEMORY_NOT_REGISTERED";
        break;
    case CUDA_ERROR_HARDWARE_STACK_ERROR:
        *pStr = "CUDA_ERROR_HARDWARE_STACK_ERROR";
        break;
    case CUDA_ERROR_ILLEGAL_INSTRUCTION:
        *pStr = "CUDA_ERROR_ILLEGAL_INSTRUCTION";
        break;
    case CUDA_ERROR_MISALIGNED_ADDRESS:
        *pStr = "CUDA_ERROR_MISALIGNED_ADDRESS";
        break;
    case CUDA_ERROR_INVALID_ADDRESS_SPACE:
        *pStr = "CUDA_ERROR_INVALID_ADDRESS_SPACE";
        break;
    case CUDA_ERROR_INVALID_PC:
        *pStr = "CUDA_ERROR_INVALID_PC";
        break;
    case CUDA_ERROR_LAUNCH_FAILED:
        *pStr = "CUDA_ERROR_LAUNCH_FAILED";
        break;
    case CUDA_ERROR_COOPERATIVE_LAUNCH_TOO_LARGE:
        *pStr = "CUDA_ERROR_COOPERATIVE_LAUNCH_TOO_LARGE";
        break;
    case CUDA_ERROR_NOT_PERMITTED:
        *pStr = "CUDA_ERROR_NOT_PERMITTED";
        break;
    case CUDA_ERROR_NOT_SUPPORTED:
        *pStr = "CUDA_ERROR_NOT_SUPPORTED";
        break;
    case CUDA_ERROR_SYSTEM_NOT_READY:
        *pStr = "CUDA_ERROR_SYSTEM_NOT_READY";
        break;
    case CUDA_ERROR_SYSTEM_DRIVER_MISMATCH:
        *pStr = "CUDA_ERROR_SYSTEM_DRIVER_MISMATCH";
        break;
    case CUDA_ERROR_COMPAT_NOT_SUPPORTED_ON_DEVICE:
        *pStr = "CUDA_ERROR_COMPAT_NOT_SUPPORTED_ON_DEVICE";
        break;
    case CUDA_ERROR_MPS_CONNECTION_FAILED:
        *pStr = "CUDA_ERROR_MPS_CONNECTION_FAILED";
        break;
    case CUDA_ERROR_MPS_RPC_FAILURE:
        *pStr = "CUDA_ERROR_MPS_RPC_FAILURE";
        break;
    case CUDA_ERROR_MPS_SERVER_NOT_READY:
        *pStr = "CUDA_ERROR_MPS_SERVER_NOT_READY";
        break;
    case CUDA_ERROR_MPS_MAX_CLIENTS_REACHED:
        *pStr = "CUDA_ERROR_MPS_MAX_CLIENTS_REACHED";
        break;
    case CUDA_ERROR_MPS_MAX_CONNECTIONS_REACHED:
        *pStr = "CUDA_ERROR_MPS_MAX_CONNECTIONS_REACHED";
        break;
    case CUDA_ERROR_MPS_CLIENT_TERMINATED:
        *pStr = "CUDA_ERROR_MPS_CLIENT_TERMINATED";
        break;
    case CUDA_ERROR_CDP_NOT_SUPPORTED:
        *pStr = "CUDA_ERROR_CDP_NOT_SUPPORTED";
        break;
    case CUDA_ERROR_CDP_VERSION_MISMATCH:
        *pStr = "CUDA_ERROR_CDP_VERSION_MISMATCH";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_UNSUPPORTED:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_UNSUPPORTED";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_INVALIDATED:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_INVALIDATED";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_MERGE:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_MERGE";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_UNMATCHED:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_UNMATCHED";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_UNJOINED:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_UNJOINED";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_ISOLATION:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_ISOLATION";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_IMPLICIT:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_IMPLICIT";
        break;
    case CUDA_ERROR_CAPTURED_EVENT:
        *pStr = "CUDA_ERROR_CAPTURED_EVENT";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_WRONG_THREAD:
        *pStr = "CUDA_ERROR_STREAM_CAPTURE_WRONG_THREAD";
        break;
    case CUDA_ERROR_TIMEOUT:
        *pStr = "CUDA_ERROR_TIMEOUT";
        break;
    case CUDA_ERROR_GRAPH_EXEC_UPDATE_FAILURE:
        *pStr = "CUDA_ERROR_GRAPH_EXEC_UPDATE_FAILURE";
        break;
    case CUDA_ERROR_EXTERNAL_DEVICE:
        *pStr = "CUDA_ERROR_EXTERNAL_DEVICE";
        break;
    case CUDA_ERROR_INVALID_CLUSTER_SIZE:
        *pStr = "CUDA_ERROR_INVALID_CLUSTER_SIZE";
        break;
    case CUDA_ERROR_FUNCTION_NOT_LOADED:
        *pStr = "CUDA_ERROR_FUNCTION_NOT_LOADED";
        break;
    case CUDA_ERROR_INVALID_RESOURCE_TYPE:
        *pStr = "CUDA_ERROR_INVALID_RESOURCE_TYPE";
        break;
    case CUDA_ERROR_INVALID_RESOURCE_CONFIGURATION:
        *pStr = "CUDA_ERROR_INVALID_RESOURCE_CONFIGURATION";
        break;
    case CUDA_ERROR_UNKNOWN:
        *pStr = "CUDA_ERROR_UNKNOWN";
        break;
    default:
        *pStr = NULL; // Set to NULL if the error code is not recognized
        return CUDA_ERROR_INVALID_VALUE;
    }

    return CUDA_SUCCESS;
}

/**
 * \brief Gets the string description of an error code
 *
 * Sets \p *pStr to the address of a NULL-terminated string description
 * of the error code \p error.
 * If the error code is not recognized, ::CUDA_ERROR_INVALID_VALUE
 * will be returned and \p *pStr will be set to the NULL address.
 *
 * \param error - Error code to convert to string
 * \param pStr - Address of the string pointer.
 *
 * \return
 * ::CUDA_SUCCESS,
 * ::CUDA_ERROR_INVALID_VALUE
 *
 * \sa
 * ::CUresult,
 * ::cudaGetErrorString
 */
CUresult cuGetErrorString(CUresult error, const char **pStr)
{
    if (pStr == NULL)
    {
        return CUDA_ERROR_INVALID_VALUE;
    }

    switch (error)
    {
    case CUDA_SUCCESS:
        *pStr = "no error";
        break;
    case CUDA_ERROR_INVALID_VALUE:
        *pStr = "invalid argument";
        break;
    case CUDA_ERROR_OUT_OF_MEMORY:
        *pStr = "out of memory";
        break;
    case CUDA_ERROR_NOT_INITIALIZED:
        *pStr = "initialization error";
        break;
    case CUDA_ERROR_DEINITIALIZED:
        *pStr = "driver shutting down";
        break;
    case CUDA_ERROR_PROFILER_DISABLED:
        *pStr = "profiler disabled while using external profiling tool";
        break;
    case CUDA_ERROR_PROFILER_NOT_INITIALIZED:
        *pStr = "profiler not initialized: call cudaProfilerInitialize()";
        break;
    case CUDA_ERROR_PROFILER_ALREADY_STARTED:
        *pStr = "profiler already started";
        break;
    case CUDA_ERROR_PROFILER_ALREADY_STOPPED:
        *pStr = "profiler already stopped";
        break;
    case CUDA_ERROR_STUB_LIBRARY:
        *pStr = "CUDA driver is a stub library";
        break;
    case CUDA_ERROR_DEVICE_UNAVAILABLE:
        *pStr = "CUDA-capable device(s) is/are busy or unavailable";
        break;
    case CUDA_ERROR_NO_DEVICE:
        *pStr = "no CUDA-capable device is detected";
        break;
    case CUDA_ERROR_INVALID_DEVICE:
        *pStr = "invalid device ordinal";
        break;
    case CUDA_ERROR_DEVICE_NOT_LICENSED:
        *pStr = "device doesn't have valid Grid license";
        break;
    case CUDA_ERROR_INVALID_IMAGE:
        *pStr = "device kernel image is invalid";
        break;
    case CUDA_ERROR_INVALID_CONTEXT:
        *pStr = "invalid device context";
        break;
    case CUDA_ERROR_CONTEXT_ALREADY_CURRENT:
        *pStr = "context already current";
        break;
    case CUDA_ERROR_MAP_FAILED:
        *pStr = "mapping of buffer object failed";
        break;
    case CUDA_ERROR_UNMAP_FAILED:
        *pStr = "unmapping of buffer object failed";
        break;
    case CUDA_ERROR_ARRAY_IS_MAPPED:
        *pStr = "array is mapped";
        break;
    case CUDA_ERROR_ALREADY_MAPPED:
        *pStr = "resource already mapped";
        break;
    case CUDA_ERROR_NO_BINARY_FOR_GPU:
        *pStr = "no kernel image is available for execution on the device";
        break;
    case CUDA_ERROR_ALREADY_ACQUIRED:
        *pStr = "resource already acquired";
        break;
    case CUDA_ERROR_NOT_MAPPED:
        *pStr = "resource not mapped";
        break;
    case CUDA_ERROR_NOT_MAPPED_AS_ARRAY:
        *pStr = "resource not mapped as array";
        break;
    case CUDA_ERROR_NOT_MAPPED_AS_POINTER:
        *pStr = "resource not mapped as pointer";
        break;
    case CUDA_ERROR_ECC_UNCORRECTABLE:
        *pStr = "uncorrectable ECC error encountered";
        break;
    case CUDA_ERROR_UNSUPPORTED_LIMIT:
        *pStr = "limit is not supported on this architecture";
        break;
    case CUDA_ERROR_CONTEXT_ALREADY_IN_USE:
        *pStr = "exclusive-thread device already in use by a different thread";
        break;
    case CUDA_ERROR_PEER_ACCESS_UNSUPPORTED:
        *pStr = "peer access is not supported between these two devices";
        break;
    case CUDA_ERROR_INVALID_PTX:
        *pStr = "a PTX JIT compilation failed";
        break;
    case CUDA_ERROR_INVALID_GRAPHICS_CONTEXT:
        *pStr = "invalid OpenGL or DirectX context";
        break;
    case CUDA_ERROR_NVLINK_UNCORRECTABLE:
        *pStr = "uncorrectable NVLink error detected during the execution";
        break;
    case CUDA_ERROR_JIT_COMPILER_NOT_FOUND:
        *pStr = "PTX JIT compiler library not found";
        break;
    case CUDA_ERROR_UNSUPPORTED_PTX_VERSION:
        *pStr = "the provided PTX was compiled with an unsupported toolchain.";
        break;
    case CUDA_ERROR_JIT_COMPILATION_DISABLED:
        *pStr = "PTX JIT compilation was disabled";
        break;
    case CUDA_ERROR_UNSUPPORTED_EXEC_AFFINITY:
        *pStr = "the provided execution affinity is not supported";
        break;
    case CUDA_ERROR_UNSUPPORTED_DEVSIDE_SYNC:
        *pStr = "the provided PTX contains unsupported call to cudaDeviceSynchronize";
        break;
    case CUDA_ERROR_INVALID_SOURCE:
        *pStr = "device kernel image is invalid";
        break;
    case CUDA_ERROR_FILE_NOT_FOUND:
        *pStr = "file not found";
        break;
    case CUDA_ERROR_SHARED_OBJECT_SYMBOL_NOT_FOUND:
        *pStr = "shared object symbol not found";
        break;
    case CUDA_ERROR_SHARED_OBJECT_INIT_FAILED:
        *pStr = "shared object initialization failed";
        break;
    case CUDA_ERROR_OPERATING_SYSTEM:
        *pStr = "OS call failed or operation not supported on this OS";
        break;
    case CUDA_ERROR_INVALID_HANDLE:
        *pStr = "invalid resource handle";
        break;
    case CUDA_ERROR_ILLEGAL_STATE:
        *pStr = "the operation cannot be performed in the present state";
        break;
    case CUDA_ERROR_LOSSY_QUERY:
        *pStr = "attempted introspection would be semantically lossy";
        break;
    case CUDA_ERROR_NOT_FOUND:
        *pStr = "named symbol not found";
        break;
    case CUDA_ERROR_NOT_READY:
        *pStr = "device not ready";
        break;
    case CUDA_ERROR_ILLEGAL_ADDRESS:
        *pStr = "an illegal memory access was encountered";
        break;
    case CUDA_ERROR_LAUNCH_OUT_OF_RESOURCES:
        *pStr = "too many resources requested for launch";
        break;
    case CUDA_ERROR_LAUNCH_TIMEOUT:
        *pStr = "the launch timed out and was terminated";
        break;
    case CUDA_ERROR_LAUNCH_INCOMPATIBLE_TEXTURING:
        *pStr = "launch uses incompatible texturing mode";
        break;
    case CUDA_ERROR_PEER_ACCESS_ALREADY_ENABLED:
        *pStr = "peer access is already enabled";
        break;
    case CUDA_ERROR_PEER_ACCESS_NOT_ENABLED:
        *pStr = "peer access has not been enabled";
        break;
    case CUDA_ERROR_PRIMARY_CONTEXT_ACTIVE:
        *pStr = "cannot set while device is active in this process";
        break;
    case CUDA_ERROR_CONTEXT_IS_DESTROYED:
        *pStr = "context is destroyed";
        break;
    case CUDA_ERROR_ASSERT:
        *pStr = "device-side assert triggered";
        break;
    case CUDA_ERROR_TOO_MANY_PEERS:
        *pStr = "peer mapping resources exhausted";
        break;
    case CUDA_ERROR_HOST_MEMORY_ALREADY_REGISTERED:
        *pStr = "part or all of the requested memory range is already mapped";
        break;
    case CUDA_ERROR_HOST_MEMORY_NOT_REGISTERED:
        *pStr = "pointer does not correspond to a registered memory region";
        break;
    case CUDA_ERROR_HARDWARE_STACK_ERROR:
        *pStr = "hardware stack error";
        break;
    case CUDA_ERROR_ILLEGAL_INSTRUCTION:
        *pStr = "an illegal instruction was encountered";
        break;
    case CUDA_ERROR_MISALIGNED_ADDRESS:
        *pStr = "misaligned address";
        break;
    case CUDA_ERROR_INVALID_ADDRESS_SPACE:
        *pStr = "operation not supported on global/shared address space";
        break;
    case CUDA_ERROR_INVALID_PC:
        *pStr = "invalid program counter";
        break;
    case CUDA_ERROR_LAUNCH_FAILED:
        *pStr = "unspecified launch failure";
        break;
    case CUDA_ERROR_COOPERATIVE_LAUNCH_TOO_LARGE:
        *pStr = "too many blocks in cooperative launch";
        break;
    case CUDA_ERROR_NOT_PERMITTED:
        *pStr = "operation not permitted";
        break;
    case CUDA_ERROR_NOT_SUPPORTED:
        *pStr = "operation not supported";
        break;
    case CUDA_ERROR_SYSTEM_NOT_READY:
        *pStr = "system not yet initialized";
        break;
    case CUDA_ERROR_SYSTEM_DRIVER_MISMATCH:
        *pStr = "system has unsupported display driver / cuda driver combination";
        break;
    case CUDA_ERROR_COMPAT_NOT_SUPPORTED_ON_DEVICE:
        *pStr = "forward compatibility was attempted on non supported HW";
        break;
    case CUDA_ERROR_MPS_CONNECTION_FAILED:
        *pStr = "MPS client failed to connect to the MPS control daemon or the MPS server";
        break;
    case CUDA_ERROR_MPS_RPC_FAILURE:
        *pStr = "the remote procedural call between the MPS server and the MPS client failed";
        break;
    case CUDA_ERROR_MPS_SERVER_NOT_READY:
        *pStr = "MPS server is not ready to accept new MPS client requests";
        break;
    case CUDA_ERROR_MPS_MAX_CLIENTS_REACHED:
        *pStr = "the hardware resources required to create MPS client have been exhausted";
        break;
    case CUDA_ERROR_MPS_MAX_CONNECTIONS_REACHED:
        *pStr = "the hardware resources required to support device connections have been exhausted";
        break;
    case CUDA_ERROR_MPS_CLIENT_TERMINATED:
        *pStr = "the MPS client has been terminated by the server";
        break;
    case CUDA_ERROR_CDP_NOT_SUPPORTED:
        *pStr = "is using CUDA Dynamic Parallelism, but the current configuration, like MPS, does not support it";
        break;
    case CUDA_ERROR_CDP_VERSION_MISMATCH:
        *pStr = "unsupported interaction between different versions of CUDA Dynamic Parallelism";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_UNSUPPORTED:
        *pStr = "operation not permitted when stream is capturing";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_INVALIDATED:
        *pStr = "operation failed due to a previous error during capture";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_MERGE:
        *pStr = "operation would result in a merge of separate capture sequences";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_UNMATCHED:
        *pStr = "capture was not ended in the same stream as it began";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_UNJOINED:
        *pStr = "capturing stream has unjoined work";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_ISOLATION:
        *pStr = "dependency created on uncaptured work in another stream";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_IMPLICIT:
        *pStr = "operation would make the legacy stream depend on a capturing blocking stream";
        break;
    case CUDA_ERROR_CAPTURED_EVENT:
        *pStr = "operation not permitted on an event last recorded in a capturing stream";
        break;
    case CUDA_ERROR_STREAM_CAPTURE_WRONG_THREAD:
        *pStr = "attempt to terminate a thread-local capture sequence from another thread";
        break;
    case CUDA_ERROR_TIMEOUT:
        *pStr = "wait operation timed out";
        break;
    case CUDA_ERROR_GRAPH_EXEC_UPDATE_FAILURE:
        *pStr = "the graph update was not performed because it included changes which violated constraints specific to instantiated graph update";
        break;
    case CUDA_ERROR_EXTERNAL_DEVICE:
        *pStr = "an async error has occured in external entity outside of CUDA";
        break;
    case CUDA_ERROR_INVALID_CLUSTER_SIZE:
        *pStr = "a kernel launch error has occurred due to cluster misconfiguration";
        break;
    case CUDA_ERROR_FUNCTION_NOT_LOADED:
        *pStr = "the function handle is not loaded when calling an API that requires a loaded function";
        break;
    case CUDA_ERROR_INVALID_RESOURCE_TYPE:
        *pStr = "one or more resources passed in are not valid resource types for the operation";
        break;
    case CUDA_ERROR_INVALID_RESOURCE_CONFIGURATION:
        *pStr = "one or more resources are insufficient or non-applicable for the operation";
        break;
    case CUDA_ERROR_UNKNOWN:
        *pStr = "unknown error";
        break;
    default:
        *pStr = NULL; // Set to NULL if the error code is not recognized
        return CUDA_ERROR_INVALID_VALUE;
    }

    return CUDA_SUCCESS;
}