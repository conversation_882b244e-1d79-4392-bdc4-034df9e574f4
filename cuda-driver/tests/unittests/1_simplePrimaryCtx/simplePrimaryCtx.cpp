#include <cstring>
#include <cuda.h>
#include <iostream>
#include <stdio.h>
#include <string.h>

// includes, project
#include <helper_cuda_drvapi.h>
#include <helper_functions.h>

// Host code
int main(int argc, char **argv)
{
    CUresult result = CUDA_SUCCESS;
    int      count  = 0;

    printf("Starting simplePrimaryCtx test...\n");

    // Initialize the CUDA driver API
    result = cuInit(0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuInit failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    // Query CUDA device count
    result = cuDeviceGetCount(&count);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGetCount failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Found %d CUDA devices.\n", count);

    // Check if any CUDA devices are available
    if (count == 0)
    {
        printf("No CUDA devices found.\n");
        return EXIT_FAILURE;
    }

    // Get the first CUDA device
    CUdevice device;
    result = cuDeviceGet(&device, 0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device 0.\n");

     // Query primary context state (flags and active status)
    unsigned int flags;
    int active;
    result = cuDevicePrimaryCtxGetState(device, &flags, &active);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxGetState failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Primary ctx flags: 0x%x, Active: %d\n", flags, active);

    if (active)
    {
        printf("Primary context is already active!!!!!\n");
        return EXIT_FAILURE;
    }

    result = cuDevicePrimaryCtxSetFlags(device, CU_CTX_SCHED_BLOCKING_SYNC);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxSetFlags failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    // Create primary context
    CUcontext primaryCtx;
    result = cuDevicePrimaryCtxRetain(&primaryCtx, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxRetain failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Primary context retained successfully.\n");


    // Set the primary context as current
    printf("Setting primary context as current...\n");
    result = cuCtxSetCurrent(primaryCtx);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxSetCurrent failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    // Reset the primary context
    result = cuDevicePrimaryCtxReset(device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxReset failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Primary context reset successfully.\n");

    printf("Primary context state after reset:\n");
    result = cuDevicePrimaryCtxGetState(device, &flags, &active);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxGetState failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Primary ctx flags: 0x%x, Active: %d\n", flags, active);
    if (active)
    {
        printf("Primary context is active after reset!!!!!\n");
        return EXIT_FAILURE;
    }

    printf("Retaining primary context again...\n");
    result = cuDevicePrimaryCtxRetain(&primaryCtx, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxRetain failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Primary context retained successfully.\n");

    printf("Primary context state after retain:\n");
    result = cuDevicePrimaryCtxGetState(device, &flags, &active);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxGetState failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Primary ctx flags: 0x%x, Active: %d\n", flags, active);

    if (!active)
    {
        printf("Primary context is not active after retain!!!!!\n");
        return EXIT_FAILURE;
    }

    // Release the primary context
    printf("Releasing primary context...\n");
    result = cuDevicePrimaryCtxRelease(device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDevicePrimaryCtxRelease failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    printf("Test PASSED\n");
    printf("Done\n");

    return EXIT_SUCCESS;
}