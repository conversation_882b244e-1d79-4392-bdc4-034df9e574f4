#include <cstring>
#include <cuda.h>
#include <iostream>
#include <stdio.h>
#include <string.h>

// includes, project
#include <helper_cuda_drvapi.h>
#include <helper_functions.h>

// Host code
int main(int argc, char **argv)
{
    CUresult result = CUDA_SUCCESS;
    int      count  = 0;

    printf("Starting simpleCudaVisibleDevices test...\n");

    // Set the CUDA_VISIBLE_DEVICES environment variable
    // This is just for demonstration; in practice, this would be set outside the program.
    setenv("CUDA_VISIBLE_DEVICES", "3,5,7,6,1,0,2,4", 1);
    printf("CUDA_VISIBLE_DEVICES set to: %s\n", getenv("CUDA_VISIBLE_DEVICES"));

    // Initialize the CUDA driver API
    result = cuInit(0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuInit failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    // Query CUDA device count
    result = cuDeviceGetCount(&count);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGetCount failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Found %d CUDA devices.\n", count);

    // Check if any CUDA devices are available
    if (count == 0)
    {
        printf("No CUDA devices found.\n");
        return EXIT_FAILURE;
    }

    // Get the first CUDA device
    CUdevice device;
    result = cuDeviceGet(&device, 0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context0;
    result = cuCtxCreate(&context0, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuDeviceGet(&device, 1);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context1;
    result = cuCtxCreate(&context1, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuDeviceGet(&device, 2);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context2;
    result = cuCtxCreate(&context2, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuDeviceGet(&device, 3);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context3;
    result = cuCtxCreate(&context3, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuDeviceGet(&device, 4);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context4;
    result = cuCtxCreate(&context4, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuDeviceGet(&device, 5);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context5;
    result = cuCtxCreate(&context5, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuDeviceGet(&device, 6);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context6;
    result = cuCtxCreate(&context6, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuDeviceGet(&device, 7);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGet failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    printf("Using CUDA device %d.\n", device);

    CUcontext context7;
    result = cuCtxCreate(&context7, 0, device);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context7);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context6);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context5);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context4);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context3);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context2);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context1);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    printf("Test PASSED\n");
    printf("Done\n");

    return EXIT_SUCCESS;
}