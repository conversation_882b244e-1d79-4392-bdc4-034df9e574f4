#include "../include/cuda.h"

#include <stdio.h>

int main()
{
    CUresult result = CUDA_SUCCESS;

    printf("Starting simpleDrvInit test...\n");

    result = cuInit(0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuInit failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    CUcontext context;
    result = cuCtxCreate(&context, 0, 0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxCreate failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    CUcontext currentContext;
    result = cuCtxGetCurrent(&currentContext);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxGetCurrent failed with error code %d\n", result);
        return EXIT_FAILURE;
    }
    if (currentContext != context)
    {
        printf("cuCtxGetCurrent returned a different context than created\n");
        return EXIT_FAILURE;
    }

    CUcontext popContext;
    result = cuCtxPopCurrent(&popContext);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxPopCurrent failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    if (popContext != context)
    {
        printf("cuCtxPopCurrent returned a different context than created\n");
        return EXIT_FAILURE;
    }

    result = cuCtxDestroy(context);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    printf("Test PASSED\n");
    printf("Done\n");

    return EXIT_SUCCESS;
}