cmake_minimum_required(VERSION 3.12)

# Set the project name
project(simpleDrvInit LANGUAGES C CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(EXECUTABLE_NAME simpleDrvInit)

# Source file
# Add target for simpleDrvInit
add_executable(${EXECUTABLE_NAME} simpleDrvInit.cpp)

# Include directories and libraries
target_include_directories(${EXECUTABLE_NAME} PRIVATE ../../../include)
target_include_directories(${EXECUTABLE_NAME} PRIVATE ../../common)

# Set the output directory for the executable
set_target_properties(${EXECUTABLE_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/tests/unittests/0_${EXECUTABLE_NAME})
target_link_libraries(${EXECUTABLE_NAME} PRIVATE cuda)