cmake_minimum_required(VERSION 3.12)

# Set the project name
project(simpleMemAlloc LANGUAGES C CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

file(RELATIVE_PATH REL_PATH_FROM_ROOT "${CMAKE_SOURCE_DIR}" "${CMAKE_CURRENT_SOURCE_DIR}")
message(STATUS "Relative path from root: ${REL_PATH_FROM_ROOT}")

set(EXECUTABLE_NAME simpleMemAlloc)

# Source file
# Add target for simpleMemAlloc
add_executable(${EXECUTABLE_NAME} simpleMemAlloc.cpp)

# Include directories and libraries
target_include_directories(${EXECUTABLE_NAME} PRIVATE ../../../include)
target_include_directories(${EXECUTABLE_NAME} PRIVATE ../../common)

# Set the output directory for the executable
set_target_properties(${EXECUTABLE_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/${REL_PATH_FROM_ROOT})
target_link_libraries(${EXECUTABLE_NAME} PRIVATE cuda)