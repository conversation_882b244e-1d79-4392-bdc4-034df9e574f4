#include <cstring>
#include <cuda.h>
#include <stdio.h>

// includes, project
#include <helper_cuda_drvapi.h>
#include <helper_functions.h>

// Host code
int main(int argc, char **argv)
{
    CUresult result = CUDA_SUCCESS;

    printf("Starting simpleMemAlloc test...\n");

    // Initialize the CUDA driver API
    result = cuInit(0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuInit failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    int deviceCount = 0;
    result = cuDeviceGetCount(&deviceCount);
    if (result != CUDA_SUCCESS)
    {
        printf("cuDeviceGetCount failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    if (deviceCount == 0)
    {
        printf("No CUDA-capable devices found, exiting...\n");
        return EXIT_FAILURE;
    }

    CUcontext cuContext;
    result = cuCtxCreate(&cuContext, 0, 0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxC<PERSON> failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    CUdeviceptr d_A;
    size_t size = 256 * sizeof(float);
    result = cuMemAlloc(&d_A, size);
    if (result != CUDA_SUCCESS)
    {
        printf("cuMemAlloc failed with error code %d\n", result);
        cuCtxDestroy(cuContext);
        return EXIT_FAILURE;
    }

    float* h_A = nullptr;
    size_t h_size = 256 * sizeof(float);
    result = cuMemAllocHost((void**)&h_A, h_size);
    if (result != CUDA_SUCCESS)
    {
        printf("cuMemAllocHost failed with error code %d\n", result);
        cuMemFree(d_A);
        cuCtxDestroy(cuContext);
        return EXIT_FAILURE;
    }

    // Initialize host memory
    for (int i = 0; i < 256; i++)
    {
        h_A[i] = static_cast<float>(i);
    }

    printf("Print the first 10 elements of the host array:\n");
    for (int i = 0; i < 10; i++)
    {
        printf("%.3f ", h_A[i]);
    }
    printf("\n");

    float* register_A = nullptr;
    register_A = (float*)malloc(h_size);
    if (register_A == nullptr)
    {
        printf("Failed to allocate register memory\n");
        cuMemFreeHost(h_A);
        cuMemFree(d_A);
        cuCtxDestroy(cuContext);
        return EXIT_FAILURE;
    }

    // Register the pageable memory
    result = cuMemHostRegister(register_A, h_size, 0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuMemHostRegister failed with error code %d\n", result);
        free(register_A);
        cuMemFreeHost(h_A);
        cuMemFree(d_A);
        cuCtxDestroy(cuContext);
        return EXIT_FAILURE;
    }

    // Copy host memory to registered memory
    memcpy(register_A, h_A, h_size);

    printf("Print the first 10 elements of the registered array:\n");
    for (int i = 0; i < 10; i++)
    {
        printf("%.3f ", register_A[i]);
    }
    printf("\n");

    // Unregister the pageable memory
    result = cuMemHostUnregister(register_A);
    if (result != CUDA_SUCCESS)
    {
        printf("cuMemHostUnregister failed with error code %d\n", result);
        free(register_A);
        cuMemFreeHost(h_A);
        cuMemFree(d_A);
        cuCtxDestroy(cuContext);
        return EXIT_FAILURE;
    }

    // Free the allocated pageable memory
    free(register_A);

    // Free host memory
    result = cuMemFreeHost(h_A);
    if (result != CUDA_SUCCESS)
    {
        printf("cuMemFreeHost failed with error code %d\n", result);
        cuMemFree(d_A);
        cuCtxDestroy(cuContext);
        return EXIT_FAILURE;
    }

    // Free device memory
    result = cuMemFree(d_A);
    if (result != CUDA_SUCCESS)
    {
        printf("cuMemFree failed with error code %d\n", result);
        cuCtxDestroy(cuContext);
        return EXIT_FAILURE;
    }

    // Destroy the CUDA context
    result = cuCtxDestroy(cuContext);
    if (result != CUDA_SUCCESS)
    {
        printf("cuCtxDestroy failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    printf("Test PASSED\n");
    printf("Done\n");

    return EXIT_SUCCESS;
}