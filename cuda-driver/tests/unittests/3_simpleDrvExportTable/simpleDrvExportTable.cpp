#include <cstring>
#include <cuda.h>
#include <stdio.h>

// includes, project
#include <helper_cuda_drvapi.h>
#include <helper_functions.h>

#include "cuda_driver_export_table.h"

// Host code
int main(int argc, char **argv)
{
    CUresult result = CUDA_SUCCESS;

    printf("Starting simpleDrvExportTable test...\n");

    // Initialize the CUDA driver API
    result = cuInit(0);
    if (result != CUDA_SUCCESS)
    {
        printf("cuInit failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    CUdriverExportTable* pExportTable = nullptr;
    const CUuuid cudaDriverFunctionExportTableUuid = {
        .bytes = {'\x00', '\x50', '\x8e', '\xae', '\xa1', '\x63', '\x48', '\xbc',
                  '\xb0', '\xac', '\x25', '\x6c', '\x43', '\x64', '\xc5', '\x6b' } };
    result = cuGetExportTable((const void**)&pExportTable, &cudaDriverFunctionExportTableUuid);
    if (result != CUDA_SUCCESS)
    {
        printf("cuGetExportTable failed with error code %d\n", result);
        return EXIT_FAILURE;
    }

    if (pExportTable == nullptr)
    {
        printf("Export table pointer is null.\n");
        return EXIT_FAILURE;
    }

    if (pExportTable->pfnInit == nullptr)
    {
        printf("Export table function pfnInit is null.\n");
        return EXIT_FAILURE;
    }
    else
    {
        printf("Export table function cuInit: %p\n", (void*)pExportTable->pfnInit);
    }

    printf("Test PASSED\n");
    printf("Done\n");

    return EXIT_SUCCESS;
}