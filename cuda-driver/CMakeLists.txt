cmake_minimum_required(VERSION 3.12)

# Set the project name and program languages.
project(CudaDriver LANGUAGES C)

# Set the static and dynamic libraries path.
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
# Set the output directory for executables
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Set the cache variable for the build type.
string(TOUPPER "${CMAKE_BUILD_TYPE}" CAPITAL_CMAKE_BUILD_TYPE)
message(STATUS "CMAKE_BUILD_TYPE: ${CMAKE_BUILD_TYPE}")

if(CAPITAL_CMAKE_BUILD_TYPE STREQUAL "DEBUGWITHASAN")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -O0 -fsanitize=address -g -fno-omit-frame-pointer")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O0 -fsanitize=address -g -fno-omit-frame-pointer")
endif()

# Set default value depending on build type (Debug: ON, others: OFF)
if(CAPITAL_CMAKE_BUILD_TYPE MATCHES "DEBUG" OR
   CAPITAL_CMAKE_BUILD_TYPE MATCHES "DEBUGWITHASAN" OR
   CAPITAL_CMAKE_BUILD_TYPE MATCHES "RELWITHDEBINFO")
    set(ENABLE_PRINTS_ASSERTS_DEFAULT ON)
    set(ENABLE_DEBUG_BREAK_DEFAULT ON)
else()
    set(ENABLE_PRINTS_ASSERTS_DEFAULT OFF)
    set(ENABLE_DEBUG_BREAK_DEFAULT OFF)
endif()

# This option can be overridden from the command line with -DENABLE_PRINTS_ASSERTS=ON/OFF
option(ENABLE_PRINTS_ASSERTS "Enable prints and asserts in code" ${ENABLE_PRINTS_ASSERTS_DEFAULT})
if (ENABLE_PRINTS_ASSERTS)
    add_definitions(-DENABLE_PRINTS_ASSERTS=1)
else()
    add_definitions(-DENABLE_PRINTS_ASSERTS=0)
endif()
message(STATUS "ENABLE_PRINTS_ASSERTS:${ENABLE_PRINTS_ASSERTS}")

option(ENABLE_DEBUG_BREAK "Enable debug break" ${ENABLE_DEBUG_BREAK_DEFAULT})
if (ENABLE_DEBUG_BREAK)
    add_definitions(-DENABLE_DEBUG_BREAK=1)
else()
    add_definitions(-DENABLE_DEBUG_BREAK=0)
endif()
message(STATUS "ENABLE_DEBUG_BREAK:${ENABLE_DEBUG_BREAK}")

if (NOT ENABLE_THUNK_LAYER)
    # If ENABLE_THUNK_LAYER is not defined, set it to kfe by default.
    set(ENABLE_THUNK_LAYER kfe)
endif()

if(ENABLE_THUNK_LAYER STREQUAL dummy)
    message(STATUS "Thunk layer select:dummy")
elseif(ENABLE_THUNK_LAYER STREQUAL kfe)
    message(STATUS "Thunk layer select:kernel-firmware-emulator")
elseif(ENABLE_THUNK_LAYER STREQUAL kmd)
    message(STATUS "Thunk layer select:kernel-mode-driver")
endif()

# Add the path of the public including files.
include_directories(${CMAKE_SOURCE_DIR}/include)

# Enable -fPIC for all targets.
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# Add specific compiler flags based on the compiler being used.
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    # For GCC and Clang compilers, enable warnings as errors.
    add_compile_options(-Wall -Wextra -Wpedantic -Werror)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    # For MSVC, enable warnings as errors.
    add_compile_options(/W4 /WX)
    # Disable specific warnings that are not relevant or too noisy.
    add_compile_options(
        /wd4100 /wd4127 /wd4201 /wd4244 /wd4267 /wd4275 /wd4305
        /wd4310 /wd4355 /wd4505 /wd4514 /wd4577 /wd4623 /wd4625
        /wd4668 /wd4710 /wd4711 /wd4820 /wd5026 /wd5031 /wd5032)
endif()

# Add the sub-directories
add_subdirectory(src/pal)  # Build the static module (libutil.a libthunk.a libpal.a)
add_subdirectory(src)      # Build dynamic library API layer (libcuda.so)

# Add the tests directory
add_subdirectory(tests)

# Install configuration
# Set the default install prefix if not specified
if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
    set(CMAKE_INSTALL_PREFIX "${CMAKE_SOURCE_DIR}/install" CACHE PATH "Installation directory" FORCE)
endif()

# Print the installation prefix
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")