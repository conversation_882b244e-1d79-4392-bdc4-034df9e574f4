# GPGPU-PerfX

**The GPGPU-PerfX software stack is a comprehensive AI computing power software solution that includes models(PerfPotion), drivers(cuda-driver), runtime(cuda-runtime), and a range of other components.**

## Build

### Bash build
```
python3 ./build_perfX_repo.sh rel|dbg|clean

# build with install option. By default, the repo will install the '<GPGPU-PerfX>/perfX_sdk'
python3 ./build_perfX_repo.sh rel|dbg|clean install

# build with extra arguments of modules
python3 ./build_perfX_repo.sh rel|dbg|clean install --<submodule_name> <your_cmake_option_lower_name>=<option_value>
# e.g.
python3 ./build_perfX_repo.sh rel|dbg|clean install --cuda-runtime enable_device_kernel_launch=on --cuda-driver enable_debug_break=on enable_thunk_layer=perfpotion --PerfPotion cmake_build_type=Debug

```