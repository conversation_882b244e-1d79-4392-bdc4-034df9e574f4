import subprocess
import sys
import os

def build_component(component, build_type, install=False, install_dir=None, extra_args=None):
    comp_dir = os.path.join(os.getcwd(), component)
    script_path = os.path.join(comp_dir, 'build.sh')
    print(f"script_path: {script_path}")
    if not os.path.isfile(script_path):
        print(f"Error: {script_path} not found.")
        return False

    # For clean, do not pass 'install' argument
    if build_type == 'clean':
        cmd = ['./build.sh', build_type]
    else:
        cmd = ['./build.sh', build_type]
        if install:
            cmd.append('install')
        if extra_args and component in extra_args:
            cmd.extend(extra_args[component])

    env = os.environ.copy()
    if install_dir:
        env['PERFX_SDK_PATH'] = install_dir
    print(f"Building {component} with: {' '.join(cmd)} (cwd={comp_dir})")

    try:
        subprocess.check_call(cmd, env=env, cwd=comp_dir)
        if build_type == 'clean':
            print(f"{component} clean succeeded.\n")
        else:
            print(f"{component} build succeeded.\n")
        return True
    except subprocess.CalledProcessError:
        if build_type == 'clean':
            print(f"{component} clean failed.\n")
        else:
            print(f"{component} build failed.\n")
        return False

def parse_component_args(argv, components):
    extra_args = {comp: [] for comp in components}
    i = 0
    while i < len(argv):
        if argv[i].startswith('--'):
            comp = argv[i][2:]
            if comp in extra_args:
                i += 1
                while i < len(argv) and not argv[i].startswith('--'):
                    keyval = argv[i].split('=', 1)
                    if len(keyval) == 2:
                        key, val = keyval
                        val_str = ""
                        if val.lower() == 'true' or val.lower() == 'on':
                            val_str = 'ON'
                        elif val.lower() == 'false' or val.lower() == 'off':
                            val_str = 'OFF'
                        else:
                            val_str = val
                        cmake_arg = f"-D{key.upper()}={val_str}"
                        extra_args[comp].append(cmake_arg)
                    i += 1
            else:
                i += 1
        else:
            i += 1
    return extra_args

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python3 build_perfX_repo.py {rel|dbg|dbg_asan|clean} [install] [--runtime enable_device_kernel_launch]")
        sys.exit(1)

    build_type = sys.argv[1]
    install = len(sys.argv) > 2 and sys.argv[2] == 'install'
    # Use environment variables first; otherwise, use the local perfX_sdk.
    install_dir = os.environ.get('PERFX_SDK_PATH', os.path.join(os.getcwd(), 'perfX_sdk'))
    components = ['cuda-runtime', 'cuda-driver', 'PerfPotion', 'kernel-firmware-emulator']

    # Parse extra arguments for components if provided
    arg_start = 3 if install else 2
    extra_args = parse_component_args(sys.argv[arg_start:], components)

    for comp in components:
        if not build_component(comp, build_type, install, install_dir, extra_args):
            sys.exit(1)

    if build_type == 'clean':
        print("All components cleaned successfully.")
    else:
        print("All components built successfully.")