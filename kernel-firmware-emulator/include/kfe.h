#ifndef KFE_H_
#define KFE_H_

#include <stddef.h>
#include <stdint.h>

#ifdef _WIN32
#define KFEAPI __stdcall
#else
#define KFEAPI
#endif

#ifdef __cplusplus
extern "C" {
#endif

// The version is defined as (1000 major + 10 minor).
#define KFE_API_VERSION ((1 * 1000) + (0 * 10))

typedef void* kfeDevice;
typedef void* kfeContext;
typedef void* kfePhysicalMemoryObject;
typedef void* kfePhysicalMemoryObjectDeviceMapped;
typedef void* kfePhysicalMemoryObjectHostMapped;
typedef void* kfePinnedMemoryObject;
typedef void* kfePinnedMemoryObjectDeviceMapped;
typedef void* kfePageableMemoryHostRegistered;
typedef void* kfePageableMemoryHostUnregistered;
typedef void* kfeUserModeQueue;
typedef void* kfeFence;

typedef enum kfeResult_enum
{
    /**
     * The API call returned with no errors.
     */
    KFE_SUCCESS                              = 0,

    /**
     * This indicates that one or more of the parameters passed to the API call
     * is not within an acceptable range of values.
     */
    KFE_ERROR_INVALID_VALUE                  = 1,

    /**
     * The API call failed because it was unable to allocate enough memory to
     * perform the requested operation.
     */
    KFE_ERROR_OUT_OF_MEMORY                  = 2,

    /**
     * This indicates that the Perf Model has not been initialized
     * or that initialization has failed.
     */
    KFE_ERROR_NOT_INITIALIZED                = 3,

    /**
     * This indicates that the Perf Model is in the process of shutting down.
     */
    KFE_ERROR_DEINITIALIZED                  = 4,

    /**
     * This indicates that no CUDA-capable devices were detected by the installed
     * CUDA driver.
     */
    KFE_ERROR_NO_DEVICE                      = 100,

    /**
     * This indicates that the device ordinal supplied by the user does not
     * correspond to a valid CUDA device or that the action requested is
     * invalid for the specified device.
     */
    KFE_ERROR_INVALID_DEVICE                 = 101,

    /**
     * This indicates that a map or register operation has failed.
     */
    KFE_ERROR_MAP_FAILED                     = 205,

    /**
     * This indicates that an unmap or unregister operation has failed.
     */
    KFE_ERROR_UNMAP_FAILED                   = 206,

    /**
     * This indicates that the specified array is currently mapped and thus
     * cannot be destroyed.
     */
    KFE_ERROR_ARRAY_IS_MAPPED                = 207,

    /**
     * This indicates that the resource is already mapped.
     */
    KFE_ERROR_ALREADY_MAPPED                 = 208,

    /**
     * This indicates that a resource is not mapped.
     */
    KFE_ERROR_NOT_MAPPED                     = 211,

    /**
     * This indicates that a mapped resource is not available for access as an
     * array.
     */
    KFE_ERROR_NOT_MAPPED_AS_ARRAY            = 212,

    /**
     * This indicates that a mapped resource is not available for access as a
     * pointer.
     */
    KFE_ERROR_NOT_MAPPED_AS_POINTER          = 213,

    /**
     * This indicates that an OS call failed.
     */
    KFE_ERROR_OPERATING_SYSTEM               = 304,

    /**
     * This indicates that asynchronous operations issued previously have not
     * completed yet. This result is not actually an error, but must be indicated
     * differently than ::KFE_SUCCESS (which indicates completion). Calls that
     * may return this value include ::kfeFenceQuery().
     */
    KFE_ERROR_NOT_READY                      = 600,

    /**
     * The API call failed because the requested operation is not supported by
     * the current device or platform.
     */
    KFE_ERROR_NOT_SUPPORTED                  = 801,

    /**
     * This error indicates that the timeout specified for the wait operation has lapsed.
     */
    KFE_ERROR_TIMEOUT                        = 909,

    /**
     * The API call failed because an unknown internal error occurred.
     */
    KFE_ERROR_UNKNOWN                        = 999,
} kfeResult;

typedef struct kfeDeviceProperties_st
{
    // The name is an ASCII string identifying the device.
    char name[256];
    // The uuid is a 16-byte unique identifier.
    char uuid[16];
    // The totalGlobalMem is the total amount of global memory available on the device in bytes.
    size_t totalGlobalMem;
    // The sharedMemPerBlock is the maximum amount of shared memory available to a thread block in bytes.
    size_t sharedMemPerBlock;
    // The regsPerBlock is the maximum number of 32-bit registers available to a thread block.
    int regsPerBlock;
    // The warpSize is the warp size in threads.
    int warpSize;
    // The memPitch is the maximum pitch in bytes allowed by the memory copy functions that involve memory
    // regions allocated through cudaMallocPitch().
    size_t memPitch;
    // The maxThreadsPerBlock is the maximum number of threads per block.
    int maxThreadsPerBlock;
    // The maxThreadsDim[3] contains the maximum size of each dimension of a block.
    int maxThreadsDim[3];
    // The maxGridSize[3] contains the maximum size of each dimension of a grid.
    int maxGridSize[3];
    // The clockRate is the clock frequency in kilohertz.
    int clockRate;
    // The totalConstMem is the total amount of constant memory available on the device in bytes.
    size_t totalConstMem;
    // The major, minor are the major and minor revision numbers defining the device's compute capability.
    int major;
    int minor;
    // The textureAlignment is the alignment requirement; texture base addresses that are aligned to
    // textureAlignment bytes do not need an offset applied to texture fetches.
    size_t textureAlignment;
    // texturePitchAlignment is the pitch alignment requirement for 2D texture references that
    // are bound to pitched memory.
    size_t texturePitchAlignment;
    // The deviceOverlap is 1 if the device can concurrently copy memory between host and device
    // while executing a kernel, or 0 if not. Deprecated, use instead asyncEngineCount.
    int deviceOverlap;
    // The multiProcessorCount is the number of multiprocessors on the device.
    int multiProcessorCount;
    // The kernelExecTimeoutEnabled is 1 if there is a run time limit for kernels executed on the
    // device, or 0 if not.
    int kernelExecTimeoutEnabled;
    // The integrated is 1 if the device is an integrated (motherboard) GPU and 0 if it is a discrete
    // (card) component.
    int integrated;
    // canMapHostMemory is 1 if the device can map host memory into the CUDA address space
    // for use with cudaHostAlloc()/cudaHostGetDevicePointer(), or 0 if not.
    int canMapHostMemory;
    // computeMode is the compute mode that the device is currently in. Available modes are as follows:
    // - cudaComputeModeDefault: Default mode - Device is not restricted and multiple
    //   threads can use cudaSetDevice() with this device.
    // - cudaComputeModeProhibited: Compute-prohibited mode - No threads can use cudaSetDevice() with this device.
    // - cudaComputeModeExclusiveProcess: Compute-exclusive-process mode - Many threads in one process will be able
    //   to use cudaSetDevice() with this device.
    int computeMode;
    // The maxTexture1D is the maximum 1D texture size.
    int maxTexture1D;
    // The maxTexture1DMipmap is the maximum 1D mipmapped texture texture size.
    int maxTexture1DMipmap;
    // The maxTexture1DLinear is the maximum 1D texture size for textures bound to linear memory.
    int maxTexture1DLinear;
    // The maxTexture2D[2] contains the maximum 2D texture dimensions.
    int maxTexture2D[2];
    // The maxTexture2DMipmap[2] contains the maximum 2D mipmapped texture dimensions.
    int maxTexture2DMipmap[2];
    // The maxTexture2DLinear[3] contains the maximum 2D texture dimensions for 2D textures bound to pitch linear memory.
    int maxTexture2DLinear[3];
    // The maxTexture2DGather[2] contains the maximum 2D texture dimensions if texture gather operations have to be performed.
    int maxTexture2DGather[2];
    // The maxTexture3D[3] contains the maximum 3D texture dimensions.
    int maxTexture3D[3];
    // The maxTexture3DAlt[3] contains the maximum alternate 3D texture dimensions.
    int maxTexture3DAlt[3];
    // The maxTextureCubemap is the maximum cubemap texture width or height.
    int maxTextureCubemap;
    // The maxTexture1DLayered[2] contains the maximum 1D layered texture dimensions.
    int maxTexture1DLayered[2];
    // The maxTexture2DLayered[3] contains the maximum 2D layered texture dimensions.
    int maxTexture2DLayered[3];
    // The maxTextureCubemapLayered[2] contains the maximum cubemap layered texture dimensions.
    int maxTextureCubemapLayered[2];
    // The maxSurface1D is the maximum 1D surface size.
    int maxSurface1D;
    // The maxSurface2D[2] contains the maximum 2D surface dimensions.
    int maxSurface2D[2];
    // The maxSurface3D[3] contains the maximum 3D surface dimensions.
    int maxSurface3D[3];
    // The maxSurface1DLayered[2] contains the maximum 1D layered surface dimensions.
    int maxSurface1DLayered[2];
    // The maxSurface2DLayered[3] contains the maximum 2D layered surface dimensions.
    int maxSurface2DLayered[3];
    // The maxSurfaceCubemap is the maximum cubemap surface width or height.
    int maxSurfaceCubemap;
    // The maxSurfaceCubemapLayered[2] contains the maximum cubemap layered surface dimensions.
    int maxSurfaceCubemapLayered[2];
    // The surfaceAlignment specifies the alignment requirements for surfaces.
    size_t surfaceAlignment;
    // The concurrentKernels is 1 if the device supports executing multiple kernels within the
    // same context simultaneously, or 0 if not. It is not guaranteed that multiple kernels will
    // be resident on the device concurrently so this feature should not be relied upon for correctness.
    int concurrentKernels;
    // The ECCEnabled is 1 if the device has ECC support turned on, or 0 if not.
    int ECCEnabled;
    // The pciBusID is the PCI bus identifier of the device.
    int pciBusID;
    // The pciDeviceID is the PCI device (sometimes called slot) identifier of the device.
    int pciDeviceID;
    // The pciDomainID is the PCI domain identifier of the device.
    int pciDomainID;
    // The tccDriver is 1 if the device is using a TCC driver or 0 if not.
    int tccDriver;
    // The asyncEngineCount is 1 when the device can concurrently copy memory between host and
    // device while executing a kernel. It is 2 when the device can concurrently copy memory
    // between host and device in both directions and execute a kernel at the same time. It is 0 if
    // neither of these is supported.
    int asyncEngineCount;
    // The unifiedAddressing is 1 if the device shares a unified address space with the host and 0 otherwise.
    int unifiedAddressing;
    // The memoryClockRate is the peak memory clock frequency in kilohertz.
    int memoryClockRate;
    // The memoryBusWidth is the memory bus width in bits.
    int memoryBusWidth;
    // The l2CacheSize is L2 cache size in bytes.
    int l2CacheSize;
    // The persistingL2CacheMaxSize is L2 cache's maximum persisting lines size in bytes.
    int persistingL2CacheMaxSize;
    // The maxThreadsPerMultiProcessor is the number of maximum resident threads per multiprocessor.
    int maxThreadsPerMultiProcessor;
    // The streamPrioritiesSupported is 1 if the device supports stream priorities, or 0 if it is not supported.
    int streamPrioritiesSupported;
    // The globalL1CacheSupported is 1 if the device supports caching of globals in L1 cache, or 0 if it is not supported.
    int globalL1CacheSupported;
    // The localL1CacheSupported is 1 if the device supports caching of locals in L1 cache, or 0 if it is not supported.
    int localL1CacheSupported;
    // The sharedMemPerMultiprocessor is the maximum amount of shared memory available to a multiprocessor in bytes;
    // this amount is shared by all thread blocks simultaneously resident on a multiprocessor.
    size_t sharedMemPerMultiprocessor;
    // The regsPerMultiprocessor is the maximum number of 32-bit registers available to a multiprocessor;
    // this number is shared by all thread blocks simultaneously resident on a multiprocessor.
    int regsPerMultiprocessor;
    // The managedMemory is 1 if the device supports allocating managed memory on this system, or 0 if it is not supported.
    int managedMemory;
    // The isMultiGpuBoard is 1 if the device is on a multi-GPU board (e.g. Gemini cards), and 0 if not;
    int isMultiGpuBoard;
    // The multiGpuBoardGroupID is a unique identifier for a group of devices associated with the same board.
    // Devices on the same multi-GPU board will share the same identifier.
    int multiGpuBoardGroupID;
    // The hostNativeAtomicSupported is 1 if the link between the device and the host supports native
    // atomic operations, or 0 if it is not supported.
    int hostNativeAtomicSupported;
    // The singleToDoublePrecisionPerfRatio is the ratio of single precision performance (in floatingpoint
    // operations per second) to double precision performance.
    int singleToDoublePrecisionPerfRatio;
    // The pageableMemoryAccess is 1 if the device supports coherently accessing pageable memory
    // without calling cudaHostRegister on it, and 0 otherwise.
    int pageableMemoryAccess;
    // The concurrentManagedAccess is 1 if the device can coherently access managed memory
    // concurrently with the CPU, and 0 otherwise.
    int concurrentManagedAccess;
    // The computePreemptionSupported is 1 if the device supports Compute Preemption, and 0 otherwise.
    int computePreemptionSupported;
    // The canUseHostPointerForRegisteredMem is 1 if the device can access host registered
    // memory at the same virtual address as the CPU, and 0 otherwise.
    int canUseHostPointerForRegisteredMem;
    // The cooperativeLaunch is 1 if the device supports launching cooperative kernels via
    // cudaLaunchCooperativeKernel, and 0 otherwise.
    int cooperativeLaunch;
    // The cooperativeMultiDeviceLaunch is 1 if the device supports launching cooperative kernels via
    // cudaLaunchCooperativeKernelMultiDevice, and 0 otherwise.
    int cooperativeMultiDeviceLaunch;
    // The sharedMemPerBlockOptin is the per device maximum shared memory per block usable by special opt in
    int sharedMemPerBlockOptin;
    // The pageableMemoryAccessUsesHostPageTables is 1 if the device accesses pageable memory
    // via the host's page tables, and 0 otherwise.
    int pageableMemoryAccessUsesHostPageTables;
    // The directManagedMemAccessFromHost is 1 if the host can directly access managed memory
    // on the device without migration, and 0 otherwise.
    int directManagedMemAccessFromHost;
    // The maxBlocksPerMultiProcessor is the maximum number of thread blocks that can reside on a multiprocessor.
    int maxBlocksPerMultiProcessor;
    // The accessPolicyMaxWindowSize is the maximum value of cudaAccessPolicyWindow::num_bytes.
    int accessPolicyMaxWindowSize;
    // The reservedSharedMemPerBlock is the shared memory reserved by CUDA driver per block in bytes.
    size_t reservedSharedMemPerBlock;
    // The hostRegisterSupported is 1 if the device supports host memory registration via cudaHostRegister,
    // and 0 otherwise.
    int hostRegisterSupported;
    // The sparseCudaArraySupported is 1 if the device supports sparse CUDA arrays and sparse
    // CUDA mipmapped arrays, 0 otherwise.
    int sparseCudaArraySupported;
    // The hostRegisterReadOnlySupported is 1 if the device supports using the cudaHostRegister
    // flag cudaHostRegisterReadOnly to register memory that must be mapped as read-only to the GPU.
    int hostRegisterReadOnlySupported;
    // The timelineSemaphoreInteropSupported is 1 if external timeline semaphore interop is supported on the device,
    // 0 otherwise.
    int timelineSemaphoreInteropSupported;
    // The memoryPoolsSupported is 1 if the device supports using the cudaMallocAsync and cudaMemPool family of APIs,
    // 0 otherwise.
    int memoryPoolsSupported;
    // The gpuDirectRDMASupported is 1 if the device supports GPUDirect RDMA APIs, 0 otherwise.
    int gpuDirectRDMASupported;
    // The gpuDirectRDMAFlushWritesOptions is a bitmask to be interpreted according to the
    // cudaFlushGPUDirectRDMAWritesOptions enum.
    unsigned int gpuDirectRDMAFlushWritesOptions;
    // The gpuDirectRDMAWritesOrdering See the cudaGPUDirectRDMAWritesOrdering enum for numerical values.
    int gpuDirectRDMAWritesOrdering;
    // The memoryPoolSupportedHandleTypes is a bitmask of handle types supported with mempoolbased IPC.
    unsigned int memoryPoolSupportedHandleTypes;
    // The deferredMappingCudaArraySupported is 1 if the device supports deferred mapping CUDA arrays
    int deferredMappingCudaArraySupported;
    // The ipcEventSupported is 1 if the device supports IPC Events, and 0 otherwise.
    int ipcEventSupported;
    // The unifiedFunctionPointers is 1 if the device support unified pointers, and 0 otherwise.
    int unifiedFunctionPointers;
    // The physicalMemoryAlignment is the alignment requirement for physical memory allocations in bytes.
    int physicalMemoryAlignment;
} kfeDeviceProperties;

typedef enum kfeCommandPacketType_enum
{
    KFE_COMMAND_PACKET_TYPE_KERNEL_DISPATCH = 0,
    KFE_COMMAND_PACKET_TYPE_MEMCPY,
    KFE_COMMAND_PACKET_TYPE_MEMSET,
    KFE_COMMAND_PACKET_TYPE_EVENT_RECORD,
    KFE_COMMAND_PACKET_TYPE_EVENT_WAIT,
    KFE_COMMAND_PACKET_TYPE_MEMORY_WRITE,
    KFE_COMMAND_PACKET_TYPE_MEMORY_WAIT,
    KFE_COMMAND_PACKET_TYPE_DEBUG_ADD,
    KFE_COMMAND_PACKET_TYPE_DEBUG_SUB,
    KFE_COMMAND_PACKET_TYPE_MAX
    // Add more command packet types as needed.
} kfeCommandPacketType;

// This is a placeholder for the actual command packet structure.
typedef struct kfeCommandPacket_st
{
    int commandPacketType; // Type of the command packet.
    int queueId; // Identifier for the queue to which this command packet belongs.
    int reserved[6]; // Reserved for future use, can be used for additional fields or padding.
    int numWaitFences; // Number of wait fences.
    const kfeFence* pWaitFences; // Pointer to an array of fences to wait on.
    kfeFence signalFence; // Pointer to signal fence after command execution.
} kfeCommandPacket;

typedef struct kfeCommandPacketKernelDispatch_st
{
    int commandPacketType; // Type of the command packet.
    int queueId; // Identifier for the queue to which this command packet belongs.
    int gridDimX; // Grid dimension in X.
    int gridDimY; // Grid dimension in Y.
    int gridDimZ; // Grid dimension in Z.
    int blockDimX; // Block dimension in X.
    int blockDimY; // Block dimension in Y.
    int blockDimZ; // Block dimension in Z.
    int numWaitFences; // Number of wait fences.
    const kfeFence* pWaitFences; // Pointer to an array of fences to wait on.
    kfeFence signalFence; // Pointer to signal fence after command execution.
} kfeCommandPacketKernelDispatch;

typedef struct kfeCommandPacketDebugAdd_st
{
    int commandPacketType; // Type of the command packet.
    int queueId; // Identifier for the queue to which this command packet belongs.
    int operand1; // First operand for the debug command.
    int operand2; // Second operand for the debug command.
    int reserved[4]; // Reserved for future use.
    int numWaitFences; // Number of wait fences.
    const kfeFence* pWaitFences; // Pointer to an array of fences to wait on.
    kfeFence signalFence; // Pointer to signal fence after command execution.
} kfeCommandPacketDebugAdd;

typedef struct kfeCommandPacketDebugSub_st
{
    int commandPacketType; // Type of the command packet.
    int queueId; // Identifier for the queue to which this command packet belongs.
    int operand1; // First operand for the debug command.
    int operand2; // Second operand for the debug command.
    int reserved[4]; // Reserved for future use.
    int numWaitFences; // Number of wait fences.
    const kfeFence* pWaitFences; // Pointer to an array of fences to wait on.
    kfeFence signalFence; // Pointer to signal fence after command execution.
} kfeCommandPacketDebugSub;

/// @brief Initialize the KFE API. This function must be called before any other KFE API functions.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfeInitialize(void);

/// @brief Returns the number of devices. Always returns 8 (simulate 8 GPUs).
/// @param pCount Pointer to an integer where the device count will be stored.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfeDeviceGetCount(int* pCount);

/// @brief Open the device specified by gpu_id. Returns a handle on success, or NULL (invalid gpu_id).
/// @param pDevice Pointer to a kfeDevice handle that will be set on success.
/// @param gpuId The ID of the GPU to open. Valid IDs are 0 to 7 (simulate 8 GPUs).
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
/// or gpuId is out of range.
kfeResult KFEAPI kfeDeviceOpen(kfeDevice* pDevice, int gpuId);

/// @brief Close the device and destroy the related resource.
/// @param device The device to close.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfeDeviceClose(kfeDevice device);

/// @brief Get the properties of the specified device.
/// @param device The device for which to get properties.
/// @param pProperties Pointer to a kfeDeviceProperties structure that will be filled with the device properties.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfeDeviceGetProperties(kfeDevice device, kfeDeviceProperties* pProperties);

/// @brief Create a context for the specified device.
/// @param pContext Pointer to a kfeDeviceContext handle that will be set on success.
/// @param flags Flags for context creation.
/// @param device The device for which to create the context.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_INVALID_VALUE if pContext is NULL
/// or device is NULL.
kfeResult KFEAPI kfeContextCreate(kfeContext* pContext, unsigned int flags, kfeDevice device);

/// @brief Destroy a device context.
/// @param context The device context to destroy.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfeContextDestroy(kfeContext context);

/// @brief Create a physical memory object.
/// @param context The context associated with the physical memory.
/// @param size The size of the physical memory to create in bytes.
/// @param pageSize The page size for the physical memory object in bytes.
/// @param flags Flags for the physical memory creation (e.g., KFE_PHYSICAL_MEMORY_READ,
///              KFE_PHYSICAL_MEMORY_WRITE).
/// @param pMemoryObject Pointer to a pointer that will be set to the created physical memory object.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryCreate(kfeContext context, uint64_t size, uint64_t pageSize, uint64_t flags,
                                         kfePhysicalMemoryObject* pMemoryObject);

/// @brief Destroy a physical memory object.
/// @param context The context associated with the physical memory.
/// @param physicalMemory The physical memory object to destroy.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryDestroy(kfeContext context, kfePhysicalMemoryObject memoryObject);

/// @brief Map a physical memory object to a device.
/// @param context The context associated with the device.
/// @param pDeviceVirtualAddress The device virtual address where the physical memory will be mapped (0 for automatic allocation).
/// @param memoryObject The physical memory object to map.
/// @param size The size of the physical memory to map in bytes.
/// @param flags Flags for the mapping operation (e.g., KFE_PHYSICAL_MEMORY_MAP_READ, KFE_PHYSICAL_MEMORY_MAP_WRITE).
/// @param pPhysicalMemoryDeviceMapped Pointer to the mapped physical memory object.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryDeviceMap(kfeContext context, void* pDeviceVirtualAddress,
                                            kfePhysicalMemoryObject memoryObject, uint64_t size, uint64_t flags,
                                            kfePhysicalMemoryObjectDeviceMapped* pPhysicalMemoryDeviceMapped);

/// @brief Unmap a physical memory object from a device.
/// @param context The context associated with the device.
/// @param physicalMemoryDeviceMapped The mapped physical memory object on the device to unmap.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryDeviceUnmap(kfeContext context,
                                              kfePhysicalMemoryObjectDeviceMapped physicalMemoryDeviceMapped);

/// @brief Map a physical memory object to the host.
/// @param context The context associated with the host.
/// @param memoryObject The physical memory object to map.
/// @param size The size of the physical memory to map in bytes.
/// @param flags Flags for the mapping operation (e.g., KFE_PHYSICAL_MEMORY_HOST_MAP_READ,
///              KFE_PHYSICAL_MEMORY_HOST_MAP_WRITE).
/// @param pPhysicalMemoryHostMapped Pointer to the mapped physical memory object on the host.
/// @param ppHostVirtualAddress Pointer to the host virtual address of the mapped memory.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryHostMap(kfeContext context, kfePhysicalMemoryObject memoryObject, uint64_t size,
                                          uint64_t flags, kfePhysicalMemoryObjectHostMapped* pPhysicalMemoryHostMapped,
                                          void** ppHostVirtualAddress);

/// @brief Unmap a physical memory object from the host.
/// @param context The context associated with the host.
/// @param physicalMemoryHostMapped The mapped physical memory object on the host to unmap.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryHostUnmap(kfeContext context, kfePhysicalMemoryObjectHostMapped physicalMemoryHostMapped);

/// @brief Export a physical memory object to a DMA buffer.
/// @param context The context associated with the physical memory.
/// @param memoryObject The physical memory object to export.
/// @param size The size of the physical memory to export in bytes.
/// @param pageSize The page size for the export operation.
/// @param flags Flags for the export operation (e.g., KFE_PHYSICAL_MEMORY_EXPORT_READ,
///              KFE_PHYSICAL_MEMORY_EXPORT_WRITE).
/// @param pDmaBufFd Pointer to an integer that will be set to the file descriptor of the exported DMA buffer.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryExportByDmaBuf(kfeContext context, kfePhysicalMemoryObject memoryObject, uint64_t size,
                                                 uint64_t pageSize, uint64_t flags, int* pDmaBufFd);

/// @brief Import a physical memory object from a DMA buffer.
/// @param context The context associated with the physical memory.
/// @param dmaBufFd The file descriptor of the DMA buffer to import.
/// @param pRemoteContext Pointer to the context of the remote imported physical memory object.
/// @param pPhysicalMemoryObject Pointer to the imported physical memory object.
/// @param pSize Pointer to a variable that will be set to the size of the imported physical memory object in bytes.
/// @param pPageSize Pointer to a variable that will be set to the page size of the imported physical memory object.
/// @param pFlags Pointer to a variable that will be set to the flags of the imported physical memory object.
///              Flags can include KFE_PHYSICAL_MEMORY_IMPORT_READ, KFE_PHYSICAL_MEMORY_IMPORT_WRITE, etc.
///              These flags indicate the access permissions for the imported memory.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePhysicalMemoryImportFromDmaBuf(kfeContext context, int dmaBufFd, kfeContext* pRemoteContext,
                                                   kfePhysicalMemoryObject* pPhysicalMemoryObject, uint64_t* pSize,
                                                   uint64_t* pPageSize, uint64_t* pFlags);

/// @brief Create a pinned memory object.
/// @param context The context associated with the pinned memory.
/// @param size The size of the pinned memory to create in bytes.
/// @param flags Flags for the pinned memory creation (e.g., KFE_PINNED_MEMORY_READ,
///              KFE_PINNED_MEMORY_WRITE).
/// @param pPinnedMemory Pointer to the created pinned memory object.
/// @param ppHostVirtualAddress Pointer to the host virtual address of the pinned memory.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePinnedMemoryCreate(kfeContext context, uint64_t size, uint64_t flags, kfePinnedMemoryObject* pPinnedMemory,
                                       void** ppHostVirtualAddress);

/// @brief Destroy a pinned memory object.
/// @param context The context associated with the pinned memory.
/// @param pinnedMemory The pinned memory object handle to destroy.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePinnedMemoryDestroy(kfeContext context, kfePinnedMemoryObject pinnedMemory);

/// @brief Register a host pointer for pageable memory.
/// @param context The context associated with the host.
/// @param pHostVirtualAddress Pointer to the host virtual address of the pageable memory to register.
/// @param size The size of the physical memory to register in bytes.
/// @param flags Flags for the registration operation (e.g., KFE_PHYSICAL_MEMORY_HOST_REGISTER_READ,
///              KFE_PHYSICAL_MEMORY_HOST_REGISTER_WRITE).
/// @param pHostPageableMemoryRegistered Pointer to the registered pageable memory on the host.
/// @param pRegisteredOffset Pointer to a variable that will be set to the offset of the registered pageable memory.
///                          This offset can be used to access the registered memory from the device.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePageableMemoryHostRegister(kfeContext context, void* pHostVirtualAddress, uint64_t size,
                                               uint64_t flags, kfePageableMemoryHostRegistered* pHostPageableMemoryRegistered,
                                               uint64_t* pRegisteredOffset);

/// @brief Unregister a host pointer that was registered for pageable memory.
/// @param context The context associated with the host.
/// @param hostPageableMemoryRegistered The registered pageable memory object to unregister.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfePageableMemoryHostUnregister(kfeContext context,
                                                 kfePageableMemoryHostRegistered hostPageableMemoryRegistered);

/// @brief Create an user mode queue from the device context.
/// @param pUserModeQueue Pointer to the user mode queue to create.
/// @param flags Flags for the user mode queue creation.
/// @param context The context associated with the user mode queue.
/// @return Returns KFE_SUCCESS when created successfully, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfeUserModeQueueCreate(kfeUserModeQueue* pUserModeQueue, unsigned int flags, kfeContext context);

/// @brief Destroy an user mode queue.
/// @param userModeQueue The user mode queue to destroy.
/// @return The result of the operation.
kfeResult KFEAPI kfeUserModeQueueDestroy(kfeUserModeQueue userModeQueue);

/// @brief Submit a command to the user mode queue.
/// @param userModeQueue The user mode queue to which the command is submitted.
/// @param pCommandPackets The command packets to submit.
/// @param numCmdPackets The number of command packets to submit.
/// @return The result of the operation.
kfeResult KFEAPI kfeUserModeQueueSubmit(kfeUserModeQueue userModeQueue, kfeCommandPacket* pCommandPackets, unsigned int numCmdPackets);

/// @brief Create a fence object for the context.
/// @param pFence Pointer to the fence to create.
/// @param flags Flags for the fence creation.
/// @param context The context associated with the fence.
/// @return Returns KFE_SUCCESS when created successfully, otherwise return KFE_ERROR_*.
kfeResult KFEAPI kfeFenceCreate(kfeFence* pFence, unsigned int flags, kfeContext context);

/// @brief Destroy a fence object.
/// @param fence The fence to destroy.
/// @return The result of the operation.
kfeResult KFEAPI kfeFenceDestroy(kfeFence fence);

/// @brief Wait for a fence to be signaled.
/// @param userModeQueue The user mode queue that will wait for the fence.
/// @param fence The fence to wait for.
/// @return The result of the operation.
kfeResult KFEAPI kfeFenceWait(kfeUserModeQueue userModeQueue, kfeFence fence);

/// @brief Signal a fence to indicate that the associated operations are complete.
/// @param userModeQueue The user mode queue that will signal the fence.
/// @param fence The fence to signal.
/// @return The result of the operation.
kfeResult KFEAPI kfeFenceSignal(kfeUserModeQueue userModeQueue, kfeFence fence);

/// @brief Query the status of a fence.
/// @param fence The fence to query.
/// @return Return KFE_SUCCESS if fence has been signaled, otherwise return KFE_ERROR_NOT_READY.
kfeResult KFEAPI kfeFenceQuery(kfeFence fence);

/// @brief Finalize the KFE API. This function should be called when the application is done using the KFE API.
/// It cleans up any resources allocated during the initialization and usage of the KFE API.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult KFEAPI kfeFinalize(void);

#ifdef __cplusplus
} // extern "C"
#endif // __cplusplus

#endif // KFE_H_