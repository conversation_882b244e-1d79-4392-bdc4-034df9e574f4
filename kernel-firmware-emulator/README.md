# Kernel-Firmware-Emulator

Kernel Firmware Emulator (KFE) is a C++ software simulator used to connect the UMD and Potion models. It simulates the internal functional implementation of KMD and Firmware, as well as their corresponding driver interfaces.

## Dependencies
### Ubuntu
```
sudo apt install libprotobuf-dev protobuf-compiler
```
### MacOS
```
brew install protobuf pkg-config
```

## Build

```
mkdir build && cd build

cmake -GNinja -DCMAKE_BUILD_TYPE=Debug -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..

ninja

# Optional, install it.
ninja install

export LD_LIBRARY_PATH=<YOUR_WORKSPACE_PATH>/kernel-firmware-emulator/install/lib:${LD_LIBRARY_PATH}
```

# Run test case

First, you need to add the corresponding library path for kernel-firmware-emulator.
```
export LD_LIBRARY_PATH=<YOUR_WORKSPACE_PATH>/kernel-firmware-emulator/build/lib:${LD_LIBRARY_PATH}

cd build/bin/tests/unittests/0_simpleAdd

./simpleAdd
```