kfe {
    global:
        kfeInitialize;
        kfeDeviceGetCount;
        kfeDeviceOpen;
        kfeDeviceClose;
        kfeDeviceGetProperties;
        kfeContextCreate;
        kfeContextDestroy;
        kfePhysicalMemoryCreate;
        kfePhysicalMemoryDestroy;
        kfePhysicalMemoryDeviceMap;
        kfePhysicalMemoryDeviceUnmap;
        kfePhysicalMemoryHostMap;
        kfePhysicalMemoryHostUnmap;
        kfePhysicalMemoryExportByDmaBuf;
        kfePhysicalMemoryImportFromDmaBuf;
        kfePinnedMemoryCreate;
        kfePinnedMemoryDestroy;
        kfePageableMemoryHostRegister;
        kfePageableMemoryHostUnregister;
        kfeUserModeQueueCreate;
        kfeUserModeQueueDestroy;
        kfeUserModeQueueSubmit;
        kfeFenceCreate;
        kfeFenceDestroy;
        kfeFenceWait;
        kfeFenceSignal;
        kfeFenceQuery;
        kfeFinalize;
    local:
        *;
};