#ifndef PROCESS_SYNC_MANAGER_H_
#define PROCESS_SYNC_MANAGER_H_

#include "kfe.h"

#include <string>
#include <fcntl.h>
#include <unistd.h>

namespace kfe
{
class ProcessSyncManager {
public:
    ProcessSyncManager(const std::string& lockFileName);
    ~ProcessSyncManager();

    // Lock management methods
    bool acquireExclusiveLock();
    bool tryAcquireExclusiveLock();
    bool acquireSharedLock();
    bool tryAcquireSharedLock();
    void releaseLock();
private:
    ProcessSyncManager(const ProcessSyncManager&) = delete;
    ProcessSyncManager& operator=(const ProcessSyncManager&) = delete;

    int m_lockFd;
    std::string m_lockFile;
};
}
#endif // PROCESS_SYNC_MANAGER_H_