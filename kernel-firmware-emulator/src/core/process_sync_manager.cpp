#include "process_sync_manager.h"

#include <cstring>
#include <sys/file.h>
#include <stdexcept>

namespace kfe
{
// =====================================================================================================================
ProcessSyncManager::ProcessSyncManager(const std::string& lockFileName)
    : m_lockFd(-1)
    , m_lockFile(lockFileName)
{
    m_lockFd = open(m_lockFile.c_str(), O_CREAT | O_RDWR, 0666);
    if (m_lockFd == -1)
    {
        std::string errorMessage = "Error opening lock file '" + m_lockFile + "': " + strerror(errno);
        throw std::runtime_error(errorMessage);
    }
}

// =====================================================================================================================
ProcessSyncManager::~ProcessSyncManager()
{
    if (m_lockFd != -1)
    {
        close(m_lockFd);
    }
}

// =====================================================================================================================
bool ProcessSyncManager::acquireExclusiveLock()
{
    if (flock(m_lockFd, LOCK_EX) == -1)
    {
        fprintf(stderr, "Failed to acquire exclusive lock: %s\n", strerror(errno));
        return false;
    }

    return true;
}

// =====================================================================================================================
bool ProcessSyncManager::tryAcquireExclusiveLock()
{
    if (flock(m_lockFd, LOCK_EX | LOCK_NB) == -1)
    {
        return false;
    }

    return true;
}

// =====================================================================================================================
bool ProcessSyncManager::acquireSharedLock()
{
    if (flock(m_lockFd, LOCK_SH) == -1)
    {
        fprintf(stderr, "Error acquiring shared lock: %s\n", strerror(errno));
        return false;
    }

    return true;
}

// =====================================================================================================================
bool ProcessSyncManager::tryAcquireSharedLock()
{
    if (flock(m_lockFd, LOCK_SH | LOCK_NB) == -1)
    {
        return false;
    }

    return true;
}

// =====================================================================================================================
void ProcessSyncManager::releaseLock()
{
    if (flock(m_lockFd, LOCK_UN) == -1)
    {
        fprintf(stderr, "Error releasing lock: %s\n", strerror(errno));
    }
}
}