cmake_minimum_required(VERSION 3.12)

# Set up protobuf compiler
find_program(PROTOBUF_PROTOC_EXECUTABLE protoc REQUIRED)

# Generate protobuf files manually
set(PROTO_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
set(PROTO_SRCS ${PROTO_OUTPUT_DIR}/kfe_protocol.pb.cc)
set(PROTO_HDRS ${PROTO_OUTPUT_DIR}/kfe_protocol.pb.h)

add_custom_command(
    OUTPUT ${PROTO_SRCS} ${PROTO_HDRS}
    COMMAND ${PROTOBUF_PROTOC_EXECUTABLE}
    ARGS --cpp_out=${PROTO_OUTPUT_DIR} --proto_path=${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/kfe_protocol.proto
    DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/kfe_protocol.proto
    COMMENT "Running cpp protocol buffer compiler on kfe_protocol.proto"
    VERBATIM
)
message(STATUS "Protobuf sources: ${PROTO_SRCS}")
message(STATUS "Protobuf headers: ${PROTO_HDRS}")

# Get the directory path of PROTO_HDRS
set(PROTO_HDRS_DIRS "")
foreach(HDR ${PROTO_HDRS})
    get_filename_component(HDR_DIR ${HDR} DIRECTORY)
    list(APPEND PROTO_HDRS_DIRS ${HDR_DIR})
endforeach()
if(PROTO_HDRS_DIRS)
    list(REMOVE_DUPLICATES PROTO_HDRS_DIRS)
    message(STATUS "All protobuf headers directories: ${PROTO_HDRS_DIRS}")
endif()

# Build static library libcore.a
add_library(core STATIC
    client_connection.cpp
    context.cpp
    device_manager.cpp
    device.cpp
    fence.cpp
    physical_memory_object.cpp
    process_sync_manager.cpp
    server.cpp
    thread_pool.cpp
    user_mode_queue.cpp
    ${PROTO_SRCS}
    ${PROTO_HDRS}
)

# Include directories
target_include_directories(core PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src/core
    ${CMAKE_SOURCE_DIR}/src/util
    ${CMAKE_SOURCE_DIR}/src/util/os
    ${CMAKE_SOURCE_DIR}/src/thunk
    ${PROTO_HDRS_DIRS} # Generated protobuf headers directory
    ${PROTOBUF_INCLUDE_DIRS}
)

if (WIN32)
    target_include_directories(core PUBLIC ${CMAKE_SOURCE_DIR}/src/util/os/windows)
elseif (UNIX)
    target_include_directories(core PUBLIC ${CMAKE_SOURCE_DIR}/src/util/os/posix)
else()
    message(FATAL_ERROR "Unknown platform!")
endif()

# Link the static library libutil.a
target_link_directories(core PUBLIC ${PROTOBUF_LIBRARY_DIRS})
target_link_libraries(core PUBLIC util thunk pthread ${PROTOBUF_LIBRARIES})