#ifndef CLIENT_CONNECTION_H_
#define CLIENT_CONNECTION_H_

#include "kfe.h"
#include "kfe_protocol.pb.h"

#include <atomic>
#include <mutex>
#include <string>
#include <vector>
#include <memory>
#include <thread>

namespace kfe
{
class ProcessSyncManager;
class ClientConnection
{
public:
    static ClientConnection& getInstance()
    {
        static ClientConnection instance;
        return instance;
    };

    ClientConnection();
    ~ClientConnection();

    inline bool isInitialized() const { return m_initialized; }
    inline unsigned long long getClientId() const { return m_clientId; }

    kfeResult initialize();
    kfeResult finalize();
    kfeResult getDeviceCount(int* pCount);
    kfeResult openDevice(kfeDevice* pDevice, int gpuId);
    kfeResult closeDevice(kfeDevice device);
    kfeResult getDeviceProperties(kfeDevice device, kfeDeviceProperties* pProperties);
    kfeResult createContext(kfeContext* pContext, unsigned int flags, kfeDevice device);
    kfeResult destroyContext(kfeContext context);
    kfeResult createPhysicalMemory(kfeContext context, unsigned long long size, unsigned long long pageSize,
                                   unsigned long long flags, kfePhysicalMemoryObject* pMemoryObject);
    kfeResult destroyPhysicalMemory(kfeContext context, kfePhysicalMemoryObject memoryObject);
    kfeResult deviceMapPhysicalMemory(kfeContext context, unsigned long long virtualAddress, kfePhysicalMemoryObject memoryObject,
                                      unsigned long long size, unsigned long long flags, void** pPhysicalMemoryDeviceMapped);
    kfeResult deviceUnmapPhysicalMemory(kfeContext context, void* pPhysicalMemoryDeviceMapped);
    kfeResult hostMapPhysicalMemory(kfeContext context, kfePhysicalMemoryObject memoryObject, unsigned long long size,
                                    unsigned long long flags, kfePhysicalMemoryObjectHostMapped* pPhysicalMemoryHostMapped,
                                    void** ppHostVirtualAddress);
    kfeResult hostUnmapPhysicalMemory(kfeContext context, kfePhysicalMemoryObjectHostMapped physicalMemoryHostMapped);
    kfeResult exportPhysicalMemoryByDmaBuf(kfeContext context, kfePhysicalMemoryObject memoryObject,
                                           unsigned long long size, unsigned long long pageSize,
                                           unsigned long long flags, int* pDmaBufFd);
    kfeResult importPhysicalMemoryFromDmaBuf(kfeContext context, int dmaBufFd, kfePhysicalMemoryObject* pMemoryObject,
                                             unsigned long long* pSize, unsigned long long* pPageSize,
                                             unsigned long long* pFlags);
    kfeResult createPinnedMemory(kfeContext context, unsigned long long size, unsigned long long flags,
                                 kfePinnedMemoryObject* pPinnedMemoryObject, void** ppHostVirtualAddress);
    kfeResult destroyPinnedMemory(kfeContext context, kfePinnedMemoryObject pinnedMemoryObject);
    kfeResult registerPageableMemory(kfeContext context, void* pHostVirtualAddress, unsigned long long size,
                                     unsigned long long flags, kfePageableMemoryHostRegistered* pPageableMemoryHostRegistered,
                                     unsigned long long* pRegisteredOffset);
    kfeResult unregisterPageableMemory(kfeContext context, kfePageableMemoryHostRegistered pageableMemoryHostRegistered);
    kfeResult createUserModeQueue(kfeUserModeQueue* pUserModeQueue, unsigned int flags, kfeContext context);
    kfeResult destroyUserModeQueue(kfeUserModeQueue userModeQueue);
    kfeResult submitUserModeQueue(kfeUserModeQueue userModeQueue, kfeCommandPacket* pCommandPackets,
                                  unsigned int numCmdPackets);
    kfeResult createFence(kfeFence* pFence, unsigned int flags, kfeContext context);
    kfeResult destroyFence(kfeFence fence);
    kfeResult waitFence(kfeUserModeQueue userModeQueue, kfeFence fence);
    kfeResult signalFence(kfeUserModeQueue userModeQueue, kfeFence fence);
    kfeResult queryFence(kfeFence fence);

private:
    ClientConnection(const ClientConnection&) = delete;
    ClientConnection& operator=(const ClientConnection&) = delete;

    kfeResult destroyPinnedMemoryImpl(kfeContext context, kfePinnedMemoryObject pinnedMemoryObject);

    // Establishes a connection to the server if not already connected.
    // Returns a kfeResult indicating success or the type of failure encountered.
    kfeResult connect();
    bool isServerRunning();
    bool connectToServer();
    bool startServerProcess();

    kfeResult registerClient();
    kfeResult unregisterClient();

    bool sendRawData(const void* data, size_t size);
    bool recvRawData(void* data, size_t size);
    std::string getProcessName();

    kfeResult sendRequest(const KfeRequest& request, KfeResponse& response);

    // Get the next request ID in a thread-safe manner
    inline unsigned long long getNextRequestId() { return m_nextRequestId.fetch_add(1); }

    std::mutex m_mutex;
    std::atomic<bool> m_initialized;
    unsigned long long m_clientId;
    std::atomic<unsigned long long> m_nextRequestId;

    std::mutex m_socketMutex;
    std::string m_socketPath;
    int m_socketFd;

    std::atomic<bool> m_connected;
    std::atomic<bool> m_running;

    std::string m_serverLockFilePath;
    std::string m_serverPidFilePath;

    std::thread m_heartbeatThread;
    void heartbeatWorker();

    // Process synchronization
    std::unique_ptr<ProcessSyncManager> m_syncManager;

    struct HostPinnedMemoryInfo
    {
        int fd;
        void* pMappedAddress;
        unsigned long long size;
    };

    std::mutex m_hostPinnedMemoryMutex;
    std::map<kfePinnedMemoryObject, std::unique_ptr<HostPinnedMemoryInfo>> m_hostPinnedMemoryMap;

    // Try to connect to the server multiple times
    static constexpr int CONNECT_RETRY_COUNT = 10;
    static constexpr int CONNECT_RETRY_DELAY_MS = 100;
    static constexpr int HEARTBEAT_INTERVAL_MS = 500;
    static constexpr int SERVER_STARTUP_DELAY_MS = 500;
};
} // namespace kfe

#endif // CLIENT_DISPATCHER_H_