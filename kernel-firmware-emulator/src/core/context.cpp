#include "context.h"
#include "device.h"
#include "fence.h"
#include "user_mode_queue.h"
#include "physical_memory_object.h"

#include <cassert>

namespace kfe
{
// =====================================================================================================================
Context::Context(Device* pDevice, unsigned int flags, unsigned long long clientId, int contextId, util::OsPid clientPid)
    : m_pDevice(pDevice)
    , m_flags(flags)
    , m_clientId(clientId)
    , m_contextId(contextId)
    , m_clientPid(clientPid)
    , m_userModeQueuesMap()
    , m_userModeQueuesMapMutex()
    , m_fencesMap()
    , m_fencesMapMutex()
    , m_physicalMemoryObjectsMap()
    , m_physicalMemoryObjectsDeviceMapped()
    , m_physicalMemoryObjectsHostMapped()
    , m_physicalMemoryObjectsMapMutex()
    , m_queueIdCounter(0)
    , m_fenceIdCounter(0)
    , m_physicalMemoryObjectIdCounter(0)
{
    // Constructor implementation
}

// =====================================================================================================================
Context::~Context()
{
    // Destructor implementation
    // Cleanup resources if necessary
    destroyAllFences();

    destroyAllUserModeQueues();

    destroyAllPhysicalMemoryObjects();
}

// =====================================================================================================================
kfeResult Context::createDevicePhysicalMemory(unsigned long long size, unsigned long long pageSize,
                                              unsigned long long flags,
                                              DevicePhysicalMemoryObject** ppPhysicalMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    assert(size > 0);
    assert(ppPhysicalMemoryObject != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    DevicePhysicalMemoryObject* pPhysicalMemoryObject =
        new (std::nothrow) DevicePhysicalMemoryObject(this, size, pageSize, flags, getNextPhysicalMemoryObjectId());
    if (pPhysicalMemoryObject == nullptr)
    {
        fprintf(stderr, "[Main Process] Failed to allocate memory for device physical memory object\n");
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    result = pPhysicalMemoryObject->init();
    if (result != KFE_SUCCESS)
    {
        delete pPhysicalMemoryObject;
        *ppPhysicalMemoryObject = nullptr;
        return result;
    }

    // Add the physical memory object to the context
    addPhysicalMemoryObject(pPhysicalMemoryObject);

    *ppPhysicalMemoryObject = pPhysicalMemoryObject;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::destroyDevicePhysicalMemory(DevicePhysicalMemoryObject* pPhysicalMemoryObject)
{
    assert(pPhysicalMemoryObject != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    // Check if the physical memory object is valid
    if (!isValidPhysicalMemoryObject(pPhysicalMemoryObject->getId()))
    {
        fprintf(stderr, "[Main Process] Invalid physical memory object ID: %d\n", pPhysicalMemoryObject->getId());
        return KFE_ERROR_INVALID_VALUE;
    }

    // Check if the physical memory object is mapped to device
    if (pPhysicalMemoryObject->getDeviceMappedHandle() != nullptr)
    {
        fprintf(stderr, "[Main Process] Cannot destroy mapped physical memory object with ID: %d\n",
                pPhysicalMemoryObject->getId());
        return KFE_ERROR_ALREADY_MAPPED;
    }

    // Check if the physical memory object is mapped to host
    if (pPhysicalMemoryObject->getHostMappedHandle() != nullptr)
    {
        fprintf(stderr, "[Main Process] Cannot destroy mapped physical memory object with ID: %d\n",
                pPhysicalMemoryObject->getId());
        return KFE_ERROR_ALREADY_MAPPED;
    }

    // Remove the physical memory object from the context
    removePhysicalMemoryObject(pPhysicalMemoryObject);

    delete pPhysicalMemoryObject;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::deviceMapPhysicalMemory(unsigned long long deviceVirtualAddress,
                                           PhysicalMemoryObject* pPhysicalMemoryObject,
                                           unsigned long long size,
                                           unsigned long long flags,
                                           void** ppPhysicalMemoryDeviceMapped)
{
    kfeResult result = KFE_SUCCESS;

    assert(pPhysicalMemoryObject != nullptr);
    assert(ppPhysicalMemoryDeviceMapped != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    // Check if the physical memory object is valid
    if (!isValidPhysicalMemoryObject(pPhysicalMemoryObject->getId()))
    {
        fprintf(stderr, "[Main Process] Invalid physical memory object ID: %d\n", pPhysicalMemoryObject->getId());
        return KFE_ERROR_INVALID_VALUE;
    }

    PhysicalMemoryMapInfo* pDeviceMappedInfo = nullptr;
    pDeviceMappedInfo = new (std::nothrow) PhysicalMemoryMapInfo(PHYSICAL_MEMORY_MAP_TYPE_DEVICE,
                                                                 reinterpret_cast<void*>(deviceVirtualAddress),
                                                                 pPhysicalMemoryObject);
    if (pDeviceMappedInfo == nullptr)
    {
        fprintf(stderr, "[Main Process] Failed to allocate memory for physical memory device mapped info\n");
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    addPhysicalMemoryObjectDeviceMappedHandle(pDeviceMappedInfo, pPhysicalMemoryObject);

    *ppPhysicalMemoryDeviceMapped = reinterpret_cast<void*>(pDeviceMappedInfo);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::deviceUnmapPhysicalMemory(void* pPhysicalMemoryDeviceMapped)
{
    kfeResult result = KFE_SUCCESS;

    assert(pPhysicalMemoryDeviceMapped != nullptr);

    PhysicalMemoryMapInfo* pDeviceMappedInfo = reinterpret_cast<PhysicalMemoryMapInfo*>(pPhysicalMemoryDeviceMapped);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    // Get the physical memory object by its virtual address
    PhysicalMemoryObject* pPhysicalMemoryObject = getPhysicalMemoryObjectByDeviceMappedHandle(pDeviceMappedInfo);
    if (pPhysicalMemoryObject == nullptr)
    {
        fprintf(stderr, "[Main Process] No physical memory object found for device mapped pointer: %p\n",
                pDeviceMappedInfo);
        return KFE_ERROR_INVALID_VALUE;
    }

    removePhysicalMemoryObjectDeviceMappedHandle(pDeviceMappedInfo);

    delete pDeviceMappedInfo;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::hostMapDevicePhysicalMemory(DevicePhysicalMemoryObject* pPhysicalMemoryObject,
                                               unsigned long long size, unsigned long long flags,
                                               void** ppPhysicalMemoryHostMapped, void** ppHostVirtualAddress)
{
    kfeResult result = KFE_SUCCESS;

    assert(pPhysicalMemoryObject != nullptr);
    assert(ppPhysicalMemoryHostMapped != nullptr);
    assert(ppHostVirtualAddress != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    return KFE_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
kfeResult Context::hostUnmapDevicePhysicalMemory(void* pPhysicalMemoryHostMapped)
{
    kfeResult result = KFE_SUCCESS;

    assert(pPhysicalMemoryHostMapped != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    return KFE_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
kfeResult Context::exportPhysicalMemoryByDmaBuf(PhysicalMemoryObject* pPhysicalMemoryObject, unsigned long long size,
                                                unsigned long long pageSize, unsigned long long flags, int* pDmaBufFd)
{
    kfeResult result = KFE_SUCCESS;

    assert(pPhysicalMemoryObject != nullptr);
    assert(pDmaBufFd != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    return KFE_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
kfeResult Context::importPhysicalMemoryFromDmaBuf(int dmaBufFd, PhysicalMemoryObject** ppPhysicalMemoryObject,
                                                  unsigned long long* pSize, unsigned long long* pPageSize,
                                                  unsigned long long* pFlags)
{
    kfeResult result = KFE_SUCCESS;

    assert(ppPhysicalMemoryObject != nullptr);
    assert(pSize != nullptr);
    assert(pPageSize != nullptr);
    assert(pFlags != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    return KFE_ERROR_NOT_SUPPORTED;
}

// =====================================================================================================================
kfeResult Context::createHostPinnedMemory(unsigned long long size, unsigned long long flags,
                                          HostPinnedMemoryObject** ppPinnedMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    assert(size > 0);
    assert(ppPinnedMemoryObject != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    HostPinnedMemoryObject* pPinnedMemoryObject =
        new (std::nothrow) HostPinnedMemoryObject(this, size, util::getPageSize(), flags,
                                                  getNextPhysicalMemoryObjectId());
    if (pPinnedMemoryObject == nullptr)
    {
        fprintf(stderr, "[Main Process] Failed to allocate memory for host pinned memory object\n");
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    result = pPinnedMemoryObject->init();
    if (result != KFE_SUCCESS)
    {
        delete pPinnedMemoryObject;
        *ppPinnedMemoryObject = nullptr;
        return result;
    }

    // Add the pinned memory object to the context
    addPhysicalMemoryObject(pPinnedMemoryObject);

    *ppPinnedMemoryObject = pPinnedMemoryObject;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::destroyHostPinnedMemory(HostPinnedMemoryObject* pPinnedMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    assert(pPinnedMemoryObject != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    // Check if the physical memory object is valid
    if (!isValidPhysicalMemoryObject(pPinnedMemoryObject->getId()))
    {
        fprintf(stderr, "[Main Process] Invalid physical memory object ID: %d\n", pPinnedMemoryObject->getId());
        return KFE_ERROR_INVALID_VALUE;
    }

    // Check if the physical memory object is mapped to device
    if (pPinnedMemoryObject->getDeviceMappedHandle() != nullptr)
    {
        fprintf(stderr, "[Main Process] Cannot destroy mapped physical memory object with ID: %d\n",
                pPinnedMemoryObject->getId());
        return KFE_ERROR_ALREADY_MAPPED;
    }

    // Remove the physical memory object from the context
    removePhysicalMemoryObject(pPinnedMemoryObject);

    delete pPinnedMemoryObject;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::registerHostPageableMemory(void* pHostVirtualAddress, unsigned long long size,
                                              unsigned long long flags, PageableMemoryObject** ppPageableMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    assert(pHostVirtualAddress != nullptr);
    assert(size > 0);
    assert(ppPageableMemoryObject != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    PageableMemoryObject* pPageableMemoryObject = new (std::nothrow) PageableMemoryObject(
        this, size, util::getPageSize(), flags, getNextPhysicalMemoryObjectId(), pHostVirtualAddress);
    if (pPageableMemoryObject == nullptr)
    {
        fprintf(stderr, "[Main Process] Failed to allocate memory for pageable memory object\n");
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    result = pPageableMemoryObject->init();
    if (result != KFE_SUCCESS)
    {
        delete pPageableMemoryObject;
        *ppPageableMemoryObject = nullptr;
        return result;
    }

    // Add the pageable memory object to the context
    addPhysicalMemoryObject(pPageableMemoryObject);

    *ppPageableMemoryObject = pPageableMemoryObject;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::unregisterHostPageableMemory(PageableMemoryObject* pPageableMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    assert(pPageableMemoryObject != nullptr);

    std::lock_guard<std::mutex> lock(m_physicalMemoryObjectMutex);

    // Check if the physical memory object is valid
    if (!isValidPhysicalMemoryObject(pPageableMemoryObject->getId()))
    {
        fprintf(stderr, "[Main Process] Invalid physical memory object ID: %d\n", pPageableMemoryObject->getId());
        return KFE_ERROR_INVALID_VALUE;
    }

    // Check if the physical memory object is mapped to device
    if (pPageableMemoryObject->getDeviceMappedHandle() != nullptr)
    {
        fprintf(stderr, "[Main Process] Cannot destroy mapped physical memory object with ID: %d\n",
                pPageableMemoryObject->getId());
        return KFE_ERROR_ALREADY_MAPPED;
    }

    // Remove the physical memory object from the context
    removePhysicalMemoryObject(pPageableMemoryObject);

    delete pPageableMemoryObject;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::createUserModeQueue(UserModeQueue** ppUserModeQueue, unsigned int flags)
{
    UserModeQueue* pUserModeQueue = nullptr;

    assert(ppUserModeQueue != nullptr);

    // Simulate creating a user mode queue by setting the pointer to a new UserModeQueue instance
    pUserModeQueue = new (std::nothrow) UserModeQueue(this, flags, getNextQueueId());
    if (pUserModeQueue == nullptr)
    {
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    addUserModeQueue(pUserModeQueue);

    *ppUserModeQueue = pUserModeQueue;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::destroyUserModeQueue(UserModeQueue* pUserModeQueue)
{
    assert(pUserModeQueue != nullptr);

    // Remove the user mode queue from the context
    removeUserModeQueue(pUserModeQueue);

    // Simulate destroying a user mode queue by deleting the UserModeQueue instance
    delete pUserModeQueue;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::createFence(Fence** ppFence, unsigned int flags)
{
    kfeResult result = KFE_SUCCESS;
    Fence* pFence = nullptr;

    assert(ppFence != nullptr);

    // For simplicity, we will just create a fence and return it
    pFence = new (std::nothrow) Fence(this, flags, getNextFenceId());
    if (pFence == nullptr)
    {
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    // Initialize the fence
    result = pFence->init();
    if (result != KFE_SUCCESS)
    {
        delete pFence;
        return result;
    }

    addFence(pFence);

    *ppFence = pFence;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Context::destroyFence(Fence* pFence)
{
    assert(pFence != nullptr);

    removeFence(pFence);

    // Simulate destroying a fence by deleting the Fence instance
    delete pFence;

    return KFE_SUCCESS;
}

// =====================================================================================================================
void Context::addUserModeQueue(UserModeQueue* pQueue)
{
    std::lock_guard<std::mutex> lock(m_userModeQueuesMapMutex);
    assert(pQueue != nullptr);

    m_userModeQueuesMap.try_emplace(pQueue->getQueueId(), pQueue);
}

// =====================================================================================================================
void Context::removeUserModeQueue(UserModeQueue* pQueue)
{
    std::lock_guard<std::mutex> lock(m_userModeQueuesMapMutex);
    assert(pQueue != nullptr);
    auto it = m_userModeQueuesMap.find(pQueue->getQueueId());
    if (it != m_userModeQueuesMap.end())
    {
        m_userModeQueuesMap.erase(it);
    }
}

// =====================================================================================================================
UserModeQueue* Context::getUserModeQueue(int queueId)
{
    std::lock_guard<std::mutex> lock(m_userModeQueuesMapMutex);
    auto it = m_userModeQueuesMap.find(queueId);
    return (it != m_userModeQueuesMap.end()) ? it->second : nullptr;
}

// =====================================================================================================================
bool Context::isValidUserModeQueue(int queueId)
{
    std::lock_guard<std::mutex> lock(m_userModeQueuesMapMutex);
    return m_userModeQueuesMap.find(queueId) != m_userModeQueuesMap.end();
}

// =====================================================================================================================
void Context::addFence(Fence* pFence)
{
    std::lock_guard<std::mutex> lock(m_fencesMapMutex);
    assert(pFence != nullptr);
    m_fencesMap.try_emplace(pFence->getFenceId(), pFence);
}

// =====================================================================================================================
void Context::removeFence(Fence* pFence)
{
    std::lock_guard<std::mutex> lock(m_fencesMapMutex);
    assert(pFence != nullptr);
    auto it = m_fencesMap.find(pFence->getFenceId());
    if (it != m_fencesMap.end())
    {
        m_fencesMap.erase(it);
    }
}

// =====================================================================================================================
Fence* Context::getFence(int fenceId)
{
    std::lock_guard<std::mutex> lock(m_fencesMapMutex);
    auto it = m_fencesMap.find(fenceId);
    return (it != m_fencesMap.end()) ? it->second : nullptr;
}

// =====================================================================================================================
bool Context::isValidFence(int fenceId)
{
    std::lock_guard<std::mutex> lock(m_fencesMapMutex);
    return m_fencesMap.find(fenceId) != m_fencesMap.end();
}

// =====================================================================================================================
void Context::addPhysicalMemoryObject(PhysicalMemoryObject* pMemoryObject)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    assert(pMemoryObject != nullptr);
    m_physicalMemoryObjectsMap.try_emplace(pMemoryObject->getId(), pMemoryObject);
}

// =====================================================================================================================
void Context::removePhysicalMemoryObject(PhysicalMemoryObject* pMemoryObject)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    assert(pMemoryObject != nullptr);
    auto it = m_physicalMemoryObjectsMap.find(pMemoryObject->getId());
    if (it != m_physicalMemoryObjectsMap.end())
    {
        m_physicalMemoryObjectsMap.erase(it);
    }
}

// =====================================================================================================================
void Context::addPhysicalMemoryObjectDeviceMappedHandle(PhysicalMemoryMapInfo* pDeviceMappedHandle,
                                                        PhysicalMemoryObject* pMemoryObject)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    assert(pMemoryObject != nullptr);
    // Map the physical memory object to the device address
    m_physicalMemoryObjectsDeviceMapped.try_emplace(pDeviceMappedHandle, pMemoryObject);
    pMemoryObject->setDeviceMappedHandle(pDeviceMappedHandle);
}

// =====================================================================================================================
void Context::removePhysicalMemoryObjectDeviceMappedHandle(PhysicalMemoryMapInfo* pDeviceMappedHandle)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    assert(pDeviceMappedHandle != nullptr);
    auto it = m_physicalMemoryObjectsDeviceMapped.find(pDeviceMappedHandle);
    if (it != m_physicalMemoryObjectsDeviceMapped.end())
    {
        it->second->setDeviceMappedHandle(nullptr);
        m_physicalMemoryObjectsDeviceMapped.erase(it);
    }
}

// =====================================================================================================================
void Context::addPhysicalMemoryObjectHostMappedHandle(PhysicalMemoryMapInfo* pHostMappedHandle,
                                                      PhysicalMemoryObject* pMemoryObject)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    assert(pMemoryObject != nullptr);
    // Map the physical memory object to the host pointer
    m_physicalMemoryObjectsHostMapped.try_emplace(pHostMappedHandle, pMemoryObject);
    reinterpret_cast<DevicePhysicalMemoryObject*>(pMemoryObject)->setHostMappedHandle(pHostMappedHandle);
}

// =====================================================================================================================
void Context::removePhysicalMemoryObjectHostMappedHandle(PhysicalMemoryMapInfo* pHostMappedHandle)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    assert(pHostMappedHandle != nullptr);
    auto it = m_physicalMemoryObjectsHostMapped.find(pHostMappedHandle);
    if (it != m_physicalMemoryObjectsHostMapped.end())
    {
        reinterpret_cast<DevicePhysicalMemoryObject*>(it->second)->setHostMappedHandle(nullptr);
        m_physicalMemoryObjectsHostMapped.erase(it);
    }
}

// =====================================================================================================================
PhysicalMemoryObject* Context::getPhysicalMemoryObjectById(int memoryObjectId)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    auto it = m_physicalMemoryObjectsMap.find(memoryObjectId);
    return (it != m_physicalMemoryObjectsMap.end()) ? it->second : nullptr;
}

// =====================================================================================================================
PhysicalMemoryObject* Context::getPhysicalMemoryObjectByDeviceMappedHandle(PhysicalMemoryMapInfo* pDeviceMappedHandle)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    auto it = m_physicalMemoryObjectsDeviceMapped.find(pDeviceMappedHandle);
    return (it != m_physicalMemoryObjectsDeviceMapped.end()) ? it->second : nullptr;
}

// =====================================================================================================================
PhysicalMemoryObject* Context::getPhysicalMemoryObjectByHostMappedHandle(PhysicalMemoryMapInfo* pHostMappedHandle)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    auto it = m_physicalMemoryObjectsHostMapped.find(pHostMappedHandle);
    return (it != m_physicalMemoryObjectsHostMapped.end()) ? it->second : nullptr;
}

// =====================================================================================================================
bool Context::isValidPhysicalMemoryObject(int memoryObjectId)
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    return m_physicalMemoryObjectsMap.find(memoryObjectId) != m_physicalMemoryObjectsMap.end();
}

// =====================================================================================================================
void Context::destroyAllUserModeQueues()
{
    std::lock_guard<std::mutex> lock(m_userModeQueuesMapMutex);
    for (auto it = m_userModeQueuesMap.begin(); it != m_userModeQueuesMap.end(); )
    {
        delete it->second;
        it = m_userModeQueuesMap.erase(it); // Erase returns the next iterator
    }
}

// =====================================================================================================================
void Context::destroyAllFences()
{
    std::lock_guard<std::mutex> lock(m_fencesMapMutex);
    for (auto it = m_fencesMap.begin(); it != m_fencesMap.end(); )
    {
        delete it->second; // Assuming Fence has a proper destructor
        it = m_fencesMap.erase(it); // Erase returns the next iterator
    }
}

// =====================================================================================================================
void Context::destroyAllPhysicalMemoryObjects()
{
    std::lock_guard<std::recursive_mutex> lock(m_physicalMemoryObjectsMapMutex);
    for (auto it = m_physicalMemoryObjectsMap.begin(); it != m_physicalMemoryObjectsMap.end(); )
    {
        delete it->second; // Assuming PhysicalMemoryObject has a proper destructor
        it = m_physicalMemoryObjectsMap.erase(it); // Erase returns the next iterator
    }

    m_physicalMemoryObjectsDeviceMapped.clear();
    m_physicalMemoryObjectsHostMapped.clear();
}

// =====================================================================================================================
void readHostMemory(uint64_t addr, size_t size, uint8_t* pData)
{
    // Read the pinned memory object with addr and size
    // and then copy the data to pData.

    (void)addr;
    (void)size;
    (void)pData;
}

// =====================================================================================================================
void writeHostMemory(uint64_t addr, size_t size, const uint8_t* pData)
{
    // Write the data from pData to the memory object with addr and size.
    (void)addr;
    (void)size;
    (void)pData;
}

} // namespace kfe