#ifndef PHYSICAL_MEMORY_OBJECT_H_
#define PHYSICAL_MEMORY_OBJECT_H_

#include "kfe.h"

#include <string>
#include <sys/uio.h>

namespace kfe
{
class Device;
class Context;

enum PhysicalMemoryMapType
{
    PHYSICAL_MEMORY_MAP_TYPE_DEVICE = 0x0,
    PHYSICAL_MEMORY_MAP_TYPE_HOST   = 0x1,
    PHYSICAL_MEMORY_MAP_TYPE_MAX,
};

class PhysicalMemoryMapInfo
{
public:
    // Functions
    PhysicalMemoryMapInfo()
     : m_mapType(PHYSICAL_MEMORY_MAP_TYPE_DEVICE)
     , m_pVirtualAddress(nullptr)
     , m_pPhysicalMemoryObject(nullptr)
    {
    }

    PhysicalMemoryMapInfo(PhysicalMemoryMapType type, void* pVirtualAddress, void* pPhysicalMemoryObject)
        : m_mapType(type)
        , m_pVirtualAddress(pVirtualAddress)
        , m_pPhysicalMemoryObject(pPhysicalMemoryObject)
    {
    }

    ~PhysicalMemoryMapInfo() = default;

    // Copy constructor
    PhysicalMemoryMapInfo(const PhysicalMemoryMapInfo& other)
        : m_mapType(other.getMapType())
        , m_pVirtualAddress(other.getVirtualAddress())
        , m_pPhysicalMemoryObject(other.getPhysicalMemoryObject())
    {
    }

    // Copy assignment operator
    PhysicalMemoryMapInfo& operator=(const PhysicalMemoryMapInfo& other)
    {
        if (this != &other)
        {
            m_mapType = other.getMapType();
            m_pVirtualAddress = other.getVirtualAddress();
            m_pPhysicalMemoryObject = other.getPhysicalMemoryObject();
        }

        return *this;
    }
    // Move constructor
    PhysicalMemoryMapInfo(PhysicalMemoryMapInfo&& other) noexcept
        : m_mapType(other.getMapType())
        , m_pVirtualAddress(other.getVirtualAddress())
        , m_pPhysicalMemoryObject(other.getPhysicalMemoryObject())
    {
        // Reset the moved-from object
        other.setMapType(PHYSICAL_MEMORY_MAP_TYPE_MAX);
        other.setVirtualAddress(nullptr);
        other.setPhysicalMemoryObject(nullptr);
    }

    // Move assignment operator
    PhysicalMemoryMapInfo& operator=(PhysicalMemoryMapInfo&& other) noexcept
    {
        if (this != &other)
        {
            // Move the data
            m_mapType = other.getMapType();
            m_pVirtualAddress = other.getVirtualAddress();
            m_pPhysicalMemoryObject = other.getPhysicalMemoryObject();

            // Reset the moved-from object
            other.setVirtualAddress(nullptr);
            other.setPhysicalMemoryObject(nullptr);
        }

        return *this;
    }

    inline PhysicalMemoryMapType getMapType() const { return m_mapType; }
    inline void setMapType(PhysicalMemoryMapType type) { m_mapType = type; }
    inline void* getVirtualAddress() const { return m_pVirtualAddress; }
    inline void setVirtualAddress(void* pVirtualAddress) { m_pVirtualAddress = pVirtualAddress; }
    inline void* getPhysicalMemoryObject() const { return m_pPhysicalMemoryObject; }
    inline void setPhysicalMemoryObject(void* pPhysicalMemoryObject) { m_pPhysicalMemoryObject = pPhysicalMemoryObject; }
private:
        // The type of mapping
    PhysicalMemoryMapType m_mapType;

    // The virtual address of the mapped physical memory
    void* m_pVirtualAddress;

    // Pointer to the physical memory object
    void* m_pPhysicalMemoryObject;
};

enum PhysicalMemoryType
{
    KFE_PHYSICAL_MEMORY_TYPE_DEVICE   = 0x0,
    KFE_PHYSICAL_MEMORY_TYPE_PINNED   = 0x1,
    KFE_PHYSICAL_MEMORY_TYPE_PAGEABLE = 0x2,
    KFE_PHYSICAL_MEMORY_TYPE_MAX,
};

// Base class for physical memory objects
class PhysicalMemoryObject
{
public:
    PhysicalMemoryObject(Context* pContext, unsigned long long size, unsigned long long pageSize,
                         unsigned long long flags, int memoryObjectId, PhysicalMemoryType type);
    ~PhysicalMemoryObject();

    // Pure virtual functions that derived classes must implement
    virtual kfeResult init() = 0;

    // get the unique ID for this physical memory object
    inline int getId() const { return m_id; }

    // Get the size of the physical memory object
    inline size_t getSize() const { return m_size; }

    // Get the page size of the physical memory object
    inline unsigned long long getPageSize() const { return m_pageSize; }

    // Get the physical memory handle of the physical memory object
    inline PhysicalMemoryType getType() const { return m_type; }

    // Get the device mapped handle
    inline PhysicalMemoryMapInfo* getDeviceMappedHandle() const { return m_pDeviceMappedHandle; }
    // Set the device mapped handle
    inline void setDeviceMappedHandle(PhysicalMemoryMapInfo* pDeviceMappedHandle)
    {
        m_pDeviceMappedHandle = pDeviceMappedHandle;
    }

protected:
    // Protected members accessible by derived classes
    Context* m_context;
    unsigned long long m_size;
    unsigned long long m_pageSize;
    unsigned long long m_flags;
    int m_id;
    PhysicalMemoryType m_type;
    PhysicalMemoryMapInfo* m_pDeviceMappedHandle;
};

// Device physical memory object - memory allocated on GPU device
class DevicePhysicalMemoryObject : public PhysicalMemoryObject
{
public:
    DevicePhysicalMemoryObject(Context* pContext, unsigned long long size, unsigned long long pageSize,
                               unsigned long long flags, int memoryObjectId);
    ~DevicePhysicalMemoryObject();

    // Implement pure virtual functions
    virtual kfeResult init() override;

    // Device-specific getter
    inline void* getPhysicalMemoryHandle() const { return m_pDeviceMemoryHandle; }

    // Get the host mapped handle
    inline PhysicalMemoryMapInfo* getHostMappedHandle() const { return m_pHostMappedHandle; }
    // Set the host mapped handle
    inline void setHostMappedHandle(PhysicalMemoryMapInfo* pHostMappedHandle)
    {
        m_pHostMappedHandle = pHostMappedHandle;
    }

private:
    // The device memory handle
    void* m_pDeviceMemoryHandle;

    // The host mapped handle
    PhysicalMemoryMapInfo* m_pHostMappedHandle;
};

// Host pinned memory object - memory pinned in host RAM for fast GPU access
class HostPinnedMemoryObject : public PhysicalMemoryObject
{
public:
    HostPinnedMemoryObject(Context* pContext, unsigned long long size, unsigned long long pageSize,
                           unsigned long long flags, int memoryObjectId);
    ~HostPinnedMemoryObject();

    // Implement pure virtual functions
    virtual kfeResult init() override;

    // Pinned-specific getter
    inline void* getPhysicalMemoryHandle() const { return m_pHostPinnedMemoryHandle; }
    inline const std::string& getShmName() const { return m_shmName; }
    inline int getShmFd() const { return m_shmFd; }
private:
    // The host pinned memory handle
    void* m_pHostPinnedMemoryHandle;

    // The name of the shared memory
    std::string m_shmName;

    // The shared memory file descriptor
    int m_shmFd;
};

// Pageable memory object - regular host memory that can be paged
class PageableMemoryObject : public PhysicalMemoryObject
{
public:
    PageableMemoryObject(Context* pContext, unsigned long long size, unsigned long long pageSize,
                         unsigned long long flags, int memoryObjectId, void* pHostVirtualAddress);
    ~PageableMemoryObject();

    // Implement pure virtual functions
    virtual kfeResult init() override;

    // Methods to access pageable memory
    kfeResult readMemory(void* pLocalBuf, unsigned long long offset, unsigned long long size);
    kfeResult writeMemory(const void* pLocalBuf, unsigned long long offset, unsigned long long size);

    // Pageable-specific getters
    inline void* getPhysicalMemoryHandle() const { return m_pHostPageableMemoryHandle; }
    inline unsigned long long getPageableMemoryOffset() const { return m_pageableMemoryOffset; }

private:
    // The host pageable memory handle
    void* m_pHostPageableMemoryHandle;

    // The user's original host virtual address
    void* m_pUserHostVirtualAddress;

    // The offset within the pageable memory object
    unsigned long long m_pageableMemoryOffset;

    // The client PID that registered this pageable memory
    util::OsPid m_clientPid;
};

} // namespace kfe

#endif // PHYSICAL_MEMORY_OBJECT_H_