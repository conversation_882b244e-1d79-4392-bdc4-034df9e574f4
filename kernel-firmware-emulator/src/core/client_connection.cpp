#include "client_connection.h"
#include "process_sync_manager.h"
#include "server.h"
#include "os.h"

#include <arpa/inet.h>
#include <fcntl.h>
#include <signal.h>
#include <sys/mman.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/un.h>
#include <unistd.h>
#include <vector>
#include <iostream>
#include <cstring>
#include <cstdlib>
#include <cerrno>
#include <cassert>
#include <thread>
#include <chrono>
#include <sys/wait.h>

namespace kfe
{
extern "C" void runServerMain(const std::string& socketPath,
                              const std::string& lockFilePath,
                              const std::string& pidFilePath);

// ====================================================================================================================
ClientConnection::ClientConnection()
    : m_mutex()
    , m_initialized(false)
    , m_clientId(0)
    , m_nextRequestId(1)
    , m_socketMutex()
    , m_socketPath{"/tmp/kernel_firmware_emulator.sock"}
    , m_socketFd(-1)
    , m_connected(false)
    , m_running(false)
    , m_serverLockFilePath{"/tmp/kernel_firmware_emulator.lock"}
    , m_serverPidFilePath{"/tmp/kernel_firmware_emulator.pid"}
    , m_heartbeatThread()
    , m_hostPinnedMemoryMutex()
    , m_hostPinnedMemoryMap()
{
    m_syncManager = std::unique_ptr<ProcessSyncManager>(new(std::nothrow) ProcessSyncManager(m_serverLockFilePath));
    if (m_syncManager == nullptr)
    {
        fprintf(stderr, "Failed to create process sync manager.\n");
        throw std::runtime_error("Failed to create process sync manager");
    }
}

// =====================================================================================================================
ClientConnection::~ClientConnection()
{
    if (m_initialized.load())
    {
        finalize();
    }
}

// =====================================================================================================================
kfeResult ClientConnection::connect()
{
    fprintf(stdout, "[Client] Attempting to connect to GPU server...\n");

    if (m_syncManager->acquireExclusiveLock())
    {
        // We acquired the lock, which means it is the first client process trying to connect.
        // This indicates that the server is not running, so we need to start it.
        fprintf(stdout, "[Client] Checking if server exists...\n");

        // Check if the server is running by querying the pid file
        if (!isServerRunning())
        {
            fprintf(stdout, "[Client] Server is not running, starting server...\n");

            // Start the server process
            if (!startServerProcess())
            {
                fprintf(stderr, "Failed to start server process.\n");
                return KFE_ERROR_NOT_INITIALIZED;
            }
        }
        else
        {
            // Release the lock immediately as we are just checking
            m_syncManager->releaseLock();
        }
    }

    // Try to connect to the server multiple times
    for (int i = 0; i < CONNECT_RETRY_COUNT; ++i)
    {
        if (connectToServer())
        {
            fprintf(stdout, "[Client] Connected to GPU server successfully.\n");

            // Register the client with the server
            kfeResult result = registerClient();
            if (result != KFE_SUCCESS)
            {
                fprintf(stderr, "Failed to register client with server: %d\n", result);
                return result;
            }

            // Set the flags
            m_connected = true;
            m_running = true;

            // Start the heartbeat thread
            m_heartbeatThread = std::thread(&ClientConnection::heartbeatWorker, this);

            return KFE_SUCCESS;
        }

        fprintf(stderr, "[Client] Failed to connect to GPU server, retrying in %d ms...\n", CONNECT_RETRY_DELAY_MS);
        std::this_thread::sleep_for(std::chrono::milliseconds(CONNECT_RETRY_DELAY_MS));
    }

    fprintf(stderr, "[Client] Failed to connect to GPU server after %d attempts.\n", CONNECT_RETRY_COUNT);

    return KFE_ERROR_NOT_INITIALIZED;
}

// =====================================================================================================================
bool ClientConnection::isServerRunning()
{
    // Check the pid file to see if the server is running
    int pidFd = open(m_serverPidFilePath.c_str(), O_RDONLY);
    if (pidFd < 0)
    {
        return false;
    }

    char pidStr[32];
    ssize_t bytes = read(pidFd, pidStr, sizeof(pidStr) - 1);
    close(pidFd);

    if (bytes < 0)
    {
        fprintf(stderr, "Failed to read PID file '%s': %s\n", m_serverPidFilePath.c_str(), strerror(errno));
        return false;
    }

    pidStr[bytes] = '\0';
    pid_t serverPid = atoi(pidStr);

    // Check if the process exists.
    bool isRunning = (kill(serverPid, 0) == 0) ? true : false;

    // Clean up the pid file if the server is not running
    if (!isRunning)
    {
        fprintf(stdout, "Cleaning up stale PID file '%s'.\n", m_serverPidFilePath.c_str());
        unlink(m_serverPidFilePath.c_str());
    }

    fprintf(stdout, "Server PID: %d, is running: %s\n", serverPid, isRunning ? "yes" : "no");

    return isRunning;
}

// =====================================================================================================================
bool ClientConnection::connectToServer()
{
    // Create a socket to connect to the server
    m_socketFd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (m_socketFd < 0)
    {
        fprintf(stderr, "Failed to create socket: %s\n", strerror(errno));
        return false;
    }

    // Set the socket to non-blocking mode
    int flags = fcntl(m_socketFd, F_GETFL, 0);
    if (fcntl(m_socketFd, F_SETFL, flags | O_NONBLOCK) < 0)
    {
        fprintf(stderr, "Failed to set socket to non-blocking mode: %s\n", strerror(errno));
        close(m_socketFd);
        m_socketFd = -1;
        return false;
    }

    // Connect to the server socket
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, m_socketPath.c_str(), sizeof(addr.sun_path) - 1);

    int result = ::connect(m_socketFd, (struct sockaddr*)&addr, sizeof(addr));
    if (result < 0 && errno != EINPROGRESS)
    {
        fprintf(stderr, "Failed to connect to server: %s\n", strerror(errno));
        close(m_socketFd);
        m_socketFd = -1;
        return false;
    }

    // Connection is in progress, wait for it to complete
    fd_set writeSet;
    FD_ZERO(&writeSet);
    FD_SET(m_socketFd, &writeSet);

    struct timeval timeout;
    timeout.tv_sec = 1;
    timeout.tv_usec = 0;

    result = select(m_socketFd + 1, nullptr, &writeSet, nullptr, &timeout);
    if (result <= 0)
    {
        fprintf(stderr, "Connection timed out or error occurred: %s\n", strerror(errno));
        close(m_socketFd);
        m_socketFd = -1;
        return false;
    }

    // Check if the socket is successfully connected
    int soError;
    socklen_t len = sizeof(soError);
    if (getsockopt(m_socketFd, SOL_SOCKET, SO_ERROR, &soError, &len) < 0)
    {
        fprintf(stderr, "Failed to get socket error: %s\n", strerror(errno));
        close(m_socketFd);
        m_socketFd = -1;
        return false;
    }

    // Reset the socket to blocking mode
    if (fcntl(m_socketFd, F_SETFL, flags & ~O_NONBLOCK) < 0)
    {
        fprintf(stderr, "Failed to reset socket to blocking mode: %s\n", strerror(errno));
        close(m_socketFd);
        m_socketFd = -1;
        return false;
    }

    return true;
}

// =====================================================================================================================
bool ClientConnection::startServerProcess()
{
    fprintf(stdout, "Starting new server process...\n");

    pid_t pid = fork();
    if (pid == -1)
    {
        // Failed to fork
        fprintf(stderr, "Failed to fork server process: %s\n", strerror(errno));
        m_syncManager->releaseLock();
        return false;
    }
    else if (pid == 0)
    {
        // Child process: running the server

        // Write the PID file
        int pidFd = open(m_serverPidFilePath.c_str(), O_CREAT | O_WRONLY | O_TRUNC, 0666);
        if (pidFd >= 0)
        {
            char pidStr[32];
            snprintf(pidStr, sizeof(pidStr), "%d", getpid());
            ssize_t bytesWritten = write(pidFd, pidStr, strlen(pidStr));
            if ((bytesWritten < 0) || (bytesWritten != (ssize_t)strlen(pidStr)))
            {
                // Log error but continue - PID file write failure is not critical.
                fprintf(stderr, "Warning: Failed to write PID file: %s\n", strerror(errno));
            }
            close(pidFd);
        }

        // Create a new session to detach from the terminal
        setsid();

        // Redirect standard output, and error to /tmp/kfe_server.log
        int logFd = open("/tmp/kfe_server.log", O_CREAT | O_WRONLY | O_APPEND, 0644);
        if (logFd != -1)
        {
            dup2(logFd, STDOUT_FILENO);
            dup2(logFd, STDERR_FILENO);
            close(logFd);
        }

        // Redirect standard input
        // This prevents the server from being affected by the terminal's input/output
        // and allows it to run in the background without any terminal interaction.
        int nullFd = open("/dev/null", O_RDWR);
        if (nullFd != -1)
        {
            dup2(nullFd, STDIN_FILENO);
            close(nullFd);
        }

        // Unlock the server lock to allow other clients to connect
        m_syncManager->releaseLock();

        // Run the server main function
        // This function is defined in server.cpp
        runServerMain(m_socketPath, m_serverLockFilePath, m_serverPidFilePath);

        // Exit the child process
        // If the server exits, we should not return to the parent process
        // This is a safeguard to ensure that the child process does not return to the parent.
        _exit(0);
    }
    else
    {
        // Parent process: waiting for the server to start
        fprintf(stdout, "Server process started with PID: %d\n", pid);

        // Wait for a short period to allow the server to start
        std::this_thread::sleep_for(std::chrono::milliseconds(SERVER_STARTUP_DELAY_MS));

        // Release the lock to allow other clients to connect
        m_syncManager->releaseLock();

        // Check if the server is started
        int status;
        pid_t result = waitpid(pid, &status, WNOHANG);
        if (result == 0)
        {
            // Server is still running, assume it started successfully
            fprintf(stdout, "Server process is running.\n");
            return true;
        }
        else if (result == pid)
        {
            // Server process has exited
            fprintf(stderr, "Server process exited immediately.\n");
            return false;
        }
        else
        {
            // Something went wrong.
            fprintf(stderr, "[Client] waitpid error: %s\n", strerror(errno));
            return false;
        }
    }

    return true;
}

// =====================================================================================================================
kfeResult ClientConnection::registerClient()
{
    kfeResult result = KFE_SUCCESS;

    // Send the initial request
    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_INITIALIZE);

    auto initReq = request.mutable_initialize();
    initReq->set_version(KFE_API_VERSION);
    initReq->set_client_name(getProcessName());

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to register client: %d\n", result);
        return result;
    }

    assert((result == KFE_SUCCESS) && (response.result() == KFE_SUCCESS));
    m_clientId = response.initialize().client_id();

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult ClientConnection::unregisterClient()
{
    kfeResult result = KFE_SUCCESS;

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_FINALIZE);
    request.set_client_id(m_clientId);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to finalize client: %d\n", result);
        return result;
    }

    return KFE_SUCCESS;
}

// ====================================================================================================================
void ClientConnection::heartbeatWorker()
{
    fprintf(stdout, "[Client] Starting heartbeat thread...\n");

    while (m_running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(HEARTBEAT_INTERVAL_MS));

        if (m_connected)
        {
            KfeRequest request;
            request.set_request_id(getNextRequestId());
            request.set_type(KFE_REQUEST_TYPE_HEART_BEAT);
            request.set_client_id(m_clientId);

            auto heartbeat = request.mutable_heart_beat();
            heartbeat->set_timestamp(
                std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now().time_since_epoch()).count());

            KfeResponse response;
            kfeResult result = sendRequest(request, response);
            if (result != KFE_SUCCESS)
            {
                fprintf(stderr, "[Client] Heartbeat failed, connection lost %d.\n", result);
                m_connected = false;
                break;
            }
        }
    }

    fprintf(stdout, "[Client] Heartbeat thread stopped\n");
}

// =====================================================================================================================
std::string ClientConnection::getProcessName()
{
    char path[256];
    char name[256];
    snprintf(path, sizeof(path), "/proc/%d/exe", getpid());

    ssize_t len = readlink(path, name, sizeof(name) - 1);
    if (len > 0)
    {
        name[len] = '\0';
        char* base = strrchr(name, '/');
        return base ? base + 1 : name;
    }

    return "unknown";
}

// =====================================================================================================================
kfeResult ClientConnection::sendRequest(const KfeRequest& request, KfeResponse& response)
{
    if (!m_initialized.load() && request.type() != KFE_REQUEST_TYPE_INITIALIZE)
    {
        fprintf(stderr, "ClientConnection is not initialized. Cannot send request.\n");
        return KFE_ERROR_NOT_INITIALIZED;
    }

    {
        std::lock_guard<std::mutex> lock(m_socketMutex);

        // Serialize the request
        std::string serialized;
        if (!request.SerializeToString(&serialized))
        {
            fprintf(stderr, "Failed to serialize request.\n");
            return KFE_ERROR_NOT_INITIALIZED;
        }

        uint32_t size = htonl(serialized.size());

        // Send the request length
        if (!sendRawData(&size, sizeof(size)))
        {
            fprintf(stderr, "Failed to send request size.\n");
            return KFE_ERROR_INVALID_VALUE;
        }

        // Send the request data
        if (!sendRawData(serialized.c_str(), serialized.size()))
        {
            fprintf(stderr, "Failed to send request data.\n");
            return KFE_ERROR_INVALID_VALUE;
        }

        // Receive the response size
        uint32_t responseSize;
        if (!recvRawData(&responseSize, sizeof(responseSize)))
        {
            fprintf(stderr, "Failed to receive response size.\n");
            return KFE_ERROR_INVALID_VALUE;
        }

        responseSize = ntohl(responseSize);
        if (responseSize > 1024 * 1024)
        {
            fprintf(stderr, "Response size too large: %d\n", responseSize);
            return KFE_ERROR_INVALID_VALUE;
        }

        // Receive the response data
        std::vector<char> buffer(responseSize);
        if (!recvRawData(buffer.data(), responseSize))
        {
            fprintf(stderr, "Failed to receive response data.\n");
            return KFE_ERROR_INVALID_VALUE;
        }

        // Parse the response
        if (!response.ParseFromArray(buffer.data(), responseSize))
        {
            fprintf(stderr, "Failed to parse response.\n");
            return KFE_ERROR_INVALID_VALUE;
        }
    }

    return KFE_SUCCESS;
}

// =====================================================================================================================
bool ClientConnection::sendRawData(const void* data, size_t size)
{
    const char* ptr = static_cast<const char*>(data);
    size_t remaining = size;

    while (remaining > 0)
    {
        ssize_t sent = send(m_socketFd, ptr, remaining, MSG_NOSIGNAL);
        if (sent == -1)
        {
            if (errno == EINTR)
            {
                continue;
            }
            else if (errno == EAGAIN || errno == EWOULDBLOCK)
            {
                // Non-blocking socket, try again later
                std::this_thread::sleep_for(std::chrono::microseconds(100));
                continue;
            }
            else if (errno == EPIPE)
            {
                fprintf(stderr, "Connection broken while sending data\n");
                return false;
            }

            fprintf(stderr, "Failed to send data: %s\n", strerror(errno));
            return false;
        }

        ptr += sent;
        remaining -= sent;
    }

    return true;
}

// =====================================================================================================================
bool ClientConnection::recvRawData(void* data, size_t size)
{
    char* ptr = static_cast<char*>(data);
    size_t remaining = size;

    while (remaining > 0)
    {
        ssize_t received = recv(m_socketFd, ptr, remaining, 0);
        if (received == -1)
        {
            if (errno == EINTR)
            {
                continue;
            }
            else if (errno == EAGAIN || errno == EWOULDBLOCK)
            {
                // Non-blocking socket, try again later
                std::this_thread::sleep_for(std::chrono::microseconds(100));
                continue;
            }

            return false;
        }

        if (received == 0)
        {
            // Connection closed by the server
            return false;
        }

        ptr += received;
        remaining -= received;
    }

    return true;
}

// =====================================================================================================================
kfeResult ClientConnection::initialize()
{
    kfeResult result = KFE_SUCCESS;

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_initialized.load())
    {
        return KFE_SUCCESS;
    }

    // Check for environment variable to override socket path
    const char* pEnvSocket = getenv("KFE_SOCKET_PATH");
    if (pEnvSocket)
    {
        m_socketPath = pEnvSocket;
        std::string lockFilePath = m_socketPath;
        std::string pidFilePath = m_socketPath;
        size_t sockPos = lockFilePath.find(".sock");
        if (sockPos != std::string::npos)
        {
            // Replace .sock with .lock
            // No need for client lock files anymore since we use std::mutex
            m_serverLockFilePath = lockFilePath.substr(0, sockPos) + ".lock";
            m_serverPidFilePath = pidFilePath.substr(0, sockPos) + ".pid";
        }
        else
        {
            // Fallback: just append .lock
            m_serverLockFilePath = lockFilePath + ".lock";
            m_serverPidFilePath = pidFilePath + ".pid";
        }
    }

    // Try to connect to the existing server
    result = connect();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to connect to server: %d\n", result);
        return result;
    }

    // Initialize flags
    m_initialized = true;

    fprintf(stdout, "Client initialized successfully, client id: %llu\n", m_clientId);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult ClientConnection::finalize()
{
    kfeResult result = KFE_SUCCESS;

    std::lock_guard<std::mutex> lock(m_mutex);
    if (!m_initialized.load())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    m_connected = false;
    m_running = false;

    // Stop the heartbeat thread
    if (m_heartbeatThread.joinable())
    {
        m_heartbeatThread.join();
    }

    unregisterClient();

    {
        std::lock_guard<std::mutex> memLock(m_hostPinnedMemoryMutex);
        m_hostPinnedMemoryMap.clear();
    }

    if (m_socketFd >= 0)
    {
        close(m_socketFd);
        m_socketFd = -1;
    }

    m_initialized = false;

    std::cout << "Client finalized" << std::endl;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult ClientConnection::getDeviceCount(int* pCount)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_DEVICE_GET_COUNT);
    request.set_client_id(m_clientId);
    request.mutable_device_get_count();

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to get device count: %d\n", result);
        return result;
    }

    *pCount = response.device_get_count().device_count();

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::openDevice(kfeDevice* pDevice, int gpuId)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_DEVICE_OPEN);
    request.set_client_id(m_clientId);

    auto openReq = request.mutable_device_open();
    openReq->set_gpu_id(gpuId);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to open device: %d\n", result);
        return result;
    }

    *pDevice = reinterpret_cast<kfeDevice>(response.device_open().device_handle());

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::closeDevice(kfeDevice device)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_DEVICE_CLOSE);
    request.set_client_id(m_clientId);

    auto closeReq = request.mutable_device_close();
    closeReq->set_device_handle(reinterpret_cast<unsigned long long>(device));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to close device: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::getDeviceProperties(kfeDevice device, kfeDeviceProperties* pProperties)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_DEVICE_GET_PROPERTIES);
    request.set_client_id(m_clientId);

    auto propertiesReq = request.mutable_device_get_properties();
    propertiesReq->set_device_handle(reinterpret_cast<unsigned long long>(device));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to get device properties: %d\n", result);
        memset(pProperties, 0, sizeof(kfeDeviceProperties));
        return result;
    }

    const auto& props = response.device_get_properties().properties();
    strncpy(pProperties->name, props.name().c_str(), sizeof(pProperties->name) - 1);
    pProperties->name[sizeof(pProperties->name) - 1] = '\0';
    memcpy(pProperties->uuid, props.uuid().data(), sizeof(pProperties->uuid));
    pProperties->totalGlobalMem = props.total_global_mem();
    pProperties->sharedMemPerBlock = props.shared_mem_per_block();
    pProperties->regsPerBlock = props.regs_per_block();
    pProperties->warpSize = props.warp_size();
    pProperties->memPitch = props.mem_pitch();
    pProperties->maxThreadsPerBlock = props.max_threads_per_block();
    pProperties->maxThreadsDim[0] = props.max_threads_dim().x();
    pProperties->maxThreadsDim[1] = props.max_threads_dim().y();
    pProperties->maxThreadsDim[2] = props.max_threads_dim().z();
    pProperties->maxGridSize[0] = props.max_grid_size().x();
    pProperties->maxGridSize[1] = props.max_grid_size().y();
    pProperties->maxGridSize[2] = props.max_grid_size().z();
    pProperties->clockRate = props.clock_rate();
    pProperties->totalConstMem = props.total_const_mem();
    pProperties->major = props.major();
    pProperties->minor = props.minor();
    pProperties->textureAlignment = props.texture_alignment();
    pProperties->texturePitchAlignment = props.texture_pitch_alignment();
    pProperties->deviceOverlap = props.device_overlap();
    pProperties->multiProcessorCount = props.multi_processor_count();
    pProperties->kernelExecTimeoutEnabled = props.kernel_exec_timeout_enabled();
    pProperties->integrated = props.integrated();
    pProperties->canMapHostMemory = props.can_map_host_memory();
    pProperties->computeMode = props.compute_mode();
    pProperties->maxTexture1D = props.max_texture_1d();
    pProperties->maxTexture1DMipmap = props.max_texture_1d_mipmap();
    pProperties->maxTexture1DLinear = props.max_texture_1d_linear();
    pProperties->maxTexture2D[0] = props.max_texture_2d().x();
    pProperties->maxTexture2D[1] = props.max_texture_2d().y();
    pProperties->maxTexture2DMipmap[0] = props.max_texture_2d_mipmap().x();
    pProperties->maxTexture2DMipmap[1] = props.max_texture_2d_mipmap().y();
    pProperties->maxTexture2DLinear[0] = props.max_texture_2d_linear().x();
    pProperties->maxTexture2DLinear[1] = props.max_texture_2d_linear().y();
    pProperties->maxTexture2DLinear[2] = props.max_texture_2d_linear().z();
    pProperties->maxTexture2DGather[0] = props.max_texture_2d_gather().x();
    pProperties->maxTexture2DGather[1] = props.max_texture_2d_gather().y();
    pProperties->maxTexture3D[0] = props.max_texture_3d().x();
    pProperties->maxTexture3D[1] = props.max_texture_3d().y();
    pProperties->maxTexture3D[2] = props.max_texture_3d().z();
    pProperties->maxTexture3DAlt[0] = props.max_texture_3d_alt().x();
    pProperties->maxTexture3DAlt[1] = props.max_texture_3d_alt().y();
    pProperties->maxTexture3DAlt[2] = props.max_texture_3d_alt().z();
    pProperties->maxTextureCubemap = props.max_texture_cubemap();
    pProperties->maxTexture1DLayered[0] = props.max_texture_1d_layered().x();
    pProperties->maxTexture1DLayered[1] = props.max_texture_1d_layered().y();
    pProperties->maxTexture2DLayered[0] = props.max_texture_2d_layered().x();
    pProperties->maxTexture2DLayered[1] = props.max_texture_2d_layered().y();
    pProperties->maxTexture2DLayered[2] = props.max_texture_2d_layered().z();
    pProperties->maxTextureCubemapLayered[0] = props.max_texture_cubemap_layered().x();
    pProperties->maxTextureCubemapLayered[1] = props.max_texture_cubemap_layered().y();
    pProperties->maxSurface1D = props.max_surface_1d();
    pProperties->maxSurface2D[0] = props.max_surface_2d().x();
    pProperties->maxSurface2D[1] = props.max_surface_2d().y();
    pProperties->maxSurface3D[0] = props.max_surface_3d().x();
    pProperties->maxSurface3D[1] = props.max_surface_3d().y();
    pProperties->maxSurface3D[2] = props.max_surface_3d().z();
    pProperties->maxSurface1DLayered[0] = props.max_surface_1d_layered().x();
    pProperties->maxSurface1DLayered[1] = props.max_surface_1d_layered().y();
    pProperties->maxSurface2DLayered[0] = props.max_surface_2d_layered().x();
    pProperties->maxSurface2DLayered[1] = props.max_surface_2d_layered().y();
    pProperties->maxSurface2DLayered[2] = props.max_surface_2d_layered().z();
    pProperties->maxSurfaceCubemap = props.max_surface_cubemap();
    pProperties->maxSurfaceCubemapLayered[0] = props.max_surface_cubemap_layered().x();
    pProperties->maxSurfaceCubemapLayered[1] = props.max_surface_cubemap_layered().y();
    pProperties->surfaceAlignment = props.surface_alignment();
    pProperties->concurrentKernels = props.concurrent_kernels();
    pProperties->ECCEnabled = props.ecc_enabled();
    pProperties->pciBusID = props.pci_bus_id();
    pProperties->pciDeviceID = props.pci_device_id();
    pProperties->pciDomainID = props.pci_domain_id();
    pProperties->tccDriver = props.tcc_driver();
    pProperties->asyncEngineCount = props.async_engine_count();
    pProperties->unifiedAddressing = props.unified_addressing();
    pProperties->memoryClockRate = props.memory_clock_rate();
    pProperties->memoryBusWidth = props.memory_bus_width();
    pProperties->l2CacheSize = props.l2_cache_size();
    pProperties->persistingL2CacheMaxSize = props.persisting_l2_cache_max_size();
    pProperties->maxThreadsPerMultiProcessor = props.max_threads_per_multi_processor();
    pProperties->streamPrioritiesSupported = props.stream_priorities_supported();
    pProperties->globalL1CacheSupported = props.global_l1_cache_supported();
    pProperties->localL1CacheSupported = props.local_l1_cache_supported();
    pProperties->sharedMemPerMultiprocessor = props.shared_mem_per_multiprocessor();
    pProperties->regsPerMultiprocessor = props.regs_per_multiprocessor();
    pProperties->managedMemory = props.managed_memory();
    pProperties->isMultiGpuBoard = props.is_multi_gpu_board();
    pProperties->multiGpuBoardGroupID = props.multi_gpu_board_group_id();
    pProperties->hostNativeAtomicSupported = props.host_native_atomic_supported();
    pProperties->singleToDoublePrecisionPerfRatio = props.single_to_double_precision_perf_ratio();
    pProperties->pageableMemoryAccess = props.pageable_memory_access();
    pProperties->concurrentManagedAccess = props.concurrent_managed_access();
    pProperties->computePreemptionSupported = props.compute_preemption_supported();
    pProperties->canUseHostPointerForRegisteredMem = props.can_use_host_pointer_for_registered_mem();
    pProperties->cooperativeLaunch = props.cooperative_launch();
    pProperties->cooperativeMultiDeviceLaunch = props.cooperative_multi_device_launch();
    pProperties->sharedMemPerBlockOptin = props.shared_mem_per_block_optin();
    pProperties->pageableMemoryAccessUsesHostPageTables = props.pageable_memory_access_uses_host_page_tables();
    pProperties->directManagedMemAccessFromHost = props.direct_managed_mem_access_from_host();
    pProperties->maxBlocksPerMultiProcessor = props.max_blocks_per_multiprocessor();
    pProperties->accessPolicyMaxWindowSize = props.access_policy_max_window_size();
    pProperties->reservedSharedMemPerBlock = props.reserved_shared_mem_per_block();
    pProperties->hostRegisterSupported = props.host_register_supported();
    pProperties->sparseCudaArraySupported = props.sparse_cuda_array_supported();
    pProperties->hostRegisterReadOnlySupported = props.host_register_read_only_supported();
    pProperties->timelineSemaphoreInteropSupported = props.timeline_semaphore_interop_supported();
    pProperties->memoryPoolsSupported = props.memory_pools_supported();
    pProperties->gpuDirectRDMASupported = props.gpu_direct_rdma_supported();
    pProperties->gpuDirectRDMAFlushWritesOptions = props.gpu_direct_rdma_flush_writes_options();
    pProperties->gpuDirectRDMAWritesOrdering = props.gpu_direct_rdma_writes_ordering();
    pProperties->memoryPoolSupportedHandleTypes = props.memory_pool_supported_handle_types();
    pProperties->deferredMappingCudaArraySupported = props.deferred_mapping_cuda_array_supported();
    pProperties->ipcEventSupported = props.ipc_event_supported();
    pProperties->unifiedFunctionPointers = props.unified_function_pointers();
    pProperties->physicalMemoryAlignment = props.physical_memory_alignment();

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::createContext(kfeContext* pContext, unsigned int flags, kfeDevice device)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_CONTEXT_CREATE);
    request.set_client_id(m_clientId);

    auto createReq = request.mutable_context_create();
    createReq->set_device_handle(reinterpret_cast<unsigned long long>(device));
    createReq->set_flags(flags);
    createReq->set_pid(util::getProcessId());

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        *pContext = nullptr;
        fprintf(stderr, "Failed to create context: %d\n", result);
        return result;
    }

    *pContext = reinterpret_cast<kfeContext>(response.context_create().context_handle());

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::destroyContext(kfeContext context)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_CONTEXT_DESTROY);
    request.set_client_id(m_clientId);

    auto destroyReq = request.mutable_context_destroy();
    destroyReq->set_context_handle(reinterpret_cast<unsigned long long>(context));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to destroy context: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::createPhysicalMemory(kfeContext context, unsigned long long size,
                                                 unsigned long long pageSize, unsigned long long flags,
                                                 kfePhysicalMemoryObject* pMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_CREATE);
    request.set_client_id(m_clientId);

    auto createReq = request.mutable_physical_memory_create();
    createReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    createReq->set_size(size);
    createReq->set_page_size(pageSize);
    createReq->set_flags(flags);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to create physical memory: %d\n", result);
        return result;
    }

    *pMemoryObject = reinterpret_cast<kfePhysicalMemoryObject>(
        response.physical_memory_create().physical_memory_handle());

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::destroyPhysicalMemory(kfeContext context, kfePhysicalMemoryObject memoryObject)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DESTROY);
    request.set_client_id(m_clientId);

    auto destroyReq = request.mutable_physical_memory_destroy();
    destroyReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    destroyReq->set_physical_memory_handle(reinterpret_cast<unsigned long long>(memoryObject));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to destroy physical memory: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::deviceMapPhysicalMemory(kfeContext context, unsigned long long virtualAddress,
                                                    kfePhysicalMemoryObject memoryObject, unsigned long long size,
                                                    unsigned long long flags, void** pPhysicalMemoryDeviceMapped)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DEVICE_MAP);
    request.set_client_id(m_clientId);

    auto mapReq = request.mutable_physical_memory_device_map();
    mapReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    mapReq->set_physical_memory_handle(reinterpret_cast<unsigned long long>(memoryObject));
    mapReq->set_size(size);
    mapReq->set_flags(flags);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to map device physical memory: %d\n", result);
        return result;
    }

    *pPhysicalMemoryDeviceMapped = reinterpret_cast<void*>(
        response.physical_memory_device_map().device_mapped_handle());

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::deviceUnmapPhysicalMemory(kfeContext context, void* pPhysicalMemoryDeviceMapped)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DEVICE_UNMAP);
    request.set_client_id(m_clientId);

    auto unmapReq = request.mutable_physical_memory_device_unmap();
    unmapReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    unmapReq->set_device_mapped_handle(reinterpret_cast<unsigned long long>(pPhysicalMemoryDeviceMapped));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to unmap device physical memory: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::hostMapPhysicalMemory(kfeContext context, kfePhysicalMemoryObject memoryObject,
                                                  unsigned long long size, unsigned long long flags,
                                                  kfePhysicalMemoryObjectHostMapped* pPhysicalMemoryHostMapped,
                                                  void** ppHostVirtualAddress)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_HOST_MAP);
    request.set_client_id(m_clientId);

    auto mapReq = request.mutable_physical_memory_host_map();
    mapReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    mapReq->set_physical_memory_handle(reinterpret_cast<unsigned long long>(memoryObject));
    mapReq->set_size(size);
    mapReq->set_flags(flags);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to map host physical memory: %d\n", result);
        return result;
    }

    *pPhysicalMemoryHostMapped = reinterpret_cast<void*>(response.physical_memory_host_map().host_mapped_handle());
    *ppHostVirtualAddress      = reinterpret_cast<void*>(response.physical_memory_host_map().host_virtual_address());

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::hostUnmapPhysicalMemory(kfeContext context,
                                                    kfePhysicalMemoryObjectHostMapped physicalMemoryHostMapped)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_HOST_UNMAP);
    request.set_client_id(m_clientId);

    auto unmapReq = request.mutable_physical_memory_host_unmap();
    unmapReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    unmapReq->set_host_mapped_handle(reinterpret_cast<unsigned long long>(physicalMemoryHostMapped));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to unmap host physical memory: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::exportPhysicalMemoryByDmaBuf(kfeContext context, kfePhysicalMemoryObject memoryObject,
                                                         unsigned long long size, unsigned long long pageSize,
                                                         unsigned long long flags, int* pDmaBufFd)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_EXPORT_BY_DMABUF);
    request.set_client_id(m_clientId);

    auto exportReq = request.mutable_physical_memory_export_by_dmabuf();
    exportReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    exportReq->set_physical_memory_handle(reinterpret_cast<unsigned long long>(memoryObject));
    exportReq->set_size(size);
    exportReq->set_page_size(pageSize);
    exportReq->set_flags(flags);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to export physical memory by DMA-BUF: %d\n", result);
        return result;
    }

    *pDmaBufFd = response.physical_memory_export_by_dmabuf().dmabuf_fd();

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::importPhysicalMemoryFromDmaBuf(kfeContext context, int dmaBufFd,
                                                           kfePhysicalMemoryObject* pMemoryObject,
                                                           unsigned long long* pSize, unsigned long long* pPageSize,
                                                           unsigned long long* pFlags)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PHYSICAL_MEMORY_IMPORT_FROM_DMABUF);
    request.set_client_id(m_clientId);

    auto importReq = request.mutable_physical_memory_import_from_dmabuf();
    importReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    importReq->set_dmabuf_fd(dmaBufFd);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to import physical memory from DMA-BUF: %d\n", result);
        return result;
    }

    *pMemoryObject = reinterpret_cast<kfePhysicalMemoryObject>(
        response.physical_memory_import_from_dmabuf().physical_memory_handle());
    *pSize         = response.physical_memory_import_from_dmabuf().size();
    *pPageSize     = response.physical_memory_import_from_dmabuf().page_size();
    *pFlags        = response.physical_memory_import_from_dmabuf().flags();

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::createPinnedMemory(kfeContext context, unsigned long long size, unsigned long long flags,
                                               kfePinnedMemoryObject* pPinnedMemoryObject, void** ppHostVirtualAddress)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PINNED_MEMORY_CREATE);
    request.set_client_id(m_clientId);

    auto createReq = request.mutable_pinned_memory_create();
    createReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    createReq->set_size(size);
    createReq->set_flags(flags);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        *pPinnedMemoryObject = nullptr;
        *ppHostVirtualAddress = nullptr;
        fprintf(stderr, "Failed to create pinned memory: %d\n", result);
        return result;
    }

    auto& pinnedMemCreateResp = response.pinned_memory_create();
    kfePinnedMemoryObject pinnedMemoryObject =
        reinterpret_cast<kfePinnedMemoryObject>(pinnedMemCreateResp.pinned_memory_handle());
    std::string pinnedMemoryName = pinnedMemCreateResp.pinned_memory_name();
    int pinnedMemoryFd = pinnedMemCreateResp.pinned_memory_fd();

    // Since host pinned memory is a shared memory in the server,
    // we need to open the shared memory here for the client.
    *ppHostVirtualAddress = util::ipcSharedMemoryOpen(pinnedMemoryName.c_str(), pinnedMemoryFd, size);
    if (*ppHostVirtualAddress == nullptr)
    {
        fprintf(stderr, "Failed to open pinned memory shared memory '%s': %s\n",
                pinnedMemoryName.c_str(), strerror(errno));
        destroyPinnedMemoryImpl(context, pinnedMemoryObject);
        *pPinnedMemoryObject = nullptr;
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    // Store the mapping for later cleanup
    auto pinnedMemoryInfo = std::make_unique<HostPinnedMemoryInfo>();
    pinnedMemoryInfo->fd = pinnedMemoryFd;
    pinnedMemoryInfo->pMappedAddress = *ppHostVirtualAddress;
    pinnedMemoryInfo->size = size;

    {
        std::lock_guard<std::mutex> lock(m_hostPinnedMemoryMutex);
        m_hostPinnedMemoryMap[pinnedMemoryObject] = std::move(pinnedMemoryInfo);
    }

    *pPinnedMemoryObject = pinnedMemoryObject;

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::destroyPinnedMemoryImpl(kfeContext context, kfePinnedMemoryObject pinnedMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PINNED_MEMORY_DESTROY);
    request.set_client_id(m_clientId);

    auto destroyReq = request.mutable_pinned_memory_destroy();
    destroyReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    destroyReq->set_pinned_memory_handle(reinterpret_cast<unsigned long long>(pinnedMemoryObject));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to destroy pinned memory: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::destroyPinnedMemory(kfeContext context, kfePinnedMemoryObject pinnedMemoryObject)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    // Cleanup the shared memory mapping
    {
        std::lock_guard<std::mutex> lock(m_hostPinnedMemoryMutex);
        auto it = m_hostPinnedMemoryMap.find(pinnedMemoryObject);
        if (it != m_hostPinnedMemoryMap.end())
        {
            util::ipcSharedMemoryClose(nullptr, it->second->fd, it->second->pMappedAddress,
                                       static_cast<size_t>(it->second->size));
            m_hostPinnedMemoryMap.erase(it);
        }
        else
        {
            fprintf(stderr, "Warning: pinned memory object not found in local map during destroy\n");
        }
    }

    return destroyPinnedMemoryImpl(context, pinnedMemoryObject);
}

// =====================================================================================================================
kfeResult ClientConnection::registerPageableMemory(kfeContext context, void* pHostVirtualAddress,
                                                   unsigned long long size, unsigned long long flags,
                                                   kfePageableMemoryHostRegistered* pPageableMemoryHostRegistered,
                                                   unsigned long long* pRegisteredOffset)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PAGEABLE_MEMORY_HOST_REGISTER);
    request.set_client_id(m_clientId);

    auto registerReq = request.mutable_pageable_memory_host_register();
    registerReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    registerReq->set_host_virtual_address(reinterpret_cast<unsigned long long>(pHostVirtualAddress));
    registerReq->set_size(size);
    registerReq->set_flags(flags);

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to host register pageable memory: %d\n", result);
        return result;
    }

    *pPageableMemoryHostRegistered = reinterpret_cast<kfePageableMemoryHostRegistered>(
        response.pageable_memory_host_register().host_registered_handle());
    *pRegisteredOffset = response.pageable_memory_host_register().registered_offset();

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::unregisterPageableMemory(kfeContext context,
                                                     kfePageableMemoryHostRegistered pageableMemoryHostRegistered)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_PAGEABLE_MEMORY_HOST_UNREGISTER);
    request.set_client_id(m_clientId);

    auto unregisterReq = request.mutable_pageable_memory_host_unregister();
    unregisterReq->set_context_handle(reinterpret_cast<unsigned long long>(context));
    unregisterReq->set_host_registered_handle(reinterpret_cast<unsigned long long>(pageableMemoryHostRegistered));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to host unregister physical memory: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::createUserModeQueue(kfeUserModeQueue* pUserModeQueue, unsigned int flags,
                                                kfeContext context)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_USER_MODE_QUEUE_CREATE);
    request.set_client_id(m_clientId);

    auto createReq = request.mutable_user_mode_queue_create();
    createReq->set_flags(flags);
    createReq->set_context_handle(reinterpret_cast<unsigned long long>(context));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        *pUserModeQueue = nullptr;
        fprintf(stderr, "Failed to create user mode queue: %d\n", result);
        return result;
    }

    *pUserModeQueue = reinterpret_cast<kfeUserModeQueue>(response.user_mode_queue_create().user_mode_queue_handle());

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::destroyUserModeQueue(kfeUserModeQueue userModeQueue)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_USER_MODE_QUEUE_DESTROY);
    request.set_client_id(m_clientId);

    auto destroyReq = request.mutable_user_mode_queue_destroy();
    destroyReq->set_user_mode_queue_handle(reinterpret_cast<unsigned long long>(userModeQueue));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to destroy user mode queue: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::submitUserModeQueue(kfeUserModeQueue userModeQueue, kfeCommandPacket* pCommandPackets,
                                                unsigned int numCmdPackets)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    fprintf(stderr, "[DEBUG] submitUserModeQueue: userModeQueue=0x%llx, numCmdPackets=%u\n",
            reinterpret_cast<unsigned long long>(userModeQueue), numCmdPackets);

    // Validate command packets before processing
    for (unsigned int i = 0; i < numCmdPackets; ++i)
    {
        fprintf(stderr, "[DEBUG] Command packet %u: type=%d, queueId=%d, numWaitFences=%d\n",
                i, pCommandPackets[i].commandPacketType, pCommandPackets[i].queueId, pCommandPackets[i].numWaitFences);

        if (pCommandPackets[i].numWaitFences > 0)
        {
            if (pCommandPackets[i].pWaitFences == nullptr)
            {
                fprintf(stderr, "[ERROR] Command packet %u has numWaitFences=%d but pWaitFences is null\n",
                        i, pCommandPackets[i].numWaitFences);
                return KFE_ERROR_INVALID_VALUE;
            }

            for (int j = 0; j < pCommandPackets[i].numWaitFences; ++j)
            {
                if (pCommandPackets[i].pWaitFences[j] == nullptr)
                {
                    fprintf(stderr, "[ERROR] Command packet %u, wait fence %d is null\n", i, j);
                    return KFE_ERROR_INVALID_VALUE;
                }
                fprintf(stderr, "[DEBUG] Command packet %u, wait fence %d: 0x%llx\n",
                        i, j, reinterpret_cast<unsigned long long>(pCommandPackets[i].pWaitFences[j]));
            }
        }

        if (pCommandPackets[i].signalFence != nullptr)
        {
            fprintf(stderr, "[DEBUG] Command packet %u, signal fence: 0x%llx\n",
                    i, reinterpret_cast<unsigned long long>(pCommandPackets[i].signalFence));
        }
    }

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_USER_MODE_QUEUE_SUBMIT);
    request.set_client_id(m_clientId);

    auto submitReq = request.mutable_user_mode_queue_submit();
    submitReq->set_user_mode_queue_handle(reinterpret_cast<unsigned long long>(userModeQueue));
    submitReq->set_num_command_packets(numCmdPackets);

    // Handle command packets and add them to the request
    for (unsigned int i = 0; i < numCmdPackets; ++i)
    {
        auto cmdPacket = submitReq->add_command_packets();

        switch (pCommandPackets[i].commandPacketType)
        {
            case KFE_COMMAND_PACKET_TYPE_KERNEL_DISPATCH:
            {
                kfeCommandPacketKernelDispatch* pKernelDispatch =
                    reinterpret_cast<kfeCommandPacketKernelDispatch*>(&pCommandPackets[i]);

                auto kernelDispatch = cmdPacket->mutable_kernel_dispatch();
                kernelDispatch->set_command_packet_type(COMMAND_PACKET_TYPE_KERNEL_DISPATCH);
                kernelDispatch->set_queue_id(pKernelDispatch->queueId);
                kernelDispatch->set_grid_dim_x(pKernelDispatch->gridDimX);
                kernelDispatch->set_grid_dim_y(pKernelDispatch->gridDimY);
                kernelDispatch->set_grid_dim_z(pKernelDispatch->gridDimZ);
                kernelDispatch->set_block_dim_x(pKernelDispatch->blockDimX);
                kernelDispatch->set_block_dim_y(pKernelDispatch->blockDimY);
                kernelDispatch->set_block_dim_z(pKernelDispatch->blockDimZ);
                kernelDispatch->set_num_wait_fences(pKernelDispatch->numWaitFences);

                // Handle the wait fences
                if (pKernelDispatch->pWaitFences != nullptr)
                {
                    for (int j = 0; j < pKernelDispatch->numWaitFences; ++j)
                    {
                        if (pKernelDispatch->pWaitFences[j] != nullptr)
                        {
                            kernelDispatch->add_wait_fence_handles(
                                reinterpret_cast<unsigned long long>(pKernelDispatch->pWaitFences[j]));
                        }
                        else
                        {
                            fprintf(stderr, "Warning: Wait fence %d is null in KERNEL_DISPATCH command\n", j);
                        }
                    }
                }
                else if (pKernelDispatch->numWaitFences > 0)
                {
                    fprintf(stderr, "Warning: pWaitFences is null but numWaitFences is %d in KERNEL_DISPATCH command\n", pKernelDispatch->numWaitFences);
                }

                // Handle the signal fence
                if (pKernelDispatch->signalFence != nullptr)
                {
                    kernelDispatch->set_signal_fence_handle(
                        reinterpret_cast<unsigned long long>(pKernelDispatch->signalFence));
                }
                else
                {
                    fprintf(stderr, "Warning: Signal fence is null in KERNEL_DISPATCH command\n");
                    kernelDispatch->set_signal_fence_handle(0);
                }
                break;
            }

            case KFE_COMMAND_PACKET_TYPE_DEBUG_ADD:
            {
                kfeCommandPacketDebugAdd* pDebugAdd =
                    reinterpret_cast<kfeCommandPacketDebugAdd*>(&pCommandPackets[i]);

                auto debugAdd = cmdPacket->mutable_debug_add();
                debugAdd->set_command_packet_type(COMMAND_PACKET_TYPE_DEBUG_ADD);
                debugAdd->set_queue_id(pDebugAdd->queueId);
                debugAdd->set_operand1(pDebugAdd->operand1);
                debugAdd->set_operand2(pDebugAdd->operand2);
                debugAdd->set_num_wait_fences(pDebugAdd->numWaitFences);

                // Handle the wait fences
                if (pDebugAdd->pWaitFences != nullptr)
                {
                    for (int j = 0; j < pDebugAdd->numWaitFences; ++j)
                    {
                        if (pDebugAdd->pWaitFences[j] != nullptr)
                        {
                            debugAdd->add_wait_fence_handles(
                                reinterpret_cast<unsigned long long>(pDebugAdd->pWaitFences[j]));
                        }
                        else
                        {
                            fprintf(stderr, "Warning: Wait fence %d is null in DEBUG_ADD command\n", j);
                        }
                    }
                }
                else if (pDebugAdd->numWaitFences > 0)
                {
                    fprintf(stderr, "Warning: pWaitFences is null but numWaitFences is %d in DEBUG_ADD command\n", pDebugAdd->numWaitFences);
                }

                // Handle the signal fence
                if (pDebugAdd->signalFence != nullptr)
                {
                    debugAdd->set_signal_fence_handle(
                        reinterpret_cast<unsigned long long>(pDebugAdd->signalFence));
                }
                else
                {
                    fprintf(stderr, "Warning: Signal fence is null in DEBUG_ADD command\n");
                    debugAdd->set_signal_fence_handle(0);
                }
                break;
            }

            case KFE_COMMAND_PACKET_TYPE_DEBUG_SUB:
            {
                kfeCommandPacketDebugSub* pDebugSub =
                    reinterpret_cast<kfeCommandPacketDebugSub*>(&pCommandPackets[i]);

                auto debugSub = cmdPacket->mutable_debug_sub();
                debugSub->set_command_packet_type(COMMAND_PACKET_TYPE_DEBUG_SUB);
                debugSub->set_queue_id(pDebugSub->queueId);
                debugSub->set_operand1(pDebugSub->operand1);
                debugSub->set_operand2(pDebugSub->operand2);
                debugSub->set_num_wait_fences(pDebugSub->numWaitFences);

                // Handle the wait fences
                if (pDebugSub->pWaitFences != nullptr)
                {
                    for (int j = 0; j < pDebugSub->numWaitFences; ++j)
                    {
                        if (pDebugSub->pWaitFences[j] != nullptr)
                        {
                            debugSub->add_wait_fence_handles(
                                reinterpret_cast<unsigned long long>(pDebugSub->pWaitFences[j]));
                        }
                        else
                        {
                            fprintf(stderr, "Warning: Wait fence %d is null in DEBUG_SUB command\n", j);
                        }
                    }
                }
                else if (pDebugSub->numWaitFences > 0)
                {
                    fprintf(stderr, "Warning: pWaitFences is null but numWaitFences is %d in DEBUG_SUB command\n", pDebugSub->numWaitFences);
                }

                // Handle the signal fence
                if (pDebugSub->signalFence != nullptr)
                {
                    debugSub->set_signal_fence_handle(
                        reinterpret_cast<unsigned long long>(pDebugSub->signalFence));
                }
                else
                {
                    fprintf(stderr, "Warning: Signal fence is null in DEBUG_SUB command\n");
                    debugSub->set_signal_fence_handle(0);
                }
                break;
            }

            default:
            {
                // For other command packet types, use generic handling
                auto common = cmdPacket->mutable_common();
                common->set_command_packet_type(static_cast<CommandPacketType>(pCommandPackets[i].commandPacketType));
                common->set_queue_id(pCommandPackets[i].queueId);
                common->set_num_wait_fences(pCommandPackets[i].numWaitFences);

                // Handle wait fences
                if (pCommandPackets[i].pWaitFences != nullptr)
                {
                    for (int j = 0; j < pCommandPackets[i].numWaitFences; ++j)
                    {
                        if (pCommandPackets[i].pWaitFences[j] != nullptr)
                        {
                            common->add_wait_fence_handles(
                                reinterpret_cast<unsigned long long>(pCommandPackets[i].pWaitFences[j]));
                        }
                        else
                        {
                            fprintf(stderr, "Warning: Wait fence %d is null in COMMON command\n", j);
                        }
                    }
                }
                else if (pCommandPackets[i].numWaitFences > 0)
                {
                    fprintf(stderr, "Warning: pWaitFences is null but numWaitFences is %d in COMMON command\n", pCommandPackets[i].numWaitFences);
                }

                // Handle signal fence
                if (pCommandPackets[i].signalFence != nullptr)
                {
                    common->set_signal_fence_handle(
                        reinterpret_cast<unsigned long long>(pCommandPackets[i].signalFence));
                }
                else
                {
                    fprintf(stderr, "Warning: Signal fence is null in COMMON command\n");
                    common->set_signal_fence_handle(0);
                }
                break;
            }
        }
    }

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to submit command packets: %d\n", result);
        return result;
    }

    fprintf(stdout, "Submitted %u command packets to user mode queue successfully\n", numCmdPackets);

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::createFence(kfeFence* pFence, unsigned int flags, kfeContext context)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_FENCE_CREATE);
    request.set_client_id(m_clientId);

    auto createReq = request.mutable_fence_create();
    createReq->set_flags(flags);
    createReq->set_context_handle(reinterpret_cast<unsigned long long>(context));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        *pFence = nullptr;
        fprintf(stderr, "Failed to create fence: %d\n", result);
        return result;
    }

    *pFence = reinterpret_cast<kfeFence>(response.fence_create().fence_handle());

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult ClientConnection::destroyFence(kfeFence fence)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_FENCE_DESTROY);
    request.set_client_id(m_clientId);

    auto destroyReq = request.mutable_fence_destroy();
    destroyReq->set_fence_handle(reinterpret_cast<unsigned long long>(fence));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to destroy fence: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::waitFence(kfeUserModeQueue userModeQueue, kfeFence fence)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    // Continuously query the fence until it's signaled
    while (true)
    {
        KfeRequest request;
        request.set_request_id(getNextRequestId());
        request.set_type(KFE_REQUEST_TYPE_FENCE_QUERY);
        request.set_client_id(m_clientId);

        auto queryReq = request.mutable_fence_query();
        queryReq->set_fence_handle(reinterpret_cast<unsigned long long>(fence));

        KfeResponse response;
        result = sendRequest(request, response);
        if (result != KFE_SUCCESS)
        {
            fprintf(stderr, "Failed to send fence query request: %d\n", result);
            return result;
        }

        kfeResult fenceStatus = static_cast<kfeResult>(response.result());
        if (fenceStatus == KFE_SUCCESS)
        {
            // Fence is signaled, we can return
            fprintf(stdout, "Fence 0x%llx is signaled, wait completed\n",
                    reinterpret_cast<unsigned long long>(fence));
            return KFE_SUCCESS;
        }
        else if (fenceStatus == KFE_ERROR_NOT_READY)
        {
            // Fence is not ready yet, sleep for a short time and try again
            fprintf(stdout, "Fence 0x%llx not ready, waiting...\n",
                    reinterpret_cast<unsigned long long>(fence));
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }
        else
        {
            // Some other error occurred
            fprintf(stderr, "Error querying fence 0x%llx: %d\n",
                    reinterpret_cast<unsigned long long>(fence), fenceStatus);
            return fenceStatus;
        }
    }

    // If we reach here, it means we exited the loop without success
    fprintf(stderr, "Failed to wait for fence 0x%llx, exiting wait loop\n",
            reinterpret_cast<unsigned long long>(fence));
    return KFE_ERROR_UNKNOWN;
}

// =====================================================================================================================
kfeResult ClientConnection::signalFence(kfeUserModeQueue userModeQueue, kfeFence fence)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_FENCE_SIGNAL);
    request.set_client_id(m_clientId);

    auto signalReq = request.mutable_fence_signal();
    signalReq->set_fence_handle(reinterpret_cast<unsigned long long>(fence));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to signal fence: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

// =====================================================================================================================
kfeResult ClientConnection::queryFence(kfeFence fence)
{
    kfeResult result = KFE_SUCCESS;

    // Use mutex to protect the communication with server
    std::lock_guard<std::mutex> lock(m_mutex);

    KfeRequest request;
    request.set_request_id(getNextRequestId());
    request.set_type(KFE_REQUEST_TYPE_FENCE_QUERY);
    request.set_client_id(m_clientId);

    auto queryReq = request.mutable_fence_query();
    queryReq->set_fence_handle(reinterpret_cast<unsigned long long>(fence));

    KfeResponse response;
    result = sendRequest(request, response);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to query fence: %d\n", result);
        return result;
    }

    return static_cast<kfeResult>(response.result());
}

} // namespace kfe