#include "context.h"
#include "device.h"
#include "kfe_thunk.h"

#include <cassert>
#include <cstring>
#include <random>

namespace kfe
{
// =====================================================================================================================
Device::Device(int gpuId)
    : m_gpuId(gpuId)
    , m_pModelDeviceHandle(nullptr)
    , m_refCount(1)
    , m_contextIdCounter(0)
    , m_clientInfoMap()
    , m_clientsMapMutex()
    , m_totalMemorySize(2ULL * 1024 * 1024 * 1024) // Example: 2 GB
    , m_freeMemorySize(2ULL * 1024 * 1024 * 1024) // Example: 2 GB
{
    memset(&m_properties, 0, sizeof(kfeDeviceProperties));
}

// =====================================================================================================================
Device::~Device()
{
    cleanupAllClients();

    if (m_pModelDeviceHandle != nullptr)
    {
        kfeThunkDestroyDevice(m_pModelDeviceHandle);
        m_pModelDeviceHandle = nullptr;
    }
}

// =====================================================================================================================
kfeResult Device::initialize()
{
    kfeResult result = KFE_SUCCESS;

    // Create the arch model device handle
    result = kfeThunkCreateDevice(&m_pModelDeviceHandle, m_gpuId,
                                  reinterpret_cast<void*>(readHostMemory),
                                  reinterpret_cast<void*>(writeHostMemory));
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to create model device for GPU ID %d: %d\n", m_gpuId, result);
        return result;
    }

    // Initialize device properties
    snprintf(m_properties.name, sizeof(m_properties.name), "GPU-%d", m_gpuId);

    // Generate a unique UUID for the device
    generateUuid();

    m_properties.totalGlobalMem = m_totalMemorySize; // Example: 2 GB
    m_properties.sharedMemPerBlock = 48 * 1024; // Example: 48 KB
    m_properties.regsPerBlock = 65536; // Example: 64K registers
    m_properties.warpSize = 32; // Example: 32 threads per warp
    m_properties.memPitch = 0; // Example: No pitch
    m_properties.maxThreadsPerBlock = 1024; // Example: 1024 threads per block
    m_properties.maxThreadsDim[0] = 1024; // Example: 1024 threads in x dimension
    m_properties.maxThreadsDim[1] = 1024; // Example: 1024 threads in y dimension
    m_properties.maxThreadsDim[2] = 64; // Example: 64 threads in z dimension
    m_properties.maxGridSize[0] = 2147483647; // Example: 2^31-1 blocks in x dimension
    m_properties.maxGridSize[1] = 65535; // Example: 65535 blocks in y dimension
    m_properties.maxGridSize[2] = 65535; // Example: 65535 blocks in z dimension
    m_properties.clockRate = 1536000; // Example: 1.536 GHz
    m_properties.totalConstMem = 64 * 1024; // Example: 64 KB of constant memory
    m_properties.major = 6; // Example: Compute capability 6.0
    m_properties.minor = 1; // Example: Compute capability 6.1
    m_properties.textureAlignment = 256; // Example: 256-byte alignment for textures
    m_properties.texturePitchAlignment = 32; // Example: 32-byte pitch alignment for textures
    m_properties.deviceOverlap = 1; // Example: Device can overlap memory copy and kernel execution
    m_properties.multiProcessorCount = 16; // Example: 16 multiprocessors
    m_properties.kernelExecTimeoutEnabled = 1; // Example: kernel execution timeout
    m_properties.integrated = 0; // Example: Discrete GPU
    m_properties.canMapHostMemory = 1; // Example: Can map host memory
    m_properties.computeMode = Default; // Example: Default compute mode
    m_properties.maxTexture1D = 65536; // Example: 65536 for 1D textures
    m_properties.maxTexture1DMipmap = 32768; // Example: 32768 for 1D mipmapped textures
    m_properties.maxTexture1DLinear = 65536; // Example: 65536 for 1D linear textures
    m_properties.maxTexture2D[0] = 65536; // Example: 65536 for 2D textures in x dimension
    m_properties.maxTexture2D[1] = 65536; // Example: 65536 for 2D textures in y dimension
    m_properties.maxTexture2DMipmap[0] = 32768; // Example: 32768 for 2D mipmapped textures in x dimension
    m_properties.maxTexture2DMipmap[1] = 32768; // Example: 32768 for 2D mipmapped textures in y dimension
    m_properties.maxTexture2DLinear[0] = 65536; // Example: 65536 for 2D linear textures in x dimension
    m_properties.maxTexture2DLinear[1] = 65536; // Example: 65536 for 2D linear textures in y dimension
    m_properties.maxTexture2DLinear[2] = 0; // Example: 0 for 2D linear textures in z dimension
    m_properties.maxTexture2DGather[0] = 65536; // Example: 65536 for 2D gather textures
    m_properties.maxTexture2DGather[1] = 65536; // Example: 65536 for 2D gather textures
    m_properties.maxTexture3D[0] = 2048; // Example: 2048 for 3D textures in x dimension
    m_properties.maxTexture3D[1] = 2048; // Example: 2048 for 3D textures in y dimension
    m_properties.maxTexture3D[2] = 2048; // Example: 2048 for 3D textures in z dimension
    m_properties.maxTexture3DAlt[0] = 2048; // Example: 2048 for alternate 3D textures in x dimension
    m_properties.maxTexture3DAlt[1] = 2048; // Example: 2048 for alternate 3D textures in y dimension
    m_properties.maxTexture3DAlt[2] = 2048; // Example: 2048 for alternate 3D textures in z dimension
    m_properties.maxTextureCubemap = 65536; // Example: 65536 for cubemap textures
    m_properties.maxTexture1DLayered[0] = 65536; // Example: 65536 for 1D layered textures
    m_properties.maxTexture1DLayered[1] = 2048; // Example: 2048 layers for 1D layered textures
    m_properties.maxTexture2DLayered[0] = 65536; // Example: 65536 for 2D layered textures in x dimension
    m_properties.maxTexture2DLayered[1] = 65536; // Example: 65536 for 2D layered textures in y dimension
    m_properties.maxTexture2DLayered[2] = 2048; // Example: 2048 layers for 2D layered textures
    m_properties.maxTextureCubemapLayered[0] = 65536; // Example: 65536 for cubemap layered textures
    m_properties.maxTextureCubemapLayered[1] = 2048; // Example: 2048 layers for cubemap layered textures
    m_properties.maxSurface1D = 65536; // Example: 65536 for 1D surfaces
    m_properties.maxSurface2D[0] = 65536; // Example: 65536 for 2D surfaces in x dimension
    m_properties.maxSurface2D[1] = 65536; // Example: 65536 for 2D surfaces in y dimension
    m_properties.maxSurface3D[0] = 2048; // Example: 2048 for 3D surfaces in x dimension
    m_properties.maxSurface3D[1] = 2048; // Example: 2048 for 3D surfaces in y dimension
    m_properties.maxSurface3D[2] = 2048; // Example: 2048 for 3D surfaces in z dimension
    m_properties.maxSurface1DLayered[0] = 65536; // Example: 65536 for 1D layered surfaces
    m_properties.maxSurface1DLayered[1] = 2048; // Example: 2048 layers for 1D layered surfaces
    m_properties.maxSurface2DLayered[0] = 65536; // Example: 65536 for 2D layered surfaces in x dimension
    m_properties.maxSurface2DLayered[1] = 65536; // Example: 65536 for 2D layered surfaces in y dimension
    m_properties.maxSurface2DLayered[2] = 2048; // Example: 2048 layers for 2D layered surfaces
    m_properties.maxSurfaceCubemap = 65536; // Example: 65536 for cubemap surfaces
    m_properties.maxSurfaceCubemapLayered[0] = 65536; // Example: 65536 for cubemap layered surfaces
    m_properties.maxSurfaceCubemapLayered[1] = 2048; // Example: 2048 layers for cubemap layered surfaces
    m_properties.surfaceAlignment = 256; // Example: 256-byte alignment for surfaces
    m_properties.concurrentKernels = 1; // Example: Device supports concurrent kernels
    m_properties.ECCEnabled = 0; // Example: ECC is disabled
    m_properties.pciBusID = 0; // Example: PCI bus ID
    m_properties.pciDeviceID = 0; // Example: PCI device ID
    m_properties.pciDomainID = 0; // Example: PCI domain ID
    m_properties.tccDriver = 0; // Example: TCC driver is not used
    m_properties.asyncEngineCount = 1; // Example: Device supports concurrent memory copy between host and device.
    m_properties.unifiedAddressing = 1; // Example: Unified addressing is supported
    m_properties.memoryClockRate = 800000; // Example: Memory clock rate in kHz
    m_properties.memoryBusWidth = 256; // Example: Memory bus width in bits
    m_properties.l2CacheSize = 2097152; // Example: L2 cache size in bytes
    m_properties.persistingL2CacheMaxSize = 2097152; // Example: Persistent L2 cache max size in bytes
    m_properties.maxThreadsPerMultiProcessor = 2048; // Example: 2048 threads per multiprocessor
    m_properties.streamPrioritiesSupported = 1; // Example: Stream priorities supported
    m_properties.globalL1CacheSupported = 1; // Example: Global L1 cache supported
    m_properties.localL1CacheSupported = 1; // Example: Local L1 cache supported
    m_properties.sharedMemPerMultiprocessor = 65536; // Example: 64 KB shared memory per multiprocessor
    m_properties.regsPerMultiprocessor = 65536; // Example: 64K registers per multiprocessor
    m_properties.managedMemory = 1; // Example: Managed memory supported
    m_properties.isMultiGpuBoard = 0; // Example: Not a multi-GPU board
    m_properties.multiGpuBoardGroupID = 0; // Example: Multi-GPU board group ID
    m_properties.hostNativeAtomicSupported = 1; // Example: Host native atomic operations supported
    m_properties.singleToDoublePrecisionPerfRatio = 1; // Example: Single precision performance
    m_properties.pageableMemoryAccess = 1; // Example: Pageable memory access supported
    m_properties.concurrentManagedAccess = 1; // Example: Concurrent managed access supported
    m_properties.computePreemptionSupported = 1; // Example: Compute preemption supported
    m_properties.canUseHostPointerForRegisteredMem = 1; // Example: Can use host pointer for registered memory
    m_properties.cooperativeLaunch = 1; // Example: Cooperative launch supported
    m_properties.cooperativeMultiDeviceLaunch = 1; // Example: Cooperative multi-device launch supported
    m_properties.sharedMemPerBlockOptin = 1; // Example: Shared memory per block opt-in supported
    m_properties.pageableMemoryAccessUsesHostPageTables = 1; // Example: Pageable memory access uses host page tables
    m_properties.directManagedMemAccessFromHost = 1; // Example: Direct managed memory access from host supported
    m_properties.maxBlocksPerMultiProcessor = 16; // Example: 16 blocks per multiprocessor
    m_properties.accessPolicyMaxWindowSize = 0; // Example: Access policy max window size
    m_properties.reservedSharedMemPerBlock = 0; // Example: Reserved shared memory per block
    m_properties.hostRegisterSupported = 1; // Example: Host register supported
    m_properties.sparseCudaArraySupported = 1; // Example: Sparse CUDA array supported
    m_properties.hostRegisterReadOnlySupported = 1; // Example: Host register read-only supported
    m_properties.timelineSemaphoreInteropSupported = 1; // Example: Timeline semaphore interoperability supported
    m_properties.memoryPoolsSupported = 1; // Example: Memory pools supported
    m_properties.gpuDirectRDMASupported = 1; // Example: GPU Direct RDMA supported
    m_properties.gpuDirectRDMAFlushWritesOptions = 1; // Example: GPU Direct RDMA flush writes options supported
    m_properties.gpuDirectRDMAWritesOrdering = 1; // Example: GPU Direct RDMA writes ordering supported
    m_properties.memoryPoolSupportedHandleTypes = 1; // Example: Memory pool supported handle types
    m_properties.deferredMappingCudaArraySupported = 1; // Example: Deferred mapping CUDA array supported
    m_properties.ipcEventSupported = 1; // Example: IPC events supported
    m_properties.unifiedFunctionPointers = 1; // Example: Unified function pointers supported
    m_properties.physicalMemoryAlignment = 4096; // Example: 4 KB physical memory alignment

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Device::createContext(Context** ppContext, unsigned int flags, unsigned long long clientId,
                                util::OsPid clientPid)
{
    assert(ppContext != nullptr);

    // Simulate creating a context by setting the pointer to a new Context instance
    Context* pContext = new (std::nothrow) Context(this, flags, clientId, getNextContextId(), clientPid);
    if (pContext == nullptr)
    {
        fprintf(stderr, "Failed to allocate memory for Context.\n");
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    addContext(pContext);

    *ppContext = pContext;

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Device::destroyContext(Context* pContext)
{
    assert(pContext != nullptr);

    removeContext(pContext);

    // Simulate destroying a context by deleting the Context instance
    delete pContext;

    return KFE_SUCCESS;
}

// =====================================================================================================================
void Device::generateUuid()
{
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    for (int i = 0; i < 16; ++i)
    {
        m_properties.uuid[i] = static_cast<char>(dis(gen));
    }
}

// =====================================================================================================================
void Device::addContext(Context* pContext)
{
    std::lock_guard<std::mutex> lock(m_clientsMapMutex);
    assert(pContext != nullptr);

    // Check if the context already exists
    auto iter = m_clientInfoMap.find(pContext->getClientId());
    if (iter == m_clientInfoMap.end())
    {
        fprintf(stderr, "Client ID %llu not found in m_clientInfoMap.\n", pContext->getClientId());
        return; // Cannot add context for a non-existent client
    }

    auto ctxIter = iter->second.m_contextsMap.find(pContext->getContextId());
    if (ctxIter != iter->second.m_contextsMap.end())
    {
        // Context already exists for this client
        fprintf(stderr, "Context with ID %d already exists for client %llu.\n",
                pContext->getContextId(), pContext->getClientId());
        return;
    }

    iter->second.m_contextsMap[pContext->getContextId()] = pContext;
    fprintf(stdout, "[Device %d] Added context with ID %d for client %llu.\n",
            m_gpuId, pContext->getContextId(), pContext->getClientId());
}

// =====================================================================================================================
void Device::removeContext(Context* pContext)
{
    std::lock_guard<std::mutex> lock(m_clientsMapMutex);
    assert(pContext != nullptr);

    // Check if the context already exists
    auto iter = m_clientInfoMap.find(pContext->getClientId());
    auto ctxIter = iter->second.m_contextsMap.find(pContext->getContextId());
    if (ctxIter != iter->second.m_contextsMap.end())
    {
        iter->second.m_contextsMap.erase(ctxIter);
    }
}

// =====================================================================================================================
void Device::cleanupAllClients()
{
    std::lock_guard<std::mutex> lock(m_clientsMapMutex);
    for (auto& [clientId, clientInfo] : m_clientInfoMap)
    {
        fprintf(stdout, "[Device %d] Destroying contexts for client %llu.\n", m_gpuId, clientId);
        for (auto& [contextId, context] : clientInfo.m_contextsMap)
        {
            delete context; // Assuming Context has a proper destructor
        }
        clientInfo.m_contextsMap.clear();
    }
}

// =====================================================================================================================
void Device::retain()
{
    m_refCount.fetch_add(1, std::memory_order_relaxed);
}

// =====================================================================================================================
void Device::release()
{
    if (m_refCount.fetch_sub(1, std::memory_order_acq_rel) == 1)
    {
        delete this; // Delete the device if the reference count reaches zero
    }
}

// =====================================================================================================================
void Device::registerClient(unsigned long long clientId)
{
    std::lock_guard<std::mutex> lock(m_clientsMapMutex);

    // Check if client already exists
    auto it = m_clientInfoMap.find(clientId);
    if (it != m_clientInfoMap.end())
    {
        // Client already registered, update activity time and connection count
        it->second.openedTime = std::chrono::steady_clock::now();
        fprintf(stdout, "[Device %d] Client %llu\n", m_gpuId, clientId);
        return;
    }

    ClientInfo clientInfo;
    clientInfo.clientId = clientId;
    clientInfo.openedTime = std::chrono::steady_clock::now();
    clientInfo.m_contextsMap = {};
    m_clientInfoMap[clientId] = std::move(clientInfo);
    fprintf(stdout, "[Device %d] Registered client: ID=%llu.\n", m_gpuId, clientId);
}

// =====================================================================================================================
void Device::unregisterClient(unsigned long long clientId)
{
    std::lock_guard<std::mutex> lock(m_clientsMapMutex);

    auto it = m_clientInfoMap.find(clientId);
    if (it != m_clientInfoMap.end())
    {
        fprintf(stdout, "[Device %d] Unregistered client with ID: %llu\n", m_gpuId, clientId);

        // Cleanup contexts associated with this client
        for (auto& [contextId, context] : it->second.m_contextsMap)
        {
            delete context; // Assuming Context has a proper destructor
        }

        it->second.m_contextsMap.clear();

        m_clientInfoMap.erase(it);
    }
    else
    {
        fprintf(stderr, "[Device %d] Client with ID: %llu not found for unregistration\n", m_gpuId, clientId);
    }
}

} // namespace kfe