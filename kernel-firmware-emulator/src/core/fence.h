#ifndef FENCE_H_
#define FENCE_H_

#include "context.h"
#include "kfe.h"

#include <mutex>
#include <string>

namespace kfe
{
// Forward declaration
class Context;

class Fence
{
public:
    Fence(Context* pContext, unsigned int flags, int fenceId);
    ~<PERSON><PERSON>();

    kfeResult init();

    // Get the associated context
    Context* getContext() const { return m_pContext; }

    // Get the unique identifier for the fence
    int getFenceId() const { return m_fenceId; }

    // Get the name of the fence
    std::string getName() const { return m_name; }

    // Get the name of the fence, useful for debugging or logging
    unsigned long long getTargetValue() const { return m_fenceTargetValue; }

    // Set the target value for the fence
    void setTargetValue(unsigned long long targetValue) { m_fenceTargetValue = targetValue; }

    // Signal the fence
    // This function marks the fence as signaled, allowing any waiting threads to proceed.
    // It can be called by the user mode queue to indicate that the operations associated with the fence are complete.
    kfeResult signal();

    // Query the fence status
    kfeResult query();
private:
    // Pointer to the associated context
    Context* m_pContext;

    // Flags for this fence
    unsigned int m_flags;

    // Unique identifier for the fence
    int m_fenceId;

    // Optional name for the fence, can be used for debugging or logging
    std::string m_name;

    // The fence memory address, used for synchronization
    void* m_fenceMemAddr;

    // The target value for the fence, used to determine when the fence is signaled.
    // By default, it is set to the fence ID, but can be changed
    unsigned long long m_fenceTargetValue;

    // Mutex for synchronizing access to the fence
    std::mutex m_mutex;
};

} // namespace kfe

#endif // FENCE_H_