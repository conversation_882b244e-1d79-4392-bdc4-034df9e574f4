#ifndef SERVER_H_
#define SERVER_H_

#include <memory>
#include <mutex>
#include <thread>
#include <unordered_map>

#include "thread_pool.h"
#include "kfe.h"
#include "kfe_protocol.pb.h"

namespace kfe
{
class ProcessSyncManager;
class DeviceManager;

class Server
{
public:
    Server(const std::string& socketPath, const std::string& lockFilePath, const std::string& pidFilePath);
    ~Server();

    // Set the socket path for the server
    const std::string& getSocketPath() const { return m_socketPath; }
    void setSocketPath(const std::string& path) { m_socketPath = path; }

    void start();
    void stop();
    void wait();
private:
    int createUnixSocket();

    bool addClientToEpoll(int epollFd, int clientFd);
    bool removeClientFromEpoll(int epollFd, int clientFd);

    bool handleClientConnect(int clientFd);
    void handleClientDisconnect(int clientFd);

    // Process incoming requests
    // This function should be overridden by derived classes to handle specific requests.
    // It takes a KfeRequest object and returns a KfeResponse object.
    void processRequest(int clientFd);
    KfeResponse handleRequest(int clientFd, const KfeRequest& request);

    // Api process function
    KfeResponse handleInitialize(unsigned long long clientId, const InitializeRequest& req);
    KfeResponse handleFinalize(unsigned long long clientId, const FinalizeRequest& req);
    KfeResponse handleDeviceGetCount(unsigned long long clientId, const DeviceGetCountRequest& req);
    KfeResponse handleDeviceOpen(unsigned long long clientId, const DeviceOpenRequest& req);
    KfeResponse handleDeviceClose(unsigned long long clientId, const DeviceCloseRequest& req);
    KfeResponse handleDeviceGetProperties(unsigned long long clientId, const DeviceGetPropertiesRequest& req);
    KfeResponse handleContextCreate(unsigned long long clientId, const ContextCreateRequest& req);
    KfeResponse handleContextDestroy(unsigned long long clientId, const ContextDestroyRequest& req);
    KfeResponse handlePhysicalMemoryCreate(unsigned long long clientId, const PhysicalMemoryCreateRequest& req);
    KfeResponse handlePhysicalMemoryDestroy(unsigned long long clientId, const PhysicalMemoryDestroyRequest& req);
    KfeResponse handlePhysicalMemoryDeviceMap(unsigned long long clientId, const PhysicalMemoryDeviceMapRequest& req);
    KfeResponse handlePhysicalMemoryDeviceUnmap(unsigned long long clientId, const PhysicalMemoryDeviceUnmapRequest& req);
    KfeResponse handlePhysicalMemoryHostMap(unsigned long long clientId, const PhysicalMemoryHostMapRequest& req);
    KfeResponse handlePhysicalMemoryHostUnmap(unsigned long long clientId, const PhysicalMemoryHostUnmapRequest& req);
    KfeResponse handlePhysicalMemoryExportByDmaBuf(unsigned long long clientId, const PhysicalMemoryExportByDmaBufRequest& req);
    KfeResponse handlePhysicalMemoryImportFromDmaBuf(unsigned long long clientId, const PhysicalMemoryImportFromDmaBufRequest& req);
    KfeResponse handlePinnedMemoryCreate(unsigned long long clientId, const PinnedMemoryCreateRequest& req);
    KfeResponse handlePinnedMemoryDestroy(unsigned long long clientId, const PinnedMemoryDestroyRequest& req);
    KfeResponse handlePageableMemoryHostRegister(unsigned long long clientId, const PageableMemoryHostRegisterRequest& req);
    KfeResponse handlePageableMemoryHostUnregister(unsigned long long clientId, const PageableMemoryHostUnregisterRequest& req);
    KfeResponse handleUserModeQueueCreate(unsigned long long clientId, const UserModeQueueCreateRequest& req);
    KfeResponse handleUserModeQueueDestroy(unsigned long long clientId, const UserModeQueueDestroyRequest& req);
    KfeResponse handleUserModeQueueSubmit(unsigned long long clientId, const UserModeQueueSubmitRequest& req);
    KfeResponse handleFenceCreate(unsigned long long clientId, const FenceCreateRequest& req);
    KfeResponse handleFenceDestroy(unsigned long long clientId, const FenceDestroyRequest& req);
    KfeResponse handleFenceSignal(unsigned long long clientId, const FenceSignalRequest& req);
    KfeResponse handleFenceQuery(unsigned long long clientId, const FenceQueryRequest& req);

    // utility functions
    bool sendResponse(int clientFd, const KfeResponse& response);
    bool receiveRequest(int clientFd, KfeRequest& request);

    inline unsigned long long allocateClientId() { return m_nextClientId.fetch_add(1, std::memory_order_relaxed); }
    unsigned long long getClientIdByFd(int clientFd);

    static constexpr size_t MAX_EVENTS = 1024;

    std::string m_socketPath;
    std::string m_lockFilePath;

    std::mutex m_sendMutex;
    std::mutex m_recvMutex;

    // Process synchronization
    std::unique_ptr<ProcessSyncManager> m_syncManager;

    // Network-related members
    int m_serverFd;

    // The lock file descriptor
    int m_epollFd;
    std::atomic<bool> m_running;

    ThreadPool m_workerPool;

    // Resource management
    std::unique_ptr<DeviceManager> m_deviceManager;

    // Client management
    struct ClientInfo
    {
        unsigned long long id;
        std::string name;
        int socketFd;
        std::chrono::steady_clock::time_point lastActivity;
        std::chrono::steady_clock::time_point lastHeartbeat;
    };
    std::unordered_map<unsigned long long, std::unique_ptr<ClientInfo>> m_clients;
    std::unordered_map<int, unsigned long long> m_fdToClientId;
    std::mutex m_clientsMutex;

    // Client ID management
    std::atomic<unsigned long long> m_nextClientId;

    std::string m_serverPidFilePath;

    // Heartbeat thread
    std::thread m_heartbeatThread;
    void heartbeatThreadFunc();

    static constexpr int HEARTBEAT_INTERVAL_MS = 500; // 500 milliseconds
    static constexpr int HEARTBEAT_TIMEOUT_MS = 1000; // 1 seconds
};

} // namespace kfe

#endif // KFE_SERVER_H