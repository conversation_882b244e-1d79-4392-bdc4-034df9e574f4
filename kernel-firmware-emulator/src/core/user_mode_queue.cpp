#include "context.h"
#include "fence.h"
#include "user_mode_queue.h"

#include <cstdio>
#include <thread>

namespace kfe
{
// =====================================================================================================================
UserModeQueue::UserModeQueue(Context* pContext, unsigned int flags, int queueId)
    : m_pContext(pContext)
    , m_flags(flags)
    , m_queueId(queueId)
    , m_threadPool(std::thread::hardware_concurrency())
{
    // Constructor implementation
}

// =====================================================================================================================
UserModeQueue::~UserModeQueue()
{
    // Stop the thread pool first
    m_threadPool.stop();

    // Destructor implementation
    // Cleanup resources if necessary
    clearAllCommandPackets();
}

// =====================================================================================================================
kfeResult UserModeQueue::submit(kfeCommandPacket* pCommandPackets, unsigned int numCmdPackets)
{
    if (pCommandPackets == nullptr || numCmdPackets == 0)
    {
        fprintf(stderr, "[UserModeQueue] Invalid command packets or count: %p, %u\n", pCommandPackets, numCmdPackets);
        return KFE_ERROR_INVALID_VALUE;
    }

    fprintf(stdout, "[Main Process] Starting to submit %u command packets to queue ID: %d\n", numCmdPackets, m_queueId);

    // Debug: Print details of received command packets before storing
    for (unsigned int i = 0; i < numCmdPackets; ++i)
    {
        fprintf(stdout, "[Main Process] Original command packet %u:\n", i + 1);
        fprintf(stdout, "  - Type: %d\n", pCommandPackets[i].commandPacketType);
        fprintf(stdout, "  - Queue ID: %d\n", pCommandPackets[i].queueId);
        fprintf(stdout, "  - numWaitFences: %d\n", pCommandPackets[i].numWaitFences);
        fprintf(stdout, "  - pWaitFences: %p\n", pCommandPackets[i].pWaitFences);
        for (int j = 0; j < pCommandPackets[i].numWaitFences; ++j)
        {
            fprintf(stdout, "  - Wait fence [%d]: %p\n", j, pCommandPackets[i].pWaitFences[j]);
        }
        fprintf(stdout, "  - signalFence: %p\n", pCommandPackets[i].signalFence);
    }

    std::lock_guard<std::mutex> lock(m_commandPacketsMutex);

    // Clear existing command packets
    m_commandPackets.clear();

    // Store new command packets in deque
    if (numCmdPackets > 0 && pCommandPackets != nullptr)
    {
        for (unsigned int i = 0; i < numCmdPackets; ++i)
        {
            kfeCommandPacket cmdPacket = pCommandPackets[i];

            // Deep copy the wait fences array if it exists
            if (cmdPacket.numWaitFences > 0 && cmdPacket.pWaitFences != nullptr)
            {
                // Create a new array to store the fence handles
                kfeFence* pNewWaitFences = new(std::nothrow) kfeFence[cmdPacket.numWaitFences];

                // Copy the fence handles
                for (int j = 0; j < cmdPacket.numWaitFences; ++j)
                {
                    pNewWaitFences[j] = cmdPacket.pWaitFences[j];
                }

                // Update the pointer to point to our new array
                cmdPacket.pWaitFences = pNewWaitFences;
            }

            fprintf(stdout, "[UserModeQueue] Command packet details:\n");
            fprintf(stdout, "  - Type: %d\n", cmdPacket.commandPacketType);
            fprintf(stdout, "  - Queue ID: %d\n", cmdPacket.queueId);
            fprintf(stdout, "  - numWaitFences: %d\n", cmdPacket.numWaitFences);
            fprintf(stdout, "  - pWaitFences: %p\n", cmdPacket.pWaitFences);
            for (int j = 0; j < cmdPacket.numWaitFences; ++j)
            {
                fprintf(stdout, "  - Wait fence [%d]: %p\n", j, cmdPacket.pWaitFences[j]);
            }
            fprintf(stdout, "  - signalFence: %p\n", cmdPacket.signalFence);

            m_commandPackets.push_back(cmdPacket);

            // Submit each command packet to the thread pool asynchronously
            // Note: We don't wait for completion here - commands run asynchronously
            m_threadPool.enqueue([this, i]() -> void {
                kfeResult packetResult = processCommandPacketAsync(i);
                if (packetResult != KFE_SUCCESS)
                {
                    fprintf(stderr, "[Worker Thread] Command packet %u failed with result: %d\n",
                            i + 1, packetResult);
                }
            });

            fprintf(stdout, "[Main Process] Submitted command packet %u (type: %d) to thread pool\n",
                    i + 1, pCommandPackets[i].commandPacketType);
        }
    }

    fprintf(stdout, "[Main Process] Successfully submitted all %u command packets for queue ID: %d\n",
            numCmdPackets, m_queueId);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeCommandPacket UserModeQueue::getCommandPacket(unsigned int index) const
{
    std::lock_guard<std::mutex> lock(m_commandPacketsMutex);

    if (index >= m_commandPackets.size())
    {
        // Return a default-constructed command packet for invalid index
        return kfeCommandPacket{};
    }

    return m_commandPackets[index];
}

// =====================================================================================================================
void UserModeQueue::clearAllCommandPackets()
{
    std::lock_guard<std::mutex> lock(m_commandPacketsMutex);

    // Free the dynamically allocated wait fences arrays
    for (auto& cmdPacket : m_commandPackets)
    {
        if (cmdPacket.numWaitFences > 0 && cmdPacket.pWaitFences != nullptr)
        {
            delete[] cmdPacket.pWaitFences;
            cmdPacket.pWaitFences = nullptr;
        }
    }

    m_commandPackets.clear();
}

// =====================================================================================================================
kfeResult UserModeQueue::processCommandPacketAsync(unsigned int packetIndex)
{
    fprintf(stdout, "[Worker Thread] Processing command packet %u for queue ID: %d\n",
            packetIndex + 1, m_queueId);

    // Get command packet from the queue by index
    unsigned int numCommandPackets = m_commandPackets.size();

    if (packetIndex >= numCommandPackets)
    {
        fprintf(stderr, "[Worker Thread] Invalid command packet index: %u (total: %u)\n",
                packetIndex, numCommandPackets);
        return KFE_ERROR_INVALID_VALUE;
    }

    kfeCommandPacket cmdPacket = getCommandPacket(packetIndex);

    // Add detailed debugging for the command packet
    fprintf(stdout, "[Worker Thread] Command packet details:\n");
    fprintf(stdout, "  - Type: %d\n", cmdPacket.commandPacketType);
    fprintf(stdout, "  - Queue ID: %d\n", cmdPacket.queueId);
    fprintf(stdout, "  - numWaitFences: %d\n", cmdPacket.numWaitFences);
    fprintf(stdout, "  - pWaitFences: %p\n", cmdPacket.pWaitFences);
    for (int j = 0; j < cmdPacket.numWaitFences; ++j)
    {
        fprintf(stdout, "  - Wait fence [%d]: %p\n", j, cmdPacket.pWaitFences[j]);
    }
    fprintf(stdout, "  - signalFence: %p\n", cmdPacket.signalFence);

    // Check for obviously corrupted data
    if (cmdPacket.commandPacketType < 0 || cmdPacket.commandPacketType > KFE_COMMAND_PACKET_TYPE_MAX)
    {
        fprintf(stderr, "[Worker Thread] ERROR: Invalid command packet type: %d\n", cmdPacket.commandPacketType);
        return KFE_ERROR_INVALID_VALUE;
    }

    if (cmdPacket.queueId != m_queueId)
    {
        fprintf(stderr, "[Worker Thread] ERROR: Queue ID mismatch - packet: %d, queue: %d\n",
                cmdPacket.queueId, m_queueId);
        return KFE_ERROR_INVALID_VALUE;
    }

    fprintf(stdout, "[Worker Thread] Processing command packet %u of type: %d\n",
            packetIndex + 1, cmdPacket.commandPacketType);

    // Step 1: Wait for all wait fences to be signaled
    if (cmdPacket.numWaitFences > 0 && cmdPacket.pWaitFences != nullptr)
    {
        fprintf(stdout, "[Worker Thread] Waiting for %d wait fences...\n", cmdPacket.numWaitFences);
        fprintf(stdout, "[Worker Thread] pWaitFences array pointer: %p\n", cmdPacket.pWaitFences);

        bool allFencesReady = false;
        while (!allFencesReady)
        {
            allFencesReady = true;

            for (int j = 0; j < cmdPacket.numWaitFences; ++j)
            {
                fprintf(stdout, "Checking the wait fence %d/%d\n", j + 1, cmdPacket.numWaitFences);

                // Check if the fence handle is valid before casting
                void* fenceHandle = cmdPacket.pWaitFences[j];
                fprintf(stdout, "[Worker Thread] Fence handle [%d]: %p\n", j, fenceHandle);

                if (fenceHandle == nullptr)
                {
                    fprintf(stderr, "[Worker Thread] ERROR: Null fence handle at index %d\n", j);
                    return KFE_ERROR_INVALID_VALUE;
                }

                // Check if the pointer looks reasonable (not obviously corrupted)
                uintptr_t addr = reinterpret_cast<uintptr_t>(fenceHandle);
                if (addr < 0x1000 || addr > 0x7fffffffffff)
                {
                    fprintf(stderr, "[Worker Thread] ERROR: Suspicious fence pointer at index %d: %p (addr: 0x%lx)\n",
                            j, fenceHandle, addr);
                    return KFE_ERROR_INVALID_VALUE;
                }

                Fence* pWaitFence = reinterpret_cast<Fence*>(fenceHandle);
                fprintf(stdout, "[Worker Thread] Casted fence pointer: %p\n", pWaitFence);

                // Try to safely access the fence ID with additional checks
                int fenceId = pWaitFence->getFenceId();
                fprintf(stdout, "[Worker Thread] Checking wait fence ID: %d, pointer: %p\n", fenceId, pWaitFence);

                kfeResult fenceResult = pWaitFence->query();
                fprintf(stdout, "[Worker Thread] Query result for fence ID: %d: %d\n", fenceId, fenceResult);

                if (fenceResult != KFE_SUCCESS)
                {
                    allFencesReady = false;
                    fprintf(stdout, "[Worker Thread] Fence ID: %d not ready yet, continuing to wait...\n", fenceId);
                    break; // Break out of the fence loop, but continue the while loop
                }
                else
                {
                    fprintf(stdout, "[Worker Thread] Fence ID: %d is ready\n", fenceId);
                }
            }

            if (!allFencesReady)
            {
                // Sleep for a short time before checking again
                fprintf(stdout, "[Worker Thread] Waiting for all fences to be ready...\n");
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }

        fprintf(stdout, "[Worker Thread] All wait fences are ready, proceeding with command execution\n");
    }

    // Step 2: Execute the command packet task based on its type
    kfeResult result = executeCommandPacket(cmdPacket);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Worker Thread] Failed to execute command packet %u of type: %d\n",
                packetIndex + 1, cmdPacket.commandPacketType);
        return result;
    }

    // Step 3: Signal the completion fence if it exists
    if (cmdPacket.signalFence != nullptr)
    {
        Fence* pSignalFence = reinterpret_cast<Fence*>(cmdPacket.signalFence);
        fprintf(stdout, "[Worker Thread] Signaling completion fence ID: %d\n", pSignalFence->getFenceId());

        // Signal the fence to indicate task completion
        pSignalFence->signal();

        fprintf(stdout, "[Worker Thread] Completion fence ID: %d has been signaled\n", pSignalFence->getFenceId());
    }

    fprintf(stdout, "[Worker Thread] Completed command packet %u of type: %d\n",
            packetIndex + 1, cmdPacket.commandPacketType);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeCommandPacket(const kfeCommandPacket& cmdPacket)
{
    switch (cmdPacket.commandPacketType)
    {
        case KFE_COMMAND_PACKET_TYPE_KERNEL_DISPATCH:
            return executeKernelDispatch(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_DEBUG_ADD:
            return executeDebugAdd(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_DEBUG_SUB:
            return executeDebugSub(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_MEMCPY:
            return executeMemcpy(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_MEMSET:
            return executeMemset(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_EVENT_RECORD:
            return executeEventRecord(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_EVENT_WAIT:
            return executeEventWait(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_MEMORY_WRITE:
            return executeMemoryWrite(cmdPacket);
        case KFE_COMMAND_PACKET_TYPE_MEMORY_WAIT:
            return executeMemoryWait(cmdPacket);
        default:
            fprintf(stderr, "[Main Process] Unknown command packet type: %d\n", cmdPacket.commandPacketType);
            return KFE_ERROR_INVALID_VALUE;
    }
}

// =====================================================================================================================
kfeResult UserModeQueue::executeKernelDispatch(const kfeCommandPacket& cmdPacket)
{
    const kfeCommandPacketKernelDispatch* pKernelDispatch =
        reinterpret_cast<const kfeCommandPacketKernelDispatch*>(&cmdPacket);

    fprintf(stdout, "[Worker Thread] Executing kernel dispatch:\n");
    fprintf(stdout, "  Queue ID: %d\n", pKernelDispatch->queueId);
    fprintf(stdout, "  Grid dimensions: (%d, %d, %d)\n",
            pKernelDispatch->gridDimX, pKernelDispatch->gridDimY, pKernelDispatch->gridDimZ);
    fprintf(stdout, "  Block dimensions: (%d, %d, %d)\n",
            pKernelDispatch->blockDimX, pKernelDispatch->blockDimY, pKernelDispatch->blockDimZ);

    // Simulate kernel execution time based on grid size
    int totalThreads = pKernelDispatch->gridDimX * pKernelDispatch->gridDimY * pKernelDispatch->gridDimZ *
                       pKernelDispatch->blockDimX * pKernelDispatch->blockDimY * pKernelDispatch->blockDimZ;

    // Simulate execution time: 1ms per 1000 threads (minimum 10ms, maximum 1000ms)
    int executionTimeMs = std::max(10, std::min(1000, totalThreads / 1000));

    fprintf(stdout, "[Worker Thread] Simulating kernel execution with %d threads for %d ms...\n",
            totalThreads, executionTimeMs);

    std::this_thread::sleep_for(std::chrono::milliseconds(executionTimeMs));

    fprintf(stdout, "[Worker Thread] Kernel dispatch completed successfully\n");
    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeDebugAdd(const kfeCommandPacket& cmdPacket)
{
    const kfeCommandPacketDebugAdd* pDebugAdd =
        reinterpret_cast<const kfeCommandPacketDebugAdd*>(&cmdPacket);

    fprintf(stdout, "[Worker Thread] Executing debug add operation:\n");
    fprintf(stdout, "  Queue ID: %d\n", pDebugAdd->queueId);
    fprintf(stdout, "  Operand1: %d\n", pDebugAdd->operand1);
    fprintf(stdout, "  Operand2: %d\n", pDebugAdd->operand2);

    // Simulate computation
    int result = pDebugAdd->operand1 + pDebugAdd->operand2;

    // Simulate processing time
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    fprintf(stdout, "[Worker Thread] Debug add result: %d + %d = %d\n",
            pDebugAdd->operand1, pDebugAdd->operand2, result);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeDebugSub(const kfeCommandPacket& cmdPacket)
{
    const kfeCommandPacketDebugSub* pDebugSub =
        reinterpret_cast<const kfeCommandPacketDebugSub*>(&cmdPacket);

    fprintf(stdout, "[Worker Thread] Executing debug sub operation:\n");
    fprintf(stdout, "  Queue ID: %d\n", pDebugSub->queueId);
    fprintf(stdout, "  Operand1: %d\n", pDebugSub->operand1);
    fprintf(stdout, "  Operand2: %d\n", pDebugSub->operand2);

    // Simulate computation
    int result = pDebugSub->operand1 - pDebugSub->operand2;

    // Simulate processing time
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    fprintf(stdout, "[Worker Thread] Debug sub result: %d - %d = %d\n",
            pDebugSub->operand1, pDebugSub->operand2, result);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeMemcpy(const kfeCommandPacket& cmdPacket)
{
    fprintf(stdout, "[Worker Thread] Executing memory copy operation on queue ID: %d\n", cmdPacket.queueId);

    // Simulate memory copy time
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    fprintf(stdout, "[Worker Thread] Memory copy operation completed\n");
    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeMemset(const kfeCommandPacket& cmdPacket)
{
    fprintf(stdout, "[Worker Thread] Executing memory set operation on queue ID: %d\n", cmdPacket.queueId);

    // Simulate memory set time
    std::this_thread::sleep_for(std::chrono::milliseconds(80));

    fprintf(stdout, "[Worker Thread] Memory set operation completed\n");
    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeEventRecord(const kfeCommandPacket& cmdPacket)
{
    fprintf(stdout, "[Worker Thread] Executing event record operation on queue ID: %d\n", cmdPacket.queueId);

    // Simulate event record time
    std::this_thread::sleep_for(std::chrono::milliseconds(20));

    fprintf(stdout, "[Worker Thread] Event record operation completed\n");
    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeEventWait(const kfeCommandPacket& cmdPacket)
{
    fprintf(stdout, "[Worker Thread] Executing event wait operation on queue ID: %d\n", cmdPacket.queueId);

    // Simulate event wait time
    std::this_thread::sleep_for(std::chrono::milliseconds(30));

    fprintf(stdout, "[Worker Thread] Event wait operation completed\n");
    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeMemoryWrite(const kfeCommandPacket& cmdPacket)
{
    fprintf(stdout, "[Worker Thread] Executing memory write operation on queue ID: %d\n", cmdPacket.queueId);

    // Simulate memory write time
    std::this_thread::sleep_for(std::chrono::milliseconds(60));

    fprintf(stdout, "[Worker Thread] Memory write operation completed\n");
    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult UserModeQueue::executeMemoryWait(const kfeCommandPacket& cmdPacket)
{
    fprintf(stdout, "[Worker Thread] Executing memory wait operation on queue ID: %d\n", cmdPacket.queueId);

    // Simulate memory wait time
    std::this_thread::sleep_for(std::chrono::milliseconds(40));

    fprintf(stdout, "[Worker Thread] Memory wait operation completed\n");
    return KFE_SUCCESS;
}

} // namespace kfe