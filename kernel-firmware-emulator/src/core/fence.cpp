#include "fence.h"
#include "context.h"

#include "os.h"

#include <thread>

namespace kfe
{
// =====================================================================================================================
Fence::Fence(Context* pContext, unsigned int flags, int fenceId)
    : m_pContext(pContext)
    , m_flags(flags)
    , m_fenceId(fenceId)
    , m_name()
    , m_fenceMemAddr(nullptr)
    , m_fenceTargetValue(fenceId + 1)
    , m_mutex()
{
    // Constructor implementation
    // Initialize the fence or any related resources
}

// =====================================================================================================================
Fence::~Fence()
{
    if (m_fenceMemAddr != nullptr)
    {
        // If the fence memory address is not null, we need to free the shared memory.
        free(m_fenceMemAddr);
        m_fenceMemAddr = nullptr; // Clear the pointer to avoid dangling pointer
    }

    if (m_name.empty() == false)
    {
        m_name.clear();
    }

    m_fenceTargetValue = 0;
    m_pContext = nullptr; // Clear the context pointer
}

// =====================================================================================================================
kfeResult Fence::init()
{
    char name[64];
    snprintf(name, sizeof(name), "kfe_fence_%d_%d", util::getProcessId(), m_fenceId);
    m_name = name;

    m_fenceMemAddr = malloc(sizeof(unsigned long long));
    if (m_fenceMemAddr == nullptr)
    {
        return KFE_ERROR_OUT_OF_MEMORY; // Failed to create shared memory for the fence
    }

    // Initialize the fence memory to 0 (unsignaled state)
    *reinterpret_cast<unsigned long long*>(m_fenceMemAddr) = 0;

    fprintf(stderr, "[Fence::init] Created fence with ID: %d, ptr: %llu, name: %s\n", m_fenceId,
            reinterpret_cast<unsigned long long>(m_fenceMemAddr), m_name.c_str());

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Fence::signal()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    fprintf(stderr, "[Fence::signal] About to signal fence ID: %d with target value: %llu\n",
            m_fenceId, m_fenceTargetValue);

    if (m_fenceMemAddr == nullptr)
    {
        fprintf(stderr, "[Fence::signal] ERROR: m_fenceMemAddr is null!\n");
        return KFE_ERROR_INVALID_VALUE;
    }

    *(reinterpret_cast<unsigned long long*>(m_fenceMemAddr)) = m_fenceTargetValue;

    fprintf(stderr, "[Fence::signal] Successfully signaled fence ID: %d\n", m_fenceId);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult Fence::query()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_fenceMemAddr == nullptr)
    {
        fprintf(stderr, "[Fence::query] ERROR: m_fenceMemAddr is null for fence ID: %d\n", m_fenceId);
        return KFE_ERROR_INVALID_VALUE;
    }

    unsigned long long currentValue = *static_cast<unsigned long long*>(m_fenceMemAddr);

    fprintf(stderr, "[Fence::query] Fence ID: %d, current value: %llu, target value: %llu\n",
            m_fenceId, currentValue, m_fenceTargetValue);

    return (currentValue == m_fenceTargetValue) ? KFE_SUCCESS : KFE_ERROR_NOT_READY;
}

} // namespace kfe