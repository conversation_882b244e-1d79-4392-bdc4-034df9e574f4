#ifndef DEVICE_MANAGER_H_
#define DEVICE_MANAGER_H_

#include "kfe.h"
#include "thread_pool.h"

#include "os/os.h"

#include <atomic>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <thread>
#include <unordered_map>
#include <unordered_set>
#include <future>

namespace kfe
{
class Device;
class Context;
class UserModeQueue;
class DeviceManager
{
public:
    // Constructor
    DeviceManager();
    // Destructor
    ~DeviceManager();

    // Initialize the resource manager
    kfeResult init();

    // Device management
    kfeResult getDeviceCount(int* pCount);
    kfeResult openDevice(kfeDevice* pDevice, int gpuId, unsigned long long clientId);
    kfeResult closeDevice(kfeDevice device, unsigned long long clientId);

    // Register and unregister client info
    void registerClient(unsigned long long clientId);
    void unregisterClient(unsigned long long clientId);
private:
    const DeviceManager& operator=(const DeviceManager&) = delete; // Disable assignment
    DeviceManager(const DeviceManager&) = delete; // Disable copy constructor

    // Add a device to the resource manager
    void addDevice(Device* pDevice);

    // Remove device from the resource manager
    void removeDevice(Device* pDevice);

    // The singleton instance
    static DeviceManager m_sInstance;

    // Number of devices available
    int m_deviceCount;

    // Map of devices indexed by GPU ID
    std::unordered_map<int, Device*> m_devicesMap;

    // Synchronization primitives
    mutable std::mutex m_devicesMapMutex;
};

} // namespace kfe

#endif // DEVICE_MANAGER_H_