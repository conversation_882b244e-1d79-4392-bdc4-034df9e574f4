#include "context.h"
#include "device.h"
#include "physical_memory_object.h"

#include <chrono>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <iomanip>
#include <memory>
#include <sstream>
#include <type_traits>

namespace kfe
{
// =====================================================================================================================
PhysicalMemoryObject::PhysicalMemoryObject(Context* pContext, unsigned long long size, unsigned long long pageSize,
                                           unsigned long long flags, int memoryObjectId, PhysicalMemoryType type)
    : m_context(pContext)
    , m_size(size)
    , m_pageSize(pageSize)
    , m_flags(flags)
    , m_id(memoryObjectId)
    , m_type(type)
    , m_pDeviceMappedHandle(nullptr)
{
}

// =====================================================================================================================
PhysicalMemoryObject::~PhysicalMemoryObject()
{
}

// =====================================================================================================================
DevicePhysicalMemoryObject::DevicePhysicalMemoryObject(Context* pContext, unsigned long long size,
                                                       unsigned long long pageSize, unsigned long long flags,
                                                       int memoryObjectId)
    : PhysicalMemoryObject(pContext, size, pageSize, flags, memoryObjectId, KFE_PHYSICAL_MEMORY_TYPE_DEVICE)
    , m_pDeviceMemoryHandle(nullptr)
    , m_pHostMappedHandle(nullptr)
{
}

// =====================================================================================================================
DevicePhysicalMemoryObject::~DevicePhysicalMemoryObject()
{
    if (m_pDeviceMemoryHandle != nullptr)
    {
        free(m_pDeviceMemoryHandle);
        m_pDeviceMemoryHandle = nullptr;
    }

    // Increment the free memory size of the device
    Device* pDevice = m_context->getDevice();
    pDevice->incrementFreeMemorySize(m_size);
}

// =====================================================================================================================
kfeResult DevicePhysicalMemoryObject::init()
{
    if (m_pageSize != 4096)
    {
        // Currently only support 4KB page size
        fprintf(stderr, "Currently only 4KB page size is supported for device physical memory object.\n");
        return KFE_ERROR_INVALID_VALUE;
    }

    if (m_size % 4096 != 0)
    {
        // Ensure size is a multiple of page size (4KB)
        fprintf(stderr, "The size of device physical memory object must be a multiple of 4096 bytes.\n");
        return KFE_ERROR_INVALID_VALUE;
    }

    Device* pDevice = m_context->getDevice();

    if (pDevice->getFreeMemorySize() < m_size)
    {
        fprintf(stderr, "Not enough free memory on device %d for physical memory object of size %llu bytes.\n",
                pDevice->getGpuId(), m_size);
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    // Allocate device memory for the physical memory object
    m_pDeviceMemoryHandle = aligned_alloc(m_pageSize, m_size);
    if (m_pDeviceMemoryHandle == nullptr)
    {
        fprintf(stderr, "Failed to allocate device memory for PhysicalMemoryObject of size %llu bytes.\n", m_size);
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    // Decrement the free memory size of the device
    pDevice->decrementFreeMemorySize(m_size);

    return KFE_SUCCESS;
}

// ====================================================================================================================
HostPinnedMemoryObject::HostPinnedMemoryObject(Context* pContext, unsigned long long size,
                                               unsigned long long pageSize, unsigned long long flags,
                                               int memoryObjectId)
    : PhysicalMemoryObject(pContext, size, pageSize, flags, memoryObjectId, KFE_PHYSICAL_MEMORY_TYPE_PINNED)
    , m_pHostPinnedMemoryHandle(nullptr)
    , m_shmFd(-1)
    , m_shmName("")
{
}

// =====================================================================================================================
HostPinnedMemoryObject::~HostPinnedMemoryObject()
{
    if (m_pHostPinnedMemoryHandle != nullptr)
    {
        util::ipcSharedMemoryClose(m_shmName.c_str(), m_shmFd, m_pHostPinnedMemoryHandle, m_size);
        m_pHostPinnedMemoryHandle = nullptr;
        m_shmFd = -1;
        m_shmName = "";
    }
}

// =====================================================================================================================
kfeResult HostPinnedMemoryObject::init()
{
    // Generate a unique name for the shared memory object
    std::stringstream ss;

    // Get current time
    auto now = std::chrono::system_clock::now();
    auto timeT = std::chrono::system_clock::to_time_t(now);
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    // Get local time structure
    struct tm* timeInfo = localtime(&timeT);

    // Format: /kfe_pinned_mem_ctx<context_id>_obj<object_id>_<YYYYMMDD>_<HHMMSS>_<milliseconds>
    ss << "/kfe_pinned_mem_ctx" << m_context->getContextId()
       << "_obj" << m_id
       << "_" << std::setfill('0')
       << std::setw(4) << (timeInfo->tm_year + 1900)
       << std::setw(2) << (timeInfo->tm_mon + 1)
       << std::setw(2) << timeInfo->tm_mday
       << "_"
       << std::setw(2) << timeInfo->tm_hour
       << std::setw(2) << timeInfo->tm_min
       << std::setw(2) << timeInfo->tm_sec
       << "_"
       << std::setw(3) << milliseconds.count();

    m_shmName = ss.str();

    m_pHostPinnedMemoryHandle = util::ipcSharedMemoryCreate(m_shmName.c_str(), m_size, &m_shmFd);
    if ((m_pHostPinnedMemoryHandle == nullptr) || (m_shmFd == -1))
    {
        fprintf(stderr, "Failed to create shared memory object for host pinned memory.\n");
        return KFE_ERROR_OPERATING_SYSTEM;
    }

    memset(m_pHostPinnedMemoryHandle, 0, m_size);

    return KFE_SUCCESS;
}

// =====================================================================================================================
PageableMemoryObject::PageableMemoryObject(Context* pContext, unsigned long long size,
                                          unsigned long long pageSize, unsigned long long flags,
                                          int memoryObjectId, void* pHostVirtualAddress)
    : PhysicalMemoryObject(pContext, size, pageSize, flags, memoryObjectId, KFE_PHYSICAL_MEMORY_TYPE_PAGEABLE)
    , m_pHostPageableMemoryHandle(nullptr)
    , m_pUserHostVirtualAddress(pHostVirtualAddress)
    , m_pageableMemoryOffset(0)
    , m_clientPid(pContext->getClientPid())
{
}

// =====================================================================================================================
PageableMemoryObject::~PageableMemoryObject()
{
}

// =====================================================================================================================
kfeResult PageableMemoryObject::init()
{
    // Calculate offset for pageable memory management
    m_pageableMemoryOffset = reinterpret_cast<unsigned long long>(m_pUserHostVirtualAddress) & (m_pageSize - 1);
    m_pHostPageableMemoryHandle = reinterpret_cast<void*>(
        reinterpret_cast<unsigned long long>(m_pUserHostVirtualAddress) - m_pageableMemoryOffset);

    return KFE_SUCCESS;
}

// ====================================================================================================================
kfeResult PageableMemoryObject::readMemory(void* pLocalBuf, unsigned long long offset, unsigned long long size)
{
    if (offset + size > m_size)
    {
        fprintf(stderr, "Read exceeds the bounds of the pageable memory object"
                " (offset: %llu, size: %llu, object size: %llu).\n", offset, size, m_size);
        return KFE_ERROR_INVALID_VALUE;
    }

    // Use process_vm_readv to read memory from the client process
    struct iovec localIov = {0};
    localIov.iov_base = pLocalBuf;
    localIov.iov_len = size;

    struct iovec remoteIov = {0};
    remoteIov.iov_base = reinterpret_cast<void*>(
        reinterpret_cast<unsigned long long>(m_pHostPageableMemoryHandle) + m_pageableMemoryOffset + offset);
    remoteIov.iov_len = size;

    ssize_t bytesRead = process_vm_readv(m_clientPid, &localIov, 1, &remoteIov, 1, 0);
    if (bytesRead == -1)
    {
        fprintf(stderr, "Failed to read memory from client process (PID: %d). Error: %s\n",
                m_clientPid, strerror(errno));
        return KFE_ERROR_OPERATING_SYSTEM;
    }
    else if (static_cast<size_t>(bytesRead) != size)
    {
        fprintf(stderr, "Partial read from client process (PID: %d). Requested: %llu bytes, Read: %zd bytes\n",
                m_clientPid, size, bytesRead);
        return KFE_ERROR_OPERATING_SYSTEM;
    }

    return KFE_SUCCESS;
}

// ====================================================================================================================
kfeResult PageableMemoryObject::writeMemory(const void* pLocalBuf, unsigned long long offset, unsigned long long size)
{
    if (offset + size > m_size)
    {
        fprintf(stderr, "Write exceeds the bounds of the pageable memory object "
                " (offset: %llu, size: %llu, object size: %llu).\n", offset, size, m_size);
        return KFE_ERROR_INVALID_VALUE;
    }

    // Use process_vm_writev to write memory to the client process
    struct iovec localIov = {0};
    localIov.iov_base = const_cast<void*>(pLocalBuf);
    localIov.iov_len = size;

    struct iovec remoteIov = {0};
    remoteIov.iov_base = reinterpret_cast<void*>(
        reinterpret_cast<unsigned long long>(m_pHostPageableMemoryHandle) + m_pageableMemoryOffset + offset);
    remoteIov.iov_len = size;

    ssize_t bytesWritten = process_vm_writev(m_clientPid, &localIov, 1, &remoteIov, 1, 0);
    if (bytesWritten == -1)
    {
        fprintf(stderr, "Failed to write memory to client process (PID: %d). Error: %s\n",
                m_clientPid, strerror(errno));
        return KFE_ERROR_OPERATING_SYSTEM;
    }
    else if (static_cast<size_t>(bytesWritten) != size)
    {
        fprintf(stderr, "Partial write to client process (PID: %d). Requested: %llu bytes, Written: %zd bytes\n",
                m_clientPid, size, bytesWritten);
        return KFE_ERROR_OPERATING_SYSTEM;
    }

    return KFE_SUCCESS;
}

} // namespace kfe