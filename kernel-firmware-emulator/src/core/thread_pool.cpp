#include "thread_pool.h"

namespace kfe
{
// =====================================================================================================================
ThreadPool::ThreadPool(size_t threads) : m_stop(false)
{
    for (size_t i = 0; i < threads; ++i)
    {
        m_workers.emplace_back([this] {
            for (;;)
            {
                std::function<void()> task;
                {
                    std::unique_lock<std::mutex> lock(this->m_queueMutex);
                    this->m_condition.wait(lock, [this] { return this->m_stop || !this->m_tasks.empty(); });

                    if (this->m_stop && this->m_tasks.empty())
                    {
                        return;
                    }

                    // Get the next task from the queue
                    task = std::move(this->m_tasks.front());
                    this->m_tasks.pop();
                }
                task();
            }
        });
    }
}

// =====================================================================================================================
ThreadPool::~ThreadPool()
{
    stop();
}

// =====================================================================================================================
void ThreadPool::stop()
{
    {
        std::unique_lock<std::mutex> lock(m_queueMutex);
        m_stop = true;
    }

    m_condition.notify_all();
    for (std::thread& worker : m_workers)
    {
        if (worker.joinable())
        {
            worker.join();
        }
    }
}

} // namespace kfe