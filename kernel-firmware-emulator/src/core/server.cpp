#include "context.h"
#include "device.h"
#include "device_manager.h"
#include "fence.h"
#include "kfe_protocol.pb.h"
#include "physical_memory_object.h"
#include "process_sync_manager.h"
#include "server.h"
#include "user_mode_queue.h"

#include <arpa/inet.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/un.h>
#ifdef __linux__
#include <sys/epoll.h>
#elif __APPLE__
#include <sys/event.h>
#include <sys/time.h>
#endif
#include <fcntl.h>
#include <unistd.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <cstring>
#include <cstdlib>
#include <chrono>
#include <atomic>

// Platform-specific event handling
#ifdef __linux__
    // Linux uses epoll
    #define POLL_FD_TYPE int
    #define POLL_EVENT_TYPE struct epoll_event
    #define POLL_EVENT_IN EPOLLIN
    #define POLL_EVENT_HUP EPOLLHUP
    #define POLL_EVENT_ERR EPOLLERR
    #define POLL_EVENT_RDHUP EPOLLRDHUP
    #define POLL_EVENT_ET EPOLLET
#elif __APPLE__
    // macOS uses kqueue
    #define POLL_FD_TYPE int
    #define POLL_EVENT_TYPE struct kevent
    #define POLL_EVENT_IN EVFILT_READ
    // No direct equivalent of EPOLLHUP in kqueue;
    // check for EV_EOF in event flags for disconnects.
    #define POLL_EVENT_HUP 0
    #define POLL_EVENT_ERR EV_ERROR
    #define POLL_EVENT_RDHUP EV_EOF
    #define POLL_EVENT_ET 0
#endif

namespace kfe
{
// Global atomic flag for signal handling (kept minimal for async-signal-safety)
static std::atomic<bool> g_shutdownRequested{false};

// Signal handler for graceful shutdown
void signalHandler(int signal)
{
    // IMPORTANT: Signal handlers must only use async-signal-safe functions
    // Async-signal-safe functions include: write(), _exit(), etc.
    // NOT safe: fprintf(), printf(), malloc(), free(), mutex operations, etc.

    // Use write() instead of fprintf() - it's async-signal-safe
    const char* msg = "Received signal, shutting down server...\n";
    ssize_t result = write(STDERR_FILENO, msg, strlen(msg) - 1);
    (void)result; // Suppress unused variable warning in signal handler

    // Set atomic flag to request shutdown (atomic operations are generally safe)
    // The main server loop will detect this and call stop() for graceful cleanup
    g_shutdownRequested.store(true);
}

// =====================================================================================================================
// This function is defined in server.cpp
// server is the actual server logic that runs in the child process.
extern "C"
void runServerMain(const std::string& socketPath, const std::string& lockFilePath, const std::string& pidFilePath)
{
    // Create server instance - RAII will handle cleanup automatically
    std::unique_ptr<Server> serverInstance;

    try
    {
        serverInstance = std::make_unique<Server>(socketPath, lockFilePath, pidFilePath);
    }
    catch (const std::exception& e)
    {
        fprintf(stderr, "Failed to create server instance: %s\n", e.what());
        return;
    }

    // Set up signal handling for graceful shutdown
    signal(SIGTERM, signalHandler);
    signal(SIGINT, signalHandler);
    signal(SIGPIPE, SIG_IGN); // Ignore broken pipe signals

    try
    {
        fprintf(stdout, "Starting KFE server on %s\n", socketPath.c_str());
        serverInstance->start();
    }
    catch (const std::exception& e)
    {
        fprintf(stderr, "Server error: %s\n", e.what());
        // RAII will automatically clean up serverInstance
        std::exit(1);
    }
    catch (...)
    {
        fprintf(stderr, "Unknown server error occurred\n");
        // RAII will automatically clean up serverInstance
        std::exit(1);
    }

    try
    {
        serverInstance->wait();
    }
    catch(const std::exception& e)
    {
        fprintf(stderr, "Server wait error: %s\n", e.what());
        std::exit(1);
    }

    // Normal exit - RAII will automatically clean up serverInstance
    fprintf(stdout, "Server finished normally\n");
}

// =====================================================================================================================
Server::Server(const std::string& socketPath, const std::string& lockFilePath, const std::string& pidFilePath)
    : m_socketPath(socketPath)
    , m_lockFilePath(lockFilePath)
    , m_sendMutex()
    , m_recvMutex()
    , m_serverFd(-1)
    , m_epollFd(-1)
    , m_running(false)
    , m_workerPool(std::thread::hardware_concurrency())
    , m_clients()
    , m_fdToClientId()
    , m_clientsMutex()
    , m_nextClientId(0)
    , m_serverPidFilePath(pidFilePath)
{
    // Initialize the ProcessSyncManager
    m_syncManager = std::unique_ptr<ProcessSyncManager>(new(std::nothrow) ProcessSyncManager(m_lockFilePath));
    if (m_syncManager == nullptr)
    {
        fprintf(stderr, "Failed to create ProcessSyncManager.\n");
        throw std::runtime_error("Failed to create ProcessSyncManager");
    }

    // Initialize the resource manager once during server startup
    m_deviceManager = std::unique_ptr<DeviceManager>(new(std::nothrow) DeviceManager());
    if (m_deviceManager == nullptr)
    {
        fprintf(stderr, "Failed to create device manager.\n");
        throw std::runtime_error("Failed to create device manager");
    }

    kfeResult result = m_deviceManager->init();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to initialize device manager: %d\n", result);
        throw std::runtime_error("Failed to initialize device manager");
    }
    else
    {
        fprintf(stdout, "Device manager initialized successfully\n");
    }
}

// =====================================================================================================================
Server::~Server()
{
    stop();
}

// =====================================================================================================================
unsigned long long Server::getClientIdByFd(int clientFd)
{
    std::lock_guard<std::mutex> lock(m_clientsMutex);
    auto it = m_fdToClientId.find(clientFd);
    if (it != m_fdToClientId.end())
    {
        return it->second;
    }

    fprintf(stderr, "getClientIdByFd: No client ID found for fd %d\n", clientFd);
    return std::numeric_limits<unsigned long long>::max(); // Invalid client ID
}

// =====================================================================================================================
void Server::start()
{
    if (m_running.load())
    {
        fprintf(stderr, "Server is already running.\n");
        return;
    }

    // Create Unix Domain Socket
    if (createUnixSocket() < 0)
    {
        fprintf(stderr, "Failed to create Unix Domain Socket.\n");
        return;
    }

    // create event polling instance
#ifdef __linux__
    m_epollFd = epoll_create1(0);
    if (m_epollFd < 0)
    {
        fprintf(stderr, "Failed to create epoll instance: %s\n", strerror(errno));
        close(m_serverFd);
        m_serverFd = -1;
        return;
    }
#elif __APPLE__
    m_epollFd = kqueue();
    if (m_epollFd < 0)
    {
        fprintf(stderr, "Failed to create kqueue instance: %s\n", strerror(errno));
        close(m_serverFd);
        m_serverFd = -1;
        return;
    }
#endif

    // Add server socket to event polling
#ifdef __linux__
    struct epoll_event ev;
    ev.events = EPOLLIN | EPOLLRDHUP;
    ev.data.fd = m_serverFd;
    if (epoll_ctl(m_epollFd, EPOLL_CTL_ADD, m_serverFd, &ev) < 0)
    {
        fprintf(stderr, "Failed to add server socket to epoll: %s\n", strerror(errno));
        close(m_serverFd);
        m_serverFd = -1;
        close(m_epollFd);
        m_epollFd = -1;
        return;
    }
#elif __APPLE__
    struct kevent ev;
    EV_SET(&ev, m_serverFd, EVFILT_READ, EV_ADD | EV_ENABLE, 0, 0, nullptr);
    if (kevent(m_epollFd, &ev, 1, nullptr, 0, nullptr) < 0)
    {
        fprintf(stderr, "Failed to add server socket to kqueue: %s\n", strerror(errno));
        close(m_serverFd);
        m_serverFd = -1;
        close(m_epollFd);
        m_epollFd = -1;
        return;
    }
#endif

    m_running = true;

    // Start main thread to accept clients
    fprintf(stdout, "Server started, listening on %s\n", m_socketPath.c_str());

#ifdef __linux__
    struct epoll_event events[MAX_EVENTS];
#elif __APPLE__
    struct kevent events[MAX_EVENTS];
    struct timespec timeout;
    timeout.tv_sec = 1;
    timeout.tv_nsec = 0;
#endif

    while (m_running.load() && !g_shutdownRequested.load())
    {
        // Wait for events on the server socket
#ifdef __linux__
        int nfds = epoll_wait(m_epollFd, events, MAX_EVENTS, 1000);
#elif __APPLE__
        int nfds = kevent(m_epollFd, nullptr, 0, events, MAX_EVENTS, &timeout);
#endif
        if (nfds == -1)
        {
            if (errno == EINTR)
            {
                // Check shutdown flag after EINTR
                if (g_shutdownRequested.load())
                {
                    break;
                }

                // Continue to wait for events
                continue;
            }

#ifdef __linux__
            fprintf(stderr, "epoll_wait error: %s\n", strerror(errno));
#elif __APPLE__
            fprintf(stderr, "kevent error: %s\n", strerror(errno));
#endif
            break;
        }

        for (int i = 0; i < nfds; i++)
        {
#ifdef __linux__
            if (events[i].data.fd == m_serverFd)
#elif __APPLE__
            if ((int)events[i].ident == m_serverFd)
#endif
            {
                // New client connection
                struct sockaddr_un client_addr;
                socklen_t client_len = sizeof(client_addr);
                int clientFd = accept(m_serverFd, (struct sockaddr*)&client_addr, &client_len);
                if (clientFd < 0)
                {
                    fprintf(stdout, "Failed to accept client: %s.\n", strerror(errno));
                    continue;
                }

                fprintf(stdout, "New client connected, fd: %d.\n", clientFd);

                if (!handleClientConnect(clientFd))
                {
                    fprintf(stderr, "Failed to handle new client connection.\n");
                    close(clientFd);
                }
            }
            else
            {
                // client socket has new events
#ifdef __linux__
                int clientFd = events[i].data.fd;
                if (events[i].events & (EPOLLHUP | EPOLLERR))
#elif __APPLE__
                int clientFd = (int)events[i].ident;
                if (events[i].flags & EV_EOF)
#endif
                {
                    // Process client disconnect
                    handleClientDisconnect(clientFd);
                }
#ifdef __linux__
                else if (events[i].events & EPOLLIN)
#elif __APPLE__
                else if (events[i].filter == EVFILT_READ)
#endif
                {
                    // Process client request
                    m_workerPool.enqueue([this, clientFd] { processRequest(clientFd); });
                }
            }
        }
    }
}

// =====================================================================================================================
void Server::stop()
{
    if (!m_running.load())
    {
        return;
    }

    fprintf(stdout, "[Server] Stopping server...\n");

    m_running = false;

    // Close all client sockets
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        for (auto& [clientId, clientInfo] : m_clients)
        {
            if (clientInfo->socketFd != -1)
            {
                close(clientInfo->socketFd);
                clientInfo->socketFd = -1;
            }
        }

        m_clients.clear();
        m_fdToClientId.clear();
    }

    // Close the server socket
    if (m_epollFd >= 0)
    {
        close(m_epollFd);
        m_epollFd = -1;
    }

    if (m_serverFd >= 0)
    {
        close(m_serverFd);
        m_serverFd = -1;
    }

    // Stop the worker pool
    m_workerPool.stop();

    if (m_heartbeatThread.joinable())
    {
        m_heartbeatThread.join();
    }

    // Clean up the socket file and PID file
    unlink(m_socketPath.c_str());
    unlink(m_serverPidFilePath.c_str());

    // Release the exclusive lock
    if (m_syncManager)
    {
        m_syncManager->releaseLock();
    }

    fprintf(stdout, "[Server] Server stopped.\n");
}

// =====================================================================================================================
void Server::wait()
{
    // Wait for the server to stop
    while (m_running.load())
    {
        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            if (m_clients.empty())
            {
                fprintf(stdout, "[Server] No clients connected, shutting down...\n");
                break;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    stop();
}

// =====================================================================================================================
int Server::createUnixSocket()
{
    // Try to acquire exclusive lock to ensure only one server instance runs
    if (!m_syncManager->acquireExclusiveLock())
    {
        fprintf(stderr, "Failed to acquire server exclusive lock. Another server may be running.\n");
        return -1;
    }

    fprintf(stdout, "Server acquired exclusive lock successfully.\n");

    m_serverFd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (m_serverFd == -1)
    {
        fprintf(stderr, "Failed to create socket: %s.\n", strerror(errno));
        m_syncManager->releaseLock();
        return -1;
    }

    // Set socket options
    int opt = 1;
    if (setsockopt(m_serverFd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0)
    {
        fprintf(stderr, "Failed to set socket options: %s.\n", strerror(errno));
        close(m_serverFd);
        m_serverFd = -1;
        m_syncManager->releaseLock();
        return -1;
    }

    // Set the socket to non-blocking mode
    int flags = fcntl(m_serverFd, F_GETFL, 0);
    fcntl(m_serverFd, F_SETFL, flags | O_NONBLOCK);

    // Bind the socket to the specified path
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, m_socketPath.c_str(), sizeof(addr.sun_path) - 1);

    // Clean up any existing socket file
    unlink(m_socketPath.c_str());

    if (bind(m_serverFd, (struct sockaddr*)&addr, sizeof(addr)) < 0)
    {
        fprintf(stderr, "Failed to bind socket: %s\n", strerror(errno));
        close(m_serverFd);
        m_serverFd = -1;
        m_syncManager->releaseLock();
        return -1;
    }

    if (listen(m_serverFd, 128) < 0)
    {
        fprintf(stderr, "Failed to listen on socket: %s\n", strerror(errno));
        close(m_serverFd);
        m_serverFd = -1;
        m_syncManager->releaseLock();
        return -1;
    }

     // Set the socket file permissions
    chmod(m_socketPath.c_str(), 0666);

    // Start the accepting thread.

    // We can use this lock_fd to ensure that the server is not started multiple times
    fprintf(stderr, "Server socket created successfully at %s.\n", m_socketPath.c_str());

    return 0;
}

// =====================================================================================================================
bool Server::addClientToEpoll(int epollFd, int clientFd)
{
    int flags = fcntl(clientFd, F_GETFL, 0);
    fcntl(clientFd, F_SETFL, flags | O_NONBLOCK);

#ifdef __linux__
    struct epoll_event ev;
    ev.events = EPOLLIN | EPOLLET;
    ev.data.fd = clientFd;

    if (epoll_ctl(epollFd, EPOLL_CTL_ADD, clientFd, &ev) == -1)
    {
        fprintf(stderr, "Failed to add client to epoll: %s.\n", strerror(errno));
        return false;
    }
#elif __APPLE__
    struct kevent ev;
    EV_SET(&ev, clientFd, EVFILT_READ, EV_ADD | EV_ENABLE, 0, 0, nullptr);

    if (kevent(epollFd, &ev, 1, nullptr, 0, nullptr) == -1)
    {
        fprintf(stderr, "Failed to add client to kqueue: %s.\n", strerror(errno));
        return false;
    }
#endif

    return true;
}

// =====================================================================================================================
bool Server::removeClientFromEpoll(int epollFd, int clientFd)
{
#ifdef __linux__
    if (epoll_ctl(epollFd, EPOLL_CTL_DEL, clientFd, nullptr) == -1)
    {
        fprintf(stderr, "Failed to remove client from epoll: %s.\n", strerror(errno));
        return false;
    }
#elif __APPLE__
    struct kevent ev;
    EV_SET(&ev, clientFd, EVFILT_READ, EV_DELETE, 0, 0, nullptr);

    if (kevent(epollFd, &ev, 1, nullptr, 0, nullptr) == -1)
    {
        fprintf(stderr, "Failed to remove client from kqueue: %s.\n", strerror(errno));
        return false;
    }
#endif

    return true;
}

// =====================================================================================================================
void Server::processRequest(int clientFd)
{
    KfeRequest request;

    if (!receiveRequest(clientFd, request))
    {
        handleClientDisconnect(clientFd);
        return;
    }

    // Process the request
    KfeResponse response = handleRequest(clientFd, request);
    response.set_request_id(request.request_id());

    // Send the response back to the client
    if (!sendResponse(clientFd, response))
    {
        handleClientDisconnect(clientFd);
        return;
    }

    {
        // Update the heartbeat time for the client
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        for (auto& [clientId, clientInfo] : m_clients)
        {
            if (clientInfo->socketFd == clientFd)
            {
                clientInfo->lastHeartbeat = std::chrono::steady_clock::now();
                break;
            }
        }
    }
}

// =====================================================================================================================
bool Server::handleClientConnect(int clientFd)
{
    if (!addClientToEpoll(m_epollFd, clientFd))
    {
        fprintf(stderr, "Failed to add client to epoll.\n");
        return false;
    }

    unsigned long long clientId = allocateClientId();

    auto clientInfo = std::make_unique<ClientInfo>();
    clientInfo->socketFd = clientFd;
    clientInfo->lastActivity = std::chrono::steady_clock::now();
    clientInfo->lastHeartbeat = std::chrono::steady_clock::now();

    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        m_clients[clientId] = std::move(clientInfo);
        m_fdToClientId[clientFd] = clientId;
    }

    return true;
}

// =====================================================================================================================
void Server::handleClientDisconnect(int clientFd)
{
    printf("Client disconnected, fd: %d\n", clientFd);

    unsigned long long clientId = 0;
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        auto it = m_fdToClientId.find(clientFd);
        if (it != m_fdToClientId.end())
        {
            clientId = it->second;
            m_fdToClientId.erase(it);
            m_clients.erase(clientId);
        }
    }

    if (m_clients.empty())
    {
        g_shutdownRequested.store(true);
        fprintf(stdout, "No more clients connected, shutting down server...\n");
    }

    removeClientFromEpoll(m_epollFd, clientFd);
    close(clientFd);
}

// =====================================================================================================================
KfeResponse Server::handleRequest(int clientFd, const KfeRequest& request)
{
    KfeResponse response;

    try
    {
        // Check if the client is registered
        unsigned long long clientId = getClientIdByFd(clientFd);
        if (clientId == std::numeric_limits<unsigned long long>::max())
        {
            fprintf(stderr, "handleRequest: No client ID found for fd %d\n", clientFd);
            response.set_result(KFE_ERROR_INVALID_VALUE);
            response.set_error_message("Client not found");
            return response;
        }

        switch (request.type())
        {
        case KFE_REQUEST_TYPE_INITIALIZE:
            response = handleInitialize(clientFd, request.initialize());
            break;
        case KFE_REQUEST_TYPE_FINALIZE:
            response = handleFinalize(clientId, request.finalize());
            break;
        case KFE_REQUEST_TYPE_DEVICE_GET_COUNT:
            response = handleDeviceGetCount(clientId, request.device_get_count());
            break;
        case KFE_REQUEST_TYPE_DEVICE_OPEN:
            response = handleDeviceOpen(clientId, request.device_open());
            break;
        case KFE_REQUEST_TYPE_DEVICE_CLOSE:
            response = handleDeviceClose(clientId, request.device_close());
            break;
        case KFE_REQUEST_TYPE_DEVICE_GET_PROPERTIES:
            response = handleDeviceGetProperties(clientId, request.device_get_properties());
            break;
        case KFE_REQUEST_TYPE_CONTEXT_CREATE:
            response = handleContextCreate(clientId, request.context_create());
            break;
        case KFE_REQUEST_TYPE_CONTEXT_DESTROY:
            response = handleContextDestroy(clientId, request.context_destroy());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_CREATE:
            response = handlePhysicalMemoryCreate(clientId, request.physical_memory_create());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DESTROY:
            response = handlePhysicalMemoryDestroy(clientId, request.physical_memory_destroy());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DEVICE_MAP:
            response = handlePhysicalMemoryDeviceMap(clientId, request.physical_memory_device_map());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DEVICE_UNMAP:
            response = handlePhysicalMemoryDeviceUnmap(clientId, request.physical_memory_device_unmap());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_HOST_MAP:
            response = handlePhysicalMemoryHostMap(clientId, request.physical_memory_host_map());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_HOST_UNMAP:
            response = handlePhysicalMemoryHostUnmap(clientId, request.physical_memory_host_unmap());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_EXPORT_BY_DMABUF:
            response = handlePhysicalMemoryExportByDmaBuf(clientId, request.physical_memory_export_by_dmabuf());
            break;
        case KFE_REQUEST_TYPE_PHYSICAL_MEMORY_IMPORT_FROM_DMABUF:
            response = handlePhysicalMemoryImportFromDmaBuf(clientId, request.physical_memory_import_from_dmabuf());
            break;
        case KFE_REQUEST_TYPE_PINNED_MEMORY_CREATE:
            response = handlePinnedMemoryCreate(clientId, request.pinned_memory_create());
            break;
        case KFE_REQUEST_TYPE_PINNED_MEMORY_DESTROY:
            response = handlePinnedMemoryDestroy(clientId, request.pinned_memory_destroy());
            break;
        case KFE_REQUEST_TYPE_PAGEABLE_MEMORY_HOST_REGISTER:
            response = handlePageableMemoryHostRegister(clientId, request.pageable_memory_host_register());
            break;
        case KFE_REQUEST_TYPE_PAGEABLE_MEMORY_HOST_UNREGISTER:
            response = handlePageableMemoryHostUnregister(clientId, request.pageable_memory_host_unregister());
            break;
        case KFE_REQUEST_TYPE_USER_MODE_QUEUE_CREATE:
            response = handleUserModeQueueCreate(clientId, request.user_mode_queue_create());
            break;
        case KFE_REQUEST_TYPE_USER_MODE_QUEUE_DESTROY:
            response = handleUserModeQueueDestroy(clientId, request.user_mode_queue_destroy());
            break;
        case KFE_REQUEST_TYPE_USER_MODE_QUEUE_SUBMIT:
            response = handleUserModeQueueSubmit(clientId, request.user_mode_queue_submit());
            break;
        case KFE_REQUEST_TYPE_FENCE_CREATE:
            response = handleFenceCreate(clientId, request.fence_create());
            break;
        case KFE_REQUEST_TYPE_FENCE_DESTROY:
            response = handleFenceDestroy(clientId, request.fence_destroy());
            break;
        case KFE_REQUEST_TYPE_FENCE_SIGNAL:
            response = handleFenceSignal(clientId, request.fence_signal());
            break;
        case KFE_REQUEST_TYPE_FENCE_QUERY:
            response = handleFenceQuery(clientId, request.fence_query());
            break;
        default:
            response.set_result(KFE_ERROR_INVALID_VALUE);
            response.set_error_message("Unknown request type");
            break;
        }
    }
    catch(const std::exception& e)
    {
        response.set_result(KFE_ERROR_OPERATING_SYSTEM);
        response.set_error_message(e.what());
    }

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleInitialize(unsigned long long clientId, const InitializeRequest& req)
{
    KfeResponse response;

    // Register the client with the resource manager
    m_deviceManager->registerClient(clientId);

    auto initResp = response.mutable_initialize();
    initResp->set_client_id(clientId); // Register the client
    initResp->set_server_version("1.0.0");
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client initialized: %s (ID: %llu)\n", req.client_name().c_str(), clientId);

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleFinalize(unsigned long long clientId, const FinalizeRequest& req)
{
    KfeResponse response;

    // Unregister the client from resource manager
    m_deviceManager->unregisterClient(clientId);

    // Remove client from server's client list
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        auto it = m_clients.find(clientId);
        if (it != m_clients.end())
        {
            int clientFd = it->second->socketFd;
            m_clients.erase(it);
            m_fdToClientId.erase(clientFd);
        }

        if (m_clients.empty())
        {
            g_shutdownRequested.store(true);
            fprintf(stdout, "No more clients connected, shutting down server...\n");
        }
    }

    response.set_result(KFE_SUCCESS);
    response.mutable_finalize();

    fprintf(stdout, "Client finalized: ID %llu\n", clientId);

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleDeviceGetCount(unsigned long long clientId, const DeviceGetCountRequest& req)
{
    KfeResponse response;

    int deviceCount = 0;
    kfeResult result = m_deviceManager->getDeviceCount(&deviceCount);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to get device count");
        return response;
    }

    auto resp = response.mutable_device_get_count();
    resp->set_device_count(deviceCount);
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client %llu requested device count: %d\n", clientId, deviceCount);

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleDeviceOpen(unsigned long long clientId, const DeviceOpenRequest& req)
{
    KfeResponse response;

    kfeDevice deviceHandle = 0;
    kfeResult result = m_deviceManager->openDevice(reinterpret_cast<kfeDevice*>(&deviceHandle),
                                                   req.gpu_id(),
                                                   clientId);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to open device");
        return response;
    }

    auto resp = response.mutable_device_open();
    resp->set_device_handle(reinterpret_cast<unsigned long long>(deviceHandle));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu opened device %d with handle %llu\n", clientId, req.gpu_id(),
            reinterpret_cast<unsigned long long>(deviceHandle));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleDeviceClose(unsigned long long clientId, const DeviceCloseRequest& req)
{
    KfeResponse response;

    kfeDevice deviceHandle = reinterpret_cast<kfeDevice>(req.device_handle());
    kfeResult result = m_deviceManager->closeDevice(deviceHandle, clientId);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to close device");
        return response;
    }

    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu closed device with handle %llu\n", clientId,
            reinterpret_cast<unsigned long long>(deviceHandle));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleDeviceGetProperties(unsigned long long clientId, const DeviceGetPropertiesRequest& req)
{
    KfeResponse response;

    Device* pDevice = reinterpret_cast<Device*>(req.device_handle());
    auto properties = pDevice->getProperties();

    auto resp = response.mutable_device_get_properties();
    auto props = resp->mutable_properties();

    props->set_name(properties.name);
    props->set_uuid(properties.uuid);
    props->set_total_global_mem(properties.totalGlobalMem);
    props->set_regs_per_block(properties.regsPerBlock);
    props->set_warp_size(properties.warpSize);
    props->set_mem_pitch(properties.memPitch);
    props->set_max_threads_per_block(properties.maxThreadsPerBlock);
    props->mutable_max_threads_dim()->set_x(properties.maxThreadsDim[0]);
    props->mutable_max_threads_dim()->set_y(properties.maxThreadsDim[1]);
    props->mutable_max_threads_dim()->set_z(properties.maxThreadsDim[2]);
    props->mutable_max_grid_size()->set_x(properties.maxGridSize[0]);
    props->mutable_max_grid_size()->set_y(properties.maxGridSize[1]);
    props->mutable_max_grid_size()->set_z(properties.maxGridSize[2]);
    props->set_clock_rate(properties.clockRate);
    props->set_total_const_mem(properties.totalConstMem);
    props->set_major(properties.major);
    props->set_minor(properties.minor);
    props->set_texture_alignment(properties.textureAlignment);
    props->set_texture_pitch_alignment(properties.texturePitchAlignment);
    props->set_device_overlap(properties.deviceOverlap);
    props->set_multi_processor_count(properties.multiProcessorCount);
    props->set_kernel_exec_timeout_enabled(properties.kernelExecTimeoutEnabled);
    props->set_integrated(properties.integrated);
    props->set_can_map_host_memory(properties.canMapHostMemory);
    props->set_compute_mode(properties.computeMode);
    props->set_max_texture_1d(properties.maxTexture1D);
    props->set_max_texture_1d_mipmap(properties.maxTexture1DMipmap);
    props->set_max_texture_1d_linear(properties.maxTexture1DLinear);
    props->mutable_max_texture_2d()->set_x(properties.maxTexture2D[0]);
    props->mutable_max_texture_2d()->set_y(properties.maxTexture2D[1]);
    props->mutable_max_texture_2d_mipmap()->set_x(properties.maxTexture2DMipmap[0]);
    props->mutable_max_texture_2d_mipmap()->set_y(properties.maxTexture2DMipmap[1]);
    props->mutable_max_texture_2d_linear()->set_x(properties.maxTexture2DLinear[0]);
    props->mutable_max_texture_2d_linear()->set_y(properties.maxTexture2DLinear[1]);
    props->mutable_max_texture_2d_gather()->set_x(properties.maxTexture2DGather[0]);
    props->mutable_max_texture_2d_gather()->set_y(properties.maxTexture2DGather[1]);
    props->mutable_max_texture_3d()->set_x(properties.maxTexture3D[0]);
    props->mutable_max_texture_3d()->set_y(properties.maxTexture3D[1]);
    props->mutable_max_texture_3d()->set_z(properties.maxTexture3D[2]);
    props->mutable_max_texture_3d_alt()->set_x(properties.maxTexture3DAlt[0]);
    props->mutable_max_texture_3d_alt()->set_y(properties.maxTexture3DAlt[1]);
    props->mutable_max_texture_3d_alt()->set_z(properties.maxTexture3DAlt[2]);
    props->set_max_texture_cubemap(properties.maxTextureCubemap);
    props->mutable_max_texture_1d_layered()->set_x(properties.maxTexture1DLayered[0]);
    props->mutable_max_texture_1d_layered()->set_y(properties.maxTexture1DLayered[1]);
    props->mutable_max_texture_2d_layered()->set_x(properties.maxTexture2DLayered[0]);
    props->mutable_max_texture_2d_layered()->set_y(properties.maxTexture2DLayered[1]);
    props->mutable_max_texture_2d_layered()->set_z(properties.maxTexture2DLayered[2]);
    props->mutable_max_texture_cubemap_layered()->set_x(properties.maxTextureCubemapLayered[0]);
    props->mutable_max_texture_cubemap_layered()->set_y(properties.maxTextureCubemapLayered[1]);
    props->set_max_surface_1d(properties.maxSurface1D);
    props->mutable_max_surface_2d()->set_x(properties.maxSurface2D[0]);
    props->mutable_max_surface_2d()->set_y(properties.maxSurface2D[1]);
    props->mutable_max_surface_3d()->set_x(properties.maxSurface3D[0]);
    props->mutable_max_surface_3d()->set_y(properties.maxSurface3D[1]);
    props->mutable_max_surface_3d()->set_z(properties.maxSurface3D[2]);
    props->mutable_max_surface_1d_layered()->set_x(properties.maxSurface1DLayered[0]);
    props->mutable_max_surface_1d_layered()->set_y(properties.maxSurface1DLayered[1]);
    props->mutable_max_surface_2d_layered()->set_x(properties.maxSurface2DLayered[0]);
    props->mutable_max_surface_2d_layered()->set_y(properties.maxSurface2DLayered[1]);
    props->mutable_max_surface_2d_layered()->set_z(properties.maxSurface2DLayered[2]);
    props->set_max_surface_cubemap(properties.maxSurfaceCubemap);
    props->mutable_max_surface_cubemap_layered()->set_x(properties.maxSurfaceCubemapLayered[0]);
    props->mutable_max_surface_cubemap_layered()->set_y(properties.maxSurfaceCubemapLayered[1]);
    props->set_surface_alignment(properties.surfaceAlignment);
    props->set_concurrent_kernels(properties.concurrentKernels);
    props->set_ecc_enabled(properties.ECCEnabled);
    props->set_pci_bus_id(properties.pciBusID);
    props->set_pci_device_id(properties.pciDeviceID);
    props->set_pci_domain_id(properties.pciDomainID);
    props->set_tcc_driver(properties.tccDriver);
    props->set_async_engine_count(properties.asyncEngineCount);
    props->set_unified_addressing(properties.unifiedAddressing);
    props->set_memory_clock_rate(properties.memoryClockRate);
    props->set_memory_bus_width(properties.memoryBusWidth);
    props->set_l2_cache_size(properties.l2CacheSize);
    props->set_persisting_l2_cache_max_size(properties.persistingL2CacheMaxSize);
    props->set_max_threads_per_multi_processor(properties.maxThreadsPerMultiProcessor);
    props->set_regs_per_multiprocessor(properties.regsPerMultiprocessor);
    props->set_managed_memory(properties.managedMemory);
    props->set_is_multi_gpu_board(properties.isMultiGpuBoard);
    props->set_multi_gpu_board_group_id(properties.multiGpuBoardGroupID);
    props->set_host_native_atomic_supported(properties.hostNativeAtomicSupported);
    props->set_single_to_double_precision_perf_ratio(properties.singleToDoublePrecisionPerfRatio);
    props->set_pageable_memory_access(properties.pageableMemoryAccess);
    props->set_concurrent_managed_access(properties.concurrentManagedAccess);
    props->set_compute_preemption_supported(properties.computePreemptionSupported);
    props->set_can_use_host_pointer_for_registered_mem(properties.canUseHostPointerForRegisteredMem);
    props->set_cooperative_launch(properties.cooperativeLaunch);
    props->set_cooperative_multi_device_launch(properties.cooperativeMultiDeviceLaunch);
    props->set_shared_mem_per_block_optin(properties.sharedMemPerBlockOptin);
    props->set_pageable_memory_access_uses_host_page_tables(properties.pageableMemoryAccessUsesHostPageTables);
    props->set_direct_managed_mem_access_from_host(properties.directManagedMemAccessFromHost);
    props->set_max_blocks_per_multiprocessor(properties.maxBlocksPerMultiProcessor);
    props->set_access_policy_max_window_size(properties.accessPolicyMaxWindowSize);
    props->set_reserved_shared_mem_per_block(properties.reservedSharedMemPerBlock);
    props->set_host_register_supported(properties.hostRegisterSupported);
    props->set_sparse_cuda_array_supported(properties.sparseCudaArraySupported);
    props->set_host_register_read_only_supported(properties.hostRegisterReadOnlySupported);
    props->set_timeline_semaphore_interop_supported(properties.timelineSemaphoreInteropSupported);
    props->set_memory_pools_supported(properties.memoryPoolsSupported);
    props->set_gpu_direct_rdma_supported(properties.gpuDirectRDMASupported);
    props->set_gpu_direct_rdma_flush_writes_options(properties.gpuDirectRDMAFlushWritesOptions);
    props->set_gpu_direct_rdma_writes_ordering(properties.gpuDirectRDMAWritesOrdering);
    props->set_memory_pool_supported_handle_types(properties.memoryPoolSupportedHandleTypes);
    props->set_deferred_mapping_cuda_array_supported(properties.deferredMappingCudaArraySupported);
    props->set_ipc_event_supported(properties.ipcEventSupported);
    props->set_unified_function_pointers(properties.unifiedFunctionPointers);
    props->set_physical_memory_alignment(properties.physicalMemoryAlignment);

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client %llu requested device properties for handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pDevice));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleContextCreate(unsigned long long clientId, const ContextCreateRequest& req)
{
    KfeResponse response;

    Context* pContext = nullptr;
    Device* pDevice = reinterpret_cast<Device*>(req.device_handle());
    unsigned int flags = req.flags();
    util::OsPid clientPid = req.pid();

    kfeResult result = pDevice->createContext(&pContext, flags, clientId, clientPid);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to create context");
        return response;
    }

    auto resp = response.mutable_context_create();
    resp->set_context_handle(reinterpret_cast<unsigned long long>(pContext));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu created context with handle %llu for device %llu and flags %u\n",
            clientId, reinterpret_cast<unsigned long long>(pContext), reinterpret_cast<unsigned long long>(pDevice),
            flags);

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleContextDestroy(unsigned long long clientId, const ContextDestroyRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    Device* pDevice = pContext->getDevice();

    kfeResult result = pDevice->destroyContext(pContext);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to destroy context");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu destroyed context with handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pContext));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryCreate(unsigned long long clientId, const PhysicalMemoryCreateRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    unsigned long long size = req.size();
    unsigned long long pageSize = req.page_size();
    unsigned long long flags = req.flags();
    DevicePhysicalMemoryObject* pPhysicalMemoryObject = nullptr;
    kfeResult result = pContext->createDevicePhysicalMemory(req.size(), pageSize, flags, &pPhysicalMemoryObject);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to create physical memory");
        return response;
    }

    auto resp = response.mutable_physical_memory_create();
    resp->set_physical_memory_handle(reinterpret_cast<unsigned long long>(pPhysicalMemoryObject));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu created physical memory with handle %llu of size %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pPhysicalMemoryObject),
            static_cast<unsigned long long>(req.size()));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryDestroy(unsigned long long clientId, const PhysicalMemoryDestroyRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    DevicePhysicalMemoryObject* pPhysicalMemoryObject =
        reinterpret_cast<DevicePhysicalMemoryObject*>(req.physical_memory_handle());
    kfeResult result = pContext->destroyDevicePhysicalMemory(pPhysicalMemoryObject);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to destroy physical memory");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu destroyed physical memory with handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pPhysicalMemoryObject));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryDeviceMap(unsigned long long clientId,
                                                  const PhysicalMemoryDeviceMapRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    unsigned long long deviceVirtualAddress = req.device_virtual_address();
    PhysicalMemoryObject* pPhysicalMemoryObject = reinterpret_cast<PhysicalMemoryObject*>(req.physical_memory_handle());
    unsigned long long size = req.size();
    unsigned long long flags = req.flags();
    void* pDeviceMappedHandle = nullptr;

    kfeResult result = pContext->deviceMapPhysicalMemory(deviceVirtualAddress, pPhysicalMemoryObject, size, flags,
                                                         &pDeviceMappedHandle);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to map physical memory to device");
        return response;
    }

    auto resp = response.mutable_physical_memory_device_map();
    resp->set_device_mapped_handle(reinterpret_cast<unsigned long long>(pDeviceMappedHandle));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu mapped physical memory with handle %llu.\n",
            clientId, reinterpret_cast<unsigned long long>(pPhysicalMemoryObject));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryDeviceUnmap(unsigned long long clientId,
                                                    const PhysicalMemoryDeviceUnmapRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    void* pDeviceMappedHandle = reinterpret_cast<void*>(req.device_mapped_handle());

    kfeResult result = pContext->deviceUnmapPhysicalMemory(pDeviceMappedHandle);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to unmap physical memory from device");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu unmapped physical memory mapping with handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pDeviceMappedHandle));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryHostMap(unsigned long long clientId, const PhysicalMemoryHostMapRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    DevicePhysicalMemoryObject* pPhysicalMemoryObject =
        reinterpret_cast<DevicePhysicalMemoryObject*>(req.physical_memory_handle());
    unsigned long long size = req.size();
    unsigned long long flags = req.flags();
    void* pHostMappedHandle = nullptr;
    void* pHostVirtualAddress  = nullptr;

    kfeResult result = pContext->hostMapDevicePhysicalMemory(pPhysicalMemoryObject, size, flags, &pHostMappedHandle,
                                                             &pHostVirtualAddress);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to map physical memory to host");
        return response;
    }

    auto resp = response.mutable_physical_memory_host_map();
    resp->set_host_mapped_handle(reinterpret_cast<unsigned long long>(pHostMappedHandle));
    resp->set_host_virtual_address(reinterpret_cast<unsigned long long>(pHostVirtualAddress));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu mapped physical memory with the host mapped handle %llu and virtual address %llu "
            " to host\n", clientId, reinterpret_cast<unsigned long long>(pHostMappedHandle),
            reinterpret_cast<unsigned long long>(pHostVirtualAddress));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryHostUnmap(unsigned long long clientId,
                                                  const PhysicalMemoryHostUnmapRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    void* pHostMappedHandle = reinterpret_cast<void*>(req.host_mapped_handle());

    kfeResult result = pContext->hostUnmapDevicePhysicalMemory(pHostMappedHandle);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to unmap physical memory from host");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu unmapped physical memory mapping with handle %llu from host\n",
            clientId, reinterpret_cast<unsigned long long>(pHostMappedHandle));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryExportByDmaBuf(unsigned long long clientId, const PhysicalMemoryExportByDmaBufRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    PhysicalMemoryObject* pPhysicalMemoryObject = reinterpret_cast<PhysicalMemoryObject*>(req.physical_memory_handle());
    unsigned long long size = req.size();
    unsigned long long pageSize = req.page_size();
    unsigned long long flags = req.flags();
    int dmaBufFd = 0;

    kfeResult result = pContext->exportPhysicalMemoryByDmaBuf(pPhysicalMemoryObject, size, pageSize, flags, &dmaBufFd);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to export physical memory by DMA-BUF");
        return response;
    }

    auto resp = response.mutable_physical_memory_export_by_dmabuf();
    resp->set_dmabuf_fd(dmaBufFd);
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu exported physical memory with handle %llu to DMA-BUF fd %d\n",
            clientId, reinterpret_cast<unsigned long long>(pPhysicalMemoryObject), dmaBufFd);

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePhysicalMemoryImportFromDmaBuf(unsigned long long clientId,
                                                         const PhysicalMemoryImportFromDmaBufRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    int dmaBufFd = req.dmabuf_fd();
    PhysicalMemoryObject* pPhysicalMemoryObject = nullptr;
    unsigned long long size = 0;
    unsigned long long pageSize = 0;
    unsigned long long flags = 0;

    kfeResult result = pContext->importPhysicalMemoryFromDmaBuf(dmaBufFd, &pPhysicalMemoryObject, &size, &pageSize,
                                                                &flags);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to import physical memory from DMA-BUF");
        return response;
    }

    auto resp = response.mutable_physical_memory_import_from_dmabuf();
    resp->set_physical_memory_handle(reinterpret_cast<unsigned long long>(pPhysicalMemoryObject));
    resp->set_size(size);
    resp->set_page_size(pageSize);
    resp->set_flags(flags);
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu imported physical memory from DMA-BUF fd %d with handle %llu\n",
            clientId, dmaBufFd, reinterpret_cast<unsigned long long>(pPhysicalMemoryObject));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePinnedMemoryCreate(unsigned long long clientId, const PinnedMemoryCreateRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    unsigned long long size = req.size();
    unsigned long long flags = req.flags();
    HostPinnedMemoryObject* pPinnedMemoryObject = nullptr;

    kfeResult result = pContext->createHostPinnedMemory(size, flags, &pPinnedMemoryObject);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to register physical memory to host");
        return response;
    }

    auto resp = response.mutable_pinned_memory_create();
    resp->set_pinned_memory_handle(reinterpret_cast<unsigned long long>(pPinnedMemoryObject));
    resp->set_pinned_memory_name(pPinnedMemoryObject->getShmName());
    resp->set_pinned_memory_fd(pPinnedMemoryObject->getShmFd());
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu create host pinned memory with %llu of size %llu at flags %llu.\n",
            clientId, reinterpret_cast<unsigned long long>(pPinnedMemoryObject),
            static_cast<unsigned long long>(req.size()), static_cast<unsigned long long>(req.flags()));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePinnedMemoryDestroy(unsigned long long clientId, const PinnedMemoryDestroyRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    HostPinnedMemoryObject* pPinnedMemoryObject = reinterpret_cast<HostPinnedMemoryObject*>(req.pinned_memory_handle());

    kfeResult result = pContext->destroyHostPinnedMemory(pPinnedMemoryObject);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to unregister physical memory from host");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu destroy the pinned memory object %llu from host\n",
            clientId, reinterpret_cast<unsigned long long>(pPinnedMemoryObject));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePageableMemoryHostRegister(unsigned long long clientId,
                                                     const PageableMemoryHostRegisterRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    void* pHostVirtualAddress = reinterpret_cast<void*>(req.host_virtual_address());
    unsigned long long size = req.size();
    unsigned long long flags = req.flags();
    PageableMemoryObject* pPageableMemoryObject = nullptr;

    kfeResult result = pContext->registerHostPageableMemory(pHostVirtualAddress, size, flags,
                                                            &pPageableMemoryObject);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to register pageable memory to host");
        return response;
    }

    auto resp = response.mutable_pageable_memory_host_register();
    resp->set_host_registered_handle(reinterpret_cast<unsigned long long>(pPageableMemoryObject));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu registered pageable memory with host ptr %llu of size %llu at flags %llu.\n",
            clientId, reinterpret_cast<unsigned long long>(pHostVirtualAddress),
            static_cast<unsigned long long>(req.size()), static_cast<unsigned long long>(req.flags()));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handlePageableMemoryHostUnregister(unsigned long long clientId,
                                                       const PageableMemoryHostUnregisterRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    PageableMemoryObject* pPageableMemoryObject = reinterpret_cast<PageableMemoryObject*>(req.host_registered_handle());

    kfeResult result = pContext->unregisterHostPageableMemory(pPageableMemoryObject);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to unregister pageable memory from host");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu unregistered pageable memory handle %llu from host\n",
            clientId, reinterpret_cast<unsigned long long>(pPageableMemoryObject));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleUserModeQueueCreate(unsigned long long clientId, const UserModeQueueCreateRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    unsigned int flags = req.flags();
    UserModeQueue* pUserModeQueue = nullptr;

    kfeResult result = pContext->createUserModeQueue(&pUserModeQueue, flags);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to create user mode queue");
        return response;
    }

    auto resp = response.mutable_user_mode_queue_create();
    resp->set_user_mode_queue_handle(reinterpret_cast<unsigned long long>(pUserModeQueue));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu created user mode queue with handle %llu for context %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pUserModeQueue), reinterpret_cast<unsigned long long>(pContext));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleUserModeQueueDestroy(unsigned long long clientId, const UserModeQueueDestroyRequest& req)
{
    KfeResponse response;

    UserModeQueue* pUserModeQueue = reinterpret_cast<UserModeQueue*>(req.user_mode_queue_handle());
    Context* pContext = pUserModeQueue->getContext();

    kfeResult result = pContext->destroyUserModeQueue(pUserModeQueue);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to destroy user mode queue");
        return response;
    }

    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu destroyed user mode queue with handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pUserModeQueue));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleUserModeQueueSubmit(unsigned long long clientId, const UserModeQueueSubmitRequest& req)
{
    KfeResponse response;

    UserModeQueue* pUserModeQueue = reinterpret_cast<UserModeQueue*>(req.user_mode_queue_handle());

    // Parameter validation
    if (pUserModeQueue == nullptr)
    {
        response.set_result(KFE_ERROR_INVALID_VALUE);
        response.set_error_message("Invalid user mode queue handle");
        return response;
    }

    // Check if command packets is empty
    if (req.command_packets().empty())
    {
        response.set_result(KFE_ERROR_INVALID_VALUE);
        response.set_error_message("Empty command packets array");
        return response;
    }

    // Convert protobuf command packets to kfeCommandPacket
    std::vector<kfeCommandPacket> packets;
    std::vector<std::vector<kfeFence>> waitFencesStorage; // Storage for wait fences memory

    packets.reserve(req.command_packets().size());
    waitFencesStorage.reserve(req.command_packets().size());

    for (const auto& protoPacket : req.command_packets())
    {
        kfeCommandPacket cmdPacket = {};

        if (protoPacket.has_kernel_dispatch())
        {
            const auto& kernelDispatch = protoPacket.kernel_dispatch();

            // Create kfeCommandPacketKernelDispatch structure
            kfeCommandPacketKernelDispatch* pKernelDispatch =
                reinterpret_cast<kfeCommandPacketKernelDispatch*>(&cmdPacket);

            pKernelDispatch->commandPacketType = KFE_COMMAND_PACKET_TYPE_KERNEL_DISPATCH;
            pKernelDispatch->queueId = kernelDispatch.queue_id();
            pKernelDispatch->gridDimX = kernelDispatch.grid_dim_x();
            pKernelDispatch->gridDimY = kernelDispatch.grid_dim_y();
            pKernelDispatch->gridDimZ = kernelDispatch.grid_dim_z();
            pKernelDispatch->blockDimX = kernelDispatch.block_dim_x();
            pKernelDispatch->blockDimY = kernelDispatch.block_dim_y();
            pKernelDispatch->blockDimZ = kernelDispatch.block_dim_z();
            pKernelDispatch->numWaitFences = kernelDispatch.num_wait_fences();
            pKernelDispatch->signalFence = reinterpret_cast<kfeFence>(kernelDispatch.signal_fence_handle());

            // Handle wait fences
            if (kernelDispatch.wait_fence_handles().size() > 0)
            {
                std::vector<kfeFence> waitFences;
                waitFences.reserve(kernelDispatch.wait_fence_handles().size());

                for (const auto& fenceHandle : kernelDispatch.wait_fence_handles())
                {
                    waitFences.push_back(reinterpret_cast<kfeFence>(fenceHandle));
                }

                waitFencesStorage.push_back(std::move(waitFences));
                pKernelDispatch->pWaitFences = waitFencesStorage.back().data();
            }
            else
            {
                pKernelDispatch->pWaitFences = nullptr;
            }
        }
        else if (protoPacket.has_debug_add())
        {
            const auto& debugAdd = protoPacket.debug_add();

            // Create kfeCommandPacketDebugAdd structure
            kfeCommandPacketDebugAdd* pDebugAdd =
                reinterpret_cast<kfeCommandPacketDebugAdd*>(&cmdPacket);

            pDebugAdd->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_ADD;
            pDebugAdd->queueId = debugAdd.queue_id();
            pDebugAdd->operand1 = debugAdd.operand1();
            pDebugAdd->operand2 = debugAdd.operand2();
            pDebugAdd->numWaitFences = debugAdd.num_wait_fences();
            pDebugAdd->signalFence = reinterpret_cast<kfeFence>(debugAdd.signal_fence_handle());

            // Handle wait fences
            if (debugAdd.wait_fence_handles().size() > 0)
            {
                std::vector<kfeFence> waitFences;
                waitFences.reserve(debugAdd.wait_fence_handles().size());

                for (const auto& fenceHandle : debugAdd.wait_fence_handles())
                {
                    waitFences.push_back(reinterpret_cast<kfeFence>(fenceHandle));
                }

                waitFencesStorage.push_back(std::move(waitFences));
                pDebugAdd->pWaitFences = waitFencesStorage.back().data();
            }
            else
            {
                pDebugAdd->pWaitFences = nullptr;
            }

            fprintf(stdout, "[Server] Original command packet %p:\n", pDebugAdd);
            fprintf(stdout, "  - Type: %d\n", pDebugAdd->commandPacketType);
            fprintf(stdout, "  - Queue ID: %d\n", pDebugAdd->queueId);
            fprintf(stdout, "  - numWaitFences: %d\n", pDebugAdd->numWaitFences);
            fprintf(stdout, "  - pWaitFences: %p\n", pDebugAdd->pWaitFences);
            for (size_t i = 0; i < pDebugAdd->numWaitFences; ++i)
            {
                fprintf(stdout, "    - Wait Fence %zu: %p\n", i, pDebugAdd->pWaitFences[i]);
            }
            fprintf(stdout, "  - signalFence: %p\n", pDebugAdd->signalFence);
        }
        else if (protoPacket.has_debug_sub())
        {
            const auto& debugSub = protoPacket.debug_sub();

            // Create kfeCommandPacketDebugSub structure
            kfeCommandPacketDebugSub* pDebugSub =
                reinterpret_cast<kfeCommandPacketDebugSub*>(&cmdPacket);

            pDebugSub->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_SUB;
            pDebugSub->queueId = debugSub.queue_id();
            pDebugSub->operand1 = debugSub.operand1();
            pDebugSub->operand2 = debugSub.operand2();
            pDebugSub->numWaitFences = debugSub.num_wait_fences();
            pDebugSub->signalFence = reinterpret_cast<kfeFence>(debugSub.signal_fence_handle());

            // Handle wait fences
            if (debugSub.wait_fence_handles().size() > 0)
            {
                std::vector<kfeFence> waitFences;
                waitFences.reserve(debugSub.wait_fence_handles().size());

                for (const auto& fenceHandle : debugSub.wait_fence_handles())
                {
                    waitFences.push_back(reinterpret_cast<kfeFence>(fenceHandle));
                }

                waitFencesStorage.push_back(std::move(waitFences));
                pDebugSub->pWaitFences = waitFencesStorage.back().data();
            }
            else
            {
                pDebugSub->pWaitFences = nullptr;
            }
        }
        else if (protoPacket.has_common())
        {
            const auto& common = protoPacket.common();

            // Use generic command packet handling for other types
            cmdPacket.commandPacketType = static_cast<int>(common.command_packet_type());
            cmdPacket.queueId = common.queue_id();
            cmdPacket.numWaitFences = common.num_wait_fences();
            cmdPacket.signalFence = reinterpret_cast<kfeFence>(common.signal_fence_handle());

            // Handle wait fences
            if (common.wait_fence_handles().size() > 0)
            {
                std::vector<kfeFence> waitFences;
                waitFences.reserve(common.wait_fence_handles().size());

                for (const auto& fenceHandle : common.wait_fence_handles())
                {
                    waitFences.push_back(reinterpret_cast<kfeFence>(fenceHandle));
                }

                waitFencesStorage.push_back(std::move(waitFences));
                cmdPacket.pWaitFences = waitFencesStorage.back().data();
            }
            else
            {
                cmdPacket.pWaitFences = nullptr;
            }
        }
        else
        {
            // Unknown command packet type
            response.set_result(KFE_ERROR_INVALID_VALUE);
            response.set_error_message("Unknown command packet type");
            return response;
        }

        packets.push_back(cmdPacket);
    }

    // Submit command packets to resource manager
    kfeResult result = pUserModeQueue->submit(packets.empty() ? nullptr : packets.data(),
                                              static_cast<unsigned int>(packets.size()));

    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to submit user mode queue");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu submitted user mode queue with handle %llu (%zu packets)\n",
            clientId, reinterpret_cast<unsigned long long>(pUserModeQueue), packets.size());

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleFenceCreate(unsigned long long clientId, const FenceCreateRequest& req)
{
    KfeResponse response;

    Context* pContext = reinterpret_cast<Context*>(req.context_handle());
    unsigned int flags = req.flags();
    Fence* pFence = nullptr;

    kfeResult result = pContext->createFence(reinterpret_cast<Fence**>(&pFence), flags);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to create fence");
        return response;
    }

    auto resp = response.mutable_fence_create();
    resp->set_fence_handle(reinterpret_cast<unsigned long long>(pFence));
    response.set_result(KFE_SUCCESS);

    fprintf(stdout, "Client ID: %llu created fence with handle %llu for context %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pFence), reinterpret_cast<unsigned long long>(pContext));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleFenceDestroy(unsigned long long clientId, const FenceDestroyRequest& req)
{
    KfeResponse response;

    Fence* pFence = reinterpret_cast<Fence*>(req.fence_handle());
    Context* pContext = pFence->getContext();

    kfeResult result = pContext->destroyFence(pFence);
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to destroy fence");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu destroyed fence with handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pFence));

    return response;
}

// =====================================================================================================================
KfeResponse Server::handleFenceSignal(unsigned long long clientId, const FenceSignalRequest& req)
{
    KfeResponse response;

    Fence* pFence = reinterpret_cast<Fence*>(req.fence_handle());

    kfeResult result = pFence->signal();
    if (result != KFE_SUCCESS)
    {
        response.set_result(result);
        response.set_error_message("Failed to signal fence");
        return response;
    }

    response.set_result(KFE_SUCCESS);
    fprintf(stdout, "Client ID: %llu signaled fence with handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pFence));
    return response;
}

// =====================================================================================================================
KfeResponse Server::handleFenceQuery(unsigned long long clientId, const FenceQueryRequest& req)
{
    KfeResponse response;

    Fence* pFence = reinterpret_cast<Fence*>(req.fence_handle());

    kfeResult result = pFence->query();
    if ((result != KFE_SUCCESS) && (result != KFE_ERROR_NOT_READY))
    {
        response.set_result(result);
        response.set_error_message("Failed to query fence");
        return response;
    }

    response.set_result(static_cast<int32_t>(result));

    fprintf(stdout, "Client ID: %llu queried fence with handle %llu\n",
            clientId, reinterpret_cast<unsigned long long>(pFence));

    return response;
}

// =====================================================================================================================
bool Server::sendResponse(int clientFd, const KfeResponse& response)
{
    std::lock_guard<std::mutex> lock(m_sendMutex);

    std::string serialized;
    if (!response.SerializeToString(&serialized))
    {
        fprintf(stderr, "Failed to serialize response.\n");
        return false;
    }

    uint32_t size = htonl(serialized.size());
    if (serialized.size() > std::numeric_limits<uint32_t>::max())
    {
        fprintf(stderr, "Serialized response size exceeds maximum limit.\n");
        return false;
    }

    // Send the size of the response first
    ssize_t bytesSent = 0;
    size_t totalSent = 0;
    const char* pSizeBuffer = reinterpret_cast<const char*>(&size);

    while (totalSent < sizeof(size))
    {
        bytesSent = send(clientFd, pSizeBuffer + totalSent, sizeof(size) - totalSent, MSG_NOSIGNAL);
        if (bytesSent < 0)
        {
            if (errno == EAGAIN || errno == EWOULDBLOCK)
            {
                // Non-blocking socket, try again later
                std::this_thread::sleep_for(std::chrono::microseconds(100));
                continue;
            }

            fprintf(stderr, "Failed to send response size: %s\n", strerror(errno));
            return false;
        }
        else if (bytesSent == 0)
        {
            // Connection closed by client
            return false;
        }

        totalSent += bytesSent;
    }

    // Send the serialized response data
    totalSent = 0;
    while (totalSent < serialized.size())
    {
        bytesSent = send(clientFd, serialized.data() + totalSent, serialized.size() - totalSent, MSG_NOSIGNAL);
        if (bytesSent < 0)
        {
            if (errno == EAGAIN || errno == EWOULDBLOCK)
            {
                // Non-blocking socket, try again later
                std::this_thread::sleep_for(std::chrono::microseconds(100));
                continue;
            }

            fprintf(stderr, "Failed to send response: %s\n", strerror(errno));
            return false;
        }
        else if (bytesSent == 0)
        {
            // Connection closed by client
            return false;
        }

        totalSent += bytesSent;
    }

    return true;
}

// ====================================================================================================================
bool Server::receiveRequest(int clientFd, KfeRequest& request)
{
    uint32_t size = 0;

    // Receive the size of the request first
    ssize_t bytesReceived = 0;
    size_t totalReceived = 0;
    char* pSizeBuffer = reinterpret_cast<char*>(&size);

    std::lock_guard<std::mutex> lock(m_recvMutex);

    while (totalReceived < sizeof(size))
    {
        bytesReceived = recv(clientFd, pSizeBuffer + totalReceived, sizeof(size) - totalReceived, 0);
        if (bytesReceived < 0)
        {
            if (errno == EAGAIN || errno == EWOULDBLOCK)
            {
                // Non-blocking socket, try again later
                std::this_thread::sleep_for(std::chrono::microseconds(100));
                continue;
            }

            fprintf(stderr, "Failed to receive request size: %s\n", strerror(errno));
            return false;
        }
        else if (bytesReceived == 0)
        {
            // Connection closed by client
            return false;
        }

        totalReceived += bytesReceived;
    }

    size = ntohl(size);
    if (size == 0)
    {
        fprintf(stderr, "Received zero-sized request.\n");
        return false;
    }

    if (size > 1024 * 1024) // 1MB limit
    {
        fprintf(stderr, "Request size too large: %u\n", size);
        return false;
    }

    std::string serialized(size, '\0');
    totalReceived = 0;

    while (totalReceived < size)
    {
        bytesReceived = recv(clientFd, &serialized[totalReceived], size - totalReceived, 0);
        if (bytesReceived < 0)
        {
            if (errno == EAGAIN || errno == EWOULDBLOCK)
            {
                // Non-blocking socket, try again later
                std::this_thread::sleep_for(std::chrono::microseconds(100));
                continue;
            }

            fprintf(stderr, "Failed to receive request data: %s\n", strerror(errno));
            return false;
        }
        else if (bytesReceived == 0)
        {
            // Connection closed by client
            return false;
        }

        totalReceived += bytesReceived;
    }

    if (!request.ParseFromString(serialized))
    {
        fprintf(stderr, "Failed to parse request.\n");
        return false;
    }

    return true;
}

// ====================================================================================================================
void Server::heartbeatThreadFunc()
{
    fprintf(stdout, "Heartbeat thread started.\n");

    while (m_running.load())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(HEARTBEAT_INTERVAL_MS));

        auto now = std::chrono::steady_clock::now();
        std::unordered_map<unsigned long long, int> expiredClients;

        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            for (const auto& [clientId, client] : m_clients) {
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - client->lastHeartbeat).count();

                if (duration > HEARTBEAT_TIMEOUT_MS)
                {
                    expiredClients[clientId] = client->socketFd;
                    fprintf(stderr, "[Server] Client %llu heartbeat timeout\n", clientId);
                }
            }
        }

        // Clean up expired clients
        for (const auto& client : expiredClients)
        {
            fprintf(stderr, "Cleaning up expired client: %llu.\n", client.first);
            m_deviceManager->unregisterClient(client.first);
            handleClientDisconnect(client.second);
        }
    }
}

} // namespace kfe