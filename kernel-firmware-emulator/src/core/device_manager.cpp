
#include "device.h"
#include "context.h"
#include "fence.h"
#include "device_manager.h"
#include "physical_memory_object.h"
#include "user_mode_queue.h"
#include "thread_pool.h"
#include "kfe_thunk.h"
#include "os.h"

#include <cassert>
#include <map>
#include <vector>
#include <chrono>
#include <atomic>
#include <cstring>

namespace kfe
{
// =====================================================================================================================
DeviceManager::DeviceManager()
    : m_deviceCount(0)
    , m_devicesMap()
    , m_devicesMapMutex()
{
    // Constructor implementation
    // Initialize any resource or state here
}

// =====================================================================================================================
DeviceManager::~DeviceManager()
{
    // Destructor implementation
    // Cleanup resource or state if necessary
    {
        std::lock_guard<std::mutex> lock(m_devicesMapMutex);
        for (auto& devicePair : m_devicesMap)
        {
            delete devicePair.second; // Delete each Device instance
        }
        m_devicesMap.clear(); // Clear the devices map
    }

    kfeThunkDeinitialize();
}

// =====================================================================================================================
kfeResult DeviceManager::init()
{
    kfeResult result = KFE_SUCCESS;

    m_deviceCount = 8;

    result = kfeThunkInitialize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "Failed to initialize thunk layer: %d\n", result);
        return result;
    }

    // Create device
    for (int i = 0; i < m_deviceCount; ++i)
    {
        Device* pDevice = new (std::nothrow) Device(i); // Simulate device creation
        if (pDevice == nullptr)
        {
            return KFE_ERROR_OUT_OF_MEMORY;
        }

        result = pDevice->initialize();
        if (result != KFE_SUCCESS)
        {
            delete pDevice;

            return result;
        }

        addDevice(pDevice);
    }

    return KFE_SUCCESS;
}

// =====================================================================================================================
void DeviceManager::registerClient(unsigned long long clientId)
{
    std::lock_guard<std::mutex> lock(m_devicesMapMutex);
    for (auto& device : m_devicesMap)
    {
        Device* pDevice = device.second;
        pDevice->registerClient(clientId); // Register the client with each device
    }

    fprintf(stdout, "[Main Process] Registered client with ID: %llu\n", clientId);
}

// =====================================================================================================================
void DeviceManager::unregisterClient(unsigned long long clientId)
{
    std::lock_guard<std::mutex> lock(m_devicesMapMutex);
    for (auto& device : m_devicesMap)
    {
        Device* pDevice = device.second;
        pDevice->unregisterClient(clientId); // Unregister the client from each device
    }

    fprintf(stdout, "[Main Process] Unregistered client with ID: %llu\n", clientId);
}

// =====================================================================================================================
void DeviceManager::addDevice(Device* pDevice)
{
    std::lock_guard<std::mutex> lock(m_devicesMapMutex);
    fprintf(stdout, "[Main Process] Adding device with GPU ID: %d\n", pDevice->getGpuId());
    m_devicesMap.try_emplace(pDevice->getGpuId(), pDevice);
}

// =====================================================================================================================
void DeviceManager::removeDevice(Device* pDevice)
{
    std::lock_guard<std::mutex> lock(m_devicesMapMutex);
    fprintf(stdout, "[Main Process] Removing device with GPU ID: %d\n", pDevice->getGpuId());
    auto it = m_devicesMap.find(pDevice->getGpuId());
    if (it != m_devicesMap.end())
    {
        m_devicesMap.erase(it); // Remove the device from the resource map
    }
}

// =====================================================================================================================
kfeResult DeviceManager::getDeviceCount(int* pCount)
{
    assert(pCount != nullptr);

    *pCount = m_deviceCount; // Simulate 8 devices available

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult DeviceManager::openDevice(kfeDevice* pDevice, int gpuId, unsigned long long clientId)
{
    Device* pDeviceHandle = nullptr;

    assert(pDevice != nullptr);
    assert(gpuId >= 0 && gpuId < m_deviceCount);

    // Check if the device already exists
    {
        std::lock_guard<std::mutex> lock(m_devicesMapMutex);
        auto it = m_devicesMap.find(gpuId);
        if (it != m_devicesMap.end())
        {
            pDeviceHandle = it->second; // Device already exists
        }
        else
        {
            // Device not found
            return KFE_ERROR_INVALID_DEVICE;
        }
    }

    // Increment the reference count
    pDeviceHandle->retain();

    // Register the client ID with the device
    pDeviceHandle->registerClient(clientId);

    // Set the device handle to the output parameter
    *pDevice = reinterpret_cast<kfeDevice>(pDeviceHandle);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult DeviceManager::closeDevice(kfeDevice device, unsigned long long clientId)
{
    Device* pDevice = nullptr;

    assert(device != nullptr);

    pDevice = reinterpret_cast<Device*>(device);
    if (pDevice->getRefCount() == 1)
    {
        fprintf(stderr, "[Main Process] Attempted to close a device that is already closed or has no references.\n");
        return KFE_ERROR_INVALID_DEVICE;
    }

    // Unregister the client ID from the device
    pDevice->unregisterClient(clientId);

    // Decrement the reference count
    pDevice->release();

    return KFE_SUCCESS;
}
} // namespace kfe