#ifndef THREAD_POOL_H_
#define THREAD_POOL_H_

#include <thread>
#include <vector>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <future>
#include <queue>

namespace kfe
{
class ThreadPool
{
public:
    ThreadPool(size_t threads = std::thread::hardware_concurrency());
    ~ThreadPool();

    template<class F, class... Args>
    auto enqueue(F&& f, Args&&... args) -> std::future<std::invoke_result_t<F, Args...>>
    {
        using return_type = std::invoke_result_t<F, Args...>;

        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );

        std::future<return_type> res = task->get_future();
        {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            if (m_stop)
            {
                // If the thread pool is stopped, we cannot enqueue new tasks
                fprintf(stderr, "enqueue on stopped ThreadPool\n");
                return std::future<return_type>();
            }

            m_tasks.emplace([task]() { (*task)(); });
        }

        m_condition.notify_one();

        return res;
    }

    void stop();

private:
    std::vector<std::thread> m_workers;
    std::queue<std::function<void()>> m_tasks;
    std::mutex m_queueMutex;
    std::condition_variable m_condition;
    bool m_stop;
};
} // namespace kfe

#endif // THREAD_POOL_H_