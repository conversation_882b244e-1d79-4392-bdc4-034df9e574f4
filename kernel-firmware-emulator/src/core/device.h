#ifndef DEVICE_H_
#define DEVICE_H_

#include "kfe.h"

#include <stddef.h>

#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <map>

#include "os.h"

namespace kfe
{
class Context;

enum ComputeMode
{
    Default = 0, // Default mode - Device is not restricted and multiple threads can use cudaSetDevice() with this device.
    Prohibited = 1, // Compute-prohibited mode - No threads can use cudaSetDevice() with this device.
    ExclusiveProcess = 2 // Compute-exclusive-process mode - Many threads in one process will be able to use cudaSetDevice() with this device.
};

class GlobalManager;
class Device
{
public:
    Device(int gpuId);
    ~Device();

    kfeResult initialize();

    kfeResult createContext(Context** pContext, unsigned int flags, unsigned long long clientId,
                            util::OsPid clientPid);
    kfeResult destroyContext(Context* pContext);

    // Get the GPU ID for the device
    int getGpuId() const { return m_gpuId; }

    // Get the next unique context ID
    inline int getNextContextId() { return m_contextIdCounter++; }

    // Get the set of contexts associated with this device
    const kfeDeviceProperties& getProperties() const { return m_properties; }

    // Get the total memory size of the device
    inline unsigned long long getTotalMemorySize() const { return m_totalMemorySize; }

    // Get the free memory size of the device
    inline unsigned long long getFreeMemorySize() const { return m_freeMemorySize.load(std::memory_order_relaxed); }

    // Increment the free memory size by the specified amount
    inline void incrementFreeMemorySize(unsigned long long size)
    {
        m_freeMemorySize.fetch_add(size, std::memory_order_relaxed);
    }

    // Decrement the free memory size by the specified amount
    inline void decrementFreeMemorySize(unsigned long long size)
    {
        m_freeMemorySize.fetch_sub(size, std::memory_order_relaxed);
    }

    // Retain the device, incrementing the reference count
    void retain();

    // Release the device, decrementing the reference count
    // If the reference count reaches zero, the device is deleted.
    void release();

    // Get the reference count for this device
    inline int getRefCount() const { return m_refCount.load(std::memory_order_relaxed); }

    // register client info
    void registerClient(unsigned long long clientId);

    // Unregister a client info
    void unregisterClient(unsigned long long clientId);
private:
    // Cleanup all clients associated with this device
    void cleanupAllClients();

    // generate uuid for the device
    void generateUuid();

    // Add a context to this device
    void addContext(Context* pContext);

    // Remove a context from this device
    void removeContext(Context* pContext);

    // GPU ID for the device
    int m_gpuId;

    // The arch model device handle
    void* m_pModelDeviceHandle;

    // The reference count for the device
    std::atomic<int> m_refCount;

    // Properties of the device
    kfeDeviceProperties m_properties;

    // Counter for generating unique context IDs
    std::atomic<int> m_contextIdCounter;

    struct ClientInfo
    {
        // Client ID that opens this device
        unsigned long long clientId;

        // Last time this device was opened
        std::chrono::steady_clock::time_point openedTime;

        // The set container of contexts associated with this device
        std::map<int, Context*> m_contextsMap;
    };

    // Client info map
    std::map<unsigned long long, ClientInfo> m_clientInfoMap;

    // The mutex for protecting the resources map
    mutable std::mutex m_clientsMapMutex;

    // The total memory size of the device
    unsigned long long m_totalMemorySize;

    // The free memory size of the device
    std::atomic<unsigned long long> m_freeMemorySize;
};

} // namespace kfe

#endif // DEVICE_H_