#ifndef USER_MODE_QUEUE_H_
#define USER_MODE_QUEUE_H_

#include "kfe.h"
#include "thread_pool.h"

#include <vector>
#include <mutex>
#include <deque>

namespace kfe
{
// Forward declaration
class Context;
class ThreadPool;
class Fence;

class UserModeQueue
{
public:
    UserModeQueue(Context* pContext, unsigned int flags, int queueId);
    ~UserModeQueue();

    // Get the associated context
    Context* getContext() const { return m_pContext; }

    // Get the unique identifier for the user mode queue
    int getQueueId() const { return m_queueId; }

    // Command packet management
    kfeResult submit(kfeCommandPacket* pCommandPackets, unsigned int numCmdPackets);

private:
    // Command packet execution methods
    kfeCommandPacket getCommandPacket(unsigned int index) const;
    kfeResult executeCommandPacket(const kfeCommandPacket& cmdPacket);
    kfeResult processCommandPacketAsync(unsigned int packetIndex);
    kfeResult executeKernelDispatch(const kfeCommandPacket& cmdPacket);
    kfeResult executeDebugAdd(const kfeCommandPacket& cmdPacket);
    kfeResult executeDebugSub(const kfeCommandPacket& cmdPacket);
    kfeResult executeMemcpy(const kfeCommandPacket& cmdPacket);
    kfeResult executeMemset(const kfeCommandPacket& cmdPacket);
    kfeResult executeEventRecord(const kfeCommandPacket& cmdPacket);
    kfeResult executeEventWait(const kfeCommandPacket& cmdPacket);
    kfeResult executeMemoryWrite(const kfeCommandPacket& cmdPacket);
    kfeResult executeMemoryWait(const kfeCommandPacket& cmdPacket);
    void clearAllCommandPackets();

    // Pointer to the associated context
    Context* m_pContext;

    // Flags for this user mode queue
    unsigned int m_flags;

    // Unique identifier for the user mode queue
    int m_queueId;

    // Command packet storage using deque for ordered processing
    std::deque<kfeCommandPacket> m_commandPackets;

    // Mutex for thread-safe access to command packets
    mutable std::mutex m_commandPacketsMutex;

    // Thread pool for executing command packets
    ThreadPool m_threadPool;
};
} // namespace kfe

#endif // USER_MODE_QUEUE_H_