#ifndef CONTEXT_H_
#define CONTEXT_H_

#include "kfe.h"
#include "os.h"

#include <atomic>
#include <mutex>
#include <map>

namespace kfe
{
// Forward declaration
class Device;
class Fence;
class UserModeQueue;
class PhysicalMemoryMapInfo;
class PhysicalMemoryObject;
class DevicePhysicalMemoryObject;
class HostPinnedMemoryObject;
class PageableMemoryObject;

void readHostMemory(uint64_t addr, size_t size, uint8_t* pData);
void writeHostMemory(uint64_t addr, size_t size, const uint8_t* pData);

class Context
{
public:
    Context(Device* pDevice, unsigned int flags, unsigned long long clientId, int contextId, util::OsPid clientPid);
    ~Context();

    // Physical memory management
    kfeResult createDevicePhysicalMemory(unsigned long long size, unsigned long long pageSize, unsigned long long flags,
                                         DevicePhysicalMemoryObject** ppPhysicalMemoryObject);
    kfeResult destroyDevicePhysicalMemory(DevicePhysicalMemoryObject* pPhysicalMemoryObject);
    kfeResult deviceMapPhysicalMemory(unsigned long long deviceVirtualAddress,
                                      PhysicalMemoryObject* pPhysicalMemoryObject,
                                      unsigned long long size,
                                      unsigned long long flags,
                                      void** ppPhysicalMemoryDeviceMapped);
    kfeResult deviceUnmapPhysicalMemory(void* pPhysicalMemoryDeviceMapped);
    kfeResult hostMapDevicePhysicalMemory(DevicePhysicalMemoryObject* pPhysicalMemoryObject, unsigned long long size,
                                          unsigned long long flags, void** ppPhysicalMemoryHostMapped,
                                          void** ppHostVirtualAddress);
    kfeResult hostUnmapDevicePhysicalMemory(void* pPhysicalMemoryHostMapped);
    kfeResult exportPhysicalMemoryByDmaBuf(PhysicalMemoryObject* pPhysicalMemoryObject, unsigned long long size,
                                           unsigned long long pageSize, unsigned long long flags, int* pDmaBufFd);
    kfeResult importPhysicalMemoryFromDmaBuf(int dmaBufFd, PhysicalMemoryObject** ppPhysicalMemoryObject,
                                             unsigned long long* pSize, unsigned long long* pPageSize,
                                             unsigned long long* pFlags);
    kfeResult createHostPinnedMemory(unsigned long long size, unsigned long long flags,
                                     HostPinnedMemoryObject** ppPinnedMemoryObject);
    kfeResult destroyHostPinnedMemory(HostPinnedMemoryObject* pPinnedMemoryObject);
    kfeResult registerHostPageableMemory(void* pHostVirtualAddress, unsigned long long size,
                                         unsigned long long flags, PageableMemoryObject** ppPageableMemoryObject);
    kfeResult unregisterHostPageableMemory(PageableMemoryObject* pPageableMemoryObject);

    // User mode queue management
    kfeResult createUserModeQueue(UserModeQueue** ppUserModeQueue, unsigned int flags);
    kfeResult destroyUserModeQueue(UserModeQueue* pUserModeQueue);

    // Fence management
    kfeResult createFence(Fence** ppFence, unsigned int flags);
    kfeResult destroyFence(Fence* pFence);

    // Get the associated device
    inline Device* getDevice() const { return m_pDevice; }

    // Get the client ID that created this context
    inline unsigned long long getClientId() const { return m_clientId; }

    // Get the unique ID for this context
    inline int getContextId() const { return m_contextId; }

    // Get the PID of the client process that created this context
    inline util::OsPid getClientPid() const { return m_clientPid; }

    // Get a user mode queue by its ID
    UserModeQueue* getUserModeQueue(int queueId);

    // Check if a user mode queue is valid
    bool isValidUserModeQueue(int queueId);

    // Get the unique ID for user mode queue.
    inline int getNextQueueId() { return m_queueIdCounter++; }

    // Get the unique ID for fence.
    inline int getNextFenceId() { return m_fenceIdCounter++; }

    // Get the unique ID for physical memory object.
    inline int getNextPhysicalMemoryObjectId() { return m_physicalMemoryObjectIdCounter++; }

    // Get a fence by its ID
    Fence* getFence(int fenceId);

    // Check if a fence is valid
    bool isValidFence(int fenceId);

    // Get a physical memory object by its ID
    PhysicalMemoryObject* getPhysicalMemoryObjectById(int memoryObjectId);

    // Get a physical memory object by its device mapped handle
    PhysicalMemoryObject* getPhysicalMemoryObjectByDeviceMappedHandle(PhysicalMemoryMapInfo* pDeviceMappedHandle);

    // Get a physical memory object by its host mapped handle
    PhysicalMemoryObject* getPhysicalMemoryObjectByHostMappedHandle(PhysicalMemoryMapInfo* pHostMappedHandle);

    // Check if a physical memory object is valid
    bool isValidPhysicalMemoryObject(int memoryObjectId);
private:
    // Add a user mode queue to this context
    void addUserModeQueue(UserModeQueue* pQueue);

    // Remove a user mode queue from this context
    void removeUserModeQueue(UserModeQueue* pQueue);

    // Destroy all user mode queues associated with this context
    void destroyAllUserModeQueues();

    // Add a fence to this context
    void addFence(Fence* pFence);

    // Remove a fence from this context
    void removeFence(Fence* pFence);

    // Destroy all fences associated with this context
    void destroyAllFences();

    // Destroy all physical memory objects associated with this context
    void destroyAllPhysicalMemoryObjects();

    // Add a physical memory object to this context
    void addPhysicalMemoryObject(PhysicalMemoryObject* pMemoryObject);

    // Remove a physical memory object from this context
    void removePhysicalMemoryObject(PhysicalMemoryObject* pMemoryObject);

    // Add a physical memory object mapped info to this context
    void addPhysicalMemoryObjectDeviceMappedHandle(PhysicalMemoryMapInfo* pDeviceMappedHandle,
                                                   PhysicalMemoryObject* pMemoryObject);

    // Remove a physical memory object mapped info to this context
    void removePhysicalMemoryObjectDeviceMappedHandle(PhysicalMemoryMapInfo* pDeviceMappedHandle);

    // Add a physical memory object host mapped info to this context
    void addPhysicalMemoryObjectHostMappedHandle(PhysicalMemoryMapInfo* pHostMappedHandle,
                                                 PhysicalMemoryObject* pMemoryObject);

    // Remove a physical memory object host mapped info to this context
    void removePhysicalMemoryObjectHostMappedHandle(PhysicalMemoryMapInfo* pHostMappedHandle);

    // Pointer to the associated device
    Device* m_pDevice;

    // Flags for this context
    unsigned int m_flags;

    // Client ID that created this context
    unsigned long long m_clientId;

    // Unique identifier for this context
    int m_contextId;

    // The PID of the client process that created this context
    util::OsPid m_clientPid;

    // The set container of user mode queues associated with this context
    std::map<int, UserModeQueue*> m_userModeQueuesMap;

    // The mutex for synchronizing access to the user mode queues map
    std::mutex m_userModeQueuesMapMutex;

    // The map container of fences associated with this context.
    std::map<int, Fence*> m_fencesMap;

    // The mutex for synchronizing access to the fences map
    std::mutex m_fencesMapMutex;

    // The map container of physical memory objects associated with this context
    std::map<int, PhysicalMemoryObject*> m_physicalMemoryObjectsMap;

    // The map container of physical memory objects mapped to device addresses
    std::map<void*, PhysicalMemoryObject*> m_physicalMemoryObjectsDeviceMapped;

    // The map container of physical memory objects mapped to host addresses
    std::map<void*, PhysicalMemoryObject*> m_physicalMemoryObjectsHostMapped;

    // The mutex for synchronizing access to the physical memory objects map
    std::recursive_mutex m_physicalMemoryObjectsMapMutex;

    // The mutex for protecting the physical memory objects
    mutable std::mutex m_physicalMemoryObjectMutex;

    // Counter for generating unique queue IDs
    std::atomic<int> m_queueIdCounter;

    // Counter for generating unique fence IDs
    std::atomic<int> m_fenceIdCounter;

    // Counter for generating unique physical memory object IDs
    std::atomic<int> m_physicalMemoryObjectIdCounter;
};
} // namespace kfe

#endif // CONTEXT_H_