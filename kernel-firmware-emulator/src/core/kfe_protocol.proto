syntax = "proto3";

package kfe;

// Enumeration type
enum RequestType
{
    KFE_REQUEST_TYPE_INITIALIZE                         = 0;
    KFE_REQUEST_TYPE_FINALIZE                           = 1;
    KFE_REQUEST_TYPE_DEVICE_GET_COUNT                   = 2;
    KFE_REQUEST_TYPE_DEVICE_OPEN                        = 3;
    KFE_REQUEST_TYPE_DEVICE_CLOSE                       = 4;
    KFE_REQUEST_TYPE_DEVICE_GET_PROPERTIES              = 5;
    KFE_REQUEST_TYPE_CONTEXT_CREATE                     = 6;
    KFE_REQUEST_TYPE_CONTEXT_DESTROY                    = 7;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_CREATE             = 8;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DESTROY            = 9;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DEVICE_MAP         = 10;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_DEVICE_UNMAP       = 11;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_HOST_MAP           = 12;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_HOST_UNMAP         = 13;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_EXPORT_BY_DMABUF   = 14;
    KFE_REQUEST_TYPE_PHYSICAL_MEMORY_IMPORT_FROM_DMABUF = 15;
    KFE_REQUEST_TYPE_PINNED_MEMORY_CREATE               = 16;
    KFE_REQUEST_TYPE_PINNED_MEMORY_DESTROY              = 17;
    KFE_REQUEST_TYPE_PAGEABLE_MEMORY_HOST_REGISTER      = 18;
    KFE_REQUEST_TYPE_PAGEABLE_MEMORY_HOST_UNREGISTER    = 19;
    KFE_REQUEST_TYPE_USER_MODE_QUEUE_CREATE             = 20;
    KFE_REQUEST_TYPE_USER_MODE_QUEUE_DESTROY            = 21;
    KFE_REQUEST_TYPE_USER_MODE_QUEUE_SUBMIT             = 22;
    KFE_REQUEST_TYPE_FENCE_CREATE                       = 23;
    KFE_REQUEST_TYPE_FENCE_DESTROY                      = 24;
    KFE_REQUEST_TYPE_FENCE_WAIT                         = 25;
    KFE_REQUEST_TYPE_FENCE_SIGNAL                       = 26;
    KFE_REQUEST_TYPE_FENCE_QUERY                        = 27;
    KFE_REQUEST_TYPE_HEART_BEAT                         = 28;
    KFE_REQUEST_TYPE_MAX                                = 29;
}

// The registration request of client
message InitializeRequest
{
    uint32 version     = 1; // API version
    string client_name = 2; // client name
}

message InitializeResponse
{
    uint64 client_id      = 1;
    string server_version = 2;
}

// Get device count request
message DeviceGetCountRequest
{
}

message DeviceGetCountResponse
{
    int32 device_count = 1;
    int32 result       = 2;
}

// Open device request
message DeviceOpenRequest
{
    int32 gpu_id     = 1;
    uint64 client_id = 2;
}

message DeviceOpenResponse
{
    uint64 device_handle = 1;
    int32 result         = 2;
}

// Close device request
message DeviceCloseRequest
{
    uint64 device_handle = 1;
}

message DeviceCloseResponse
{
    int32 result = 1;
}

message dim2
{
    int32 x = 1;
    int32 y = 2;
}

message dim3
{
    int32 x = 1;
    int32 y = 2;
    int32 z = 3;
}

// The structure of device properties
message DeviceProperties
{
    string name                                         = 1;
    bytes  uuid                                         = 2;
    uint64 total_global_mem                             = 3;
    uint64 shared_mem_per_block                         = 4;
    int32  regs_per_block                               = 5;
    int32  warp_size                                    = 6;
    uint64 mem_pitch                                    = 7;
    int32  max_threads_per_block                        = 8;
    dim3   max_threads_dim                              = 9;
    dim3   max_grid_size                                = 10;
    int32  clock_rate                                   = 11;
    uint64 total_const_mem                              = 12;
    int32  major                                        = 13;
    int32  minor                                        = 14;
    uint64 texture_alignment                            = 15;
    uint64 texture_pitch_alignment                      = 16;
    int32  device_overlap                               = 17;
    int32  multi_processor_count                        = 18;
    int32  kernel_exec_timeout_enabled                  = 19;
    int32  integrated                                   = 20;
    int32  can_map_host_memory                          = 21;
    int32  compute_mode                                 = 22;
    int32  max_texture_1d                               = 23;
    int32  max_texture_1d_mipmap                        = 24;
    int32  max_texture_1d_linear                        = 25;
    dim2   max_texture_2d                               = 26;
    dim2   max_texture_2d_mipmap                        = 27;
    dim3   max_texture_2d_linear                        = 28;
    dim2   max_texture_2d_gather                        = 29;
    dim3   max_texture_3d                               = 30;
    dim3   max_texture_3d_alt                           = 31;
    int32  max_texture_cubemap                          = 32;
    dim2   max_texture_1d_layered                       = 33;
    dim3   max_texture_2d_layered                       = 34;
    dim2   max_texture_cubemap_layered                  = 35;
    int32  max_surface_1d                               = 36;
    dim2   max_surface_2d                               = 37;
    dim3   max_surface_3d                               = 38;
    dim2   max_surface_1d_layered                       = 39;
    dim3   max_surface_2d_layered                       = 40;
    int32  max_surface_cubemap                          = 41;
    dim2   max_surface_cubemap_layered                  = 42;
    uint64 surface_alignment                            = 43;
    int32  concurrent_kernels                           = 44;
    int32  ecc_enabled                                  = 45;
    int32  pci_bus_id                                   = 46;
    int32  pci_device_id                                = 47;
    int32  pci_domain_id                                = 48;
    int32  tcc_driver                                   = 49;
    int32  async_engine_count                           = 50;
    int32  unified_addressing                           = 51;
    int32  memory_clock_rate                            = 52;
    int32  memory_bus_width                             = 53;
    int32  l2_cache_size                                = 54;
    int32  persisting_l2_cache_max_size                 = 55;
    int32  max_threads_per_multi_processor              = 56;
    int32  stream_priorities_supported                  = 57;
    int32  global_l1_cache_supported                    = 58;
    int32  local_l1_cache_supported                     = 59;
    uint64 shared_mem_per_multiprocessor                = 60;
    int32  regs_per_multiprocessor                      = 61;
    int32  managed_memory                               = 62;
    int32  is_multi_gpu_board                           = 63;
    int32  multi_gpu_board_group_id                     = 64;
    int32  host_native_atomic_supported                 = 65;
    int32  single_to_double_precision_perf_ratio        = 66;
    int32  pageable_memory_access                       = 67;
    int32  concurrent_managed_access                    = 68;
    int32  compute_preemption_supported                 = 69;
    int32  can_use_host_pointer_for_registered_mem      = 70;
    int32  cooperative_launch                           = 71;
    int32  cooperative_multi_device_launch              = 72;
    int32  shared_mem_per_block_optin                   = 73;
    int32  pageable_memory_access_uses_host_page_tables = 74;
    int32  direct_managed_mem_access_from_host          = 75;
    int32  max_blocks_per_multiprocessor                = 76;
    int32  access_policy_max_window_size                = 77;
    uint64 reserved_shared_mem_per_block                = 78;
    int32  host_register_supported                      = 79;
    int32  sparse_cuda_array_supported                  = 80;
    int32  host_register_read_only_supported            = 81;
    int32  timeline_semaphore_interop_supported         = 82;
    int32  memory_pools_supported                       = 83;
    int32  gpu_direct_rdma_supported                    = 84;
    uint32 gpu_direct_rdma_flush_writes_options         = 85;
    int32  gpu_direct_rdma_writes_ordering              = 86;
    uint32 memory_pool_supported_handle_types           = 87;
    int32  deferred_mapping_cuda_array_supported        = 88;
    int32  ipc_event_supported                          = 89;
    int32  unified_function_pointers                    = 90;
    int32  physical_memory_alignment                    = 91;
}

message DeviceGetPropertiesRequest
{
    uint64 device_handle = 1;
}

message DeviceGetPropertiesResponse
{
    DeviceProperties properties = 1;
}

// Context create request
message ContextCreateRequest
{
    uint64 device_handle = 1;
    uint32 flags         = 2;
    int32  pid           = 3;
}

message ContextCreateResponse
{
    uint64 context_handle = 1;
}

// Context destroy request
message ContextDestroyRequest
{
    uint64 context_handle = 1;
}

message ContextDestroyResponse
{
}

// Create physical memory request
message PhysicalMemoryCreateRequest
{
    uint64 context_handle = 1;
    uint64 size           = 2;
    uint64 page_size      = 3;
    uint64 flags          = 4;
}

message PhysicalMemoryCreateResponse
{
    uint64 physical_memory_handle = 1;
}

// Destroy physical memory request
message PhysicalMemoryDestroyRequest
{
    uint64 context_handle         = 1;
    uint64 physical_memory_handle = 2;
}

message PhysicalMemoryDestroyResponse
{
}

// Physical memory device map request
message PhysicalMemoryDeviceMapRequest
{
    uint64 context_handle         = 1;
    uint64 device_virtual_address = 2;
    uint64 physical_memory_handle = 3;
    uint64 size                   = 4;
    uint64 flags                  = 5;
}

message PhysicalMemoryDeviceMapResponse
{
    uint64 device_mapped_handle = 1;
}

// Physical memory device unmap request
message PhysicalMemoryDeviceUnmapRequest
{
    uint64 context_handle       = 1;
    uint64 device_mapped_handle = 2;
}

message PhysicalMemoryDeviceUnmapResponse
{
}

// Physical memory host map request
message PhysicalMemoryHostMapRequest
{
    uint64 context_handle         = 1;
    uint64 physical_memory_handle = 2;
    uint64 size                   = 3;
    uint64 flags                  = 4;
}

message PhysicalMemoryHostMapResponse
{
    uint64 host_mapped_handle   = 1;
    uint64 host_virtual_address = 2;
}

// Physical memory host unmap request
message PhysicalMemoryHostUnmapRequest
{
    uint64 context_handle     = 1;
    uint64 host_mapped_handle = 2;
}

message PhysicalMemoryHostUnmapResponse
{
}

// Physical memory export by dmaBuf fd request
message PhysicalMemoryExportByDmaBufRequest
{
    uint64 context_handle         = 1;
    uint64 physical_memory_handle = 2;
    uint64 size                   = 3;
    uint64 page_size              = 4;
    uint64 flags                  = 5;
}

message PhysicalMemoryExportByDmaBufResponse
{
    int32 dmabuf_fd = 1;
}

// Physical memory import from dmaBuf fd request
message PhysicalMemoryImportFromDmaBufRequest
{
    uint64 context_handle = 1;
    int32 dmabuf_fd       = 2;
}

message PhysicalMemoryImportFromDmaBufResponse
{
    uint64 physical_memory_handle = 1;
    uint64 size                   = 2;
    uint64 page_size              = 3;
    uint64 flags                  = 4;
}

// Pinned memory create request
message PinnedMemoryCreateRequest
{
    uint64 context_handle = 1;
    uint64 size           = 2;
    uint64 flags          = 3;
}

message PinnedMemoryCreateResponse
{
    uint64 pinned_memory_handle = 1;
    string pinned_memory_name   = 2;
    int32  pinned_memory_fd     = 3;
}

// Pinned memory destroy request
message PinnedMemoryDestroyRequest
{
    uint64 context_handle       = 1;
    uint64 pinned_memory_handle = 2;
}

message PinnedMemoryDestroyResponse
{
}

// Pageable memory host register request
message PageableMemoryHostRegisterRequest
{
    uint64 context_handle       = 1;
    uint64 host_virtual_address = 2;
    uint64 size                 = 3;
    uint64 flags                = 4;
}

message PageableMemoryHostRegisterResponse
{
    uint64 host_registered_handle = 1;
    uint64 registered_offset = 2;
}

// Pageable memory host unregister request
message PageableMemoryHostUnregisterRequest
{
    uint64 context_handle         = 1;
    uint64 host_registered_handle = 2;
}

message PageableMemoryHostUnregisterResponse
{
}

// User mode queue create request
message UserModeQueueCreateRequest
{
    uint64 context_handle = 1;
    uint32 flags          = 2;
}

message UserModeQueueCreateResponse
{
    uint64 user_mode_queue_handle = 1;
}

// User mode queue destroy request
message UserModeQueueDestroyRequest
{
    uint64 user_mode_queue_handle = 1;
}

message UserModeQueueDestroyResponse
{
}

//------------------- CommandPacket Type -------------------
enum CommandPacketType
{
    COMMAND_PACKET_TYPE_KERNEL_DISPATCH = 0;
    COMMAND_PACKET_TYPE_MEMCPY          = 1;
    COMMAND_PACKET_TYPE_MEMSET          = 2;
    COMMAND_PACKET_TYPE_EVENT_RECORD    = 3;
    COMMAND_PACKET_TYPE_EVENT_WAIT      = 4;
    COMMAND_PACKET_TYPE_MEMORY_WRITE    = 5;
    COMMAND_PACKET_TYPE_MEMORY_WAIT     = 6;
    COMMAND_PACKET_TYPE_DEBUG_ADD       = 7;
    COMMAND_PACKET_TYPE_DEBUG_SUB       = 8;
}

//------------------- Command Packets (multiple oneof) -----------
message CommandPacketCommon
{
    CommandPacketType command_packet_type = 1;
    int32 queue_id                        = 2;
    repeated int32 reserved               = 3; // 6 reserved
    int32 num_wait_fences                 = 4;
    repeated uint64 wait_fence_handles    = 5;
    uint64 signal_fence_handle            = 6;
}

message CommandPacketKernelDispatch
{
    CommandPacketType command_packet_type = 1;
    int32 queue_id                        = 2;
    int32 grid_dim_x                      = 3;
    int32 grid_dim_y                      = 4;
    int32 grid_dim_z                      = 5;
    int32 block_dim_x                     = 6;
    int32 block_dim_y                     = 7;
    int32 block_dim_z                     = 8;
    int32 num_wait_fences                 = 9;
    repeated uint64 wait_fence_handles    = 10;
    uint64 signal_fence_handle            = 11;
}

message CommandPacketDebugAdd
{
    CommandPacketType command_packet_type = 1;
    int32 queue_id                        = 2;
    int32 operand1                        = 3;
    int32 operand2                        = 4;
    repeated int32 reserved               = 5; // 4 reserved
    int32 num_wait_fences                 = 6;
    repeated uint64 wait_fence_handles    = 7;
    uint64 signal_fence_handle            = 8;
}

message CommandPacketDebugSub
{
    CommandPacketType command_packet_type = 1;
    int32 queue_id                        = 2;
    int32 operand1                        = 3;
    int32 operand2                        = 4;
    repeated int32 reserved               = 5; // 4 reserved
    int32 num_wait_fences                 = 6;
    repeated uint64 wait_fence_handles    = 7;
    uint64 signal_fence_handle            = 8;
}

// ... Others

message CommandPacket
{
    oneof cmd
    {
        CommandPacketCommon common                  = 1;
        CommandPacketKernelDispatch kernel_dispatch = 2;
        CommandPacketDebugAdd debug_add             = 3;
        CommandPacketDebugSub debug_sub             = 4;
        // ... Others
    }
}

// User mode queue submit request
message UserModeQueueSubmitRequest
{
    uint64 user_mode_queue_handle          = 1;
    repeated CommandPacket command_packets = 2;
    uint32 num_command_packets             = 3;
}

message UserModeQueueSubmitResponse
{
}

// Fence Create request
message FenceCreateRequest
{
    uint64 context_handle = 1;
    uint32 flags          = 2;
}

message FenceCreateResponse
{
    uint64 fence_handle = 1;
}

// Fence destroy request
message FenceDestroyRequest
{
    uint64 fence_handle = 1;
}

message FenceDestroyResponse
{
}

// Fence wait request
message FenceWaitRequest
{
    uint64 user_mode_queue_handle = 1;
    uint64 fence_handle           = 2;
}

message FenceWaitResponse
{
}

// Fence signal request
message FenceSignalRequest
{
    uint64 user_mode_queue_handle = 1;
    uint64 fence_handle           = 2;
}

message FenceSignalResponse
{
}

// Fence query request
message FenceQueryRequest
{
    uint64 fence_handle = 1;
}

message FenceQueryResponse
{
}

// The finalize request of client
message FinalizeRequest
{
}

message FinalizeResponse
{
}

// The heartbeat request of client
message HeartbeatRequest
{
    uint64 timestamp = 1;
}

message HeartbeatResponse
{
}

// General request (multiple oneof)
message KfeRequest
{
    uint64      request_id = 1;
    RequestType type       = 2;
    uint64      client_id  = 3;  // client ID

    oneof request_data
    {
        InitializeRequest                     initialize                         = 10;
        FinalizeRequest                       finalize                           = 11;
        DeviceGetCountRequest                 device_get_count                   = 12;
        DeviceOpenRequest                     device_open                        = 13;
        DeviceCloseRequest                    device_close                       = 14;
        DeviceGetPropertiesRequest            device_get_properties              = 15;
        ContextCreateRequest                  context_create                     = 16;
        ContextDestroyRequest                 context_destroy                    = 17;
        PhysicalMemoryCreateRequest           physical_memory_create             = 18;
        PhysicalMemoryDestroyRequest          physical_memory_destroy            = 19;
        PhysicalMemoryDeviceMapRequest        physical_memory_device_map         = 20;
        PhysicalMemoryDeviceUnmapRequest      physical_memory_device_unmap       = 21;
        PhysicalMemoryHostMapRequest          physical_memory_host_map           = 22;
        PhysicalMemoryHostUnmapRequest        physical_memory_host_unmap         = 23;
        PhysicalMemoryExportByDmaBufRequest   physical_memory_export_by_dmabuf   = 24;
        PhysicalMemoryImportFromDmaBufRequest physical_memory_import_from_dmabuf = 25;
        PinnedMemoryCreateRequest             pinned_memory_create               = 26;
        PinnedMemoryDestroyRequest            pinned_memory_destroy              = 27;
        PageableMemoryHostRegisterRequest     pageable_memory_host_register      = 28;
        PageableMemoryHostUnregisterRequest   pageable_memory_host_unregister    = 29;
        UserModeQueueCreateRequest            user_mode_queue_create             = 30;
        UserModeQueueDestroyRequest           user_mode_queue_destroy            = 31;
        UserModeQueueSubmitRequest            user_mode_queue_submit             = 32;
        FenceCreateRequest                    fence_create                       = 33;
        FenceDestroyRequest                   fence_destroy                      = 34;
        FenceWaitRequest                      fence_wait                         = 35;
        FenceSignalRequest                    fence_signal                       = 36;
        FenceQueryRequest                     fence_query                        = 37;
        HeartbeatRequest                      heart_beat                         = 38;
    }
}

// General reponse (multiple oneof)
message KfeResponse
{
    uint64 request_id    = 1;
    int32  result        = 2;
    string error_message = 3;

    oneof response_data
    {
        InitializeResponse                     initialize                         = 10;
        FinalizeResponse                       finalize                           = 11;
        DeviceGetCountResponse                 device_get_count                   = 12;
        DeviceOpenResponse                     device_open                        = 13;
        DeviceCloseResponse                    device_close                       = 14;
        DeviceGetPropertiesResponse            device_get_properties              = 15;
        ContextCreateResponse                  context_create                     = 16;
        ContextDestroyResponse                 context_destroy                    = 17;
        PhysicalMemoryCreateResponse           physical_memory_create             = 18;
        PhysicalMemoryDestroyResponse          physical_memory_destroy            = 19;
        PhysicalMemoryDeviceMapResponse        physical_memory_device_map         = 20;
        PhysicalMemoryDeviceUnmapResponse      physical_memory_device_unmap       = 21;
        PhysicalMemoryHostMapResponse          physical_memory_host_map           = 22;
        PhysicalMemoryHostUnmapResponse        physical_memory_host_unmap         = 23;
        PhysicalMemoryExportByDmaBufResponse   physical_memory_export_by_dmabuf   = 24;
        PhysicalMemoryImportFromDmaBufResponse physical_memory_import_from_dmabuf = 25;
        PinnedMemoryCreateResponse             pinned_memory_create               = 26;
        PinnedMemoryDestroyResponse            pinned_memory_destroy              = 27;
        PageableMemoryHostRegisterResponse     pageable_memory_host_register      = 28;
        PageableMemoryHostUnregisterResponse   pageable_memory_host_unregister    = 29;
        UserModeQueueCreateResponse            user_mode_queue_create             = 30;
        UserModeQueueDestroyResponse           user_mode_queue_destroy            = 31;
        UserModeQueueSubmitResponse            user_mode_queue_submit             = 32;
        FenceCreateResponse                    fence_create                       = 33;
        FenceDestroyResponse                   fence_destroy                      = 34;
        FenceWaitResponse                      fence_wait                         = 35;
        FenceSignalResponse                    fence_signal                       = 36;
        FenceQueryResponse                     fence_query                        = 37;
        HeartbeatResponse                      heart_beat                         = 38;
    }
}