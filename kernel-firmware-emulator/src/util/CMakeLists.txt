cmake_minimum_required(VERSION 3.12)

# Build static library libutil.a
add_library(util STATIC
    misc.cpp
)

# The related implementation with OS
if (WIN32)
    target_sources(util PRIVATE os/windows/os_windows.cpp)
    target_include_directories(util PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/os/windows)
elseif (UNIX)
    target_sources(util PRIVATE os/posix/os_posix.cpp)
    target_include_directories(util PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/os/posix)
else()
    message(FATAL_ERROR "Unknown platform!")
endif()

# Include directories
target_include_directories(util PUBLIC
    ${PAL_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/os
)