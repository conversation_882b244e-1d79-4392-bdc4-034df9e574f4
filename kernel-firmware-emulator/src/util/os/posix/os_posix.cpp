#include <dlfcn.h>
#include <errno.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/mman.h>
#ifdef __linux__
#include <sys/prctl.h>
#endif
#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <time.h>

#include "os.h"

namespace kfe
{
namespace util
{
// =====================================================================================================================
OsPid getProcessId(void)
{
    // Get the process ID of the current process.
    return ::getpid();
}

// =====================================================================================================================
OsThreadId getThreadId(void)
{
    // Get the thread ID of the current thread.
    return ::pthread_self();
}

// =====================================================================================================================
unsigned long long getPageSize(void)
{
    return static_cast<unsigned long long>(sysconf(_SC_PAGESIZE));
}

// =====================================================================================================================
void sleep(unsigned int msec)
{
    struct timespec timeReq = {0};
    struct timespec timeRem = {0};
    unsigned int    ret     = 0;

    timeReq.tv_sec  = msec / 1000;
    timeReq.tv_nsec = (msec % 1000) * 1000000;

    ret = nanosleep(&timeReq, &timeRem);

    // If interrupted by a non-blocked signal copy remaining time to the requested time.
    while (ret != 0 && errno == EINTR)
    {
        timeReq = timeRem;
        ret = nanosleep(&timeReq, &timeRem);
    }
}

// =====================================================================================================================
ssize_t write(int fd, const void* buf, size_t n)
{
    return ::write(fd, buf, n);
}

// =====================================================================================================================
ssize_t read(int fd, void* buf, size_t n)
{
    return ::read(fd, buf, n);
}

// =====================================================================================================================
int close(int fd)
{
    return ::close(fd);
}

// =====================================================================================================================
int shutdown(int fd, int how)
{
    return ::shutdown(fd, how);
}

// =====================================================================================================================
void exitNow(int code)
{
    ::_exit(code);
}

// =====================================================================================================================
OsPid fork()
{
    return ::fork();
}

// =====================================================================================================================
OsPid waitpid(int pid, int* status, int options)
{
    return ::waitpid(pid, status, options | WNOHANG);
}

// =====================================================================================================================
int socketpair(int domain, int type, int protocol, int sv[2])
{
    return ::socketpair(domain, type, protocol, sv);
}

// =====================================================================================================================
void* ipcSharedMemoryCreate(const char* pFname, size_t size, int* pFd)
{
    *pFd = shm_open(pFname, O_RDWR | O_CREAT, S_IRWXU | S_IRWXG | S_IRWXO);
    if (*pFd < 0)
    {
        return NULL;
    }

    int status = ftruncate(*pFd, size);
    if (status != 0)
    {
        return NULL;
    }

    return mmap(0, size, PROT_READ | PROT_WRITE, MAP_SHARED, *pFd, 0);
}

// =====================================================================================================================
void* ipcSharedMemoryOpen(const char* pFname, const int fd, size_t size)
{
    int handle = fd;

    if (pFname != NULL)
    {
        handle = shm_open(pFname, O_RDWR, S_IRWXU | S_IRWXG | S_IRWXO);
    }

    if (handle < 0)
    {
        return NULL;
    }

    return mmap(0, size, PROT_READ | PROT_WRITE, MAP_SHARED, handle, 0);
}

// =====================================================================================================================
void ipcSharedMemoryClose(const char* pFname, const int fd, const void* ptr, size_t size)
{
    if (ptr != NULL)
    {
        munmap((void*)ptr, size);
    }

    if (fd >= 0)
    {
        close(fd);
    }

    if (pFname != NULL)
    {
        shm_unlink(pFname);
    }
}

// =====================================================================================================================
void* loadLibrary(const char* filename)
{
    return (*filename == '\0') ? nullptr : ::dlopen(filename, RTLD_NOW | RTLD_LOCAL);
}

// =====================================================================================================================
void unloadLibrary(void* handle)
{
    if (handle == nullptr)
    {
        return;
    }

    ::dlclose(handle);
}

// =====================================================================================================================
void* getSymbol(void* handle, const char* name)
{
    return (handle == nullptr) ? nullptr : ::dlsym(handle, name);
}

} // namespace util
} // namespace kfe