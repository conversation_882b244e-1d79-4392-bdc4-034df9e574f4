#ifndef OS_POSIX_H_
#define OS_POSIX_H_

#include <memory.h>
#include <pthread.h>
#include <semaphore.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <stdint.h>
#include <sched.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/stat.h>
#include <unistd.h>

namespace kfe
{
namespace util
{
typedef FILE* OsFileHandle;

typedef pid_t OsPid;

typedef pthread_t OsThreadId;

#define OS_AF_UNIX AF_UNIX
#define OS_SOCK_STREAM SOCK_STREAM
#define OS_SHUT_RDWR SHUT_RDWR

} // namespace util
} // namespace kfe

#endif // OS_POSIX_H_