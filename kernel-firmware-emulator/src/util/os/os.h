#ifndef PAL_OS_H_
#define PAL_OS_H_

/* Platform specific types and #defines */
#if defined(_WIN32)
// Add windows exclusive header file.
/* Ensure proper CRT prototype of isatty() is included */
#include <io.h>
#include "os_windows.h"
#else // UNIX
#include "os_posix.h"
#endif

namespace kfe
{
namespace util
{
/// @brief Gets the process ID of the current process.
/// @return Returns the process ID of the current process.
/// @note This function is not thread-safe.
OsPid getProcessId(void);

/// @brief Gets the thread ID of the current thread.
/// @return Returns the thread ID of the current thread.
/// @note This function is not thread-safe.
OsThreadId getThreadId(void);

/// @brief Gets the system page size.
/// @return Returns the system page size in bytes.
unsigned long long getPageSize(void);

/// @brief Sleeps for the specified number of milliseconds.
/// @param msec The number of milliseconds to sleep.
/// @note This function is not thread-safe.
void sleep(unsigned int msec);

/// @brief Writes data to a file descriptor.
/// @param fd The file descriptor to write to.
/// @param buf The buffer containing the data to write.
/// @param n The number of bytes to write.
/// @return Returns the number of bytes written, or -1 on error.
ssize_t write(int fd, const void* buf, size_t n);

/// @brief Reads data from a file descriptor.
/// @param fd The file descriptor to read from.
/// @param buf The buffer to store the read data.
/// @param n The number of bytes to read.
/// @return Returns the number of bytes read, or -1 on error.
ssize_t read(int fd, void* buf, size_t n);

/// @brief Closes a file descriptor.
/// @param fd The file descriptor to close.
/// @return Returns 0 on success, or -1 on error.
int close(int fd);

/// @brief Shuts down a socket.
/// @param fd The file descriptor of the socket to shut down.
/// @param how The shutdown type (e.g., SHUT_RD, SHUT_WR, SHUT_RDWR).
/// @return Returns 0 on success, or -1 on error.
int shutdown(int fd, int how);

/// @brief Exits the program immediately with the specified exit code.
/// @param code The exit code to use.
/// @note This function does not return.
void exitNow(int code);

/// @brief Creates a new process.
/// @return Returns the process ID of the child process on success, or -1 on error.
/// @note This function is a wrapper around the fork() system call.
OsPid fork();

/// @brief Waits for a process to change state.
/// @param pid The process ID of the child process to wait for.
/// @param status A pointer to an integer where the exit status of the child process will be stored.
/// @param options Options for the waitpid call (e.g., WNOHANG).
/// @return Returns the process ID of the child process on success, or -1 on error.
OsPid waitpid(int pid, int* status, int options);

/// @brief socketpair creates a pair of connected sockets.
/// @param domain The communication domain (e.g., AF_UNIX).
/// @param type The type of socket (e.g., SOCK_STREAM).
/// @param protocol The protocol to be used with the socket (usually 0).
/// @param sv An array of two integers that will hold the file descriptors for the sockets.
/// @return Returns 0 on success, or -1 on error.
/// @note This function is used to create a pair of sockets that can be used for inter-process communication.
int socketpair(int domain, int type, int protocol, int sv[2]);

/// @brief Creates a shared memory segment.
/// @param pFname The name of the shared memory segment.
/// @param size The size of the shared memory segment.
/// @param pFd The file descriptor for the shared memory segment.
/// @return Returns a pointer to the shared memory segment, or NULL if the creation failed.
void* ipcSharedMemoryCreate(const char* pFname, size_t size, int* pFd);

/// @brief Opens a shared memory segment.
/// @param pFname The name of the shared memory segment.
/// @param fd The file descriptor for the shared memory segment.
/// @param size The size of the shared memory segment.
/// @return Returns a pointer to the shared memory segment, or NULL if the opening failed.
void* ipcSharedMemoryOpen(const char* pFname, const int fd, size_t size);

/// @brief Closes a shared memory segment.
/// @param pFname The name of the shared memory segment.
/// @param fd The file descriptor for the shared memory segment.
/// @param ptr The pointer to the shared memory segment.
/// @param size The size of the shared memory segment.
void ipcSharedMemoryClose(const char* pFname, const int fd, const void* ptr, size_t size);

/// @brief Load a shared library.
/// @param filename The path to the shared library file.
/// @return Returns a handle to the loaded library, or nullptr on failure.
void* loadLibrary(const char* filename);

/// @brief Unload a shared library.
/// @param handle The handle to the loaded library.
void unloadLibrary(void* handle);

//! Return the address of the function identified by \a name.
/// @param handle The handle to the loaded library.
/// @param name The name of the function to retrieve.
/// @return Returns a pointer to the function, or nullptr if the function is not found.
void* getSymbol(void* handle, const char* name);

} // namespace util
} // namespace kfe

#endif // PAL_OS_H_