#include "kfe_thunk.h"
#include "os.h"

typedef void* (*ppCreateDevice)(int gpuId, void* pReadHostMemFunc, void* pWriteHostMemFunc);
typedef void  (*ppDestroyDevice)(void* pDevice);
typedef void  (*ppReadMemory)(void* pDevice, uint64_t addr, size_t size, uint8_t* pData);
typedef void  (*ppWriteMemory)(void* pDevice, uint64_t addr, size_t size, const uint8_t* pData);
typedef void  (*ppReadRegister)(void* pDevice, uint64_t addr, uint64_t* pValue);
typedef void  (*ppWriteRegister)(void* pDevice, uint64_t addr, uint64_t value);

typedef struct PotionModelManager_st
{
    // Handle to the loaded PotionModel library
    void* pLibraryHandle;

    // Add function pointers for the PotionModel API functions as needed
    ppCreateDevice  pfnCreateDevice;
    ppDestroyDevice pfnDestroyDevice;
    ppReadMemory    pfnReadMemory;
    ppWriteMemory   pfnWriteMemory;
    ppReadRegister  pfnReadRegister;
    ppWriteRegister pfnWriteRegister;
} PotionModelManager;

// Global instance of the perfpotion manager.
// This should be initialized in palThunkInitialize() and cleaned up in palThunkDeinitialize().
static PotionModelManager* g_pPerfPotionManager = NULL;

#define KFE_THUNK_LOAD_MODEL_FUNCTION(manager, funcName)                    \
    do {                                                                    \
        (manager)->pfn##funcName = (pp##funcName)kfe::util::getSymbol(      \
            (manager)->pLibraryHandle, "pp" #funcName);                     \
        if ((manager)->pfn##funcName == NULL) {                             \
            fprintf(stderr, "Failed to load kfe" #funcName " function.\n"); \
            kfe::util::unloadLibrary((manager)->pLibraryHandle);            \
            delete manager;                                                 \
            manager = NULL;                                                 \
            return KFE_ERROR_NOT_INITIALIZED;                               \
        }                                                                   \
    } while(0)

// =====================================================================================================================
kfeResult kfeThunkInitialize(void)
{
    kfeResult result = KFE_SUCCESS;

    // Load the PotionModel library
#ifdef __APPLE__
    const char* libraryName = "libarchmodel.dylib";
#else
    const char* libraryName = "libarchmodel.so";
#endif

    // Load the function pointers for the Perf potion API.
    g_pPerfPotionManager = new PotionModelManager();
    if (g_pPerfPotionManager == nullptr)
    {
        return KFE_ERROR_OUT_OF_MEMORY;
    }

    g_pPerfPotionManager->pLibraryHandle = kfe::util::loadLibrary(libraryName);
    if (g_pPerfPotionManager->pLibraryHandle == nullptr)
    {
        delete g_pPerfPotionManager;
        g_pPerfPotionManager = nullptr;
        return KFE_ERROR_NOT_INITIALIZED;
    }

    KFE_THUNK_LOAD_MODEL_FUNCTION(g_pPerfPotionManager, CreateDevice);
    KFE_THUNK_LOAD_MODEL_FUNCTION(g_pPerfPotionManager, DestroyDevice);
    KFE_THUNK_LOAD_MODEL_FUNCTION(g_pPerfPotionManager, ReadMemory);
    KFE_THUNK_LOAD_MODEL_FUNCTION(g_pPerfPotionManager, WriteMemory);
    KFE_THUNK_LOAD_MODEL_FUNCTION(g_pPerfPotionManager, ReadRegister);
    KFE_THUNK_LOAD_MODEL_FUNCTION(g_pPerfPotionManager, WriteRegister);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult kfeThunkCreateDevice(void** ppDevice, int gpuId, void* pReadHostMemFunc, void* pWriteHostMemFunc)
{
    if (ppDevice == nullptr || gpuId < 0 || pReadHostMemFunc == nullptr || pWriteHostMemFunc == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    if (g_pPerfPotionManager == nullptr || g_pPerfPotionManager->pfnCreateDevice == nullptr)
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    *ppDevice = g_pPerfPotionManager->pfnCreateDevice(gpuId, pReadHostMemFunc, pWriteHostMemFunc);
    if (*ppDevice == nullptr)
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult kfeThunkDestroyDevice(void* pDevice)
{
    if (pDevice == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    if (g_pPerfPotionManager == nullptr || g_pPerfPotionManager->pfnDestroyDevice == nullptr)
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    g_pPerfPotionManager->pfnDestroyDevice(pDevice);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult kfeThunkReadMemory(void* pDevice, uint64_t addr, size_t size, uint8_t* pData)
{
    if (pDevice == nullptr || pData == nullptr || size == 0)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    if (g_pPerfPotionManager == nullptr || g_pPerfPotionManager->pfnReadMemory == nullptr)
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    g_pPerfPotionManager->pfnReadMemory(pDevice, addr, size, pData);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult kfeThunkWriteMemory(void* pDevice, uint64_t addr, size_t size, const uint8_t* pData)
{
    if (pDevice == nullptr || pData == nullptr || size == 0)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    if (g_pPerfPotionManager == nullptr || g_pPerfPotionManager->pfnWriteMemory == nullptr)
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    g_pPerfPotionManager->pfnWriteMemory(pDevice, addr, size, pData);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult kfeThunkReadRegister(void* pDevice, uint64_t addr, uint64_t* pValue)
{
    if (pDevice == nullptr || pValue == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    if (g_pPerfPotionManager == nullptr || g_pPerfPotionManager->pfnReadRegister == nullptr)
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    g_pPerfPotionManager->pfnReadRegister(pDevice, addr, pValue);

    return KFE_SUCCESS;
}

// =====================================================================================================================
kfeResult kfeThunkWriteRegister(void* pDevice, uint64_t addr, uint64_t value)
{
    if (pDevice == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    if (g_pPerfPotionManager == nullptr || g_pPerfPotionManager->pfnWriteRegister == nullptr)
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    g_pPerfPotionManager->pfnWriteRegister(pDevice, addr, value);

    return KFE_SUCCESS;
}

// =====================================================================================================================
void kfeThunkDeinitialize(void)
{
    if (g_pPerfPotionManager != nullptr)
    {
        if (g_pPerfPotionManager->pLibraryHandle != nullptr)
        {
            kfe::util::unloadLibrary(g_pPerfPotionManager->pLibraryHandle);
        }

        delete g_pPerfPotionManager;
        g_pPerfPotionManager = nullptr;
    }
}