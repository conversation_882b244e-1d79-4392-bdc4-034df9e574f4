#ifndef KFE_THUNK_H_
#define KFE_THUNK_H_

#include "kfe.h"

/// @brief Initialize the KFE Thunk layer.
///
/// This function should be called before any other KFE Thunk functions are used.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
kfeResult kfeThunkInitialize(void);

/// @brief Create a device instance for the specified GPU ID.
/// @param ppDevice Pointer to a pointer that will be set to the created device instance.
/// @param gpuId The ID of the GPU for which to create the device instance.
/// @param pReadHostMemFunc Pointer to the function used to read host memory.
/// @param pWriteHostMemFunc Pointer to the function used to write host memory.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult kfeThunkCreateDevice(void** ppDevice, int gpuId, void* pReadHostMemFunc, void* pWriteHostMemFunc);

/// @brief Destroy a device instance.
/// @param pDevice Pointer to the device instance to destroy.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult kfeThunkDestroyDevice(void* pDevice);

/// @brief Read memory from the device.
/// @param pDevice Pointer to the device instance.
/// @param addr The address in device memory to read from.
/// @param size The number of bytes to read.
/// @param pData Pointer to a buffer where the read data will be stored.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult kfeThunkReadMemory(void* pDevice, uint64_t addr, size_t size, uint8_t* pData);

/// @brief Write memory to the device.
/// @param pDevice Pointer to the device instance.
/// @param addr The address in device memory to write to.
/// @param size The number of bytes to write.
/// @param pData Pointer to a buffer containing the data to write.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult kfeThunkWriteMemory(void* pDevice, uint64_t addr, size_t size, const uint8_t* pData);

/// @brief Read a register from the device.
/// @param pDevice Pointer to the device instance.
/// @param addr The address of the register to read.
/// @param pValue Pointer to a variable where the read value will be stored.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult kfeThunkReadRegister(void* pDevice, uint64_t addr, uint64_t* pValue);

/// @brief Write a value to a register on the device.
/// @param pDevice Pointer to the device instance.
/// @param addr The address of the register to write to.
/// @param value The value to write to the register.
/// @return Returns KFE_SUCCESS on success, otherwise returns KFE_ERROR_*.
kfeResult kfeThunkWriteRegister(void* pDevice, uint64_t addr, uint64_t value);

/// @brief Deinitialize the KFE Thunk layer.
///
/// This function should be called when the KFE Thunk layer is no longer needed.
/// @return Returns PAL_SUCCESS on success, or an error code on failure.
void kfeThunkDeinitialize(void);

#endif // KFE_THUNK_H_