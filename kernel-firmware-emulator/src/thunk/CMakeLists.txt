cmake_minimum_required(VERSION 3.12)

set(THUNK_SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/kfe_thunk.cpp)

# Generate static library libthunk.a
add_library(thunk STATIC ${THUNK_SOURCES})

# Add the including file path.
target_include_directories(thunk PUBLIC
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src/util
    ${CMAKE_SOURCE_DIR}/src/util/os
    ${CMAKE_CURRENT_SOURCE_DIR}
)

if(WIN32)
    target_include_directories(thunk PUBLIC ${CMAKE_SOURCE_DIR}/src/util/os/windows)
elseif(UNIX)
    target_include_directories(thunk PUBLIC ${CMAKE_SOURCE_DIR}/src/util/os/posix)
else()
    message(FATAL_ERROR "Unknown platform!")
endif()