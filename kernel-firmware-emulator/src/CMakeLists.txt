cmake_minimum_required(VERSION 3.12)

add_subdirectory(util)  # Build the static module (libutil.a)
add_subdirectory(core)  # Build the static module (libcore.a)
add_subdirectory(thunk) # Build the static module (libthunk.a)

add_library(kfe SHARED
    kfe.cpp
)

# Link the dependencies of module.
target_link_directories(kfe PUBLIC ${PROTOBUF_LIBRARY_DIRS})
target_link_libraries(kfe PUBLIC util thunk core)

# Add the path of including file.
target_include_directories(kfe PUBLIC
    ${CMAKE_SOURCE_DIR}/src/core    # public API
    ${CMAKE_SOURCE_DIR}/src/util    # Util module
    ${CMAKE_SOURCE_DIR}/src/thunk   # Thunk module
)

# Dynamic library symbol control
if (WIN32)
    target_sources(kfe PRIVATE ${CMAKE_SOURCE_DIR}/kfe.def)
elseif (APPLE)
    # macOS uses exported symbols list
    set_target_properties(kfe PROPERTIES
        LINK_FLAGS "-Wl,-exported_symbols_list,${CMAKE_SOURCE_DIR}/kfe_macos.exports"
    )
elseif (UNIX)
    # Linux uses version script
    set_target_properties(kfe PROPERTIES
        LINK_FLAGS "-Wl,--version-script=${CMAKE_SOURCE_DIR}/kfe.def"
    )
endif()

# Set the output name with dynamic library.
set_target_properties(kfe PROPERTIES OUTPUT_NAME "kfe")

# compiler options
target_compile_options(kfe PRIVATE -Wno-deprecated-declarations)

# Install libraries (both static and dynamic)
install(TARGETS kfe
    LIBRARY DESTINATION lib # For shared libraries
    ARCHIVE DESTINATION lib # For static libraries
)

# Install header files
install(FILES
    ${CMAKE_SOURCE_DIR}/include/kfe.h
    ${CMAKE_SOURCE_DIR}/include/kfe_typedefs.h
    DESTINATION include
)