
#include "kfe.h"
#include "kfe_protocol.pb.h"
#include "client_connection.h"

extern "C" {
kfeResult kfeInitialize()
{
    return kfe::ClientConnection::getInstance().initialize();
}

kfeResult kfeDeviceGetCount(int* pCount)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if (pCount == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.getDeviceCount(pCount);
}

kfeResult kfeDeviceOpen(kfeDevice* pDevice, int gpuId)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((pDevice == nullptr) || (gpuId < 0))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.openDevice(pDevice, gpuId);
}

kfeResult kfeDeviceClose(kfeDevice device)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if (device == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.closeDevice(device);
}

kfeResult kfeDeviceGetProperties(kfeDevice device, kfeDeviceProperties* pProperties)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((device == nullptr) || (pProperties == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.getDeviceProperties(device, pProperties);
}

kfeResult kfeContextCreate(kfeContext* pContext, unsigned int flags, kfeDevice device)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((pContext == nullptr) || (device == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.createContext(pContext, flags, device);
}

kfeResult kfeContextDestroy(kfeContext context)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if (context == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.destroyContext(context);
}

kfeResult kfePhysicalMemoryCreate(kfeContext context, uint64_t size, uint64_t pageSize, uint64_t flags,
                                  kfePhysicalMemoryObject* pMemoryObject)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (pMemoryObject == nullptr) || (size <= 0))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.createPhysicalMemory(context, size, pageSize, flags, pMemoryObject);
}

kfeResult kfePhysicalMemoryDestroy(kfeContext context, kfePhysicalMemoryObject memoryObject)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (memoryObject == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.destroyPhysicalMemory(context, memoryObject);
}

kfeResult kfePhysicalMemoryDeviceMap(kfeContext context, void* pDeviceVirtualAddress,
                                     kfePhysicalMemoryObject memoryObject, uint64_t size, uint64_t flags,
                                     kfePhysicalMemoryObjectDeviceMapped* pPhysicalMemoryDeviceMapped)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (pDeviceVirtualAddress == nullptr) || (memoryObject == nullptr) ||
        (size <= 0) || (pPhysicalMemoryDeviceMapped == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.deviceMapPhysicalMemory(context, reinterpret_cast<uint64_t>(pDeviceVirtualAddress), memoryObject,
                                                    size, flags, pPhysicalMemoryDeviceMapped);
}

kfeResult kfePhysicalMemoryDeviceUnmap(kfeContext context,
                                       kfePhysicalMemoryObjectDeviceMapped physicalMemoryDeviceMapped)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (physicalMemoryDeviceMapped == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.deviceUnmapPhysicalMemory(context, physicalMemoryDeviceMapped);
}

kfeResult kfePhysicalMemoryHostMap(kfeContext context, kfePhysicalMemoryObject memoryObject, uint64_t size,
                                   uint64_t flags, kfePhysicalMemoryObjectHostMapped* pPhysicalMemoryHostMapped,
                                   void** ppHostVirtualAddress)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (memoryObject == nullptr) || (size <= 0) ||
        (pPhysicalMemoryHostMapped == nullptr) || (ppHostVirtualAddress == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.hostMapPhysicalMemory(context, memoryObject, size, flags, pPhysicalMemoryHostMapped,
                                                  ppHostVirtualAddress);
}

kfeResult kfePhysicalMemoryHostUnmap(kfeContext context, kfePhysicalMemoryObjectHostMapped physicalMemoryHostMapped)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (physicalMemoryHostMapped == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.hostUnmapPhysicalMemory(context, physicalMemoryHostMapped);
}

kfeResult kfePhysicalMemoryExportByDmaBuf(kfeContext context, kfePhysicalMemoryObject memoryObject, uint64_t size,
                                          uint64_t pageSize, uint64_t flags, int* pDmaBufFd)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (memoryObject == nullptr) || (size == 0) || (pDmaBufFd == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.exportPhysicalMemoryByDmaBuf(context, memoryObject, size, pageSize, flags, pDmaBufFd);
}

kfeResult kfePhysicalMemoryImportFromDmaBuf(kfeContext context, int dmaBufFd, kfeContext* pRemoteContext,
                                            kfePhysicalMemoryObject* pPhysicalMemoryObject, uint64_t* pSize,
                                            uint64_t* pPageSize, uint64_t* pFlags)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (pRemoteContext == nullptr) || (pPhysicalMemoryObject == nullptr) ||
        (dmaBufFd < 0) || (pSize == nullptr) || (pPageSize == nullptr) || (pFlags == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.importPhysicalMemoryFromDmaBuf(context, dmaBufFd, pPhysicalMemoryObject,
                                                           reinterpret_cast<unsigned long long*>(pSize),
                                                           reinterpret_cast<unsigned long long*>(pPageSize),
                                                           reinterpret_cast<unsigned long long*>(pFlags));
}

kfeResult kfePinnedMemoryCreate(kfeContext context, uint64_t size, uint64_t flags, kfePinnedMemoryObject* pPinnedMemory,
                                void** ppHostVirtualAddress)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (size <= 0) || (pPinnedMemory == nullptr) || (ppHostVirtualAddress == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.createPinnedMemory(context, size, flags, pPinnedMemory, ppHostVirtualAddress);
}

kfeResult kfePinnedMemoryDestroy(kfeContext context, kfePinnedMemoryObject pinnedMemory)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (pinnedMemory == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.destroyPinnedMemory(context, pinnedMemory);
}

kfeResult KFEAPI kfePageableMemoryHostRegister(kfeContext context, void* pHostVirtualAddress, uint64_t size,
                                               uint64_t flags,
                                               kfePageableMemoryHostRegistered* pHostPageableMemoryRegistered,
                                               uint64_t* pRegisteredOffset)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (pHostVirtualAddress == nullptr) || (size <= 0) ||
        (pHostPageableMemoryRegistered == nullptr) || (pRegisteredOffset == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.registerPageableMemory(context, pHostVirtualAddress, size, flags,
                                                   pHostPageableMemoryRegistered,
                                                   reinterpret_cast<unsigned long long*>(pRegisteredOffset));
}

kfeResult KFEAPI kfePageableMemoryHostUnregister(kfeContext context,
                                                 kfePageableMemoryHostRegistered hostPageableMemoryRegistered)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((context == nullptr) || (hostPageableMemoryRegistered == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.unregisterPageableMemory(context, hostPageableMemoryRegistered);
}

kfeResult kfeUserModeQueueCreate(kfeUserModeQueue* pUserModeQueue, unsigned int flags, kfeContext context)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((pUserModeQueue == nullptr) || (context == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.createUserModeQueue(pUserModeQueue, flags, context);
}

kfeResult kfeUserModeQueueDestroy(kfeUserModeQueue userModeQueue)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if (userModeQueue == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.destroyUserModeQueue(userModeQueue);
}

kfeResult kfeUserModeQueueSubmit(kfeUserModeQueue userModeQueue,
                                 kfeCommandPacket* pCommandPackets,
                                 unsigned int numCmdPackets)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((userModeQueue == nullptr) || (pCommandPackets == nullptr) || (numCmdPackets <= 0))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.submitUserModeQueue(userModeQueue, pCommandPackets, numCmdPackets);
}

kfeResult kfeFenceCreate(kfeFence* pFence, unsigned int flags, kfeContext context)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((pFence == nullptr) || (context == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.createFence(pFence, flags, context);
}

kfeResult kfeFenceDestroy(kfeFence fence)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if (fence == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.destroyFence(fence);
}

kfeResult kfeFenceWait(kfeUserModeQueue userModeQueue, kfeFence fence)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((userModeQueue == nullptr) || (fence == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.waitFence(userModeQueue, fence);
}

kfeResult kfeFenceSignal(kfeUserModeQueue userModeQueue, kfeFence fence)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if ((userModeQueue == nullptr) || (fence == nullptr))
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.signalFence(userModeQueue, fence);
}

kfeResult kfeFenceQuery(kfeFence fence)
{
    auto& clientConnection = kfe::ClientConnection::getInstance();
    if (!clientConnection.isInitialized())
    {
        return KFE_ERROR_NOT_INITIALIZED;
    }

    if (fence == nullptr)
    {
        return KFE_ERROR_INVALID_VALUE;
    }

    return clientConnection.queryFence(fence);
}

kfeResult kfeFinalize()
{
    return kfe::ClientConnection::getInstance().finalize();
}

} // extern "C"