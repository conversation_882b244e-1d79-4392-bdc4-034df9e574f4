name: Delete Old Workflow Runs

on:
  schedule:
    - cron: '0 1 * * *'  # Runs daily at 1 a.m.
  workflow_dispatch:  # Allows manual triggering of the workflow

concurrency:
  group: delete-workflow-runs-cleanup
  cancel-in-progress: true

jobs:
  delete-workflows:
    runs-on: ubuntu-latest
    permissions:
      actions: write  # Required to delete workflow runs
      contents: read  # Required to read repository contents

    steps:
      - name: Delete old workflow runs
        uses: Mattraks/delete-workflow-runs@v2
        with:
          retain_days: 1           # Keep workflow runs from the last 1 day
          keep_minimum_runs: 3     # Keep at least 3 runs

      - name: Delete old workflow artifacts
        uses: geekyeggo/delete-artifact@v2
        with:
          name: '*'
          failOnError: false

      - name: Summary
        run: |
          echo "### Cleanup Summary 🧹" >> $GITHUB_STEP_SUMMARY
          echo "- Deleted workflow runs older than 1 days" >> $GITHUB_STEP_SUMMARY
          echo "- Kept minimum 3 runs per workflow" >> $GITHUB_STEP_SUMMARY
          echo "- Deleted old artifacts" >> $GITHUB_STEP_SUMMARY
