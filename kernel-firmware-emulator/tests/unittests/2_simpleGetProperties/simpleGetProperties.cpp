#include "kfe.h"
#include "kfe_typedefs.h"

#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <unistd.h>

int main()
{
    kfeResult result;

    // Initialize the KFE API
    result = kfeInitialize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeInitialize failed: %d\n", result);
        return EXIT_FAILURE;
    }

    int deviceCount = 0;
    result = kfeDeviceGetCount(&deviceCount);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeGetDeviceCount failed: %d\n", result);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    if (deviceCount <= 0)
    {
        fprintf(stderr, "Invalid device count: %d\n", deviceCount);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "Number of devices: %d\n", deviceCount);

    kfeDevice device[deviceCount];
    for (int i = 0; i < deviceCount; ++i)
    {
        result = kfeDeviceOpen(&device[i], i);
        if (result != KFE_SUCCESS)
        {
            fprintf(stderr, "kfeDeviceOpen failed for device %d: %d\n", i, result);
            for (int j = 0; j < i; ++j)
            {
                kfeDeviceClose(device[j]);
            }

            kfeFinalize();

            return EXIT_FAILURE;
        }

        kfeDeviceProperties properties = {0};
        result = kfeDeviceGetProperties(device[i], &properties);
        if (result != KFE_SUCCESS)
        {
            fprintf(stderr, "kfeDeviceGetProperties failed for device %d: %d\n", i, result);
            kfeDeviceClose(device[i]);
            for (int j = 0; j < i; ++j)
            {
                kfeDeviceClose(device[j]);
            }

            kfeFinalize();

            return EXIT_FAILURE;
        }

        fprintf(stdout, "Device %d: %s\n", i, properties.name);
        fprintf(stdout, "  UUID: ");
        for (int j = 0; j < 16; ++j)
        {
            if (j == 3 || j == 5 || j == 7 || j == 9)
            {
                fprintf(stdout, "-");
            }

            // Print UUID as hex
            fprintf(stdout, "%02x", (unsigned char)properties.uuid[j]);
        }
        fprintf(stdout, "\n");
        fprintf(stdout, "  Total Global Memory: %zu bytes\n", properties.totalGlobalMem);
        fprintf(stdout, "  Shared Memory per Block: %zu bytes\n", properties.sharedMemPerBlock);
        fprintf(stdout, "  Registers per Block: %d\n", properties.regsPerBlock);
        fprintf(stdout, "  Warp Size: %d\n", properties.warpSize);
        fprintf(stdout, "  Memory Pitch: %zu bytes\n", properties.memPitch);
        fprintf(stdout, "  Max Threads per Block: %d\n", properties.maxThreadsPerBlock);
        fprintf(stdout, "  Max Threads Dimensions: [%d, %d, %d]\n",
                properties.maxThreadsDim[0], properties.maxThreadsDim[1], properties.maxThreadsDim[2]);
        fprintf(stdout, "  Max Grid Size: [%d, %d, %d]\n",
                properties.maxGridSize[0], properties.maxGridSize[1], properties.maxGridSize[2]);
        fprintf(stdout, "  Clock Rate: %d kHz\n", properties.clockRate);
        fprintf(stdout, "  Total Constant Memory: %zu bytes\n", properties.totalConstMem);
        fprintf(stdout, "  Compute Capability: %d.%d\n",
                properties.major, properties.minor);
        fprintf(stdout, "  Texture Alignment: %zu bytes\n", properties.textureAlignment);
        fprintf(stdout, "  Texture Pitch Alignment: %zu bytes\n", properties.texturePitchAlignment);
        fprintf(stdout, "  Device Overlap: %d\n", properties.deviceOverlap);
        fprintf(stdout, "  Multi-Processor Count: %d\n", properties.multiProcessorCount);
        fprintf(stdout, "  Kernel Exec Timeout Enabled: %d\n", properties.kernelExecTimeoutEnabled);
        fprintf(stdout, "  Integrated: %d\n", properties.integrated);
        fprintf(stdout, "  Can Map Host Memory: %d\n", properties.canMapHostMemory);
        fprintf(stdout, "  Compute Mode: %d\n", properties.computeMode);
        fprintf(stdout, "  Max Texture 1D: %d\n", properties.maxTexture1D);
        fprintf(stdout, "  Max Texture 1D Mipmap: %d\n", properties.maxTexture1DMipmap);
        fprintf(stdout, "  Max Texture 1D Linear: %d\n", properties.maxTexture1DLinear);
        fprintf(stdout, "  Max Texture 2D: [%d, %d]\n",
                properties.maxTexture2D[0], properties.maxTexture2D[1]);
        fprintf(stdout, "  Max Texture 2D Mipmap: [%d, %d]\n",
                properties.maxTexture2DMipmap[0], properties.maxTexture2DMipmap[1]);
        fprintf(stdout, "  Max Texture 2D Linear: [%d, %d]\n",
                properties.maxTexture2DLinear[0], properties.maxTexture2DLinear[1]);
        fprintf(stdout, "  Max Texture 2D Gather: [%d, %d]\n",
                properties.maxTexture2DGather[0], properties.maxTexture2DGather[1]);
        fprintf(stdout, "  Max Texture 3D: [%d, %d, %d]\n",
                properties.maxTexture3D[0], properties.maxTexture3D[1], properties.maxTexture3D[2]);
        fprintf(stdout, "  Max Texture 3D Alt: [%d, %d, %d]\n",
                properties.maxTexture3DAlt[0], properties.maxTexture3DAlt[1], properties.maxTexture3DAlt[2]);
        fprintf(stdout, "  Max Texture Cubemap: %d\n",
                properties.maxTextureCubemap);
        fprintf(stdout, "  Max Texture 1D Layered: [%d, %d]\n",
                properties.maxTexture1DLayered[0], properties.maxTexture1DLayered[1]);
        fprintf(stdout, "  Max Texture 2D Layered: [%d, %d, %d]\n",
                properties.maxTexture2DLayered[0], properties.maxTexture2DLayered[1], properties.maxTexture2DLayered[2]);
        fprintf(stdout, "  Max Texture Cubemap Layered: [%d, %d]\n",
                properties.maxTextureCubemapLayered[0], properties.maxTextureCubemapLayered[1]);
        fprintf(stdout, "  Max Surface 1D: %d\n", properties.maxSurface1D);
        fprintf(stdout, "  Max Surface 2D: [%d, %d]\n",
                properties.maxSurface2D[0], properties.maxSurface2D[1]);
        fprintf(stdout, "  Max Surface 3D: [%d, %d, %d]\n",
                properties.maxSurface3D[0], properties.maxSurface3D[1], properties.maxSurface3D[2]);
        fprintf(stdout, "  Max Surface 1D Layered: [%d, %d]\n",
                properties.maxSurface1DLayered[0], properties.maxSurface1DLayered[1]);
        fprintf(stdout, "  Max Surface 2D Layered: [%d, %d, %d]\n",
                properties.maxSurface2DLayered[0], properties.maxSurface2DLayered[1], properties.maxSurface2DLayered[2]);
        fprintf(stdout, "  Max Surface Cubemap: %d\n",
                properties.maxSurfaceCubemap);
        fprintf(stdout, "  Max Surface Cubemap Layered: [%d, %d]\n",
                properties.maxSurfaceCubemapLayered[0], properties.maxSurfaceCubemapLayered[1]);
        fprintf(stdout, "  Surface alignment: %zu bytes\n",
                properties.surfaceAlignment);
        fprintf(stdout, "  concurrentKernels: %d\n", properties.concurrentKernels);
        fprintf(stdout, "  ECC Enabled: %d\n", properties.ECCEnabled);
        fprintf(stdout, "  PCI Bus ID: %d\n", properties.pciBusID);
        fprintf(stdout, "  PCI Device ID: %d\n", properties.pciDeviceID);
        fprintf(stdout, "  PCI Domain ID: %d\n", properties.pciDomainID);
        fprintf(stdout, "  TCC Driver: %d\n", properties.tccDriver);
        fprintf(stdout, "  Async Engine Count: %d\n", properties.asyncEngineCount);
        fprintf(stdout, "  unifiedAddressing: %d\n", properties.unifiedAddressing);
        fprintf(stdout, "  Memory Clock Rate: %d kHz\n", properties.memoryClockRate);
        fprintf(stdout, "  Memory Bus Width: %d bits\n", properties.memoryBusWidth);
        fprintf(stdout, "  L2 Cache Size: %d bytes\n", properties.l2CacheSize);
        fprintf(stdout, "  Max Persisting L2 Cache: %d\n", properties.persistingL2CacheMaxSize);
        fprintf(stdout, "  Max Threads Per Multi-Processor: %d\n", properties.maxThreadsPerMultiProcessor);
        fprintf(stdout, "  Stream Priorities Supported: %d\n", properties.streamPrioritiesSupported);
        fprintf(stdout, "  Global L1 Cache Supported: %d\n", properties.globalL1CacheSupported);
        fprintf(stdout, "  Local L1 Cache Supported: %d\n", properties.localL1CacheSupported);
        fprintf(stdout, "  Shared Memory Per Multiprocessor: %zu bytes\n", properties.sharedMemPerMultiprocessor);
        fprintf(stdout, "  Registers Per Multiprocessor: %d\n", properties.regsPerMultiprocessor);
        fprintf(stdout, "  Managed Memory: %d\n", properties.managedMemory);
        fprintf(stdout, "  Is multi-GPU: %d\n", properties.isMultiGpuBoard);
        fprintf(stdout, "  Multi-GPU Board Group ID: %d\n", properties.multiGpuBoardGroupID);
        fprintf(stdout, "  Host Native Atomic Operations Supported: %d\n", properties.hostNativeAtomicSupported);
        fprintf(stdout, "  Single To Double Precision Performance Ratio: %d\n", properties.singleToDoublePrecisionPerfRatio);
        fprintf(stdout, "  Pageable Memory Access: %d\n", properties.pageableMemoryAccess);
        fprintf(stdout, "  Concurrent Managed Access: %d\n", properties.concurrentManagedAccess);
        fprintf(stdout, "  Compute Preemption Supported: %d\n", properties.computePreemptionSupported);
        fprintf(stdout, "  Can Use Host Pointer for Registered Memory: %d\n", properties.canUseHostPointerForRegisteredMem);
        fprintf(stdout, "  Cooperative launch: %d\n", properties.cooperativeLaunch);
        fprintf(stdout, "  Cooperative Multi-Device Launch: %d\n", properties.cooperativeMultiDeviceLaunch);
        fprintf(stdout, "  Shared Memory Per Block Optin: %d bytes\n", properties.sharedMemPerBlockOptin);
        fprintf(stdout, "  Pageable Memory Access Uses Host Page Tables: %d\n", properties.pageableMemoryAccessUsesHostPageTables);
        fprintf(stdout, "  Direct Managed Memory Access From Host: %d\n", properties.directManagedMemAccessFromHost);
        fprintf(stdout, "  Max Blocks Per Multi-Processor: %d\n", properties.maxBlocksPerMultiProcessor);
        fprintf(stdout, "  Access Policy Max Window Size: %d bytes\n", properties.accessPolicyMaxWindowSize);
        fprintf(stdout, "  Reserved Shared Memory Per Block: %zu bytes\n", properties.reservedSharedMemPerBlock);
        fprintf(stdout, "  Host Register Supported: %d\n", properties.hostRegisterSupported);
        fprintf(stdout, "  Sparse Cuda Array Supported: %d\n", properties.sparseCudaArraySupported);
        fprintf(stdout, "  Host Register Read Only Supported: %d\n", properties.hostRegisterReadOnlySupported);
        fprintf(stdout, "  Timeline Semaphore Interop Supported: %d\n", properties.timelineSemaphoreInteropSupported);
        fprintf(stdout, "  Memory Pools Supported: %d\n", properties.memoryPoolsSupported);
        fprintf(stdout, "  Gpu Direct RDMA Supported: %d\n", properties.gpuDirectRDMASupported);
        fprintf(stdout, "  Gpu Direct RDMA Flush Writes Options: %d\n", properties.gpuDirectRDMAFlushWritesOptions);
        fprintf(stdout, "  Gpu Direct RDMA Writes Ordering: %d\n", properties.gpuDirectRDMAWritesOrdering);
        fprintf(stdout, "  Memory Pool Supported Handle Types: %d\n", properties.memoryPoolSupportedHandleTypes);
        fprintf(stdout, "  Deferred Mapping Cuda Array Supported: %d\n", properties.deferredMappingCudaArraySupported);
        fprintf(stdout, "  Ipc Event Supported: %d\n", properties.ipcEventSupported);
        fprintf(stdout, "  Unified Function Pointers: %d\n", properties.unifiedFunctionPointers);
        fprintf(stdout, "  Physical Memory Alignment: %d bytes\n", properties.physicalMemoryAlignment);
    }

    for (int i = 0; i < deviceCount; ++i)
    {
        kfeDeviceClose(device[i]);
    }

    // Finalize the KFE API
    result = kfeFinalize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeFinalize failed: %d\n", result);

        return EXIT_FAILURE;
    }

    fprintf(stdout, "All resources cleaned up successfully\n");

    return EXIT_SUCCESS;
}