cmake_minimum_required(VERSION 3.12)

# Set the project name
project(simpleGetProperties LANGUAGES C CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(EXECUTABLE_NAME simpleGetProperties)

# Include directories and libraries
include_directories(../../../include)

# Source file
# Add target for simpleGetProperties
add_executable(${EXECUTABLE_NAME} simpleGetProperties.cpp)
set_target_properties(${EXECUTABLE_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/tests/unittests/2_${EXECUTABLE_NAME})
target_link_libraries(${EXECUTABLE_NAME} PRIVATE kfe)