#include "kfe.h"
#include "kfe_typedefs.h"

#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <unistd.h>
#include <sys/wait.h>
#include <sys/types.h>
#include <string.h>
#include <errno.h>

// Parent process function
int parent_process()
{
    printf("[Parent Process %d] Starting KFE operations\n", getpid());

    kfeResult result;

    // Initialize the KFE API
    result = kfeInitialize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeInitialize failed: %d\n", result);
        return EXIT_FAILURE;
    }

    int deviceCount = 0;
    result = kfeDeviceGetCount(&deviceCount);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeGetDeviceCount failed: %d\n", result);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    if (deviceCount <= 0)
    {
        fprintf(stderr, "[Parent Process] Invalid device count: %d\n", deviceCount);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Parent Process] Number of devices: %d\n", deviceCount);

    kfeDevice device[deviceCount];
    for (int i = 0; i < deviceCount; ++i)
    {
        result = kfeDeviceOpen(&device[i], i);
        if (result != KFE_SUCCESS)
        {
            fprintf(stderr, "[Parent Process] kfeDeviceOpen failed for device %d: %d\n", i, result);
            for (int j = 0; j < i; ++j)
            {
                kfeDeviceClose(device[j]);
            }

            kfeFinalize();
            return EXIT_FAILURE;
        }
    }

    kfeContext context;
    result = kfeContextCreate(&context, 0, device[0]);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeContextCreate failed: %d\n", result);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }
    fprintf(stdout, "[Parent Process] Context created successfully for device 0\n");

    kfeUserModeQueue userModeQueue;
    result = kfeUserModeQueueCreate(&userModeQueue, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeUserModeQueueCreate failed: %d\n", result);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }
    fprintf(stdout, "[Parent Process] User mode queue created successfully\n");

    kfeFence fenceA;
    result = kfeFenceCreate(&fenceA, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeFenceCreate failed: %d\n", result);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Parent Process] Fence A: %p created successfully\n", fenceA);

    kfeFence fenceB;
    result = kfeFenceCreate(&fenceB, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeFenceCreate failed: %d\n", result);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Parent Process] Fence B: %p created successfully\n", fenceB);

    kfeFence fenceC;
    result = kfeFenceCreate(&fenceC, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeFenceCreate failed: %d\n", result);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Parent Process] Fence C: %p created successfully.\n", fenceC);

    kfeCommandPacket cmdPackets[2] = {};
    kfeFence waitFencesA[1] = { fenceA };
    kfeFence waitFencesB[1] = { fenceB };

    kfeCommandPacketDebugAdd* cmdPacketDebugAdd = reinterpret_cast<kfeCommandPacketDebugAdd*>(&cmdPackets[0]);
    cmdPacketDebugAdd->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_ADD;
    cmdPacketDebugAdd->queueId = 0;
    cmdPacketDebugAdd->operand1 = 16;
    cmdPacketDebugAdd->operand2 = 16;
    cmdPacketDebugAdd->numWaitFences = 1;
    cmdPacketDebugAdd->pWaitFences = waitFencesA;
    cmdPacketDebugAdd->signalFence = fenceB;

    kfeCommandPacketDebugSub* cmdPacketDebugSub = reinterpret_cast<kfeCommandPacketDebugSub*>(&cmdPackets[1]);
    cmdPacketDebugSub->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_SUB;
    cmdPacketDebugSub->queueId = 0;
    cmdPacketDebugSub->operand1 = 32;
    cmdPacketDebugSub->operand2 = 16;
    cmdPacketDebugSub->numWaitFences = 1;
    cmdPacketDebugSub->pWaitFences = waitFencesB;
    cmdPacketDebugSub->signalFence = fenceC;

    result = kfeUserModeQueueSubmit(userModeQueue, &cmdPackets[0], 1);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeUserModeQueueSubmit failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Parent Process] Command packet 0 submitted successfully\n");

    result = kfeFenceSignal(userModeQueue, fenceA);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeFenceSignal failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    usleep(100000); // Simulate some processing time
    fprintf(stdout, "[Parent Process] Fence A signaled successfully\n");

    result = kfeUserModeQueueSubmit(userModeQueue, &cmdPackets[1], 1);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeUserModeQueueSubmit failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Parent Process] Command packet 1 submitted successfully\n");

    result = kfeFenceWait(userModeQueue, fenceC);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeFenceWait fence C failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        fprintf(stderr, "[Parent Process] Failed to wait for fence C\n");
        return EXIT_FAILURE;
    }
    fprintf(stdout, "[Parent Process] Fence C wait completed successfully\n");

    // Cleanup
    kfeFenceDestroy(fenceC);
    kfeFenceDestroy(fenceB);
    kfeFenceDestroy(fenceA);
    kfeUserModeQueueDestroy(userModeQueue);
    kfeContextDestroy(context);
    for (int i = 0; i < deviceCount; ++i)
    {
        kfeDeviceClose(device[i]);
    }

    // Finalize the KFE API
    result = kfeFinalize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Parent Process] kfeFinalize failed: %d\n", result);
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Parent Process] All resources cleaned up successfully\n");
    return EXIT_SUCCESS;
}

// Child process function
int child_process()
{
    printf("[Child Process %d] Starting KFE operations\n", getpid());

    kfeResult result;

    // Initialize the KFE API
    result = kfeInitialize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeInitialize failed: %d\n", result);
        return EXIT_FAILURE;
    }

    int deviceCount = 0;
    result = kfeDeviceGetCount(&deviceCount);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeGetDeviceCount failed: %d\n", result);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    if (deviceCount <= 0)
    {
        fprintf(stderr, "[Child Process] Invalid device count: %d\n", deviceCount);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Child Process] Number of devices: %d\n", deviceCount);

    kfeDevice device[deviceCount];
    for (int i = 0; i < deviceCount; ++i)
    {
        result = kfeDeviceOpen(&device[i], i);
        if (result != KFE_SUCCESS)
        {
            fprintf(stderr, "[Child Process] kfeDeviceOpen failed for device %d: %d\n", i, result);
            for (int j = 0; j < i; ++j)
            {
                kfeDeviceClose(device[j]);
            }

            kfeFinalize();
            return EXIT_FAILURE;
        }
    }

    kfeContext context;
    result = kfeContextCreate(&context, 0, device[0]);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeContextCreate failed: %d\n", result);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }
    fprintf(stdout, "[Child Process] Context created successfully for device 0\n");

    kfeUserModeQueue userModeQueue;
    result = kfeUserModeQueueCreate(&userModeQueue, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeUserModeQueueCreate failed: %d\n", result);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }
    fprintf(stdout, "[Child Process] User mode queue created successfully\n");

    kfeFence fenceA;
    result = kfeFenceCreate(&fenceA, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeFenceCreate failed: %d\n", result);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Child Process] Fence A: %p created successfully\n", fenceA);

    kfeFence fenceB;
    result = kfeFenceCreate(&fenceB, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeFenceCreate failed: %d\n", result);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Child Process] Fence B: %p created successfully\n", fenceB);

    kfeFence fenceC;
    result = kfeFenceCreate(&fenceC, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeFenceCreate failed: %d\n", result);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Child Process] Fence C: %p created successfully.\n", fenceC);

    kfeCommandPacket cmdPackets[2] = {};
    kfeFence waitFencesA[1] = { fenceA };
    kfeFence waitFencesB[1] = { fenceB };

    kfeCommandPacketDebugAdd* cmdPacketDebugAdd = reinterpret_cast<kfeCommandPacketDebugAdd*>(&cmdPackets[0]);
    cmdPacketDebugAdd->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_ADD;
    cmdPacketDebugAdd->queueId = 0;
    cmdPacketDebugAdd->operand1 = 25;
    cmdPacketDebugAdd->operand2 = 17;
    cmdPacketDebugAdd->numWaitFences = 1;
    cmdPacketDebugAdd->pWaitFences = waitFencesA;
    cmdPacketDebugAdd->signalFence = fenceB;

    kfeCommandPacketDebugSub* cmdPacketDebugSub = reinterpret_cast<kfeCommandPacketDebugSub*>(&cmdPackets[1]);
    cmdPacketDebugSub->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_SUB;
    cmdPacketDebugSub->queueId = 0;
    cmdPacketDebugSub->operand1 = 42;
    cmdPacketDebugSub->operand2 = 25;
    cmdPacketDebugSub->numWaitFences = 1;
    cmdPacketDebugSub->pWaitFences = waitFencesB;
    cmdPacketDebugSub->signalFence = fenceC;

    result = kfeUserModeQueueSubmit(userModeQueue, &cmdPackets[0], 1);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeUserModeQueueSubmit failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Child Process] Command packet 0 submitted successfully\n");

    result = kfeFenceSignal(userModeQueue, fenceA);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeFenceSignal failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    usleep(150000); // Simulate some processing time (slightly different from parent)
    fprintf(stdout, "[Child Process] Fence A signaled successfully\n");

    result = kfeUserModeQueueSubmit(userModeQueue, &cmdPackets[1], 1);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeUserModeQueueSubmit failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Child Process] Command packet 1 submitted successfully\n");

    result = kfeFenceWait(userModeQueue, fenceC);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeFenceWait fence C failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        fprintf(stderr, "[Child Process] Failed to wait for fence C\n");
        return EXIT_FAILURE;
    }
    fprintf(stdout, "[Child Process] Fence C wait completed successfully\n");

    // Cleanup
    kfeFenceDestroy(fenceC);
    kfeFenceDestroy(fenceB);
    kfeFenceDestroy(fenceA);
    kfeUserModeQueueDestroy(userModeQueue);
    kfeContextDestroy(context);
    for (int i = 0; i < deviceCount; ++i)
    {
        kfeDeviceClose(device[i]);
    }

    // Finalize the KFE API
    result = kfeFinalize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "[Child Process] kfeFinalize failed: %d\n", result);
        return EXIT_FAILURE;
    }

    fprintf(stdout, "[Child Process] All resources cleaned up successfully\n");
    return EXIT_SUCCESS;
}

int main()
{
    printf("[Main Process %d] Starting multi-process KFE test\n", getpid());

    // Create child process immediately
    pid_t child_pid = fork();

    if (child_pid == -1)
    {
        fprintf(stderr, "[Main Process] Failed to fork child process: %s\n", strerror(errno));
        return EXIT_FAILURE;
    }
    else if (child_pid == 0)
    {
        // Child process: Run the original test procedure
        return child_process();
    }
    else
    {
        // Parent process: Run the original test procedure
        fprintf(stdout, "[Main Process] Created child process with PID: %d\n", child_pid);

        int parent_result = parent_process();

        // Wait for child process to complete
        int child_status;
        waitpid(child_pid, &child_status, 0);

        if (WIFEXITED(child_status))
        {
            int exit_code = WEXITSTATUS(child_status);
            fprintf(stdout, "[Main Process] Child process exited with code: %d\n", exit_code);
        }
        else
        {
            fprintf(stderr, "[Main Process] Child process terminated abnormally\n");
        }

        // Return success only if both parent and child succeeded
        if (parent_result == EXIT_SUCCESS && WIFEXITED(child_status) && WEXITSTATUS(child_status) == EXIT_SUCCESS)
        {
            fprintf(stdout, "[Main Process] Both parent and child processes completed successfully\n");
            return EXIT_SUCCESS;
        }
        else
        {
            fprintf(stderr, "[Main Process] One or both processes failed\n");
            return EXIT_FAILURE;
        }
    }
}