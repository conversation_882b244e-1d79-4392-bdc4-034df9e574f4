#include "kfe.h"
#include "kfe_typedefs.h"

#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <unistd.h>

int main()
{
    kfeResult result;

    // Initialize the KFE API
    result = kfeInitialize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeInitialize failed: %d\n", result);
        return EXIT_FAILURE;
    }

    int deviceCount = 0;
    result = kfeDeviceGetCount(&deviceCount);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeGetDeviceCount failed: %d\n", result);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    if (deviceCount <= 0)
    {
        fprintf(stderr, "Invalid device count: %d\n", deviceCount);
        kfeFinalize();
        return EXIT_FAILURE;
    }

    fprintf(stdout, "Number of devices: %d\n", deviceCount);

    kfeDevice device[deviceCount];
    for (int i = 0; i < deviceCount; ++i)
    {
        result = kfeDeviceOpen(&device[i], i);
        if (result != KFE_SUCCESS)
        {
            fprintf(stderr, "kfeDeviceOpen failed for device %d: %d\n", i, result);
            for (int j = 0; j < i; ++j)
            {
                kfeDeviceClose(device[j]);
            }

            kfeFinalize();

            return EXIT_FAILURE;
        }
    }

    kfeContext context;
    result = kfeContextCreate(&context, 0, device[0]);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeContextCreate failed: %d\n", result);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }
    fprintf(stdout, "Context created successfully for device 0\n");

    kfeUserModeQueue userModeQueue;
    result = kfeUserModeQueueCreate(&userModeQueue, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeUserModeQueueCreate failed: %d\n", result);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }
    fprintf(stdout, "User mode queue created successfully\n");

    kfeFence fenceA;
    result = kfeFenceCreate(&fenceA, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeFenceCreate failed: %d\n", result);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }

    fprintf(stdout, "Fence A: %p created successfully\n", fenceA);

    kfeFence fenceB;
    result = kfeFenceCreate(&fenceB, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeFenceCreate failed: %d\n", result);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }

    fprintf(stdout, "Fence B: %p created successfully\n", fenceB);

    kfeFence fenceC;
    result = kfeFenceCreate(&fenceC, 0, context);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeFenceCreate failed: %d\n", result);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }

    fprintf(stdout, "Fence C: %p created successfully.\n", fenceC);

    kfeCommandPacket cmdPackets[2] = {};
    kfeFence waitFencesA[1] = { fenceA };
    kfeFence waitFencesB[1] = { fenceB };

    kfeCommandPacketDebugAdd* cmdPacketDebugAdd = reinterpret_cast<kfeCommandPacketDebugAdd*>(&cmdPackets[0]);
    cmdPacketDebugAdd->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_ADD;
    cmdPacketDebugAdd->queueId = 0;
    cmdPacketDebugAdd->operand1 = 16;
    cmdPacketDebugAdd->operand2 = 16;
    cmdPacketDebugAdd->numWaitFences = 1;
    cmdPacketDebugAdd->pWaitFences = waitFencesA;
    cmdPacketDebugAdd->signalFence = fenceB;

    kfeCommandPacketDebugSub* cmdPacketDebugSub = reinterpret_cast<kfeCommandPacketDebugSub*>(&cmdPackets[1]);
    cmdPacketDebugSub->commandPacketType = KFE_COMMAND_PACKET_TYPE_DEBUG_SUB;
    cmdPacketDebugSub->queueId = 0;
    cmdPacketDebugSub->operand1 = 32;
    cmdPacketDebugSub->operand2 = 16;
    cmdPacketDebugSub->numWaitFences = 1;
    cmdPacketDebugSub->pWaitFences = waitFencesB;
    cmdPacketDebugSub->signalFence = fenceC;

    result = kfeUserModeQueueSubmit(userModeQueue, &cmdPackets[0], 1);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeUserModeQueueSubmit failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }

    fprintf(stdout, "Command packet 0 submitted successfully\n");

    result = kfeFenceSignal(userModeQueue, fenceA);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeFenceSignal failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }

    usleep(100000); // Simulate some processing time
    fprintf(stdout, "Fence A signaled successfully\n");

    result = kfeUserModeQueueSubmit(userModeQueue, &cmdPackets[1], 1);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeUserModeQueueSubmit failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        return EXIT_FAILURE;
    }

    fprintf(stdout, "Command packet 1 submitted successfully\n");

    result = kfeFenceWait(userModeQueue, fenceC);
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeFenceWait fence C failed: %d\n", result);
        kfeFenceDestroy(fenceC);
        kfeFenceDestroy(fenceB);
        kfeFenceDestroy(fenceA);
        kfeUserModeQueueDestroy(userModeQueue);
        kfeContextDestroy(context);
        for (int i = 0; i < deviceCount; ++i)
        {
            kfeDeviceClose(device[i]);
        }

        kfeFinalize();

        fprintf(stderr, "Failed to wait for fence C\n");

        return EXIT_FAILURE;
    }
    fprintf(stdout, "Fence C wait completed successfully\n");

    // Cleanup
    kfeFenceDestroy(fenceC);
    kfeFenceDestroy(fenceB);
    kfeFenceDestroy(fenceA);
    kfeUserModeQueueDestroy(userModeQueue);
    kfeContextDestroy(context);
    for (int i = 0; i < deviceCount; ++i)
    {
        kfeDeviceClose(device[i]);
    }

    // Finalize the KFE API
    result = kfeFinalize();
    if (result != KFE_SUCCESS)
    {
        fprintf(stderr, "kfeFinalize failed: %d\n", result);

        return EXIT_FAILURE;
    }

    fprintf(stdout, "All resources cleaned up successfully\n");

    return EXIT_SUCCESS;
}