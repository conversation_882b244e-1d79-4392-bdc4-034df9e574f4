# 引言

以数据为燃料、以算法为导航、以算力为引擎的LLM快车在不断的推陈出新，AI新时代改变着人们的工作生活逻辑，对算力的需求也在与日俱增，

提到算力不得不提到皮衣刀客，AI时代的发展被黄教主的产能约束，scaling Law让英伟达的股价一路走高，股价没多少波动的千年老二AMD在AI时代是不是被抛弃了？从Mi50→Mi300，AMD一直在努力尝试切分AI算力的蛋糕，但是硬件架构与软件的脱节，让这艘破船跑的越来越慢。每一代AMD的硬件架构在对比NV的算力与带宽上都有20%左右的优势，完整的模型跑下来却只有竞争对手的80%。怎么把卡用起来变成了在AMD内部炙手可热的话题\~\~最近几年，我们国产卡在“AMD based”的路上将算力及带宽也推了上来，同样面临这怎么被用起来的问题。

或许是时候整合出来一套性能调优的方法论\~\~

性能提升不仅仅是硬件中增加FLOPS的概念，简单堆砌的计算单元带不来足够多的计算能力提升。性能提升是个系统性的工程，软件栈的调整、算法的改进都应该是性能调优的一部分。DeepGemm、flash attention等调优案例都是深度结合硬件框架及软件工具做的伟大尝试。

PerfPotion的目标就是找到调配性能提升的良药。以我们现有的能力及经验，先从几个大方面入手。(抛砖引玉）

1. 硬件架构： 硬件架构的调优方向暂时能想到的有
   1. Kernel 执行调优，涉及编译器与硬件指令配合，硬件设计各种约束的满足
      1. 案例flash attention，组合多种指令，减少reduction的个数，带来性能提升
      2. multi stage kernel编程范式，边搬边算将latency隐藏
      3. cute swizzle，减少bankconflict in Share mem……
   2. Kernel 调度调优，涉及kernel所需硬件资源分配负载均衡
      1. 指定warp编程范式，将load/store资源重新分配
      2. 降低warp并行度，改变数据locality
   3. 多Dispatch调度调优，多dispatch间资源的抢占与均衡
      1. 投机的调度reduce kernel与大型GEMM 计算kernel并行
      2. dispatch dependency 由GPU解决，减少host→device通信开销
2. 软件框架：不熟悉待补充
   1. 对device内存管理，缺页问题带来的perf drop不容忽视
   2. 对device fence管理
   3. 对多卡通信管理，node内通信/scale up/scale out……
3. Profiling工具
   1. 构建类ncu类似的工具 （这是性能调优的入口？？)

## E2E性能调优

端到端的性能提升是我们制作药水的目标，需要我们对整个AIsystem 有所了解\~\~本着学习的目标调配秘方，可以先从检查的知识储备开始 (待添加）：

1. [https://chenzomi12.github.io/01Introduction/README.html](https://chenzomi12.github.io/01Introduction/README.html "https://chenzomi12.github.io/01Introduction/README.html")
