name: CMake Build and Run Tests

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]
  workflow_call:
  workflow_dispatch:

jobs:
  build-and-test:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest]
        build_type: [Debug, Release]
        cmake_generator: [Ninja]
        c_compiler: [gcc]
        cpp_compiler: [g++]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y cmake ninja-build
          sudo apt-get install -y libprotobuf-dev protobuf-compiler

      - name: Set reusable strings
        # Turn repeated input strings (such as the build output directory) into step outputs. These step outputs can be used throughout the workflow file.
        id: strings
        shell: bash
        run: |
          echo "CMAKE_BUILD_TYPE=${{ matrix.build_type }}" >> "$GITHUB_OUTPUT"
          echo "CMAKE_GENERATOR=${{ matrix.cmake_generator }}" >> "$GITHUB_OUTPUT"
          echo "C_COMPILER=${{ matrix.c_compiler }}" >> "$GITHUB_OUTPUT"
          echo "CPP_COMPILER=${{ matrix.cpp_compiler }}" >> "$GITHUB_OUTPUT"
          echo "BUILD_OUTPUT_DIR=${{ github.workspace }}/build" >> "$GITHUB_OUTPUT"

      - name: CMake version and configuration
        run: |
          cmake --version
          echo "Configuring CMake with the following parameters:"
          echo "BUILD_OUTPUT_DIR: ${{ steps.strings.outputs.BUILD_OUTPUT_DIR }}"
          echo "CMAKE_BUILD_TYPE: ${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}"
          echo "CMAKE_GENERATOR: ${{ steps.strings.outputs.CMAKE_GENERATOR }}"
          echo "C_COMPILER: ${{ steps.strings.outputs.C_COMPILER }}"
          echo "CPP_COMPILER: ${{ steps.strings.outputs.CPP_COMPILER }}"

      - name: Setup SSH key for private repo
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.TARGET_MACHINE_RSA_PRIVATE_KEY }}

      - name: Build PerfPotion
        run: |
          echo "Building PerfPotion with the following parameters:"
          cmake -B "${{ steps.strings.outputs.BUILD_OUTPUT_DIR }}" \
            -G "${{ steps.strings.outputs.CMAKE_GENERATOR }}" \
            -DCMAKE_BUILD_TYPE="${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" \
            -DCMAKE_C_COMPILER="${{ steps.strings.outputs.C_COMPILER }}" \
            -DCMAKE_CXX_COMPILER="${{ steps.strings.outputs.CPP_COMPILER }}" \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
            -S ${{ github.workspace }}
          cmake --build "${{ steps.strings.outputs.BUILD_OUTPUT_DIR }}" --config "${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" --parallel 4
          echo "LD_LIBRARY_PATH=${{ github.workspace }}/build:$LD_LIBRARY_PATH" >> $GITHUB_ENV

      - name: Run PerfPotion tests
        working-directory: ${{ github.workspace }}
        run: |
          if [ -f ./run_tests.sh ]; then
            echo "Running PerfPotion tests..."
            chmod +x ./run_tests.sh
            echo "Executing tests script..."
            if ! ./run_tests.sh; then
              echo "Tests failed! Printing server log for debugging..."
              echo "=========== /tmp/kfe_server.log content =========="
              if [ -f /tmp/kfe_server.log ]; then
                cat /tmp/kfe_server.log
              else
                echo "No /tmp/kfe_server.log file found"
              fi
              echo "========== End of server log =========="
              exit 1
            fi
            echo "PerfPotion tests completed."
          else
            echo "ERROR: ./run_tests.sh not found in $(pwd)"
            exit 1
          fi

      - name: Download kernel-firmware-emulator
        working-directory: /tmp
        run: |
          echo "Downloading kernel-firmware-emulator from repository..."
          <NAME_EMAIL>:GPGPU-PerfX/kernel-firmware-emulator.git /tmp/kernel-firmware-emulator

      - name: Build kernel-firmware-emulator
        working-directory: /tmp/kernel-firmware-emulator
        run: |
          echo "Building kernel-firmware-emulator with the following parameters:"
          cmake -B build -G "${{ steps.strings.outputs.CMAKE_GENERATOR }}" \
            -DCMAKE_BUILD_TYPE="${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" \
            -DCMAKE_C_COMPILER="${{ steps.strings.outputs.C_COMPILER }}" \
            -DCMAKE_CXX_COMPILER="${{ steps.strings.outputs.CPP_COMPILER }}" \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
            -S .
          cmake --build build --config "${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" --parallel 4
          echo "Kernel-firmware-emulator built successfully."
          echo "LD_LIBRARY_PATH=/tmp/kernel-firmware-emulator/build/lib:$LD_LIBRARY_PATH" >> $GITHUB_ENV

      - name: Run kernel-firmware-emulator tests
        working-directory: /tmp/kernel-firmware-emulator
        run: |
          if [ -f ./run_tests.sh ]; then
            echo "Running kernel-firmware-emulator tests..."
            chmod +x ./run_tests.sh
            echo "Executing tests script..."
            if ! ./run_tests.sh; then
              echo "Tests failed! Printing server log for debugging..."
              echo "=========== /tmp/kfe_server.log content =========="
              if [ -f /tmp/kfe_server.log ]; then
                cat /tmp/kfe_server.log
              else
                echo "No /tmp/kfe_server.log file found"
              fi
              echo "========== End of server log =========="
              exit 1
            fi
            echo "kernel-firmware-emulator tests completed."
          else
            echo "ERROR: ./run_tests.sh not found in $(pwd)"
            exit 1
          fi

      - name: Download cuda-driver
        working-directory: /tmp
        run: |
          echo "Downloading cuda-driver from repository..."
          <NAME_EMAIL>:GPGPU-PerfX/cuda-driver.git /tmp/cuda-driver

      - name: Build cuda-driver
        working-directory: /tmp/cuda-driver
        run: |
          echo "Building cuda-driver with the following parameters:"
          cmake -B build \
            -G "${{ steps.strings.outputs.CMAKE_GENERATOR }}" \
            -DCMAKE_BUILD_TYPE="${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" \
            -DCMAKE_C_COMPILER="${{ steps.strings.outputs.C_COMPILER }}" \
            -DCMAKE_CXX_COMPILER="${{ steps.strings.outputs.CPP_COMPILER }}" \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
            -DENABLE_DEBUG_BREAK=OFF \
            -DENABLE_THUNK_LAYER=kfe \
            -S .
          cmake --build build --config "${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" --parallel 4
          echo "cuda-driver built successfully."
          echo "LD_LIBRARY_PATH=/tmp/cuda-driver/build/lib:$LD_LIBRARY_PATH" >> $GITHUB_ENV

      - name: Run cuda-driver tests
        working-directory: /tmp/cuda-driver
        run: |
          if [ -f ./run_tests.sh ]; then
            echo "Running cuda-driver tests..."
            chmod +x ./run_tests.sh
            echo "Executing tests script..."
            if ! ./run_tests.sh; then
              echo "CUDA driver tests failed! Printing server log for debugging..."
              echo "========== /tmp/kfe_server.log content =========="
              if [ -f /tmp/kfe_server.log ]; then
                cat /tmp/kfe_server.log
              else
                echo "No /tmp/kfe_server.log file found"
              fi
              echo "========== End of server log =========="
              exit 1
            fi
            echo "cuda-driver tests completed."
          else
            echo "ERROR: ./run_tests.sh not found in $(pwd)"
            exit 1
          fi

      - name: Download cuda-runtime
        working-directory: /tmp
        run: |
          echo "Downloading cuda-runtime from repository..."
          <NAME_EMAIL>:GPGPU-PerfX/cuda-runtime.git /tmp/cuda-runtime

      - name: Build cuda-runtime
        working-directory: /tmp/cuda-runtime
        run: |
          echo "Building cuda-runtime with the following parameters:"
          cmake -B build -G "${{ steps.strings.outputs.CMAKE_GENERATOR }}" \
            -DCMAKE_BUILD_TYPE="${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" \
            -DCMAKE_C_COMPILER="${{ steps.strings.outputs.C_COMPILER }}" \
            -DCMAKE_CXX_COMPILER="${{ steps.strings.outputs.CPP_COMPILER }}" \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
            -S .
          cmake --build build --config "${{ steps.strings.outputs.CMAKE_BUILD_TYPE }}" --parallel 4
          echo "cuda-runtime built successfully."
          echo "LD_LIBRARY_PATH=/tmp/cuda-runtime/build/lib:$LD_LIBRARY_PATH" >> $GITHUB_ENV

      - name: Run cuda-runtime tests
        working-directory: /tmp/cuda-runtime
        run: |
          if [ -f ./run_tests.sh ]; then
            echo "Running cuda-runtime tests..."
            chmod +x ./run_tests.sh
            echo "Executing tests script..."
            if ! ./run_tests.sh; then
              echo "CUDA runtime tests failed! Printing server log for debugging..."
              echo "========== /tmp/kfe_server.log content =========="
              if [ -f /tmp/kfe_server.log ]; then
                cat /tmp/kfe_server.log
              else
                echo "No /tmp/kfe_server.log file found"
              fi
              echo "========== End of server log =========="
              exit 1
            fi
            echo "cuda-runtime tests completed."
          else
            echo "ERROR: ./run_tests.sh not found in $(pwd)"
            exit 1
          fi