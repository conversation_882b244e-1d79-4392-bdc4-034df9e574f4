cmake_minimum_required(VERSION 3.12)

# Set the project name and program languages.
project(perfPotion LANGUAGES C CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
# Enable -fPIC for all targets.
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# Set the static and dynamic libraries path.
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR})
# Set the output directory for executables
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Set the cache variable for the build type.
string(TOUPPER "${CMAKE_BUILD_TYPE}" CAPITAL_CMAKE_BUILD_TYPE)
message(STATUS "CMAKE_BUILD_TYPE: ${CMAKE_BUILD_TYPE}")

if(CAPITAL_CMAKE_BUILD_TYPE STREQUAL "DEBUGWITHASAN")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -O0 -fsanitize=address -g -fno-omit-frame-pointer")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O0 -fsanitize=address -g -fno-omit-frame-pointer")
endif()

# Add the path of the public including files.
# Include arch_model headers
include_directories(${CMAKE_SOURCE_DIR}/arch_model)

# Add the sub-directories
add_subdirectory(arch_model)  # Build the architecture model library

# Add the tests directory
add_subdirectory(tests)

# Install configuration
# Set the default install prefix if not specified
if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
    set(CMAKE_INSTALL_PREFIX "${CMAKE_SOURCE_DIR}/install" CACHE PATH "Installation directory" FORCE)
endif()

# Print the installation prefix
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")