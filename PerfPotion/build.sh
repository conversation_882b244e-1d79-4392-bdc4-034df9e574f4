#!/bin/bash

set -e

if [ ! -z "$2" ] && [ "$2" == "install" ]; then
    EXTRA_CMAKE_ARGS="${@:3}"
else
    EXTRA_CMAKE_ARGS="${@:2}"
fi

if [ ! -z "$1" ] && [ "$1" == 'rel' ]; then
    cmake -B build -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=${PERFX_SDK_PATH} $EXTRA_CMAKE_ARGS
    cmake --build build --parallel
elif [ ! -z "$1" ] && [ "$1" == 'dbg' ]; then
    cmake -B build -DCMAKE_BUILD_TYPE=Debug -DCMAKE_INSTALL_PREFIX=${PERFX_SDK_PATH} $EXTRA_CMAKE_ARGS
    cmake --build build --parallel
elif [ ! -z "$1" ] && [ "$1" == 'dbg_asan' ]; then
    cmake -B build -DCMAKE_BUILD_TYPE=DebugWithASan -DCMAKE_INSTALL_PREFIX=${PERFX_SDK_PATH} $EXTRA_CMAKE_ARGS
    cmake --build build --parallel
elif [ ! -z "$1" ] && [ "$1" == 'doc' ]; then
    cmake -B build -DCMAKE_INSTALL_PREFIX=${PERFX_SDK_PATH}
    cmake --build build -- docs
elif [ ! -z "$1" ] && [ "$1" == 'clean' ]; then
    rm -rf build
else
    echo 'build.sh {rel|dbg|dbg_asan|doc|clean}'
    exit 1
fi
if [ ! -z "$2" ] && [ "$2" == 'install' ]; then
    cmake --install build
fi