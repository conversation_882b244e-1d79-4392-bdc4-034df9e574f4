#include <thread>
#include <atomic>
#include "perfpotion_config.h"
#include "chip.h"

namespace perfpotion_model {

using TimePoint = std::chrono::time_point<std::chrono::high_resolution_clock>;

class PotionModel {
public:
    PotionModel();

    int GetchipNum() const {
        return m_config.chip_num;
    }

    void SetHostMemoryIf(int chip_id, HostMemoryIf host_mem_if) {
        
        m_chips[chip_id]->SetHostMemoryIf(host_mem_if);
    }

    void ReadMemory(int chip_id, uint64_t addr, size_t size, uint8_t* dst) {
        m_chips[chip_id]->ReadMemory(addr, size, dst);
    }

    void WriteMemory(int chip_id, uint64_t addr, size_t size, const uint8_t* src) {
        m_chips[chip_id]->WriteMemory(addr, size, src);
    }

    // void ReadRegister(int chip_id, uint64_t addr, uint64_t* value) {
    //     m_chips[chip_id]->ReadRegister(addr, value);
    // }

    // void WriteRegister(int chip_id, uint64_t addr, uint64_t value) {
    //     m_chips[chip_id]->WriteRegister(addr, value);
    // }

    void Run();
    void Stop();

private:
    void RunSp();
    void RunMp();
    void UpdateStepCnt();

    PerfPotionConfig m_config;  // Must be first so it's initialized before m_memory_lock
    MemoryLock m_memory_lock;
    std::vector<std::unique_ptr<Chip>> m_chips;

    std::unique_ptr<std::thread> m_model_thread;
    std::atomic<bool> m_running;

    uint64_t m_step_cnt = 0;
    uint64_t m_next_heart_beat_step_cnt = 0;
    TimePoint m_start_tp;
};


}
