#pragma once

#include <iostream>
#include <mutex>
#include <atomic>

#include <unistd.h>

#include <string>
#include <chrono>
#include <cstdio>
#include <ctime>

/**
 * @brief SpinLock class providing lightweight thread synchronization mechanism
 * 
 * Implemented using std::atomic_flag, supports C++20 wait/notify mechanism to reduce CPU usage
 * Provides RAII-style automatic lock/unlock through internal ScopeLock class
 */
class SpinLock {
public:
    /**
     * @brief RAII-style scoped lock that automatically manages lock lifetime
     * 
     * Automatically locks on construction and unlocks on destruction, ensuring exception safety
     * Supports move semantics, disables copy semantics to prevent accidental lock duplication
     */
    class ScopeLock {
    public:
        /**
         * @brief Constructor that automatically acquires the lock
         * @param lock Pointer to SpinLock, if nullptr no locking operation is performed
         */
        ScopeLock(SpinLock* lock) : m_lock(lock) {
            if (m_lock) {
                m_lock->Lock();  // Lock immediately on construction
            }
        }

        /**
         * @brief Deleted copy constructor
         * 
         * Prevents accidental lock duplication, avoiding multiple unlocks of the same lock
         */
        ScopeLock(const ScopeLock&) = delete;

        /**
         * @brief Move constructor that transfers lock ownership
         * @param rhs ScopeLock object being moved
         * 
         * After move, the original object's m_lock is set to nullptr to avoid double unlock on destruction
         */
        ScopeLock(ScopeLock&& rhs) {
            m_lock = rhs.m_lock;        // Transfer lock ownership
            rhs.m_lock = nullptr;       // Clear original object to prevent double unlock
        }

        /**
         * @brief Destructor that automatically releases the lock
         * 
         * Automatically releases the lock whether exiting scope normally or due to exception
         */
        ~ScopeLock() {
            if (m_lock) {
                m_lock->Unlock();  // Automatically unlock on destruction
            }
        }

    private:
        SpinLock* m_lock = nullptr;  ///< Pointer to the associated SpinLock object
    };

    /**
     * @brief Constructor
     * @param enabled Whether to enable lock functionality, when false GetScopeLock() returns empty lock
     * 
     * The enabled parameter allows dynamic control of whether to actually lock at runtime
     * This is very useful for debugging or performance testing
     */
    SpinLock(bool enabled) : m_enabled(enabled) {
    }

    /**
     * @brief Deleted copy constructor
     * 
     * SpinLock objects should not be copied as this would cause multiple objects to operate on the same atomic_flag
     */
    SpinLock(const SpinLock&) = delete;

    /**
     * @brief Default move constructor
     * 
     * Allows SpinLock objects to be moved, useful when storing SpinLock in containers
     */
    SpinLock(SpinLock&&) = default;

    /**
     * @brief Get a scoped lock object
     * @return ScopeLock object, returns empty lock if lock is disabled
     * 
     * If m_enabled is true, returns valid ScopeLock that immediately locks
     * If m_enabled is false, returns empty ScopeLock that performs no lock operations
     */
    ScopeLock GetScopeLock() {
        return m_enabled ? ScopeLock{this} : ScopeLock{nullptr};
    }

private:
    /**
     * @brief Internal implementation for acquiring the lock
     * 
     * Uses test_and_set atomic operation to attempt lock acquisition
     * If lock is occupied, uses wait instead of busy-waiting to reduce CPU usage
     * 
     * Memory ordering explanation:
     * - memory_order_acquire: ensures memory operations after lock acquisition are not reordered before lock acquisition
     */
    void Lock() {
        // Spin wait until successfully acquiring the lock
        while (m_flag.test_and_set(std::memory_order_acquire)) {
            // C++20 feature: wait for flag to become false, avoiding busy-waiting
            // memory_order_relaxed: only need atomicity of read operation, no need to synchronize other memory operations
            m_flag.wait(true, std::memory_order_relaxed);
        }
    }

    /**
     * @brief Internal implementation for releasing the lock
     * 
     * Clears the atomic_flag and notifies waiting threads
     * 
     * Memory ordering explanation:
     * - memory_order_release: ensures memory operations before lock release are not reordered after lock release
     */
    void Unlock() {
        // Release lock: clear the flag
        m_flag.clear(std::memory_order_release);
        
        // C++20 feature: notify one waiting thread
        // Used together with wait() in Lock() to improve performance
        m_flag.notify_one();
    }

    std::atomic_flag m_flag;     ///< Atomic flag for implementing the core spinlock mechanism
    bool m_enabled = false;      ///< Flag controlling whether the lock is enabled
};

/**
 * @brief Mutex wrapper class providing controllable thread synchronization mechanism
 * 
 * Based on std::mutex implementation, uses std::unique_lock as RAII-style lock manager
 * Supports dynamic control of lock enable/disable state through enabled parameter
 * Compared to SpinLock, suitable for scenarios where critical sections have longer execution times
 */
class Lock {
public:
    /**
     * @brief Type alias using std::unique_lock as scoped lock
     * 
     * std::unique_lock provides more functionality compared to std::lock_guard:
     * - Supports deferred locking, manual unlocking, lock transfer, etc.
     * - Can be used with condition variables
     * - Supports timed locking operations
     */
    using ScopeLock = std::unique_lock<std::mutex>;

    /**
     * @brief Constructor
     * @param enabled Whether to enable lock functionality
     * 
     * When enabled is true: GetScopeLock() returns valid lock that actually locks
     * When enabled is false: GetScopeLock() returns empty lock that performs no locking
     * 
     * This design is useful in the following scenarios:
     * - Disable locks in debug mode to troubleshoot deadlock issues
     * - Compare performance with/without locks during performance testing
     * - Dynamically decide whether synchronization is needed based on runtime conditions
     */
    Lock(bool enabled) : m_enabled(enabled) {
    }

    /**
     * @brief Deleted copy constructor
     * 
     * Lock objects should not be copied because:
     * - std::mutex itself is not copyable
     * - Copying locks would cause multiple objects to operate on different mutexes, losing synchronization meaning
     * - Avoids logical errors caused by accidental lock copying
     */
    Lock(const Lock&) = delete;

    /**
     * @brief Default move constructor
     * 
     * Allows Lock objects to be moved, useful in the following scenarios:
     * - Storing Lock objects in containers
     * - Returning Lock objects from functions
     * - Transferring ownership of lock objects
     * 
     * std::mutex supports move semantics (since C++11)
     */
    Lock(Lock&&) = default;

    /**
     * @brief Get a scoped lock object
     * @return ScopeLock object
     * 
     * Return behavior:
     * - If m_enabled is true: returns ScopeLock{m_mtx}, locks immediately on construction
     * - If m_enabled is false: returns ScopeLock{}, default-constructed empty lock not associated with any mutex
     * 
     * Usage example:
     * @code
     * Lock lock(true);
     * {
     *     auto scopeLock = lock.GetScopeLock();  // Automatically lock
     *     // Critical section code
     * }  // scopeLock destructor automatically unlocks
     * @endcode
     * 
     * Empty lock behavior:
     * - Default-constructed std::unique_lock is not associated with any mutex
     * - No unlock operation performed on destruction
     * - All lock operations are invalid but do not throw exceptions
     */
    ScopeLock GetScopeLock() {
        return m_enabled ? ScopeLock{m_mtx} : ScopeLock{};
    }

private:
    /**
     * @brief Internal mutex
     * 
     * std::mutex characteristics:
     * - Blocking lock, thread is blocked (sleeps) when unable to acquire lock
     * - More efficient than spinlock when critical section execution time is long
     * - Supports advanced features like priority inheritance (depends on operating system)
     * - Non-recursive, repeated locking by the same thread leads to undefined behavior
     */
    std::mutex m_mtx;

    /**
     * @brief Flag controlling whether the lock is enabled
     * 
     * This design allows dynamic control of synchronization behavior at runtime:
     * - Can disable locks in single-threaded environments to improve performance
     * - Can temporarily disable locks during debugging to troubleshoot issues
     * - Flexible control based on configuration or runtime conditions
     */
    bool m_enabled = false;
};

/**
 * @brief Recursive mutex wrapper class supporting multiple locks by the same thread
 * 
 * Based on std::recursive_mutex implementation, allows the same thread to acquire the lock multiple times without deadlock
 * Uses std::unique_lock as RAII-style lock manager
 * Supports dynamic control of lock enable/disable state through enabled parameter
 * 
 * Use cases:
 * - Locking needed in recursive functions
 * - One function calls another function that needs the same lock
 * - Complex call chains that may repeatedly acquire the same lock
 */
class RecursiveLock {
public:
    /**
     * @brief Type alias using std::unique_lock to manage recursive mutex
     * 
     * Combination of std::unique_lock and std::recursive_mutex provides:
     * - Recursive locking capability: same thread can acquire the lock multiple times
     * - Flexible lock management: supports deferred locking, early unlocking, lock transfer, etc.
     * - Condition variable support: can be used with std::condition_variable_any
     * - Timeout mechanism: supports try_lock_for, try_lock_until, etc.
     */
    using ScopeLock = std::unique_lock<std::recursive_mutex>;

    /**
     * @brief Constructor
     * @param enabled Whether to enable recursive lock functionality
     * 
     * Role of enabled parameter:
     * - true: GetScopeLock() returns valid recursive lock that actually locks
     * - false: GetScopeLock() returns empty lock that performs no locking operations
     * 
     * Use cases:
     * - Disable locks during debugging to troubleshoot recursive deadlock issues
     * - Compare locking/non-locking overhead during performance testing
     * - Disable locks in known single-threaded environments to improve performance
     * - Decide whether thread safety is needed based on runtime configuration
     */
    RecursiveLock(bool enabled) : m_enabled(enabled) {
    }

    /**
     * @brief Deleted copy constructor
     * 
     * Recursive locks should not be copied because:
     * - std::recursive_mutex itself is not copyable
     * - Copying locks creates independent mutexes, losing synchronization effect
     * - Recursive lock state (lock count, owner thread, etc.) cannot be safely copied
     * - Avoids synchronization logic errors caused by accidental copying
     */
    RecursiveLock(const RecursiveLock&) = delete;

    /**
     * @brief Default move constructor
     * 
     * Allows RecursiveLock objects to be moved:
     * - std::recursive_mutex supports move semantics (since C++11)
     * - After move, original object becomes invalid, target object gets complete lock state
     * - Supports storing RecursiveLock objects in containers
     * - Supports returning RecursiveLock objects from functions
     * 
     * Note: Move operation does not transfer current lock state, only the mutex object itself
     */
    RecursiveLock(RecursiveLock&&) = default;

    /**
     * @brief Get a recursive scoped lock object
     * @return ScopeLock object supporting recursive locking
     * 
     * Return behavior:
     * - If m_enabled is true: returns ScopeLock{m_mtx}, immediately acquires recursive lock
     * - If m_enabled is false: returns ScopeLock{}, empty lock performs no operations
     * 
     * Special behavior of recursive lock:
     * - Same thread can call GetScopeLock() multiple times without deadlock
     * - Each successful lock increases internal counter
     * - Must call unlock operations same number of times to actually release the lock
     * - Only the last unlock operation actually releases the mutex
     * 
     * Usage example:
     * @code
     * RecursiveLock lock(true);
     * 
     * void funcA() {
     *     auto scopeLock = lock.GetScopeLock();  // First lock
     *     funcB();  // Call function that may lock again
     * }
     * 
     * void funcB() {
     *     auto scopeLock = lock.GetScopeLock();  // Recursive lock, no deadlock
     *     // Execute operations requiring synchronization
     * }
     * @endcode
     * 
     * Performance considerations:
     * - Recursive locks have additional overhead compared to regular locks (need to track owner thread and lock count)
     * - Use only when recursive locking is actually needed, otherwise prefer regular mutex
     */
    ScopeLock GetScopeLock() {
        return m_enabled ? ScopeLock{m_mtx} : ScopeLock{};
    }

private:
    /**
     * @brief Internal recursive mutex
     * 
     * std::recursive_mutex characteristics:
     * - Allows same thread to acquire lock multiple times without deadlock
     * - Internally maintains lock counter and owner thread ID
     * - Must call unlock() same number of times to actually release the lock
     * - Has more memory and performance overhead than regular mutex
     * - Still mutually exclusive between different threads, only lock owner thread can recursively lock
     * 
     * Internal implementation principle:
     * - Records thread ID currently holding the lock
     * - Maintains lock count counter for that thread
     * - When same thread locks again, only increments counter without blocking
     * - Decrements counter on unlock, only actually releases mutex when counter reaches 0
     * 
     * Usage considerations:
     * - Avoid overuse, code refactoring is usually better than using recursive locks
     * - Ensure each GetScopeLock() has corresponding scope end (automatic unlock)
     * - Pay special attention to lock release in exception handling
     */
    std::recursive_mutex m_mtx;

    /**
     * @brief Flag controlling whether recursive lock is enabled
     * 
     * This design is particularly useful in the following scenarios:
     * - Debugging lock contention issues in recursive calls
     * - Measuring recursive lock overhead during performance analysis
     * - Disabling synchronization in explicit single-threaded environments
     * - Dynamically enabling/disabling locks based on runtime conditions (such as thread count)
     */
    bool m_enabled = false;
};

namespace model_engine {

using TimePoint = std::chrono::time_point<std::chrono::high_resolution_clock>;
using Duration = double;

inline TimePoint Now() {
    return std::chrono::high_resolution_clock::now();
}

inline Duration GetDuration(const TimePoint& t1, const TimePoint& t2) {
    return std::chrono::duration_cast<std::chrono::microseconds>(t2 - t1).count();
}

inline std::string GetLocalTimeString() {
    char buf[sizeof("YYYY-MM-DD HH:MM:SS:UUUUUU")];
    auto tp = std::chrono::system_clock::now();
    auto us = std::chrono::duration_cast<std::chrono::microseconds>(tp.time_since_epoch()) - std::chrono::duration_cast<std::chrono::seconds>(tp.time_since_epoch());
    auto t = std::chrono::system_clock::to_time_t(tp);
    auto pos = std::strftime(buf, sizeof(buf), "%F %T", std::localtime(&t));
    std::sprintf(buf+pos, ":%06ld", us.count());
    return std::string(buf);
}

}
