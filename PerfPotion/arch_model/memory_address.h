#pragma once

#include <cstdint>
#include <cassert>
#include "perf_potion_mmio.h"

#define TEMP_MACRO_DEVICE_MEM_ADDR_BIT_INDEX 47

#define TEMP_MACRO_MEM_TYPE_START_BIT (45)
#define TEMP_MACRO_MEM_TYPE_MASK      (0b111ul << TEMP_MACRO_MEM_TYPE_START_BIT)
#define TEMP_MACRO_MEM_TYPE(x)        ((x & TEMP_MACRO_MEM_TYPE_MASK) >> TEMP_MACRO_MEM_TYPE_START_BIT)

#define TEMP_MACRO_MEM_SUBTYPE_START_BIT (36)
#define TEMP_MACRO_MEM_SUBTYPE_MASK      (0b1'1111'1111ul << TEMP_MACRO_MEM_SUBTYPE_START_BIT)
#define TEMP_MACRO_MEM_SUBTYPE(x)        ((x & TEMP_MACRO_MEM_SUBTYPE_MASK) >> TEMP_MACRO_MEM_SUBTYPE_START_BIT)
#define _HOSTMEM_BASE (0x1000'0000)

namespace model_common
{
enum class MemZone { LocalDRAM, LocalL2B, RemoteDRAM, RemoteL2B, DeviceMMIO, HostDRAM, Reserved };

inline bool is_mmio_addr(uint64_t addr) { return addr >= MMIO_MACRO_CP_TOP_BASE && addr < (MMIO_MACRO_CP_TOP_BASE + MMIO_MACRO_CP_TOP_SIZE); }


inline bool is_host_addr(uint64_t addr) { return TEMP_MACRO_MEM_TYPE(addr) < 0b100 ; }

inline bool is_local_addr(uint64_t addr) { return TEMP_MACRO_MEM_TYPE(addr) == 0b110; }

// Local Device Global Memory
inline bool is_device_addr(uint64_t addr) { return is_local_addr(addr) && TEMP_MACRO_MEM_SUBTYPE(addr) == 0b0'0000'0000; }

// Local Device Shared Memory
inline bool is_shared_addr(uint64_t addr) { return is_local_addr(addr) && TEMP_MACRO_MEM_SUBTYPE(addr) == 0b0'0000'0010; }

// New Style APIs
// Local Device Global Memory
inline bool is_local_glboal_addr(uint64_t addr) { return is_device_addr(addr); }
// Local Device Shared Memory
inline bool is_local_shared_addr(uint64_t addr) { return is_shared_addr(addr); }
// Remote Device Global Memory
inline bool in_remote_global_addr(uint64_t addr) { return TEMP_MACRO_MEM_TYPE(addr) == 0b100; }
// Remote Device Shared Memory
inline bool is_remote_shared_addr(uint64_t addr) { return TEMP_MACRO_MEM_TYPE(addr) == 0b101; }

inline MemZone sipu_mem_type(uint64_t addr) {
    if (is_host_addr(addr)) {
        return model_common::MemZone::HostDRAM;
    } else if (is_local_glboal_addr(addr)) {
        return model_common::MemZone::LocalDRAM;
    } else if (is_local_shared_addr(addr)) {
        return model_common::MemZone::LocalL2B;
    } else if (in_remote_global_addr(addr)) {
        return model_common::MemZone::RemoteDRAM;
    } else if (is_remote_shared_addr(addr)) {
        return model_common::MemZone::RemoteL2B;
    }
    // assert(true, "This is a segment of reserved memory");
    return model_common::MemZone::Reserved;
}

inline uint64_t sipu_mem_base(MemZone type, uint64_t chip_id = 0) {
    switch (type) {
    case model_common::MemZone::HostDRAM:
        return _HOSTMEM_BASE;
    case model_common::MemZone::LocalDRAM:
        return 0b110ul << TEMP_MACRO_MEM_TYPE_START_BIT;
    case model_common::MemZone::LocalL2B:
        return 0b110ul << TEMP_MACRO_MEM_TYPE_START_BIT | 0b10ul << TEMP_MACRO_MEM_SUBTYPE_START_BIT;
    case model_common::MemZone::RemoteDRAM:
        return 0b100ul << TEMP_MACRO_MEM_TYPE_START_BIT | chip_id << TEMP_MACRO_MEM_SUBTYPE_START_BIT;
    case model_common::MemZone::RemoteL2B:
        return 0b101ul << TEMP_MACRO_MEM_TYPE_START_BIT | chip_id << TEMP_MACRO_MEM_SUBTYPE_START_BIT;
    default:
        return 0;
    }
}
} // namespace model_common
