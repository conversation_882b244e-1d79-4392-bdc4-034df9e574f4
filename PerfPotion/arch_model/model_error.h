#pragma once

#include <iostream>
#include <sstream>
#include <vector>
#include <string>
#include <cstdio>
#include <cstdlib>
#include <stdexcept>
#include <chrono>
#include <ctime>


#define CHECK(cond, msg...)                                                                                                                                                                         \
    do {                                                                                                                                                                                            \
        if(!(cond)) {                                                                                                                                                                               \
            std::ostringstream oss;                                                                                                                                                                 \
            oss << perf_potion_engine::GetMsgHeader() << __FILE__ << ":" << std::to_string(__LINE__) << " '" << #cond"' check failed in " << __func__ << "(): " << GetMsgStr(msg) << std::endl; \
            std::cerr << oss.str() << std::endl;                                                                                                                                                    \
            throw std::logic_error{"Assertion Failed"};                                                                                                                                             \
            exit(1);                                                                                                                                                                                \
        }                                                                                                                                                                                           \
    } while (false)

namespace perf_potion_engine {
using TimePoint = std::chrono::time_point<std::chrono::high_resolution_clock>;
using Duration = double;

inline TimePoint Now() {
    return std::chrono::high_resolution_clock::now();
}

inline Duration GetDuration(const TimePoint& t1, const TimePoint& t2) {
    return std::chrono::duration_cast<std::chrono::microseconds>(t2 - t1).count();
}

inline std::string GetLocalTimeString() {
    char buf[sizeof("YYYY-MM-DD HH:MM:SS:UUUUUU")];
    auto tp = std::chrono::system_clock::now();
    auto us = std::chrono::duration_cast<std::chrono::microseconds>(tp.time_since_epoch()) - std::chrono::duration_cast<std::chrono::seconds>(tp.time_since_epoch());
    auto t = std::chrono::system_clock::to_time_t(tp);
    auto pos = std::strftime(buf, sizeof(buf), "%F %T", std::localtime(&t));
    std::sprintf(buf+pos, ":%06ld", us.count());
    return std::string(buf);
}

inline const char* GetMsgStr() {
    return "";
}

inline const char* GetMsgStr(const char* s) {
    return s;
}

inline std::string GetMsgStr(const std::string& s) {
    return s;
}

template <typename... Args>
inline std::string GetMsgStr(const char* fmt, Args&&... args) {
    int size = snprintf(nullptr, 0, fmt, args...);
    std::string s(size, '\0');
    snprintf(s.data(), size+1, fmt, args...);
    return s;
}

inline std::string GetMsgHeader() {
    return "[" + GetLocalTimeString() + "][PerfPotion] ";
}

}