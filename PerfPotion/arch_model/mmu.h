#pragma once

// 前置声明，避免重复包含
class AbstractMemory;
#include "pte.h"
#include <optional>

class MMU {
public:
    MMU(AbstractMemory& memory);

    // Translate a virtual address to a physical address
    std::optional<uint64_t> Translate(uint64_t va, bool is_write);

    // Set the root of the page table hierarchy (e.g., CR3)
    void SetPageTableRoot(uint64_t root_pa);

private:
    uint64_t m_page_table_root = 0;
    AbstractMemory& m_memory;

    // Helper function to walk the page tables
    std::optional<uint64_t> PageWalk(uint64_t va, bool is_write);
};
