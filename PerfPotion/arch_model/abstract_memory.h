#pragma once

#include <unordered_map>
#include <vector>
#include <cstring>
#include <algorithm>
#include <memory>
#include <shared_mutex>
#include <mutex>

/**
 * @brief Abstract memory management class
 * 
 * Implements block-based memory management with on-demand allocation and efficient read/write operations.
 * Uses a chunked storage strategy with each block sized at 4KB (2^12 bytes).
 * Thread-safe with support for multiple readers and single writer concurrent access pattern.
 */
class AbstractMemory {
public:
    // Block size bit width, 12 represents 4KB blocks (2^12 = 4096 bytes)
    static constexpr int BLOCK_SIZE_WIDTH = 12;
    
    // Actual block size: 4KB
    static constexpr int BLOCK_SIZE = 1 << BLOCK_SIZE_WIDTH;
    
    // Block offset mask for fast calculation of intra-block offset (equivalent to % BLOCK_SIZE, but faster)
    static constexpr uint64_t BLOCK_MASK = BLOCK_SIZE - 1;

    /**
     * @brief Read data from specified address
     * 
     * @param addr Starting address (64-bit virtual address)
     * @param size Number of bytes to read
     * @param dst Destination buffer pointer
     * 
     * @note Supports cross-block reading with automatic block boundary handling
     * @note Thread-safe, multiple threads can read simultaneously
     */
    void Read(uint64_t addr, size_t size, uint8_t* dst) {
        while (size) {
            // Calculate target block index (high-order bits of address)
            uint64_t block_index = addr >> BLOCK_SIZE_WIDTH;
            
            // Calculate intra-block offset (low-order bits of address, using bitwise operation instead of modulo)
            uint64_t offset = addr & BLOCK_MASK;
            
            // Calculate number of bytes to copy in this iteration (cannot exceed remaining space in current block)
            size_t copy_size = std::min(size, static_cast<size_t>(BLOCK_SIZE - offset));
            
            // Get block pointer and perform memory copy
            uint8_t* block = GetBlock(block_index);
            std::memcpy(dst, block + offset, copy_size);
            
            // Update address, remaining size, and destination pointer
            addr += copy_size;
            size -= copy_size;
            dst += copy_size;
        }
    }

    /**
     * @brief Write data to specified address
     * 
     * @param addr Starting address (64-bit virtual address)
     * @param size Number of bytes to write
     * @param src Source data buffer pointer
     * 
     * @note Supports cross-block writing with automatic block boundary handling
     * @note Automatically allocates required memory blocks
     * @note Thread-safe, but write operations are mutually exclusive
     */
    void Write(uint64_t addr, size_t size, const uint8_t* src) {
        while (size) {
            // Calculate target block index
            // Calculate target block index
            uint64_t block_index = addr >> BLOCK_SIZE_WIDTH;
            
            // Calculate intra-block offset
            uint64_t offset = addr & BLOCK_MASK;
            
            // Calculate number of bytes to copy in this iteration
            size_t copy_size = std::min(size, static_cast<size_t>(BLOCK_SIZE - offset));
            
            // Allocate block (if it doesn't exist) and perform memory copy
            uint8_t* block = AllocateBlock(block_index);
            std::memcpy(block + offset, src, copy_size);
            
            // Update address, remaining size, and source pointer
            addr += copy_size;
            size -= copy_size;
            src += copy_size;
        }
    }

    /**
     * @brief Batch read interface to reduce lock contention
     * 
     * @param requests List of read requests, each element contains (address, size)
     * @param results Result vector storing read data for each corresponding request
     * 
     * @note Completes all read operations under a single lock protection, improving batch operation performance
     * @note Result vector is automatically resized to match the number of requests
     */
    void ReadBatch(const std::vector<std::pair<uint64_t, size_t>>& requests,
                   std::vector<std::vector<uint8_t>>& results) {
        results.resize(requests.size());
        
        // Acquire shared lock, allowing multiple batch reads to execute in parallel
        std::shared_lock<std::shared_mutex> lock(m_mutex);

        for (size_t i = 0; i < requests.size(); ++i) {
            uint64_t addr = requests[i].first;
            size_t size = requests[i].second;
            results[i].resize(size);
            
            // Use internal unlocked version since lock is already held
            ReadUnsafe(addr, size, results[i].data());
        }
    }

    /**
     * @brief Memory usage statistics information
     */
    struct MemoryStats {
        size_t allocated_blocks;      ///< Number of allocated blocks
        size_t total_memory_bytes;    ///< Total memory usage in bytes
        double fragmentation_ratio;   ///< Fragmentation ratio (0.0-1.0)
    };

    /**
     * @brief Get memory usage statistics
     * 
     * @return MemoryStats Current memory usage statistics
     * 
     * @note Fragmentation ratio calculation: 1 - (actual block count / max block index + 1)
     * @note Thread-safe, can be called at any time
     */
    MemoryStats GetStats() const {
        std::shared_lock<std::shared_mutex> lock(m_mutex);
        MemoryStats stats;
        stats.allocated_blocks = m_blocks.size();
        stats.total_memory_bytes = stats.allocated_blocks * BLOCK_SIZE;
        
        // Simple fragmentation calculation: based on block index continuity
        // If block indices are not continuous, it indicates fragmentation
        stats.fragmentation_ratio = m_blocks.empty() ? 0.0 : 
            1.0 - (static_cast<double>(m_blocks.size()) / (std::prev(m_blocks.end())->first + 1));

        return stats;
    }

    /**
     * @brief Clean up unused memory blocks
     * 
     * @note Current implementation retains all blocks, LRU cleanup strategy can be added in the future
     * @note Can be used to actively release memory when under memory pressure
     */
    void Cleanup() {
        std::unique_lock<std::shared_mutex> lock(m_mutex);
        // TODO: Implement LRU cleanup logic
        // Current version retains all blocks to ensure data integrity
    }

private:
    // Reader-writer lock: supports multiple readers and single writer concurrent pattern
    // shared_lock for read operations, unique_lock for write operations
    mutable std::shared_mutex m_mutex;
    
    // Memory block storage: using unique_ptr to avoid unnecessary memory copying
    // key: block index (address right-shifted by BLOCK_SIZE_WIDTH bits)
    // value: smart pointer to 4KB memory block
    std::unordered_map<uint64_t, std::unique_ptr<uint8_t[]>> m_blocks;

    /**
     * @brief Get memory block at specified index (read-only access)
     * 
     * @param index Block index
     * @return uint8_t* Pointer to memory block
     * 
     * @note If block doesn't exist, automatically allocates new block
     * @note Uses lock upgrade mechanism: first try read lock, upgrade to write lock when needed
     */
    uint8_t* GetBlock(uint64_t index) const {
        // First try to find existing block with read lock
        std::shared_lock<std::shared_mutex> lock(m_mutex);
        auto it = m_blocks.find(index);
        if (it != m_blocks.end()) {
            return it->second.get();
        }
        
        // Block doesn't exist, release read lock and allocate new block
        lock.unlock();
        return const_cast<AbstractMemory*>(this)->AllocateBlock(index);
    }

    /**
     * @brief Allocate memory block at specified index
     * 
     * @param index Block index
     * @return uint8_t* Pointer to newly allocated (or existing) memory block
     * 
     * @note Uses double-checked locking pattern to avoid duplicate allocation
     * @note Newly allocated blocks are zero-initialized
     * @note Thread-safe, multiple threads allocating different blocks simultaneously won't conflict
     */
    uint8_t* AllocateBlock(uint64_t index) {
        std::unique_lock<std::shared_mutex> lock(m_mutex);
        
        // Double check: check again if block exists after acquiring write lock
        // Avoids the case where another thread has already allocated the same block while waiting for the lock
        auto it = m_blocks.find(index);
        if (it != m_blocks.end()) {
            return it->second.get();
        }

        // Allocate new 4KB memory block and zero-initialize
        auto block = std::make_unique<uint8_t[]>(BLOCK_SIZE);
        std::memset(block.get(), 0, BLOCK_SIZE);
        
        // Save pointer and move block into map
        uint8_t* ptr = block.get();
        m_blocks[index] = std::move(block);
        return ptr;
    }

    /**
     * @brief Internal unlocked read function
     * 
     * @param addr Starting address
     * @param size Number of bytes to read
     * @param dst Destination buffer
     * 
     * @note Must already hold appropriate lock before calling
     * @note For unallocated blocks, returns zero-filled data
     * @note Primarily used in batch operations to avoid repeated locking
     */
    void ReadUnsafe(uint64_t addr, size_t size, uint8_t* dst) const {
        while (size) {
            uint64_t block_index = addr >> BLOCK_SIZE_WIDTH;
            uint64_t offset = addr & BLOCK_MASK;
            size_t copy_size = std::min(size, static_cast<size_t>(BLOCK_SIZE - offset));
            
            // Find block, if it doesn't exist return zero data
            auto it = m_blocks.find(block_index);
            if (it != m_blocks.end()) {
                std::memcpy(dst, it->second.get() + offset, copy_size);
            } else {
                // Unallocated memory regions return zero, simulating virtual memory behavior
                std::memset(dst, 0, copy_size);
            }

            addr += copy_size;
            size -= copy_size;
            dst += copy_size;
        }
    }
};