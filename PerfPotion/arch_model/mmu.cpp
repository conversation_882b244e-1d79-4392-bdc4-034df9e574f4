#include "abstract_memory.h"
#include "mmu.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>

MMU::MMU(AbstractMemory& memory)
    : m_memory(memory), m_page_table_root(0) {}

void MMU::SetPageTableRoot(uint64_t root_pa) {
    m_page_table_root = root_pa;
}

std::optional<uint64_t> MMU::Translate(uint64_t va, bool is_write) {
    if (m_page_table_root == 0) {
        // MMU is not configured, perform 1:1 mapping
        return va;
    }
    return PageWalk(va, is_write);
}

std::optional<uint64_t> MMU::PageWalk(uint64_t va, bool is_write) {
    VirtualAddress vaddr;
    vaddr.full = va;

    uint64_t current_table_pa = m_page_table_root;
    PageTableEntry pte;

    // Level 4
    uint64_t p4_entry_addr = current_table_pa + vaddr.fields.p4_index * sizeof(PageTableEntry);
    m_memory.Read(p4_entry_addr, sizeof(pte), reinterpret_cast<uint8_t*>(&pte));
    if (!pte.present) {
        return std::nullopt; // Page fault
    }
    current_table_pa = pte.ppn << PAGE_SHIFT;

    // Level 3
    uint64_t p3_entry_addr = current_table_pa + vaddr.fields.p3_index * sizeof(PageTableEntry);
    m_memory.Read(p3_entry_addr, sizeof(pte), reinterpret_cast<uint8_t*>(&pte));
    if (!pte.present) {
        return std::nullopt; // Page fault
    }
    current_table_pa = pte.ppn << PAGE_SHIFT;

    // Level 2
    uint64_t p2_entry_addr = current_table_pa + vaddr.fields.p2_index * sizeof(PageTableEntry);
    m_memory.Read(p2_entry_addr, sizeof(pte), reinterpret_cast<uint8_t*>(&pte));
    if (!pte.present) {
        return std::nullopt; // Page fault
    }
    current_table_pa = pte.ppn << PAGE_SHIFT;

    // Level 1
    uint64_t p1_entry_addr = current_table_pa + vaddr.fields.p1_index * sizeof(PageTableEntry);
    m_memory.Read(p1_entry_addr, sizeof(pte), reinterpret_cast<uint8_t*>(&pte));
    if (!pte.present || (is_write && !pte.writable)) {
        return std::nullopt; // Page fault or protection fault
    }

    // Update accessed and dirty bits
    if (!pte.accessed || (is_write && !pte.dirty)) {
        pte.accessed = 1;
        if (is_write) {
            pte.dirty = 1;
        }
        m_memory.Write(p1_entry_addr, sizeof(pte), reinterpret_cast<const uint8_t*>(&pte));
    }

    uint64_t physical_address = (pte.ppn << PAGE_SHIFT) + vaddr.fields.offset;
    return physical_address;
}

void MMU::Read(uint64_t va, size_t size, uint8_t* dst) {
    while (size > 0) {
        auto pa_opt = Translate(va, false);
        if (!pa_opt) throw std::runtime_error("MMU: VA read page fault");
        uint64_t pa = *pa_opt;
        uint64_t page_offset = va & PAGE_MASK;
        size_t to_read = std::min(size, static_cast<size_t>(PAGE_SIZE - page_offset));
        m_memory.Read(pa, to_read, dst);
        va += to_read;
        dst += to_read;
        size -= to_read;
    }
}

void MMU::Write(uint64_t va, size_t size, const uint8_t* src) {
    while (size > 0) {
        auto pa_opt = Translate(va, true);
        if (!pa_opt) throw std::runtime_error("MMU: VA write page fault");
        uint64_t pa = *pa_opt;
        uint64_t page_offset = va & PAGE_MASK;
        size_t to_write = std::min(size, static_cast<size_t>(PAGE_SIZE - page_offset));
        m_memory.Write(pa, to_write, src);
        va += to_write;
        src += to_write;
        size -= to_write;
    }
}
