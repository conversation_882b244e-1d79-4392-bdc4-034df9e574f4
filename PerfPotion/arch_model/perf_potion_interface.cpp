#include <iostream>
#include <memory>
#include <map>
#include <set>
#include <sstream>

#include "model_lib_utility.h"
#include "model_error.h"
#include "cmodel_interface.h"
#include "model.h"
#include "cmodel_memory_if.h"

using namespace perf_potion_engine;

namespace {

// 创建适配器函数，将C风格的回调转换为std::function
ReadHostMemFn createReadMemAdapter(void* read_host_mem_cb) {
    using ReadMemFnPtr = void(*)(void*, uint64_t, size_t, uint8_t*);
    auto fn_ptr = reinterpret_cast<ReadMemFnPtr>(read_host_mem_cb);
    return [fn_ptr](uint64_t addr, size_t size, uint8_t* data) {
        fn_ptr(nullptr, addr, size, data);
    };
}

WriteHostMemFn createWriteMemAdapter(void* write_host_mem_cb) {
    using WriteMemFnPtr = void(*)(void*, uint64_t, size_t, const uint8_t*);
    auto fn_ptr = reinterpret_cast<WriteMemFnPtr>(write_host_mem_cb);
    return [fn_ptr](uint64_t addr, size_t size, const uint8_t* data) {
        fn_ptr(nullptr, addr, size, data);
    };
}

class ModelManager {
public:
    static ModelManager& Instance() {
        static ModelManager inst;
        return inst;
    }

    ~ModelManager() noexcept {
        // Just clear the model without calling Stop since we don't start it in our test
        if (m_model) {
            if (!m_alive_chip_ids.empty()) {
                std::cout << "[PotionModel] Info: cleaning up model with active devices" << std::endl;
            }
            // Don't call Stop() since we never call Run() in the test
            m_model.reset();
        }
    }

    int* CreateDevice(int chip_id, const HostMemoryIf& host_memory_if) {
        if (!m_model) {
            m_model = std::make_unique<perfpotion_model::PotionModel>();
        }
        
        // Use config to validate chip_id range
        perfpotion_model::PerfPotionConfig config;
        if (chip_id >= 0 && chip_id < config.chip_num) {
            auto [it, success] = m_alive_chip_ids.insert(chip_id);
            if (!success) {
                std::cerr << "[PotionModel] Error: create device with duplicated chip_id " << chip_id << std::endl;
                return nullptr;
            }
            m_model->SetHostMemoryIf(chip_id, host_memory_if);
            return reinterpret_cast<int*>(static_cast<uintptr_t>(chip_id + 1)); // +1 to avoid nullptr
        }
        std::cerr << "[PotionModel] Error: chip_id " << chip_id << " out of range [0, " << config.chip_num << ")" << std::endl;
        return nullptr;
    }

    void DestroyDevice(int chip_id) {
        if (!m_model) {
            std::cerr << "Error: model does not exist anymore" << std::endl;
            return;
        }

        perfpotion_model::PerfPotionConfig config;
        if (chip_id < 0 || chip_id >= config.chip_num) {
            std::cerr << "Error: destroy device with chip_id out of range" << std::endl;
            return;
        }

        auto it = m_alive_chip_ids.find(chip_id);
        if (it == m_alive_chip_ids.end()) {
            std::cerr << "Error: destroy device with non-existing chip_id" << std::endl;
            return;
        }

        m_alive_chip_ids.erase(it);
        if (m_alive_chip_ids.empty()) {
            m_model.reset();
        }
    }

    int GetDeviceCount() {
        // Return default chip count from config even if model is not created yet
        perfpotion_model::PerfPotionConfig config;
        return config.chip_num;
    }

    void ReadMemory(int chip_id, uint64_t addr, size_t size, uint8_t* data, int vmid = 0) {
        if (!m_model) {
            std::cerr << "Error: model is null" << std::endl;
            return;
        }
        if (!m_alive_chip_ids.contains(chip_id)) {
            std::cerr << "Error: chip_id " << chip_id << " not in alive set" << std::endl;
            return;
        }
        if (!data) {
            std::cerr << "Error: data pointer is null" << std::endl;
            return;
        }
        m_model->ReadMemory(chip_id, addr, size, data, vmid);
    }

    void WriteMemory(int chip_id, uint64_t addr, size_t size, const uint8_t* data, int vmid = 0) {
        if (!m_model) {
            std::cerr << "Error: model is null" << std::endl;
            return;
        }
        if (!m_alive_chip_ids.contains(chip_id)) {
            std::cerr << "Error: chip_id " << chip_id << " not in alive set" << std::endl;
            return;
        }
        m_model->WriteMemory(chip_id, addr, size, data, vmid);
    }

    void ReadRegister(int chip_id, uint64_t addr, uint64_t* value) {
        if (!m_model || !m_alive_chip_ids.contains(chip_id)) {
            std::cerr << "Error: device does not exist" << std::endl;
            return;
        }
        m_model->ReadMemory(chip_id, addr, sizeof(uint64_t), reinterpret_cast<uint8_t*>(value));
    }

    void WriteRegister(int chip_id, uint64_t addr, uint64_t value) {
        if (!m_model || !m_alive_chip_ids.contains(chip_id)) {
            std::cerr << "Error: device does not exist" << std::endl;
            return;
        }
        m_model->WriteMemory(chip_id, addr, sizeof(uint64_t), reinterpret_cast<const uint8_t*>(&value));
    }

private:
    std::unique_ptr<perfpotion_model::PotionModel> m_model;
    std::set<int> m_alive_chip_ids;
};

} // anonymous namespace

// C API interface functions
extern "C" {

PERF_POTION_MODEL_DECLARE_LIB_API void* ppCreateDevice(int chip_id, void* read_host_mem_cb, void* write_host_mem_cb) {
    HostMemoryIf memIf{
        createReadMemAdapter(read_host_mem_cb),
        createWriteMemAdapter(write_host_mem_cb)
    };
    return ModelManager::Instance().CreateDevice(chip_id, memIf);
}

PERF_POTION_MODEL_DECLARE_LIB_API void ppDestroyDevice(void* handle) {
    if (!handle) return;
    int chip_id = static_cast<int>(reinterpret_cast<uintptr_t>(handle)) - 1; // -1 to reverse +1 from create
    ModelManager::Instance().DestroyDevice(chip_id);
}

PERF_POTION_MODEL_DECLARE_LIB_API int ppGetDeviceCount() {
    return ModelManager::Instance().GetDeviceCount();
}

PERF_POTION_MODEL_DECLARE_LIB_API void ppReadMemory(void* handle, uint64_t addr, size_t size, uint8_t* data) {
    if (!handle) return;
    int chip_id = static_cast<int>(reinterpret_cast<uintptr_t>(handle)) - 1; // -1 to reverse +1 from create
    ModelManager::Instance().ReadMemory(chip_id, addr, size, data);
}

PERF_POTION_MODEL_DECLARE_LIB_API void ppWriteMemory(void* handle, uint64_t addr, size_t size, const uint8_t* data) {
    if (!handle) return;
    int chip_id = static_cast<int>(reinterpret_cast<uintptr_t>(handle)) - 1; // -1 to reverse +1 from create
    ModelManager::Instance().WriteMemory(chip_id, addr, size, data);
}

PERF_POTION_MODEL_DECLARE_LIB_API void ppReadRegister(void* handle, uint64_t addr, uint64_t* value) {
    if (!handle) return;
    int chip_id = static_cast<int>(reinterpret_cast<uintptr_t>(handle)) - 1; // -1 to reverse +1 from create
    ModelManager::Instance().ReadRegister(chip_id, addr, value);
}

PERF_POTION_MODEL_DECLARE_LIB_API void ppWriteRegister(void* handle, uint64_t addr, uint64_t value) {
    if (!handle) return;
    int chip_id = static_cast<int>(reinterpret_cast<uintptr_t>(handle)) - 1; // -1 to reverse +1 from create
    ModelManager::Instance().WriteRegister(chip_id, addr, value);
}

} // extern "C"
