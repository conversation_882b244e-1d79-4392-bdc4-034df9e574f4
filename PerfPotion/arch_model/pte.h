#pragma once

#include <cstdint>

// Define page size, e.g., 4KB
constexpr uint64_t PAGE_SIZE = 4096;
constexpr uint64_t PAGE_SHIFT = 12;
constexpr uint64_t PAGE_MASK = PAGE_SIZE - 1;
constexpr uint64_t PTE_COUNT = PAGE_SIZE / sizeof(uint64_t);

// Page Table Entry (PTE) structure
struct PageTableEntry {
    uint64_t present : 1;       // 1 if page is present in memory
    uint64_t writable : 1;      // 1 if page is writable
    uint64_t user : 1;          // 1 if page is accessible from user mode
    uint64_t accessed : 1;      // 1 if page was accessed
    uint64_t dirty : 1;         // 1 if page was written to
    uint64_t reserved : 7;      // Reserved for future use
    uint64_t ppn : 40;          // Physical Page Number (for 52-bit physical address space)
    uint64_t reserved2 : 12;    // Reserved
};

// Virtual Address structure for a 4-level paging system
union VirtualAddress {
    uint64_t full;
    struct {
        uint64_t offset : 12;
        uint64_t p1_index : 9;
        uint64_t p2_index : 9;
        uint64_t p3_index : 9;
        uint64_t p4_index : 9;
        uint64_t sign_extend : 16;
    } fields;
};
