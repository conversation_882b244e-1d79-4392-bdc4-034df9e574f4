#pragma once

#include <iostream>
#include <memory>
#include <algorithm>
#include "cmodel_interface.h"
#include "memory.h"
#include <array>

namespace perfpotion_model{

class Chip {
public:
    Chip(int chip_id,  MemoryLock* memory_lock) : m_chip_id(chip_id), m_memory(memory_lock), m_memory_lock(memory_lock) {
        m_memory.SetChip(this);
        std::cout << "chip #" << chip_id << " created" << std::endl;
    }

    ~Chip();

    void SetHostMemoryIf(HostMemoryIf host_mem_if) { m_memory.SetHostMemoryIf(host_mem_if); }

    void ReadMemory(uint64_t addr, size_t size, uint8_t* dst, int vmid = 0) {
        m_memory.Read(addr, size, dst, vmid);
    }

    void WriteMemory(uint64_t addr, size_t size, const uint8_t* src, int vmid = 0) {
        m_memory.Write(addr, size, src, vmid);
    }

    //access cp/core register
    void ReadRegister(uint64_t addr, uint64_t* value);
    void WriteRegister(uint64_t addr, uint64_t value);

    void Step();

    uint64_t GetStepCnt() const { return m_step_cnt; }

private:
    void UpdateStepCnt();

    int m_chip_id = -1;
    Memory m_memory;
    std::array<uint64_t, 16> m_vmid_pte_bases{};

    uint64_t m_step_cnt = 0;
    MemoryLock* m_memory_lock = nullptr;

    // void AccessRegister(uint64_t addr, uint64_t& value, bool write);
};

}