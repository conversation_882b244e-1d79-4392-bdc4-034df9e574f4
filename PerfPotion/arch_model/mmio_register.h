#pragma once

#include "abstract_memory.h"
#include "cmodel_mmio_register_if.h"

class MMIORegister :public MMIORegisterIf{
    public:
    void Read(uint64_t addr, uint64_t *value) override { m_registers.Read(addr, sizeof(uint64_t), reinterpret_cast<uint8_t *>(value)); }

    void Write(uint64_t addr, uint64_t value) override { m_registers.Write(addr, sizeof(uint64_t), reinterpret_cast<const uint8_t *>(&value)); }

  private:
    AbstractMemory m_registers;
};