cmake_minimum_required(VERSION 3.22)
project(arch_model)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_CXX_FLAGS "-Werror -Wno-pmf-conversions -ggdb -DBOOST_STACKTRACE_USE_BACKTRACE")

if(CMAKE_BUILD_TYPE STREQUAL "DebugWithASan")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address")
endif()

# 添加头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

add_library(archmodel STATIC
    perf_potion_interface.cpp
    model.cpp
    memory.cpp
    chip.cpp
    mmu.cpp
    virtual_memory_manager.cpp
)

# Also create a shared library for dlopen tests
add_library(archmodel_shared SHARED
    perf_potion_interface.cpp
    model.cpp
    memory.cpp
    chip.cpp
    mmu.cpp
    virtual_memory_manager.cpp
)

target_include_directories(archmodel
    PUBLIC
    ${PROJECT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}
)

target_include_directories(archmodel_shared
    PUBLIC
    ${PROJECT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add version script to control symbol visibility
target_link_options(archmodel PRIVATE -Wl,--version-script=${CMAKE_CURRENT_SOURCE_DIR}/perfpotion_symbols.map)
target_link_options(archmodel_shared PRIVATE -Wl,--version-script=${CMAKE_CURRENT_SOURCE_DIR}/perfpotion_symbols.map)

# Set visibility to default for proper symbol export
set_target_properties(archmodel PROPERTIES
    CXX_VISIBILITY_PRESET default
    VISIBILITY_INLINES_HIDDEN OFF
)

set_target_properties(archmodel_shared PROPERTIES
    CXX_VISIBILITY_PRESET default
    VISIBILITY_INLINES_HIDDEN OFF
    OUTPUT_NAME "archmodel"
)
