#pragma once

#include <string>
#include <memory>
#include <functional>

#include "dlfcn.h"

#include "model_error.h"

#define PERF_POTION_MODEL_DECLARE_LIB_API __attribute__((visibility("default")))
#define PERF_POTION_MODEL_DECLARE_LIB_API_TYPE(type, fn) using type = decltype(fn)*

#define PERF_POTION_MODEL_DECLARE_LIB(name) std::unique_ptr<void, perf_potion_engine::VoidDeleter> m_##name
#define PERF_POTION_MODEL_INIT_LIB(lib_path, name) do { m_##name = std::unique_ptr<void, perf_potion_engine::VoidDeleter>(dlopen(std::string(lib_path).c_str(), RTLD_LAZY | RTLD_LOCAL), [](void* p){ dlclose(p); }); CHECK(m_##name, "fail to open '"+std::string(lib_path)+"', "+dlerror()); } while (false)
#define PERF_POTION_MODEL_DECLARE_LIB_FN(type, name) type m_##name = nullptr
#define PERF_POTION_MODEL_INIT_LIB_FN(lib, type, name) do { m_##name = reinterpret_cast<type>(dlsym(m_##lib.get(), #name)); CHECK(m_##name, "can't find symbol '"+std::string(#name)+"'"); } while (false)
#define PERF_POTION_MODEL_DECLARE_LIB_INSTANCE(name) std::unique_ptr<void, perf_potion_engine::VoidDeleter> m_##name;
#define PERF_POTION_MODEL_INIT_LIB_INSTANCE(name, create_fn, destroy_fn, create_args...) do { m_##name = std::unique_ptr<void, perf_potion_engine::VoidDeleter>(create_fn(create_args), [this](void* p){ destroy_fn(p); }); CHECK(m_##name, "can't create '"+std::string(#name)+"'"); } while (false)

namespace perf_potion_engine {

using VoidDeleter = std::function<void(void*)>;

}
