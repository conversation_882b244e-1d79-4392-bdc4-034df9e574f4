#pragma once

#include <cstddef>
#include <cstdint>
#include <functional>

using ReadHostMemFn = std::function<void(uint64_t, size_t, uint8_t*)>;
using WriteHostMemFn = std::function<void(uint64_t, size_t, const uint8_t*)>;



class HostMemoryIf {
public:
    HostMemoryIf() = default;

    HostMemoryIf(ReadHostMemFn read_host_mem_fn, WriteHostMemFn write_host_mem_fn) :
        m_read_host_mem_fn(read_host_mem_fn),
        m_write_host_mem_fn(write_host_mem_fn) {}

    void Read(uint64_t addr, size_t size, uint8_t* dst) {
        m_read_host_mem_fn(addr, size, dst);
    }

    void Write(uint64_t addr, size_t size, const uint8_t* src) {
        m_write_host_mem_fn(addr, size, src);
    }

private:
    ReadHostMemFn m_read_host_mem_fn;
    WriteHostMemFn m_write_host_mem_fn;
};


