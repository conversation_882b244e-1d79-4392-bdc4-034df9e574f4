#include "virtual_memory_manager.h"
#include <algorithm>

void VirtualMemoryManager::Read(uint64_t va, size_t size, uint8_t* dst, MMU& mmu, AbstractMemory& physical_memory) {
    while (size > 0) {
        auto pa_opt = mmu.Translate(va, false);
        if (!pa_opt) {
            throw std::runtime_error("Page fault on read");
        }
        uint64_t pa = *pa_opt;
        uint64_t page_offset = va & AbstractMemory::BLOCK_MASK;
        size_t to_read = std::min(size, static_cast<size_t>(AbstractMemory::BLOCK_SIZE - page_offset));

        physical_memory.Read(pa, to_read, dst);

        va += to_read;
        dst += to_read;
        size -= to_read;
    }
}

void VirtualMemoryManager::Write(uint64_t va, size_t size, const uint8_t* src, MMU& mmu, AbstractMemory& physical_memory) {
    while (size > 0) {
        auto pa_opt = mmu.Translate(va, true);
        if (!pa_opt) {
            throw std::runtime_error("Page fault on write");
        }
        uint64_t pa = *pa_opt;
        uint64_t page_offset = va & AbstractMemory::BLOCK_MASK;
        size_t to_write = std::min(size, static_cast<size_t>(AbstractMemory::BLOCK_SIZE - page_offset));

        physical_memory.Write(pa, to_write, src);

        va += to_write;
        src += to_write;
        size -= to_write;
    }
}
