#pragma once

#include <set>
#include <string>


namespace perfpotion_model {



struct PerfPotionConfig {
    int chip_num = 8;
    bool threading = false;
    int thread_num = 0;

    // PerfPotionConfig();

    // template <typename T> T Get(const std::string &name, T default_value) const { return m_config.getScalar<T>(name, default_value); }

//   private:
    // mutable sipu::config::ConfigManager m_config;
};

}
