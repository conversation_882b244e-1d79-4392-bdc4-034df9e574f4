#include "chip.h"
#include "perfpotion_mmio.h"



perfpotion_model::Chip::~Chip()
{
}

void perfpotion_model::Chip::Step()
{
    UpdateStepCnt();    
}

void perfpotion_model::Chip::UpdateStepCnt()
{
}

namespace perfpotion_model{

void Chip::ReadRegister(uint64_t addr, uint64_t* value) {
    if (addr >= PERFPOTION_MMIO_VMID_PTE_BASE_0 && addr <= PERFPOTION_MMIO_VMID_PTE_BASE_15) {
        int vmid = (addr - PERFPOTION_MMIO_VMID_PTE_BASE_0) / 8;
        *value = m_vmid_pte_bases[vmid];
    } else {
        // Handle other registers
    }
}

void Chip::WriteRegister(uint64_t addr, uint64_t value) {
    if (addr >= PERFPOTION_MMIO_VMID_PTE_BASE_0 && addr <= PERFPOTION_MMIO_VMID_PTE_BASE_15) {
        int vmid = (addr - PERFPOTION_MMIO_VMID_PTE_BASE_0) / 8;
        m_vmid_pte_bases[vmid] = value;
        // Optionally, update MMU page table root for this VMID
        // m_memory.GetMMU(vmid).SetPageTableRoot(value);
    } else {
        // Handle other registers
    }
}

} // namespace perfpotion_model
