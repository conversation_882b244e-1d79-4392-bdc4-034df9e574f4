#include <chrono>

#include "model_lib_utility.h"
#include "model.h"

namespace perfpotion_model {

PotionModel::PotionModel() : m_memory_lock(m_config.threading) {
    // if (m_config.random_scheduling) {
    //     if (m_config.random_scheduling_seed == 0) {
    //         m_config.random_scheduling_seed = std::chrono::high_resolution_clock::now().time_since_epoch().count();
    //     }
    //     std::cout << "[PotionModel] random scheduling seed: " << m_config.random_scheduling_seed << std::endl;
    // }
    for (int chip_id = 0; chip_id < m_config.chip_num; ++chip_id) {
        m_chips.push_back(std::make_unique<Chip>(chip_id, &m_memory_lock));
    }
}

void PotionModel::Run() {
    m_running = true;
    m_model_thread = std::make_unique<std::thread>([this]{
        m_start_tp = model_engine::Now();
        RunSp();
        // if (m_config.threading) {
        //     RunMp();
        // } else {
        //     RunSp();
        // }
        auto duration = model_engine::GetDuration(m_start_tp, model_engine::Now());
        // std::cout << "[PotionModel] threading on: " << m_config.threading << std::endl;
        // std::cout << "[PotionModel] thread num: " << std::dec << m_config.thread_num << std::endl;
        std::cout << "[PotionModel] step cnt: " << std::dec << m_step_cnt << std::endl;
        std::cout << "[PotionModel] time elapsed: " << std::dec << (duration / 1e6) << "s" << std::endl;
        std::cout << "[PotionModel] simulation speed: " << std::dec << static_cast<uint64_t>(m_step_cnt * 1e6 / duration) << std::endl;
    });
}

void PotionModel::RunSp() {
    while(m_running) {
        // if (m_config.heart_beat_interval) {
        //     while (m_step_cnt >= m_next_heart_beat_step_cnt) {
        //         std::cout << "[PotionModel] step cnt " << std::dec << m_next_heart_beat_step_cnt << std::endl;
        //         m_next_heart_beat_step_cnt += m_config.heart_beat_interval;
        //     }
        // }
        for(auto& Chip : m_chips) {
            Chip->Step();
        }
        UpdateStepCnt();
    }
}

void PotionModel::RunMp() {
}

void PotionModel::UpdateStepCnt() {
    m_step_cnt = 0;
    for (auto& Chip : m_chips) {
        m_step_cnt += Chip->GetStepCnt();
    }
}

void PotionModel::Stop() {
    m_running = false;
    m_model_thread->join();
}

}
