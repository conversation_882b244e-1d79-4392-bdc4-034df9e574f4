#pragma once

class Chip;

#include "cmodel_memory_if.h"
#include "cmodel_interface.h"
#include "model_utils.h"
#include "abstract_memory.h"
#include "mmu.h"
#include "virtual_memory_manager.h"
#include <array>
#include <memory>

namespace perfpotion_model{

using MemoryLock = RecursiveLock;

class Memory : public MemoryIf{
public:
    Memory(MemoryLock* lock);
    void SetChip(Chip* chip) { m_chip = chip;}

    void SetHostMemoryIf(HostMemoryIf host_mem_if) { m_host_mem_if = host_mem_if;}

    void Read(uint64_t addr, size_t size, uint8_t* dst) override;
    void Write(uint64_t addr, size_t size, const uint8_t* dst) override;

private:
    Chip* m_chip = nullptr;
    AbstractMemory m_device_memory;
    HostMemoryIf m_host_mem_if;
    MemoryLock* m_lock = nullptr;
    std::array<std::unique_ptr<MMU>, 16> m_mmus;
};
}