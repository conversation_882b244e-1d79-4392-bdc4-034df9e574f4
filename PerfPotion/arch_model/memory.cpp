#include "memory_address.h"
#include "memory.h"
#include "chip.h"
#include "perfpotion_mmio.h"

namespace perfpotion_model {

Memory::Memory(MemoryLock* lock) : m_lock(lock) {
    for (auto& mmu : m_mmus) {
        mmu = std::make_unique<MMU>(m_device_memory);
    }
}

void Memory::Read(uint64_t addr, size_t size, uint8_t* dst, int vmid) {
    assert(m_chip && "Memory::Read: m_chip not set!");
    assert(vmid >= 0 && vmid < 16 && "Memory::Read: vmid out of range!");
    uint64_t pte_base = 0;
    m_chip->ReadRegister(PERFPOTION_MMIO_VMID_PTE_BASE_0 + vmid * 8, &pte_base);
    m_mmus[vmid]->SetPageTableRoot(pte_base);
    VirtualMemoryManager::Read(addr, size, dst, *m_mmus[vmid], m_device_memory);
}

void Memory::Write(uint64_t addr, size_t size, const uint8_t* src, int vmid) {
    assert(m_chip && "Memory::Write: m_chip not set!");
    assert(vmid >= 0 && vmid < 16 && "Memory::Write: vmid out of range!");
    uint64_t pte_base = 0;
    m_chip->ReadRegister(PERFPOTION_MMIO_VMID_PTE_BASE_0 + vmid * 8, &pte_base);
    m_mmus[vmid]->SetPageTableRoot(pte_base);
    VirtualMemoryManager::Write(addr, size, src, *m_mmus[vmid], m_device_memory);
}

} // namespace perfpotion_model