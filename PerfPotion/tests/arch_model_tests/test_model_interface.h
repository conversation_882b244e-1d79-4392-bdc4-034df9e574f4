#include <iostream>
#include <thread>
#include <chrono>
#include <cassert>

// Forward declarations for PerfPotion API
extern "C" {
    void* ppCreateDevice(int chip_id, void* read_host_mem_cb, void* write_host_mem_cb);
    void ppDestroyDevice(void* handle);
    int ppGetDeviceCount();
    void ppReadMemory(void* handle, uint64_t addr, size_t size, uint8_t* data);
    void ppWriteMemory(void* handle, uint64_t addr, size_t size, const uint8_t* data);
    void ppReadRegister(void* handle, uint64_t addr, uint64_t* value);
    void ppWriteRegister(void* handle, uint64_t addr, uint64_t value);
}

// Host memory callback functions
void read_host_memory(void* context, uint64_t addr, size_t size, uint8_t* data) {
    // Simple implementation: initialize with pattern for testing
    for (size_t i = 0; i < size; ++i) {
        data[i] = static_cast<uint8_t>((addr + i) & 0xFF);
    }
    std::cout << "Host read: addr=0x" << std::hex << addr << ", size=" << std::dec << size << std::endl;
}

void write_host_memory(void* context, uint64_t addr, size_t size, const uint8_t* data) {
    std::cout << "Host write: addr=0x" << std::hex << addr << ", size=" << std::dec << size;
    std::cout << ", data=0x";
    for (size_t i = 0; i < std::min(size, size_t(8)); ++i) {
        std::cout << std::hex << static_cast<int>(data[i]);
    }
    std::cout << std::dec << std::endl;
}

int main() {
    std::cout << "Starting PerfPotion arch_model API test..." << std::endl;
    
    // Get device count
    int device_count = ppGetDeviceCount();
    std::cout << "Available devices: " << device_count << std::endl;
    
    constexpr int chip_num = 4;
    void* devices[chip_num] = {nullptr};
    
    // Create devices
    std::cout << "Creating devices..." << std::endl;
    for(int chip_id = 0; chip_id < chip_num; ++chip_id) {
        devices[chip_id] = ppCreateDevice(chip_id, 
                                         reinterpret_cast<void*>(read_host_memory),
                                         reinterpret_cast<void*>(write_host_memory));
        if (devices[chip_id] != nullptr) {
            std::cout << "Device " << chip_id << " created successfully" << std::endl;
        } else {
            std::cout << "Failed to create device " << chip_id << std::endl;
            continue;
        }
        
        // Test memory operations
        uint64_t test_addr = 0xc00000000000;  // local global memory
        uint64_t test_value = 0x1234567890ABCDEF;
        uint64_t read_value = 0;
        
        std::cout << "Testing device " << chip_id << " memory operations..." << std::endl;
        
        // Test memory write/read
        ppWriteMemory(devices[chip_id], test_addr, sizeof(test_value), 
                     reinterpret_cast<const uint8_t*>(&test_value));
        
        ppReadMemory(devices[chip_id], test_addr, sizeof(read_value), 
                    reinterpret_cast<uint8_t*>(&read_value));
        
        std::cout << "Memory test - wrote: 0x" << std::hex << test_value 
                  << ", read: 0x" << read_value << std::dec << std::endl;
        
        // Test register operations
        uint64_t reg_addr = 0x1000;
        uint64_t reg_value = 0xDEADBEEF;
        uint64_t reg_read = 0;
        
        ppWriteRegister(devices[chip_id], reg_addr, reg_value);
        ppReadRegister(devices[chip_id], reg_addr, &reg_read);
        
        std::cout << "Register test - wrote: 0x" << std::hex << reg_value 
                  << ", read: 0x" << reg_read << std::dec << std::endl;
    }
    
    // Let the model run for a bit
    std::cout << "Running model for 1 second..." << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    // Cleanup devices
    std::cout << "Cleaning up devices..." << std::endl;
    for(int chip_id = 0; chip_id < chip_num; ++chip_id) {
        if (devices[chip_id] != nullptr) {
            ppDestroyDevice(devices[chip_id]);
            std::cout << "Device " << chip_id << " destroyed" << std::endl;
        }
    }
    
    std::cout << "PerfPotion arch_model API test completed successfully!" << std::endl;
    return 0;
}

