# Architecture Model Interface Test

## 目的
测试PerfPotion架构模型的完整API接口功能，包括设备创建、内存操作、寄存器操作等。

## 测试内容
- 设备创建和销毁
- 内存读写操作（64位、32位、字节数组）
- 寄存器读写操作
- 主机内存回调函数
- 多种数据类型的验证

## 运行方式
```bash
./bin/test_model_interface
```

## 预期输出
```
Starting PerfPotion arch_model API test...
Available devices: 8
Creating devices...
Device 0 created successfully
Testing device 0 memory write...
Device memory write completed successfully
✓ Memory write/read verification PASSED for device 0
✓ Register write/read verification PASSED for device 0
32-bit test - wrote: 0x12345678, read: 0x12345678 ✓ PASSED
Array test - wrote: 0xaa 0xbb 0xcc 0xdd 0xee 0xff 0x11 0x22 
           - read:  0xaa 0xbb 0xcc 0xdd 0xee 0xff 0x11 0x22  ✓ PASSED
PerfPotion arch_model API test completed successfully!
```
