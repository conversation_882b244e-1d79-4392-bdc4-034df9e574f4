#include <iostream>
#include <cassert>

// Forward declarations for PerfPotion API
extern "C" {
    void* ppCreateDevice(int chip_id, void* read_host_mem_cb, void* write_host_mem_cb);
    void ppDestroyDevice(void* handle);
    int ppGetDeviceCount();
    void ppReadMemory(void* handle, uint64_t addr, size_t size, uint8_t* data);
    void ppWriteMemory(void* handle, uint64_t addr, size_t size, const uint8_t* data);
}

// Host memory callback functions
void read_host_memory(void* context, uint64_t addr, size_t size, uint8_t* data) {
    for (size_t i = 0; i < size; ++i) {
        data[i] = static_cast<uint8_t>((addr + i) & 0xFF);
    }
    std::cout << "Host read: addr=0x" << std::hex << addr << ", size=" << std::dec << size << std::endl;
}

void write_host_memory(void* context, uint64_t addr, size_t size, const uint8_t* data) {
    std::cout << "Host write: addr=0x" << std::hex << addr << ", size=" << std::dec << size << std::endl;
}

int main() {
    std::cout << "=== PerfPotion VMID Parameter Example ===" << std::endl;
    
    // Create device
    void* device = ppCreateDevice(0, reinterpret_cast<void*>(read_host_memory), 
                                    reinterpret_cast<void*>(write_host_memory));
    if (!device) {
        std::cerr << "Failed to create device" << std::endl;
        return 1;
    }
    
    std::cout << "Device created successfully" << std::endl;
    
    // Test memory operations with default vmid (0)
    uint64_t test_addr = 0xc00000000000;  // local global memory
    uint64_t test_value = 0x1234567890ABCDEF;
    uint64_t read_value = 0;
    
    std::cout << "\n--- Testing with default VMID (0) ---" << std::endl;
    ppWriteMemory(device, test_addr, sizeof(test_value), 
                 reinterpret_cast<const uint8_t*>(&test_value));
    ppReadMemory(device, test_addr, sizeof(read_value), 
                reinterpret_cast<uint8_t*>(&read_value));
    
    std::cout << "VMID 0 - wrote: 0x" << std::hex << test_value 
              << ", read: 0x" << read_value << std::dec << std::endl;
    assert(test_value == read_value);
    std::cout << "✓ VMID 0 test PASSED" << std::endl;
    
    // Note: The current ppReadMemory/ppWriteMemory API doesn't expose vmid parameter yet
    // This example shows the internal API structure that now supports vmid
    // In the future, you could extend the API to include vmid parameter like:
    // ppReadMemoryWithVMID(device, test_addr, sizeof(read_value), 
    //                      reinterpret_cast<uint8_t*>(&read_value), vmid);
    
    std::cout << "\n--- API Structure Information ---" << std::endl;
    std::cout << "Internal Memory API now supports:" << std::endl;
    std::cout << "- Memory::Read(addr, size, dst, vmid)" << std::endl;
    std::cout << "- Memory::Write(addr, size, src, vmid)" << std::endl;
    std::cout << "- Chip::ReadMemory(addr, size, dst, vmid)" << std::endl;
    std::cout << "- Chip::WriteMemory(addr, size, src, vmid)" << std::endl;
    std::cout << "- VMID range: 0-15 (16 virtual memory contexts)" << std::endl;
    std::cout << "- VMID is no longer extracted from address high bits" << std::endl;
    std::cout << "- Each VMID has its own MMU and page table root" << std::endl;
    
    // Cleanup
    ppDestroyDevice(device);
    std::cout << "\nDevice destroyed" << std::endl;
    std::cout << "=== VMID Example Completed Successfully ===" << std::endl;
    
    return 0;
}
