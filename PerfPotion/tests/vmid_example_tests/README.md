# VMID Parameter Example Test

## 目的
演示新的VMID参数API的使用方法，展示VMID从地址高位提取改为独立参数的变化。

## 测试内容
- 展示新的API结构和参数
- 演示默认VMID(0)的使用
- 说明VMID参数的范围和功能
- 展示向后兼容性

## 运行方式
```bash
./bin/test_vmid_example
```

## 预期输出
```
=== PerfPotion VMID Parameter Example ===
Device created successfully

--- Testing with default VMID (0) ---
VMID 0 - wrote: 0x1234567890abcdef, read: 0x1234567890abcdef
✓ VMID 0 test PASSED

--- API Structure Information ---
Internal Memory API now supports:
- Memory::Read(addr, size, dst, vmid)
- Memory::Write(addr, size, src, vmid)
- Chip::ReadMemory(addr, size, dst, vmid)
- Chip::WriteMemory(addr, size, src, vmid)
- VMID range: 0-15 (16 virtual memory contexts)
- VMID is no longer extracted from address high bits
- Each VMID has its own MMU and page table root

=== VMID Example Completed Successfully ===
```
