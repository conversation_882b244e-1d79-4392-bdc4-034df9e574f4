cmake_minimum_required(VERSION 3.12)

add_subdirectory(arch_model_tests)
add_subdirectory(dlopen_tests)

# Add VA->PA test
add_executable(test_va2pa test_va2pa.cpp)
target_include_directories(test_va2pa PRIVATE ${CMAKE_SOURCE_DIR}/arch_model)
target_link_libraries(test_va2pa archmodel)
add_test(NAME test_va2pa COMMAND test_va2pa)

# Add VMID VA->PA test
add_executable(test_vmid_va2pa test_vmid_va2pa.cpp)
target_include_directories(test_vmid_va2pa PRIVATE ${CMAKE_SOURCE_DIR}/arch_model)
target_link_libraries(test_vmid_va2pa archmodel)
add_test(NAME test_vmid_va2pa COMMAND test_vmid_va2pa)

# Add VMID example test
add_executable(test_vmid_example test_vmid_example.cpp)
target_include_directories(test_vmid_example PRIVATE ${CMAKE_SOURCE_DIR}/arch_model)
target_link_libraries(test_vmid_example archmodel)
add_test(NAME test_vmid_example COMMAND test_vmid_example)