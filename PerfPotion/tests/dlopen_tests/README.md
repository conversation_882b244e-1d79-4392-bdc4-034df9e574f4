# Dynamic Library Loading Test

## 目的
测试PerfPotion动态库的加载和符号解析功能，验证dlopen/dlsym机制的正确性。

## 测试内容
- 动态加载libarchmodel.so
- 解析所有API函数符号
- 通过动态加载的函数进行设备操作
- 测试多设备并发操作
- 验证动态库的卸载

## 运行方式
```bash
./bin/test_dlopen_interface
```

## 预期输出
```
Starting PerfPotion dlopen test...
Successfully loaded libarchmodel.so
All function symbols loaded successfully
Available devices: 8
Creating devices...
Device 0 created successfully via dlopen
Testing device 0 memory operations via dlopen...
64-bit memory test - wrote: 0x1234567890abcdef, read: 0x1234567890abcdef
32-bit memory test - wrote: 0xdeadbeef, read: 0xdeadbeef
Array memory test - wrote: 0xaa 0xbb 0xcc 0xdd 0xee 0xff 0x11 0x22 
                    read: 0xaa 0xbb 0xcc 0xdd 0xee 0xff 0x11 0x22 
Register test - wrote: 0xcafebabe, read: 0xcafebabe
PerfPotion dlopen test completed successfully!
```
