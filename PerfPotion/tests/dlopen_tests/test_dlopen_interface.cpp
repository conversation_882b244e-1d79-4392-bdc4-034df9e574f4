#include <iostream>
#include <dlfcn.h>
#include <cassert>
#include <cstring>
#include <thread>
#include <chrono>

// Function pointer types for the PerfPotion API
typedef void* (*ppCreateDevice_t)(int chip_id, void* read_host_mem_cb, void* write_host_mem_cb);
typedef void (*ppDestroyDevice_t)(void* handle);
typedef int (*ppGetDeviceCount_t)();
typedef void (*ppReadMemory_t)(void* handle, uint64_t addr, size_t size, uint8_t* data);
typedef void (*ppWriteMemory_t)(void* handle, uint64_t addr, size_t size, const uint8_t* data);
typedef void (*ppReadRegister_t)(void* handle, uint64_t addr, uint64_t* value);
typedef void (*ppWriteRegister_t)(void* handle, uint64_t addr, uint64_t value);

// Host memory callback functions
void read_host_memory(void* context, uint64_t addr, size_t size, uint8_t* data) {
    // Simple implementation: initialize with pattern for testing
    for (size_t i = 0; i < size; ++i) {
        data[i] = static_cast<uint8_t>((addr + i) & 0xFF);
    }
    std::cout << "Host read: addr=0x" << std::hex << addr << ", size=" << std::dec << size << std::endl;
}

void write_host_memory(void* context, uint64_t addr, size_t size, const uint8_t* data) {
    std::cout << "Host write: addr=0x" << std::hex << addr << ", size=" << std::dec << size;
    std::cout << ", data=0x";
    for (size_t i = 0; i < std::min(size, size_t(8)); ++i) {
        std::cout << std::hex << static_cast<int>(data[i]);
    }
    std::cout << std::dec << std::endl;
}

int main() {
    std::cout << "Starting PerfPotion dlopen test..." << std::endl;
    
    // Load the shared library
    // First try relative to current directory (when run from PerfPotion/)
    void* lib_handle = dlopen("./build/libarchmodel.so", RTLD_LAZY);
    if (!lib_handle) {
        // Try relative to build/bin/ directory
        lib_handle = dlopen("../libarchmodel.so", RTLD_LAZY);
    }
    if (!lib_handle) {
        // Try current directory (when run from build/)
        lib_handle = dlopen("./libarchmodel.so", RTLD_LAZY);
    }
    if (!lib_handle) {
        std::cerr << "Error loading library: " << dlerror() << std::endl;
        return 1;
    }
    
    std::cout << "Successfully loaded libarchmodel.so" << std::endl;
    
    // Clear any existing error
    dlerror();
    
    // Load function symbols
    ppCreateDevice_t ppCreateDevice = (ppCreateDevice_t) dlsym(lib_handle, "ppCreateDevice");
    if (!ppCreateDevice) {
        std::cerr << "Error loading ppCreateDevice: " << dlerror() << std::endl;
        dlclose(lib_handle);
        return 1;
    }
    
    ppDestroyDevice_t ppDestroyDevice = (ppDestroyDevice_t) dlsym(lib_handle, "ppDestroyDevice");
    if (!ppDestroyDevice) {
        std::cerr << "Error loading ppDestroyDevice: " << dlerror() << std::endl;
        dlclose(lib_handle);
        return 1;
    }
    
    ppGetDeviceCount_t ppGetDeviceCount = (ppGetDeviceCount_t) dlsym(lib_handle, "ppGetDeviceCount");
    if (!ppGetDeviceCount) {
        std::cerr << "Error loading ppGetDeviceCount: " << dlerror() << std::endl;
        dlclose(lib_handle);
        return 1;
    }
    
    ppReadMemory_t ppReadMemory = (ppReadMemory_t) dlsym(lib_handle, "ppReadMemory");
    if (!ppReadMemory) {
        std::cerr << "Error loading ppReadMemory: " << dlerror() << std::endl;
        dlclose(lib_handle);
        return 1;
    }
    
    ppWriteMemory_t ppWriteMemory = (ppWriteMemory_t) dlsym(lib_handle, "ppWriteMemory");
    if (!ppWriteMemory) {
        std::cerr << "Error loading ppWriteMemory: " << dlerror() << std::endl;
        dlclose(lib_handle);
        return 1;
    }
    
    ppReadRegister_t ppReadRegister = (ppReadRegister_t) dlsym(lib_handle, "ppReadRegister");
    if (!ppReadRegister) {
        std::cerr << "Error loading ppReadRegister: " << dlerror() << std::endl;
        dlclose(lib_handle);
        return 1;
    }
    
    ppWriteRegister_t ppWriteRegister = (ppWriteRegister_t) dlsym(lib_handle, "ppWriteRegister");
    if (!ppWriteRegister) {
        std::cerr << "Error loading ppWriteRegister: " << dlerror() << std::endl;
        dlclose(lib_handle);
        return 1;
    }
    
    std::cout << "All function symbols loaded successfully" << std::endl;
    
    // Test the API functions
    int device_count = ppGetDeviceCount();
    std::cout << "Available devices: " << device_count << std::endl;
    
    constexpr int chip_num = 2;  // Test with fewer devices for dlopen test
    void* devices[chip_num] = {nullptr};
    
    // Create devices
    std::cout << "Creating devices..." << std::endl;
    for(int chip_id = 0; chip_id < chip_num; ++chip_id) {
        devices[chip_id] = ppCreateDevice(chip_id, 
                                         reinterpret_cast<void*>(read_host_memory),
                                         reinterpret_cast<void*>(write_host_memory));
        if (devices[chip_id] != nullptr) {
            std::cout << "Device " << chip_id << " created successfully via dlopen" << std::endl;
        } else {
            std::cout << "Failed to create device " << chip_id << " via dlopen" << std::endl;
            continue;
        }
        
        // Test memory operations with different data sizes
        std::cout << "Testing device " << chip_id << " memory operations via dlopen..." << std::endl;
        
        // Test 64-bit memory operations
        uint64_t test_addr = 0xc00000000000;  // local global memory
        uint64_t test_value_64 = 0x1234567890ABCDEF;
        uint64_t read_value_64 = 0;
        
        ppWriteMemory(devices[chip_id], test_addr, sizeof(test_value_64), 
                     reinterpret_cast<const uint8_t*>(&test_value_64));
        
        ppReadMemory(devices[chip_id], test_addr, sizeof(read_value_64), 
                    reinterpret_cast<uint8_t*>(&read_value_64));
        
        std::cout << "64-bit memory test - wrote: 0x" << std::hex << test_value_64 
                  << ", read: 0x" << read_value_64 << std::dec << std::endl;
        
        assert(test_value_64 == read_value_64);
        
        // Test 32-bit memory operations
        uint32_t test_value_32 = 0xDEADBEEF;
        uint32_t read_value_32 = 0;
        uint64_t test_addr_32 = test_addr + 16;
        
        ppWriteMemory(devices[chip_id], test_addr_32, sizeof(test_value_32), 
                     reinterpret_cast<const uint8_t*>(&test_value_32));
        
        ppReadMemory(devices[chip_id], test_addr_32, sizeof(read_value_32), 
                    reinterpret_cast<uint8_t*>(&read_value_32));
        
        std::cout << "32-bit memory test - wrote: 0x" << std::hex << test_value_32 
                  << ", read: 0x" << read_value_32 << std::dec << std::endl;
        
        assert(test_value_32 == read_value_32);
        
        // Test byte array operations
        uint8_t test_array[8] = {0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x11, 0x22};
        uint8_t read_array[8] = {0};
        uint64_t test_addr_array = test_addr + 32;
        
        ppWriteMemory(devices[chip_id], test_addr_array, sizeof(test_array), test_array);
        ppReadMemory(devices[chip_id], test_addr_array, sizeof(read_array), read_array);
        
        std::cout << "Array memory test - wrote: ";
        for (int i = 0; i < 8; ++i) {
            std::cout << "0x" << std::hex << static_cast<int>(test_array[i]) << " ";
        }
        std::cout << std::dec << std::endl << "                    read: ";
        for (int i = 0; i < 8; ++i) {
            std::cout << "0x" << std::hex << static_cast<int>(read_array[i]) << " ";
        }
        std::cout << std::dec << std::endl;
        
        assert(memcmp(test_array, read_array, sizeof(test_array)) == 0);
        
        // Test register operations
        uint64_t reg_addr = 0x1000;
        uint64_t reg_value = 0xCAFEBABE;
        uint64_t reg_read = 0;
        
        ppWriteRegister(devices[chip_id], reg_addr, reg_value);
        ppReadRegister(devices[chip_id], reg_addr, &reg_read);
        
        std::cout << "Register test - wrote: 0x" << std::hex << reg_value 
                  << ", read: 0x" << reg_read << std::dec << std::endl;
    }
    
    // Let the model run for a bit
    std::cout << "Running model for 1 second..." << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    // Cleanup devices
    std::cout << "Cleaning up devices..." << std::endl;
    for(int chip_id = 0; chip_id < chip_num; ++chip_id) {
        if (devices[chip_id] != nullptr) {
            ppDestroyDevice(devices[chip_id]);
            std::cout << "Device " << chip_id << " destroyed" << std::endl;
        }
    }
    
    // Close the library
    dlclose(lib_handle);
    std::cout << "Library closed successfully" << std::endl;
    
    std::cout << "PerfPotion dlopen test completed successfully!" << std::endl;
    return 0;
}