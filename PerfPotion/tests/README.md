# PerfPotion Tests

本目录包含了PerfPotion架构模型的所有测试用例，每个测试用例都有独立的目录和文档。

## 目录结构

```
tests/
├── README.md                    # 本文件
├── CMakeLists.txt              # 主构建配置
├── arch_model_tests/           # 架构模型接口测试
│   ├── README.md
│   ├── CMakeLists.txt
│   ├── test_model_interface.cpp
│   └── test_model_interface.h
├── dlopen_tests/               # 动态库加载测试
│   ├── README.md
│   ├── CMakeLists.txt
│   └── test_dlopen_interface.cpp
├── va2pa_tests/                # VA->PA转换测试
│   ├── README.md
│   ├── CMakeLists.txt
│   └── test_va2pa.cpp
├── vmid_va2pa_tests/           # VMID VA->PA转换测试
│   ├── README.md
│   ├── CMakeLists.txt
│   └── test_vmid_va2pa.cpp
└── vmid_example_tests/         # VMID参数示例测试
    ├── README.md
    ├── CMakeLists.txt
    └── test_vmid_example.cpp
```

## 测试用例说明

| 测试目录 | 测试目的 | 主要功能 |
|---------|---------|---------|
| `arch_model_tests` | 架构模型API测试 | 设备管理、内存操作、寄存器操作 |
| `dlopen_tests` | 动态库加载测试 | dlopen/dlsym、符号解析、动态调用 |
| `va2pa_tests` | 地址转换测试 | 四级页表、VA->PA转换、MMU功能 |
| `vmid_va2pa_tests` | 多VMID地址转换测试 | 多虚拟内存空间、VMID寄存器 |
| `vmid_example_tests` | VMID参数示例 | 新API演示、向后兼容性 |

## 运行所有测试

### 方法1：使用测试脚本
```bash
cd PerfPotion
./run_tests.sh
```

### 方法2：单独运行测试
```bash
cd PerfPotion/build

# 架构模型接口测试
./bin/test_model_interface

# 动态库加载测试
./bin/test_dlopen_interface

# VA->PA转换测试
./bin/test_va2pa

# VMID VA->PA转换测试
./bin/test_vmid_va2pa

# VMID参数示例测试
./bin/test_vmid_example
```

### 方法3：使用CTest
```bash
cd PerfPotion/build
ctest --verbose
```

## 构建要求

- CMake 3.12+
- C++20 支持的编译器
- Linux 环境

## 测试覆盖范围

- ✅ 基础API功能
- ✅ 内存管理和虚拟内存
- ✅ 设备管理
- ✅ 寄存器操作
- ✅ 动态库加载
- ✅ 多VMID支持
- ✅ 向后兼容性

## 添加新测试

1. 在 `tests/` 目录下创建新的测试目录
2. 添加 `CMakeLists.txt` 和 `README.md`
3. 在主 `tests/CMakeLists.txt` 中添加 `add_subdirectory(your_test_dir)`
4. 重新构建项目
