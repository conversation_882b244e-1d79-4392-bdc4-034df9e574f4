#include "abstract_memory.h"
#include "mmu.h"
#include "pte.h"
#include <cassert>
#include <iostream>
#include <vector>
#include <array>

// 模拟16个VMID的页表基地址寄存器和多MMU
int main() {
    AbstractMemory pmem;
    std::array<MMU, 16> mmus = {MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem), MMU(pmem)};
    std::array<uint64_t, 16> vmid_pte_base = {};

    // 选一个vmid
    int vmid = 7;
    uint64_t p4_pa = 0x10000 + vmid * 0x1000;
    uint64_t p3_pa = 0x20000 + vmid * 0x1000;
    uint64_t p2_pa = 0x30000 + vmid * 0x1000;
    uint64_t p1_pa = 0x40000 + vmid * 0x1000;
    uint64_t data_pa = 0x50000 + vmid * 0x1000;
    uint64_t va = (static_cast<uint64_t>(vmid) << 48) | 0x12345000;

    // 解析各级索引
    VirtualAddress vaddr;
    vaddr.full = va;
    size_t p4_idx = vaddr.fields.p4_index;
    size_t p3_idx = vaddr.fields.p3_index;
    size_t p2_idx = vaddr.fields.p2_index;
    size_t p1_idx = vaddr.fields.p1_index;

    // 构造并写入P4表
    PageTableEntry p4_entry = {};
    p4_entry.present = 1;
    p4_entry.writable = 1;
    p4_entry.ppn = p3_pa >> PAGE_SHIFT;
    pmem.Write(p4_pa + p4_idx * sizeof(PageTableEntry), sizeof(PageTableEntry), (uint8_t*)&p4_entry);

    // 构造并写入P3表
    PageTableEntry p3_entry = {};
    p3_entry.present = 1;
    p3_entry.writable = 1;
    p3_entry.ppn = p2_pa >> PAGE_SHIFT;
    pmem.Write(p3_pa + p3_idx * sizeof(PageTableEntry), sizeof(PageTableEntry), (uint8_t*)&p3_entry);

    // 构造并写入P2表
    PageTableEntry p2_entry = {};
    p2_entry.present = 1;
    p2_entry.writable = 1;
    p2_entry.ppn = p1_pa >> PAGE_SHIFT;
    pmem.Write(p2_pa + p2_idx * sizeof(PageTableEntry), sizeof(PageTableEntry), (uint8_t*)&p2_entry);

    // 构造并写入P1表
    PageTableEntry p1_entry = {};
    p1_entry.present = 1;
    p1_entry.writable = 1;
    p1_entry.ppn = data_pa >> PAGE_SHIFT;
    pmem.Write(p1_pa + p1_idx * sizeof(PageTableEntry), sizeof(PageTableEntry), (uint8_t*)&p1_entry);

    // 设置vmid寄存器
    vmid_pte_base[vmid] = p4_pa;
    mmus[vmid].SetPageTableRoot(vmid_pte_base[vmid]);

    // 在物理页写入数据
    uint64_t test_value = 0x1234567890abcdef;
    pmem.Write(data_pa, sizeof(test_value), (uint8_t*)&test_value);

    // VA->PA转换
    auto pa_opt = mmus[vmid].Translate(va, false);
    assert(pa_opt.has_value());
    assert(*pa_opt == data_pa);

    // 通过MMU读数据
    uint64_t read_value = 0;
    mmus[vmid].Read(va, sizeof(read_value), (uint8_t*)&read_value);
    assert(read_value == test_value);

    std::cout << "VMID寄存器/多MMU VA->PA测试通过: VMID=" << vmid << ", VA 0x" << std::hex << va << " -> PA 0x" << *pa_opt << ", value=0x" << read_value << std::endl;
    return 0;
}
