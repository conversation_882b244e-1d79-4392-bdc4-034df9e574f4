# PerfPotion

PerfPotion model is an architectural performance optimization model for AI processors. It integrates design patterns from GPGPU-SIM (https://github.com/gpgpu-sim/gpgpu-sim_distribution) and gem5-gpu (https://github.com/gem5-gpu), introduces further innovations, and is easier to adapt to upper-level CUDA drivers and the OpenCL API.

We aim to explore new designs on these traditional architectures and bring something fresh to the world. Everything you need is in the source code. Come on! Start your PerfPotion journey.

## Build

```
mkdir build && cd build

cmake -GNinja -DCMAKE_BUILD_TYPE=Debug -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..

ninja

# Optional, install it.
ninja install

export LD_LIBRARY_PATH=<YOUR_WORKSPACE_PATH>/PerfPotion/install/lib:${LD_LIBRARY_PATH}
```

# Run test case

First, you need to add the corresponding library path for perfpotion.
```
export LD_LIBRARY_PATH=<YOUR_WORKSPACE_PATH>/PerfPotion/build/lib:${LD_LIBRARY_PATH}

cd build/bin/tests/unittests/0_simpleAdd

./simpleAdd
```