---
 # Language: None, Cpp, Java, JavaScript, ObjC, Proto, TableGen, TextProto
 Language: Cpp
 # BasedOnStyle: LLVM
 # Offset for access specifiers (public, private, etc.)
 AccessModifierOffset: -4
 # Alignment after open bracket (open parenthesis, open angle bracket, open square bracket): <PERSON>gn, <PERSON>t<PERSON><PERSON>gn, AlwaysBreak (always break after open bracket)
 AlignAfterOpenBracket: Align
 # Align all equal signs when consecutive assignments
 AlignConsecutiveAssignments: true
 # Align all variable names when consecutive declarations
 AlignConsecutiveDeclarations: true
 # Left-align escaped newlines (using backslash for line continuation)
 AlignEscapedNewlinesLeft: true
 # Horizontally align operands of binary and ternary expressions
 AlignOperands: true
 # Align consecutive trailing comments
 AlignTrailingComments: true
 # Allow all parameters of function declaration to be placed on the next line
 AllowAllParametersOfDeclarationOnNextLine: true
 # Allow short blocks to be placed on a single line
 AllowShortBlocksOnASingleLine: false
 # Allow short case labels to be placed on a single line
 AllowShortCaseLabelsOnASingleLine: false
 # Allow short functions on a single line: None, InlineOnly (defined in class), Empty (empty functions), Inline (defined in class, empty functions), All
 AllowShortFunctionsOnASingleLine: Empty
 # Allow short if statements to stay on the same line
 AllowShortIfStatementsOnASingleLine: false
 # Allow short loops to stay on the same line
 AllowShortLoopsOnASingleLine: false
 # Always break after definition return type (deprecated)
 AlwaysBreakAfterDefinitionReturnType: None
 # Always break after return type: None, All, TopLevel (top-level functions, not including functions in classes), 
 #   AllDefinitions (all definitions, not including declarations), TopLevelDefinitions (all top-level function definitions)
 AlwaysBreakAfterReturnType: None
 # Always break before multiline string literals
 AlwaysBreakBeforeMultilineStrings: false
 # Always break after template declarations
 AlwaysBreakTemplateDeclarations: false
 # false means function arguments are either all on the same line or each on its own line
 BinPackArguments: true
 # false means all parameters are either all on the same line or each on its own line
 BinPackParameters: true
 # Brace wrapping, only effective when BreakBeforeBraces is set to Custom
 BraceWrapping:   
   # After class definition
  AfterClass: false
   # After control statements
  AfterControlStatement: false
   # After enum definition
  AfterEnum: false
   # After function definition
  AfterFunction: false
   # After namespace definition
  AfterNamespace: false
   # After ObjC definition
  AfterObjCDeclaration: false
   # After struct definition
  AfterStruct: false
   # After union definition
  AfterUnion: false
   # Before catch
  BeforeCatch: true
   # Before else
  BeforeElse: true
   # Indent braces
  IndentBraces: false
 # Break before binary operators: None (break after operator), NonAssignment (break before non-assignment operators), All (break before operators)
 BreakBeforeBinaryOperators: NonAssignment
 # Break before braces: Attach (always attach braces to surrounding context), Linux (like Attach, except for functions, namespaces and class definitions), 
 #   Mozilla (like Attach, except for enums, functions, record definitions), Stroustrup (like Attach, except for function definitions, catch, else), 
 #   Allman (always break before braces), GNU (always break before braces, and add extra indentation for control statement braces), WebKit (break before functions), Custom
 #   Note: statement blocks are also considered functions here
 BreakBeforeBraces: Custom
 # Break before ternary operators
 BreakBeforeTernaryOperators: true
 # Break before comma in constructor initializer lists
 BreakConstructorInitializersBeforeComma: false
 # Character limit per line, 0 means no limit
 ColumnLimit: 200
 # Regular expression describing comments with special meaning that should not be split into multiple lines or otherwise changed
 CommentPragmas: '^ IWYU pragma:'
 # Constructor initializer list either all on one line or each on its own line
 ConstructorInitializerAllOnOneLineOrOnePerLine: false
 # Indentation width for constructor initializer lists
 ConstructorInitializerIndentWidth: 4
 # Indentation width for continuation lines
 ContinuationIndentWidth: 4
 # Remove spaces after { and before } in C++11 braced lists
 Cpp11BracedListStyle: false
 # Inherit the most commonly used pointer and reference alignment style
 DerivePointerAlignment: false
 # Disable formatting
 DisableFormat: false
 # Automatically detect if function calls and definitions are formatted as one parameter per line (Experimental)
 ExperimentalAutoDetectBinPacking: false
 # Macros that need to be interpreted as foreach loops rather than function calls
 ForEachMacros: [ foreach, Q_FOREACH, BOOST_FOREACH ]
 # Sort #include, #include matching certain regular expressions have corresponding priority, those that don't match default to INT_MAX priority (lower priority sorts first),
 #   negative priorities can be defined to ensure certain #include are always at the front
 IncludeCategories: 
   - Regex: '^"(llvm|llvm-c|clang|clang-c)/'
    Priority: 2
   - Regex: '^(<|"(gtest|isl|json)/)'
    Priority: 3
   - Regex: '.*'
    Priority: 1
 # Indent case labels
 IndentCaseLabels: false
 # Indentation width
 IndentWidth: 4
 # When function return type is on its own line, indent the function name in function declarations or definitions
 IndentWrappedFunctionNames: false
 # Keep empty lines at the start of blocks
 KeepEmptyLinesAtTheStartOfBlocks: true
 # Regular expression for macros that start a block
 MacroBlockBegin: ''
 # Regular expression for macros that end a block
 MacroBlockEnd: ''
 # Maximum number of consecutive empty lines
 MaxEmptyLinesToKeep: 1
 # Namespace indentation: None, Inner (indent content in nested namespaces), All
 NamespaceIndentation: Inner
 # Indentation width when using ObjC blocks
 ObjCBlockIndentWidth: 4
 # Add a space after @ in ObjC @property
 ObjCSpaceAfterProperty: false
 # Add a space before protocol list in ObjC
 ObjCSpaceBeforeProtocolList: true
 # Penalty for breaking function calls after (
 PenaltyBreakBeforeFirstCallParameter: 19
 # Penalty for introducing a line break in a comment
 PenaltyBreakComment: 300
 # Penalty for first break before << 
 PenaltyBreakFirstLessLess: 120
 # Penalty for introducing a line break in a string literal
 PenaltyBreakString: 1000
 # Penalty for each character outside the line character limit
 PenaltyExcessCharacter: 1000000
 # Penalty for putting function return type on its own line
 PenaltyReturnTypeOnItsOwnLine: 60
 # Pointer and reference alignment: Left, Right, Middle
 PointerAlignment: Left
 # Allow reformatting comments
 ReflowComments: true
 # Allow sorting #include
 SortIncludes: true
 # Add space after C-style cast
 SpaceAfterCStyleCast: false
 # Add space before assignment operators
 SpaceBeforeAssignmentOperators: true
 # Add space before opening parenthesis: Never, ControlStatements, Always
 SpaceBeforeParens: ControlStatements
 # Add space in empty parentheses
 SpaceInEmptyParentheses: false
 # Number of spaces before trailing comments (only applies to //)
 SpacesBeforeTrailingComments: 2
 # Add spaces after < and before > in angle brackets
 SpacesInAngles: true
 # Add spaces in container literals (ObjC and JavaScript arrays and dictionaries, etc.)
 SpacesInContainerLiterals: true
 # Add spaces in C-style cast parentheses
 SpacesInCStyleCastParentheses: true
 # Add spaces after ( and before ) in parentheses
 SpacesInParentheses: true
 # Add spaces after [ and before ] in square brackets, lambda expressions and unsized array declarations are not affected
 SpacesInSquareBrackets: true
 # Standard: Cpp03, Cpp11, Auto
 Standard: Auto
 # Tab width
 TabWidth: 4
 # Use tab characters: Never, ForIndentation, ForContinuationAndIndentation, Always
 UseTab: Never
 ...